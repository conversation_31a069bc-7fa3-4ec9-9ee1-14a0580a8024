package com.qunar.search.common;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.math.NumberUtils;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.util.JsonUtils;
import com.qunar.search.common.util.SerializeUtil;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.DeflaterOutputStream;

/**
 * <AUTHOR>
 * @date 2019/2/21
 */
@Slf4j
public class JsonUtilsTest {

	/**
	 * 测试反序列化
	 */
	@Test
	@SuppressWarnings("unchecked")
	public void testDeserialization() throws Exception{
		// Bean
		String jsonStr = "{\"name\":\"A\"}";
		TestBean bean = JsonUtils.fromJson(jsonStr, TestBean.class);
		Assert.assertNotNull(bean);
		Assert.assertTrue(StringUtils.equals(bean.getName(), "A"));

		// Bean<T>
		jsonStr = "{\"ret\":true,\"data\":{\"name\":\"A\",\"list\":[\"a\",\"b\",\"c\"]},\"errorMsg\":\"failed\",\"errorCode\":-1}";
		ApiResult<TestBean> result = JsonUtils.toBean(jsonStr, JsonUtils.buildParametricType(ApiResult.class, TestBean.class));
		Assert.assertNotNull(result);
		Assert.assertEquals(result.getData().getName(), "A");
		Assert.assertEquals(result.getData().getList().size(), 3);
		Assert.assertEquals(result.getData().getList().get(0), "a");

		// Map<String, String>
		String str = "{\"name\":\"A\",\"city\":\"beijing\"}";
		Map<String, String> simpleMap = JsonUtils.toBean(str, Map.class);
		Assert.assertNotNull(simpleMap);
		Assert.assertEquals(simpleMap.get("name"), "A");
		Assert.assertEquals(simpleMap.get("city"), "beijing");

		// Map<String, Object>
		Map<String, Object> map = JsonUtils.toBean(jsonStr, JsonUtils.buildMapType(Map.class, String.class, Object.class));
		Assert.assertNotNull(map);
		Assert.assertEquals(map.get("ret"), true);
		Assert.assertEquals(map.get("errorMsg"), "failed");
		Assert.assertTrue(map.get("data") instanceof LinkedHashMap);
		Assert.assertEquals(map.get("errorCode"), -1);
		Assert.assertEquals(((Map<String, Object>)map.get("data")).get("name"), "A");

		// 对于Map<String, String> 或 Map<String, Object> 都可以采用 JsonUtils.toBean(jsonStr, Map.class) 的方式来简化代码
		map = JsonUtils.toBean(jsonStr, Map.class);
		Assert.assertNotNull(map);
		Assert.assertEquals(map.get("ret"), true);
		Assert.assertEquals(map.get("errorMsg"), "failed");
		Assert.assertTrue(map.get("data") instanceof LinkedHashMap);
		Assert.assertEquals(map.get("errorCode"), -1);
		Assert.assertEquals(((Map<String, Object>)map.get("data")).get("name"), "A");

		// Map<String, Bean>
		String mapStr = "{\"testA\":{\"name\":\"A\",\"list\":[\"a\",\"b\",\"c\"]},\"testB\":{\"name\":\"B\",\"list\":[\"a\",\"b\",\"c\"]}}";
		Map<String, TestBean> map1 = JsonUtils.toBean(mapStr, JsonUtils.buildMapType(Map.class, String.class, TestBean.class));
		Assert.assertNotNull(map1);
		Assert.assertEquals(map1.get("testA").getName(), "A");
		Assert.assertEquals(map1.get("testB").getName(), "B");

		// Map<K, V>
		Map<String, TestBean> map2 = JsonUtils.toBean(mapStr, JsonUtils.buildParametricType(Map.class, String.class, TestBean.class));
		Assert.assertNotNull(map2);
		Assert.assertEquals(map2.get("testA"), map1.get("testA"));
		Assert.assertEquals(map2.get("testB").getName(), "B");

		// Map<String, Map<String, Bean>>
		mapStr = "{\"map1\":{\"testA\":{\"name\":\"A\",\"list\":[\"a\",\"b\",\"c\"]}}, \"map2\":{\"testB\":{\"name\":\"B\",\"list\":[\"a\",\"b\",\"c\"]}}}";
        JavaType valueType = JsonUtils.buildMapType(Map.class, String.class, TestBean.class);
		Map<String, Map<String, TestBean>> map3 = JsonUtils.toBean(mapStr, JsonUtils.buildMapType(Map.class, JsonUtils.buildType(String.class), valueType));
		Assert.assertNotNull(map3);
		Assert.assertEquals(map3.get("map1").get("testA").getName(), "A");
        Assert.assertEquals(map3.get("map2").get("testB").getName(), "B");
        Assert.assertEquals(map3.get("map1").get("testA").getList().get(0), "a");

		// List<String>
		String listString = "[\"A\",\"B\",\"C\"]";
		List<String> list = JsonUtils.toBean(listString, List.class);
		Assert.assertNotNull(list);
		Assert.assertEquals(list.get(0), "A");
		Assert.assertEquals(list.size(), 3);

		// List<Object>
		listString = "[\"A\",\"B\",1]";
		List<Object> list1 = JsonUtils.toBean(listString, List.class);
		Assert.assertNotNull(list1);
		Assert.assertEquals(list1.get(0), "A");
		Assert.assertEquals(list1.size(), 3);
		Assert.assertEquals(list1.get(2), 1);

		// List<Bean>
		String beanListString = "[{\"name\":\"A\"},{\"name\":\"B\"}]";
		List<TestBean> beanList = JsonUtils.fromJson(beanListString, JsonUtils.buildCollectionType(List.class, TestBean.class));
		Assert.assertNotNull(beanList);
		Assert.assertEquals(beanList.get(0).getName(), "A");
		Assert.assertEquals(beanList.get(1).getName(), "B");
		Assert.assertEquals(beanList.get(0).getDefaultValue(), "hello");
	}

	/**
	 * 测试序列化对象/集合到Json字符串.
	 */
	@Test
	public void testSerialization() throws Exception {
		// Bean
		TestBean bean = new TestBean("A");
		String beanString = JsonUtils.toJson(bean);
		Assert.assertEquals(beanString,"{\"name\":\"A\",\"defaultValue\":\"hello\",\"nullValue\":null,\"emptyValue\":[],\"list\":[]}");

		// Map
		Map<String, Object> map = Maps.newLinkedHashMap();
		map.put("name", "A");
		map.put("age", 2);
		String mapString = JsonUtils.toJson(map);
		Assert.assertEquals(mapString, "{\"name\":\"A\",\"age\":2}");

		// List<String>
		List<String> stringList = Lists.newArrayList("A", "B", "C");
		String listString = JsonUtils.toJson(stringList);
		Assert.assertEquals(listString, "[\"A\",\"B\",\"C\"]");

		// List<Bean>
		List<TestBean> beanList = Lists.newArrayList(new TestBean("A"), new TestBean("B"));
		String beanListString = JsonUtils.fromBean(beanList);
		Assert.assertEquals(beanListString,"[{\"name\":\"A\",\"defaultValue\":\"hello\",\"nullValue\":null,\"emptyValue\":[],\"list\":[]},{\"name\":\"B\",\"defaultValue\":\"hello\",\"nullValue\":null,\"emptyValue\":[],\"list\":[]}]");

		// Bean[]
		TestBean[] beanArray = new TestBean[] { new TestBean("A"), new TestBean("B") };
		String beanArrayString = JsonUtils.toJson(beanArray);
		Assert.assertEquals(beanArrayString,"[{\"name\":\"A\",\"defaultValue\":\"hello\",\"nullValue\":null,\"emptyValue\":[],\"list\":[]},{\"name\":\"B\",\"defaultValue\":\"hello\",\"nullValue\":null,\"emptyValue\":[],\"list\":[]}]");
	}

	@Data
	@NoArgsConstructor
	private static class TestBean {
		private String name;
		private String defaultValue = "hello";
		private String nullValue = null;
		private List<String> emptyValue = new ArrayList<>();
		private List<String> list = new ArrayList<>();

		TestBean(String name) {
			this.name = name;
		}
	}

	@Data
	private static class ApiResult<T> {
		private boolean ret;
		private T data;
		private String errorMsg;
		private int errorCode;
	}

	@Test
	public void test() {
		ArrayList<Range> fromToDateList = Lists.newArrayList();
		LocalDate fromDate = LocalDate.parse("2019-04-30", DateTimeFormatter.ISO_LOCAL_DATE);
		LocalDate toDate = LocalDate.parse("2019-05-05", DateTimeFormatter.ISO_LOCAL_DATE);
		fromToDateList.add(Range.closed(fromDate,toDate));

		LocalDate searchCheckInDate = LocalDate.parse("2019-04-30", DateTimeFormatter.ISO_LOCAL_DATE);
		boolean b = fromToDateList.stream().anyMatch(s -> s.contains(searchCheckInDate));
		System.out.println(b);
	}

	@Test
	public void testLogJson() {
		ModelHotelFeature modelHotelFeature = new ModelHotelFeature();
		modelHotelFeature.setHotelSeq("beijing_100");
		modelHotelFeature.setStars(5);
		EnumMap<FeatureType, Double> offlineFeature = Maps.newEnumMap(FeatureType.class);
		offlineFeature.put(FeatureType.ORD_MEDIAN_400D, 10.0);
		modelHotelFeature.setOfflineFeature(offlineFeature);
		System.out.println(SerializeUtil.serialize(modelHotelFeature));
        System.out.println(JsonUtils.toJson(modelHotelFeature));
	}

	@Test
	public void testLogInt() {
//		String str = "1.0";
//		double doubleV = Double.parseDouble(str);
//		int intV = (int) doubleV;
//		if (doubleV == (double)intV) {
//			System.out.println("11");
//		}
//		System.out.println((double)(int)intV);


		Double a = getDouble();

		System.out.println(a);

	}



	private Double getDouble(){
		return null;
	}
}
