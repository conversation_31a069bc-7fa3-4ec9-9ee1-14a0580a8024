package com.qunar.search.common;

import com.qunar.search.common.model.convertor.ConvertorBase;
import com.qunar.search.common.model.convertor.L1LRV2FeatureConvertor;
import org.junit.Test;

public class BucketIndexTest {
    @Test
    public void test() {
        double[] HOTEL_PRICE_BUCKET = {0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 700, 800, 1000, 1500, 2000, 2500, 3000, 5000, Double.MAX_VALUE}; // 20

        L1LRV2FeatureConvertor l1LRV2FeatureConvertor = new L1LRV2FeatureConvertor();
        int r1 = l1LRV2FeatureConvertor.getBucketIndexQuick(HOTEL_PRICE_BUCKET, 120);
        int r2 = l1LRV2FeatureConvertor.getBucketIndexQuick(HOTEL_PRICE_BUCKET, 100);
        int r3 = l1LRV2FeatureConvertor.getBucketIndexQuick(HOTEL_PRICE_BUCKET, 0);
        int r4 = l1LRV2FeatureConvertor.getBucketIndexQuick(HOTEL_PRICE_BUCKET, -10);
        int r5 = l1LRV2FeatureConvertor.getBucketIndexQuick(HOTEL_PRICE_BUCKET, 100000);
        System.out.println(r1);
        System.out.println(r2);
        System.out.println(r3);
        System.out.println(r4);
        System.out.println(r5);
    }
}
