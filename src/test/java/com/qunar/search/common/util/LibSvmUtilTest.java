package com.qunar.search.common.util;

import com.google.common.collect.Maps;

import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;

import com.qunar.search.common.enums.HotelTypeEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.List;

import static com.qunar.search.common.enums.FeatureType.ORD_CNT_6_MONTH;

/**
 * <AUTHOR>
 * @create 2020-08-17 下午7:01
 * @DESCRIPTION
 **/
public class LibSvmUtilTest {

    @Test
    public void toMapTest() {
        String str = "1:1 2:2";
        EnumMap<FeatureType, Double> featureTypeDoubleEnumMap = LibSvmUtil.stringToEnumMap(str);
        System.out.println(featureTypeDoubleEnumMap);
        assert featureTypeDoubleEnumMap.size() == 2;
    }

    @Test
    public void toLibsvmTest() {
        EnumMap<FeatureType, Double> map = Maps.newEnumMap(FeatureType.class);
        map.put(FeatureType.ORD_MEDIAN_400D, 1.0);
        map.put(FeatureType.HOLIDAY_ORDER_RATE, 0.3);
        map.put(FeatureType.SAME_CITY_ORDER_RATE, 0.8);
        String s = LibSvmUtil.enumMapToString(map);
        System.out.println(s);
    }

    @Test
    public void testMatch() {
        Assert.assertEquals(FeatureType.matchIndex(1),ORD_CNT_6_MONTH);
        Assert.assertNull(FeatureType.matchIndex(100000));
    }

    @Test
    public void jsonTest() throws IOException {
        ModelHotelFeature modelHotelFeature = new ModelHotelFeature();
        // 线上填充
        modelHotelFeature.setHotelSeq("beijing_001");
        modelHotelFeature.setOfflineFeature(LibSvmUtil.stringToEnumMap("1:1 2:2"));
        // 线上使用
        assert modelHotelFeature.getOfflineFeature().get(FeatureType.matchIndex(1)) == 1.0d;

        // 日志打印
        modelHotelFeature.setOfflineFeature(LibSvmUtil.EMPTY_MAP);
        String log = SerializeUtil.serialize(modelHotelFeature);
        System.out.println("log:  " + log);
        assert log != null;

        // 离线还原
        ModelHotelFeature recover = JsonUtils.toBean(log, ModelHotelFeature.class);
        recover.setOfflineFeatureLibSvm("1:1 2:2 3:3");
        String serialize = SerializeUtil.serialize(recover);
        System.out.println(serialize);
        // 离线使用
        assert recover.getOfflineFeature().get(FeatureType.matchIndex(1)) == 1.0d;

    }

    @Test
    public void testEnum(){

        HotelTypeEnum hotelTypeEnum = HotelTypeEnum.parseByChHotelTypeOrTypeName(null);
        System.out.println( hotelTypeEnum.getNumber() );
        System.out.println( Double.valueOf( "0.0").intValue() );
        System.out.println( Double.valueOf( "1.0").intValue() );
    }

    @Test
    public void testGuassRank(){
        for(int i =0;i<=600;i++){
            double v = RankGuassUtil.calRankGuassByIndex(i, 600);
            System.out.print(i);
            System.out.print("    ");
            System.out.println(v);
        }

    }

    private static Comparator<HotelItem> orderCnt3MonthComparator = (o1, o2) -> Integer.compare(o2.getOrderCnt3Month(), o1.getOrderCnt3Month()) ;
    /**
     * 按照三个月内单量倒序排列
     * @param hotelItemList
     */
    public static void sortItemByOrderVount(List<HotelItem> hotelItemList){

        hotelItemList.sort(orderCnt3MonthComparator);

        for(int i=0; i<hotelItemList.size(); i++){

            hotelItemList.get(i).setOrderVolumeNumber(i);
        }

    }


    @Test
    public void testCompareLoad() {

        List<HotelItem> list = new ArrayList<HotelItem>();

        HotelItem ht1 = new HotelItem();
        ht1.setOrderCnt3Month(100);
        list.add(ht1);

        HotelItem ht2 = new HotelItem();
        ht2.setOrderCnt3Month(200);
        list.add(ht2);


        HotelItem ht3 = new HotelItem();
        ht3.setOrderCnt3Month(50);
        list.add(ht3);

        HotelItem ht4 = new HotelItem();
        list.add(ht4);

        sortItemByOrderVount(list);
    }

}
