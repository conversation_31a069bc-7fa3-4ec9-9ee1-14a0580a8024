package com.qunar.search.common.util;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * Test BitUtils
 */
public class BitUtilsTest {
    @Test
    public void testFlagBit() {
        int t = 0;
        assertEquals(4, BitUtils.flagBit(t, 3));

        t = 0;
        assertEquals(0x80000000, BitUtils.flagBit(t, 32));

        t = 0;
        assertEquals(1, BitUtils.flagBit(t, 1));

        try {
            t = 0;
            BitUtils.flagBit(t, -1);
        } catch (AssertionError e) {
            assertTrue(true);
        }
    }

    public void testClearBit() {
        int t = 0;
        assertEquals(0, BitUtils.clearBit(t, 3));

        t = 0xFF;
        assertEquals(0x7F, BitUtils.clearBit(t, 8));

        t = 0xFF;
        assertEquals(0xFE, BitUtils.clearBit(t, 1));

        t = 0xFFFFFFFF;
        assertEquals(0xEFFFFFFF, BitUtils.clearBit(t, 32));

        try {
            t = 0;
            BitUtils.clearBit(t, -1);
        } catch (AssertionError e) {
            assertTrue(true);
        }
    }

    public void testGetBit() {
        assertTrue(BitUtils.getBit(0x1, 1));
        assertTrue(!BitUtils.getBit(0, 1));
        assertTrue(BitUtils.getBit(0xFFFFFFFF, 32));
        assertTrue(!BitUtils.getBit(0xEFFFFFFF, 32));
    }
}
