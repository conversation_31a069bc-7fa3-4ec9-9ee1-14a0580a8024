package com.qunar.search.common;

import com.qunar.search.common.price.render.TorchLevelUpParam;
import com.qunar.search.common.util.JsonUtils;
import org.junit.Test;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.Assert.*;

public class TorchLevelUpParamTest {
    @Test
    public void test() throws InvocationTargetException, IllegalAccessException {
        Method parse = ReflectionUtils.findMethod(TorchLevelUpParam.class, "parse", String.class);
        parse.setAccessible(true);
        String json = "{\n" +
                "    \"ver\": \"1.0.0\",\n" +
                "    \"status\": 0,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"hotelSeq\": \"shanghai_city_4910\",\n" +
                "            \"torchInfoMap\": {\n" +
                "                \"hotelIdentityLevel_HZ\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"rezen_member\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"1000\",\n" +
                "                    \"mappingLevelUp\": \"1000\",\n" +
                "                    \"hotelRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"identityRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YG\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YXH\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"0\",\n" +
                "                    \"mappingLevelUp\": \"0\",\n" +
                "                    \"hotelRule\": \"no_hit_rule\",\n" +
                "                    \"identityRule\": \"no_hit_rule\",\n" +
                "                    \"userLevelOptimize\": true,\n" +
                "                    \"userLevelStock\": {\n" +
                "                        \"memberInfoR4\": true,\n" +
                "                        \"memberInfoR2\": false,\n" +
                "                        \"memberInfoR1_5\": false,\n" +
                "                        \"memberInfoR3\": false\n" +
                "                    }\n" +
                "                },\n" +
                "                \"atour_member\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"17150109\",\n" +
                "                    \"mappingLevelUp\": \"17150109\",\n" +
                "                    \"hotelRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"identityRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"mian_sha\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"mk_memberR1\",\n" +
                "                    \"mappingLevelUp\": \"mk_memberR1\",\n" +
                "                    \"hotelRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"identityRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_RJ\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                }\n" +
                "            },\n" +
                "            \"memberGroupExpandMap\": {\n" +
                "                \"123\": {\n" +
                "                    \"ruleId\": \"2\",\n" +
                "                    \"originIdentity\": \"R1\",\n" +
                "                    \"expandIdentity\": \"R3\",\n" +
                "                    \"expId\": \"230619_ta_other_test_group_upper\",\n" +
                "                    \"budget\": \"A\"\n" +
                "                },\n" +
                "                \"234\": {\n" +
                "                    \"ruleId\": \"3\",\n" +
                "                    \"originIdentity\": \"R1\",\n" +
                "                    \"expandIdentity\": \"R3\",\n" +
                "                    \"expId\": \"230619_ta_other_test_group_upper\",\n" +
                "                    \"budget\": \"A\"\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"hotelSeq\": \"beijing_city_11352\",\n" +
                "            \"torchInfoMap\": {\n" +
                "                \"hotelIdentityLevel_HZ\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"rezen_member\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"1000\",\n" +
                "                    \"mappingLevelUp\": \"1000\",\n" +
                "                    \"hotelRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"identityRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YG\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YXH\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"0\",\n" +
                "                    \"mappingLevelUp\": \"0\",\n" +
                "                    \"hotelRule\": \"no_hit_rule\",\n" +
                "                    \"identityRule\": \"no_hit_rule\",\n" +
                "                    \"userLevelOptimize\": true,\n" +
                "                    \"userLevelStock\": {\n" +
                "                        \"memberInfoR4\": true,\n" +
                "                        \"memberInfoR2\": false,\n" +
                "                        \"memberInfoR1_5\": false,\n" +
                "                        \"memberInfoR3\": false\n" +
                "                    }\n" +
                "                },\n" +
                "                \"atour_member\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"17150109\",\n" +
                "                    \"mappingLevelUp\": \"17150109\",\n" +
                "                    \"hotelRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"identityRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"mian_sha\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"mk_memberR1\",\n" +
                "                    \"mappingLevelUp\": \"mk_memberR1\",\n" +
                "                    \"hotelRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"identityRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_RJ\": {\n" +
                "                    \"qunarLevel\": \"21\",\n" +
                "                    \"qunarLevelUp\": \"21\",\n" +
                "                    \"mappingLevel\": \"101\",\n" +
                "                    \"mappingLevelUp\": \"101\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                }\n" +
                "            },\n" +
                "            \"memberGroupExpandMap\": {\n" +
                "                \"123\": {\n" +
                "                    \"ruleId\": \"2\",\n" +
                "                    \"originIdentity\": \"R1\",\n" +
                "                    \"expandIdentity\": \"R3\",\n" +
                "                    \"expId\": \"230619_ta_other_test_group_upper\",\n" +
                "                    \"budget\": \"A\"\n" +
                "                },\n" +
                "                \"234\": {\n" +
                "                    \"ruleId\": \"3\",\n" +
                "                    \"originIdentity\": \"R1\",\n" +
                "                    \"expandIdentity\": \"R3\",\n" +
                "                    \"expId\": \"230619_ta_other_test_group_upper\",\n" +
                "                    \"budget\": \"A\"\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"ok\": true\n" +
                "}";
        Map<String, String> result = (Map<String, String>) parse.invoke(new TorchLevelUpParam(), json);
        assertNotNull(result);
        assertEquals(2, result.size());

        json = "{\n" +
                "    \"ver\": \"1.0.0\",\n" +
                "    \"status\": 0,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"hotelSeq\": \"rizhao_5609\",\n" +
                "            \"torchInfoMap\": {\n" +
                "                \"hotelIdentityLevel_HZ\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"189\",\n" +
                "                    \"mappingLevelUp\": \"189\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"rezen_member\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"1000\",\n" +
                "                    \"mappingLevelUp\": \"1000\",\n" +
                "                    \"hotelRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"identityRule\": \"YXH202204062044bdc9c8778a\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YG\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"189\",\n" +
                "                    \"mappingLevelUp\": \"189\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_YXH\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"24\",\n" +
                "                    \"mappingLevel\": \"10011\",\n" +
                "                    \"mappingLevelUp\": \"10001\",\n" +
                "                    \"hotelRule\": \"YXH20220406205265aaf6bd49\",\n" +
                "                    \"identityRule\": \"YXH20220406205265aaf6bd49\",\n" +
                "                    \"userLevelOptimize\": true,\n" +
                "                    \"userLevelStock\": {\n" +
                "                        \"memberInfoR4\": true,\n" +
                "                        \"memberInfoR2\": true,\n" +
                "                        \"memberInfoR1_5\": false,\n" +
                "                        \"memberInfoR3\": true\n" +
                "                    }\n" +
                "                },\n" +
                "                \"atour_member\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"17150213\",\n" +
                "                    \"mappingLevelUp\": \"17150213\",\n" +
                "                    \"hotelRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"identityRule\": \"YXH2022040620433a5cfe4014\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"mian_sha\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"memberinfoR1_5\",\n" +
                "                    \"mappingLevelUp\": \"memberinfoR1_5\",\n" +
                "                    \"hotelRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"identityRule\": \"YXH202204062042fbe1219a7d\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                },\n" +
                "                \"hotelIdentityLevel_RJ\": {\n" +
                "                    \"qunarLevel\": \"31\",\n" +
                "                    \"qunarLevelUp\": \"31\",\n" +
                "                    \"mappingLevel\": \"189\",\n" +
                "                    \"mappingLevelUp\": \"189\",\n" +
                "                    \"hotelRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"identityRule\": \"YXH20220406203716ad669056\",\n" +
                "                    \"userLevelOptimize\": false\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"ok\": true\n" +
                "}";

        result = (Map<String, String>) parse.invoke(new TorchLevelUpParam(), json);
        assertNotNull(result);
        assertEquals(1, result.size());
        Map map = JsonUtils.fromJson(result.get("rizhao_5609"), Map.class);
//        assertFalse(map.containsKey("torchInfoMap"));
    }
}