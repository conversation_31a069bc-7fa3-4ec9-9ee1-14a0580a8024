package com.qunar.search.common.constants;

/**
 * 配置相关的常量数据.
 */
public class DependCloseConst {

	public static final String FAKELM = "fakeLm";
	
	public static final String PLANEORDERDETAIL = "planeOrderDetail";
	
	public static final String QUICK = "quick";
	
	public static final String RECOMMEND = "recoommend";
	// channel city 日志开关：1:开，0:关
	public static final String CHANNAL_CITY_LOG_SWITCH = "channelCityLogOpen";
	public static final String BROWSEHISTORYENTRANCE = "browseHistoryEntrance";
	//读取缓存
	public static final String MEMCACHEDREAD = "memcachedRead";
	//写缓存
	public static final String MEMCACHEDWRITE = "memcachedWrite";
	//小时房销量展示开关
	public static final String HOURROOMSALESCOUNTOPEN = "hourRoomSalesCountOpen";
	//房型实时数据开关 1为开,0为关
	public static final String HOTELROOMSTATUS_SWITCH = "realtimeRoomstatusSwitch";

	/** 包房自动加权开关 1 open, 0 close */
	public static final String BAOFANG_AUTO_LEVEL = "baofangAutoLevel";

	/** 包房自动加权日志记录开关 1 open, 0 close */
	public static final String BAOFANG_AUTO_LEVEL_LOG = "baofangAutoLevelLog";

    //mock开关
    public static final String ISMOCK = "isMock";

    //querydiff更新datacenter开关
    public final static String ISDIFF = "isDiff";

	/* 123位置推广处理生效开关 */
	public static final String ZHEKOU_BAOFANG_TUIGUANG = "zhekouBaofangTuiguang";


    public final static String PORFIT_MODEL_FOR_INTERNATIONAL_HOTELS = "profitModelForInternationalHotels";
}
