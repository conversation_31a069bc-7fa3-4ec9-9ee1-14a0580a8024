package com.qunar.search.common.constants;

/**
 * 跟配置相关的一些const变量.
 */
public class ConfigConstants {

	// 开关打开
	public static final String OPEN = "1";
	// 开关关闭
	public static final String CLOSE = "0";

	// 是否用压缩redis
	public static final String COMPRESS = "COMPRESS";

	// redis1 控制开关key
	public static final String REDIS_USERPROFILE = "REDIS_USERPROFILE";

	// gpsredis控制开关key
	public static final String REDIS_GPS = "REDIS_GPS";

	// list列表页超时时间，单位秒
	public static final String REDIS_EXPIRE_LISTSHOW = "REDIS_EXPIRE_LISTSHOW";

	// realTimeRedis控制开关key
	public static final String REDIS_REALTIME = "REDIS_REALTIME";
	
	// readtime click redis 控制开关
	public static final String REDIS_REALTIME_CLICK = "REDIS_REALTIME_CLICK";

	/** 用户实时筛选数据开关 */
	public static final String REDIS_REALTIME_FILTER = "REDIS_REALTIME_FILTER";

	// 用哪个redis
	public static final String REDIS_SWITCH = "REDIS_SWITCH";

	//四星酒店好评数系数
	public static final String FOURSTARGOODREPUTION = "fourStarGoodRepution";
	//五星酒店好评数系数
	public static final String FIVESTARGOODREPUTION = "fiveStarGoodRepution";
	//统计系数Za
	public static final String ZA = "Za";
	
	//是否显示国际酒店的折扣标签
	public static final String ABROAD = "ABROAD";

	/**
	 * 国际酒店传当地时间开关
	 */
	public static final String isDestTimeOpen = "isDestTimeOpen";

	/* 酒店推广时计算服务指标时预留房得房率项的系数 */
	public static final String TUIGUANG_COEFFICIENT_RESERVED_GET_RATE = "tuiguang_coefficient_reserved_get_rate";
	public static final String TUIGUANG_COEFFICIENT_CONFIRM_RATE = "tuiguang_coefficient_confirm_rate";
	public static final String TUIGUANG_COEFFICIENT_ARRIEV_NO_ROOM_REGRET_RATE = "tuiguang_coefficient_arrive_no_room_regret_rate";
	public static final String TUIGUANG_COEFFICIENT_ORDER_PROC_TIME = "tuiguang_coefficient_order_proc_time";

	/* 推广酒店默认的权重 */
	public static final String TUIGUANG_DEFAULT_WEIGHT = "tuiguang_default_weight";

	public static final String HOTEL_BAOZHUANG_QUERY_DIFF_DISPLAY_POSITION = "displayPosition";

	
	// feature 日志级别
    public static final String FEATURE_LOG_LEVEL = "FEATURE_LOG_LEVEL";
    // feature 日志最大输出酒店数
    public static final String FEATURE_LOG_MAX_HOTELS = "FEATURE_LOG_MAX_HOTELS";
    // feature 日志过期时间（小时）
    public static final String FEATURE_LOG_EXPIRE_HOURS = "FEATURE_LOG_EXPIRE_HOURS";
    // feature 取消记录日志的磁盘空间临界点
    public static final String FEATURE_LOG_CRITICAL_DISK_GIBABYTES = "FEATURE_LOG_CRITICAL_DISK_GIGABYTES";

}
