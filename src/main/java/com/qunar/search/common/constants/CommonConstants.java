package com.qunar.search.common.constants;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

/**
 * 公共基础常量类
 * 
 * <AUTHOR>
 *
 */
public class CommonConstants {
    /**
     * 签到红包最低面额
     */
    public final static String SIGNINGIFTLOWMONEY = "signInGiftLowMoney";
    /**
     * 签到红包中间面额
     */
    public final static String SIGNINGIFTMIDDLEMONEY = "signInGiftMiddleMoney";
    /**
     * 签到红包最大面额
     */
    public final static String SIGNINGIFTHIGHMONEY = "signInGiftHighMoney";
    /**
     * A2B限制距离
     */
    public final static String A2BMINDISTANCE = "a2bMinDistance";
    /**
     * A2B允许传入最大seq数
     */
    public final static String A2BMAXSEQNUM = "a2bMaxSeqNum";
    /**
     * 星券黑名单允许传入最大seq数
     */
    public final static String STARBLACKMAXSEQNUM = "starBlackMaxSeqNum";
    /**
     * 计算最低价和取报价配置文件名
     */
    public final static String HPRICPARAMINFOFILENAME = "hprice_param_info.properties";

    /**
     * sort 报价定级配置文件
     */
    public final static String SORT_PRICE_LEVEL_FILENAME = "sort_price_level.properties";

    /**
     *  watcher用到的配置文件
     */
    public final static String WATCHER_CONFIG_FILENAME = "watcher.monitor.properties";

    /**
     * terminal_type:MOBILE
     */
    public final static String TERMINAL_MOBILE = "MOBILE";
    
    /**
     * terminal_type:TOUCH
     *  
     */
    public final static String TERMINAL_TOUCH = "TOUCH";

    public final static String KRYO_VERSION = "_515";

    /**
     * 是否属于民宿大类的标志的字段名
     */
    public static final String BNB_FLAG = "bnbFlag";

    /**
     * 基础数据中inlandCommonTag的取值。wiki:http://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=111794720
     * 23 家庭旅馆 22 招待所 21 农家院 20 棋牌麻将 19 民宿 18 客栈 17 青年旅舍 16 高端连锁 15 快捷连锁 14 酒店式公寓
     * 13 别墅 12 湖景风光 11 海滨风光 10 宠物友好 9 温泉酒店 8 豪华酒店 7 会展会务 6 休闲度假 5 主题特色 4 商务出行
     * 3 团建轰趴 2 情侣约会 1 亲子家庭
     */
    public static final String INLAND_COMMON_TAG = "inlandCommonTag";

    public static final String AND_AND = "&&";

	public static final Splitter COMMA_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    public static final Splitter PIPE_SPLITTER = Splitter.on("|").trimResults().omitEmptyStrings();

    public static final Splitter POUND_SPLITTER = Splitter.on("#").omitEmptyStrings().trimResults();

    public static final Splitter AND_AND_SPLITTER = Splitter.on(AND_AND).trimResults().omitEmptyStrings();

    public static final Joiner UNDERLINE_JOINER = Joiner.on("_").skipNulls();

    public static final Joiner PIPELINE_JOINER = Joiner.on("|").skipNulls();

    public static final Joiner COMMA_JOINER = Joiner.on(",").skipNulls();

    public static final Joiner BLANK_JOINER = Joiner.on(" ").skipNulls();

    public static final Joiner POUND_JOINER = Joiner.on("#").skipNulls();

    public static final Splitter BLANK_SPLITTER = Splitter.on(" ").omitEmptyStrings().trimResults();

    public static final String RELATIVITY = "relativity";

    public static final double FULL_MATCH_RELATIVITY = 0.98D;

    public static final double GROUP_FEATURE_MIN_VALUE = 0.0001;


    public static final double HOTEL_MAX_PRICE = 20000.0;  // 酒店最大间夜价格

    public static final double HOTEL_MIN_PRICE = 50.0;  // 酒店最低间夜价格

    public static final int TOTAL_NUMS = 600;  // 精排候选酒店默认数量

    public static final double MAX_DISTANCE = 30000.0;  // 距离最大值

}
