package com.qunar.search.common.model.feature;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.bean.*;
import com.qunar.search.common.bean.DetailShow.*;
import com.qunar.search.common.enums.UserQCOrderClickFeature;
import com.qunar.search.common.enums.UserWeekDayOrderFeature;
import com.qunar.search.common.enums.cUserDatas.CUserClickBookCollectData;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.enums.cUserDatas.UserFlightHotelTrainBnbOrderData;
import com.qunar.search.common.enums.cUserDatas.UserPoiOrderDistancePriceData;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.feature.XhsDyShowHotelFeature;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.math.data.UserBehave;
import com.qunar.search.common.math.data.UserBehaveSequence;
import com.qunar.search.common.math.data.UserShowOrderData;

import java.io.Serializable;
import java.util.*;

import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2019-09-30 上午11:30
 * @DESCRIPTION
 * 1、模型计算使用的用户数据
 * 2、特征日志打印对象
 **/

@Data
public class ModelUserFeature implements Serializable {

	/**
	 * 1、实时数据，用户进入到详情页查看的酒店数据
	 * 2、从列表，我的收藏，订单，主页等等入口所有进入到请求
	 * 3、去重后 top 100
	 */
	List<DetailShow> detailShowHotels;

	/**
	 * 1、实时数据，用户通过列表点击进入详情
	 * 2、带有价格
	 * 3、去重后 top 100
	 */
	List<ListClick> listClickHotels;

	/**
	 *  用户实时点击酒店的 点击-下单关联性
	 *  解释: https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=558568724
	 */
	Map<String,Double>  mapRealTimeOrderRela = Collections.emptyMap();

	/**
	 * 用户24小时内得实时点击酒店融合到一起（宽窄，老点击）,已排好序，时间戳降序
	 */
	List<String> userRealTimeClickSeq24Hour = Collections.emptyList();

	/**
	 * 用户24小时内得实时点击同城酒店融合到一起（宽窄，老点击）,已排好序，时间戳降序
	 */
	List<String> userRealTimeClickSeq24HourSameCity = Collections.emptyList();

	/**
	 * 用户24小时内得实时点击同城酒店 对应的经纬度坐标, 时间戳降序
	 */
	List<GLatLng> userRealClickGLatLngMap = Collections.emptyList();

	/**
	 * 用户浏览、点击、下单数据
	 */
	public Map<String, UserShowOrderData.PvInfo> hisShowSt = Collections.emptyMap();

	/**
	 * 用户历史点击档次排名，key:dangci，value:排名
	 */
	private Map<Integer, Integer> dangciSort = Collections.emptyMap();

	/**
	 * 用户历史订单价格均值
	 */
	private double orderAvgPrice;

	/**
	 * 用户在当前城市下400天内的平均订单金额
	 */
	private double currentCityOrder400DAvg;

	/**
	 * 用户每个城市下最近一笔订单， TODO:这里存储了所有城市，用户请求城市和召回返回城市可能不一致，能不能只存 HotelItem 酒店前缀这个城市的数据
	 */
	public Map<String, UserShowOrderData.OrderInfo> cityLastOrder = Collections.emptyMap();

	/**
	 * 用户历史订单，key:hotelSeq, value:订单列表
	 */
	private Map<String, List<UserHotel.OrderHotel>> historyOrders = Collections.emptyMap();

	/**
	 * 历史订单档次--key:档次，value:该档次订单数目
	 */
	public Map<Integer, Integer> historyDangciMap = Collections.emptyMap();

	/**
	 * 历史订单星级--key:星级，value:该星级订单数目
	 */
	private Map<Integer, Integer> historyStarMap = Collections.emptyMap();

	/**
	 * 历史订单品牌--key:品牌，value:该品牌订单数目
	 */
	private Map<String, Integer> historyBrandMap = Collections.emptyMap();

	/**
	 * 历史订单类型--key:星级，value:该类型订单数目
	 */
	private Map<String, Integer> historyTypeMap = Collections.emptyMap();

	/**
	 * 历史订单商圈--key:星级，value:该商圈订单数目
	 */
	private Map<String, Integer> historyTradingAreaMap = Collections.emptyMap();

	/**
	 * 收藏的酒店
	 */
	private Map<String, UserHotel.FavoriteHotel> favoriteHotels = Collections.emptyMap();

	/**
	 * 实时点击酒店的平均价格
	 */
	private double realtimeClickAvgPrice;

	/**
	 * 用户历史400天订单序列特征 最长200
	 */
	UserBehaveSequence submitOrderSequence;

	/**
	 * 用户实时点击序列特征（2天内）最长100
	 */
	UserBehaveSequence realTimeListClickSequence;


	/**
	 * 实时点击酒店算出来的档次排名
	 */
	private Map<Integer, Integer> realTimeDangciSort = Collections.emptyMap();

	/**
	 * 实时筛选档次--时间排序
	 */
	private List<String> realTimeFilterDangciTimeSort = Collections.emptyList();
	/**
	 * 实时筛选品牌--时间排序
	 */
	private List<String> realTimeFilterBrandTimeSort = Collections.emptyList();
	/**
	 * 实时筛选商业区--时间排序
	 */
	private List<String> realTimeFilterTradingTimeSort = Collections.emptyList();
	/**
	 * 实时筛选价格区间--时间排序
	 */
	private List<double[]> realTimeFilterPriceTimeSort = Collections.emptyList();
	/**
	 * 实时搜索词--时间排序
	 */
	private List<String> realTimeQueryTimeSort = Collections.emptyList();

	/**
	 * 实时关键词对应的候选酒店 <q, hotelseq1,hotelseq2,hotelseq3>
	 *
	 * 线上不在赋值使用该字段，暂未删除是兼容线下历史日志反序列化及特征转换
	 */
	private Map<String, Set<String>> realTimeQueryCandidates = Collections.emptyMap();

	/**
	 * 用户实时点击的酒店头图平均质量分
	 */
	private double rtClickImgScoreAvg = -1;

	/**
	 * 用户实时点击的酒店，所有酒店的平均图片数量
	 */
	private double rtClickImgNumAvg = -1;

	/**
	 * 用户是否新用户
	 */
	private boolean isNewUser = false;

	/**
	 * 旧的日志对象
	 */
	private UserShowOrderData userShowOrderData;

	/**
	 * 旧的日志对象，点击酒店 实时点击
	 */
	private List<UserHotel.RealtimeClickHotel> clickHotels = Collections.emptyList();

	/**
	 * 用户过去365天内所有订单，不同入住天数（间夜）的订单占比
	 */
	private Map<Integer, Double> checkInOrderRate = Collections.emptyMap();

	/**
	 * 用户点击过的酒店序列，相邻酒店之间建个小于30min
	 */
	List<Double> orderListHotelAvgEmbedding = Collections.emptyList();

	/**
	 * 最后一家下单的酒店向量
	 */
	List<Double> lastOrderHotelEmbedding = Collections.emptyList();

	/**
	 * 用户实时（24h内）点击酒店的embedding，可重复的seq
	 */
	List<List<Double>> realTimeClickHotelEmbedding = Collections.emptyList();

	/**
	 * 用户历史点击数据聚合特征
	 */
	UserBehave userHistoryClickFeature;

	/**
	 * 用户31天收藏数据聚合特征
	 */
	UserBehave user31DayCollectFeature;

	/**
	 * 用户实时点击数据聚合特征
	 */
	UserBehave userRealtimeClickFeature;

	/**
	 * 用户实时24小时点击行为序列数据聚合特征（新版本特征）
	 */
	UserBehave userRealtimeClickFeature24h;

	/**
	 * 用户实时筛选数据聚合特征
	 */
	UserBehave userRealtimeQueryFeature;

	/**
	 * 用户使用机型数据聚合特征
	 */
	UserBehave userModelFeature;

	/**
	 * 用户历史180天订单数据聚合特征
	 */
	UserBehave user180DayOrderFeature;

	/**
	 * 用户历史365天订单数据聚合特征
	 */
	UserBehave user365DayOrderFeature;

	/**
	 * 用户历史800天订单数据聚合特征
	 */
	UserBehave user800DayOrderFeature;

	/**
	 * 用户画像数据
	 */
	UserProfile userProfile;

	/**
	 * 用户14天内 + 实时（24h内）点击酒店，可重复的seq
	 * 可能会有重复（当24h内的T-1有点击时）
	 * 只保留最近的100家
	 */
	List<String> lastTwoWeekClickHotels = Collections.emptyList();

	/**
	 * 用户历史180天订单酒店
	 * 只保留最近的100家
	 */
	List<String> last180DayOrderHotels = Collections.emptyList();

    /**
     * 用户历史90天机火常住地订单信息
     */
    UserTravelInfo userTravelInfo;

	/**
	 * 用户的身份信息: https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=*********
	 */
	Map<String, UserIdentityInfo> userIdentityInfoMap;

	/**
	 * 用户实时点击酒店列表(hotel id)
	 */
	String realTimeClickHotelIds;

	/***
	 * 用户历史下单酒店列表(hotel id)
	 */
	String historyOrderHotelSeqs;

	/**
	 * 用户历史收藏订单列表 (hotel id)
	 */
	String historyCollectHotelSeqs;
	/**
	 * 用户当前城市历史订单列表
	 */
	String currentCityOrderHotelSeqs;

	/**
	 * 用户当前时间距离在c下单的最近一次时间的时间天数
	 */
	double cLastOrderDaysDiff;

	/**
	 * 用户14天内 + 实时（24h内）点击酒店的embedding，可重复的seq
	 * 可能会有重复（当24h内的T-1有点击时）
	 * 只保留最近的100家
	 */
	@JsonIgnore
	List<List<Double>> lastTwoWeekClickHotelEmbedding = Collections.emptyList();

	/**
	 * 用户历史180天订单酒店的embedding
	 * 只保留最近的100家
	 */
	@JsonIgnore
	List<List<Double>> last180DayOrderHotelEmbedding = Collections.emptyList();

	/**
	 * 用户身份折扣, 其中身份为memberInfoR1, memberInfoR2, memberInfoR3, memberInfoR4中的一个，并且唯一
	 */
	String userIdentityDiscount;

	/**
	 * 用户所有身份集合, 包括新用户、机票等， 身份按 ; 拼接。
	 */
	String userIdentityString;

	/**
	 * 用户活动、身份到唯一ID的映射关系
	 */
	Map<String, UserActivityItem> userActivityItemMap;

	/**
	 * 用户相关的Convertor特征
	 */
	Map<Integer, String> userIdentityFeature;

	/**
	 * 用户过去3天点击的搜索城市的价格均值
	 */
	Map<String, Double> searchCityPriceAvg3D = Collections.emptyMap();

	/**
	 * 用户过去3天点击的搜索城市的价格中位数
	 */
	Map<String, Double> searchCityPriceMedia3D = Collections.emptyMap();

	/**
	 * 用户3天内在酒店详情页的行为数据, 保留最近 50个行为
	 */
	Map<String, UserHotelDetailStayTime> userHotelDetailStayTimeMap = Collections.emptyMap();

	/**
	 * 用户在周末平日的相关特征
	 */
	Map<UserWeekDayOrderFeature, Object> userWeekDayOrderFeatureObjectMap = Collections.emptyMap();

	/**
	 * qc交叉用户在C的订单和点击特征
	 */
	Map<UserQCOrderClickFeature, Object> userQCOrderClickMap = Collections.emptyMap();

	/**
	 * 用户在C的订单行为
	 */
	Map<CUserOrderData, Object> userCOrderAction = Collections.emptyMap();

	/**
	 * 用户在C的click book 收藏行为
	 */
	Map<CUserClickBookCollectData, Object> userCtripClickBookCollectAction = Collections.emptyMap();

	/**
	 * 用户在poi下距离价格信息
	 */
	Map<UserPoiOrderDistancePriceData, Object> userPoiOrderDistancePriceData = Collections.emptyMap();

	/**
	 * 用户在机酒火民价格信息
	 */
	Map<UserFlightHotelTrainBnbOrderData, Object> userFlightHotelTrainBnbOrderData = Collections.emptyMap();

	/**
	 *用户券相关
	 */
	List<String> userCouponList = Collections.emptyList();

	/**
	 * query词下总订单
	 */
	private int queryOrderTotalNum;

	/**
	 * query 词下订单平均展示价格
	 */
	private double queryOrderAvgShowPrice;

	/**
	 * 请求来源
	 * source=mini 是小程序，=APP 是app
	 */
	String requestSource ;

	/**
	 * 小红书等新媒体来源特征
	 */
	XhsDyShowHotelFeature xhsDyShowHotelFeature;


}
