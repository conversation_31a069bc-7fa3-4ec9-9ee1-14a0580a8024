package com.qunar.search.common.model.feature;

import com.qunar.search.common.model.memory.MemoryData;
import com.qunar.search.common.util.LibSvmUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2023-09-30 上午11:30
 * @DESCRIPTION
 * 特征日志打印对象
 **/

@Data
public class FastFilterModelFeature implements Serializable {

	/**
	 * 该请求对应的首屏请求trace
	 */
	String requestTime;

	/**
	 * 模型用到的请求特征
	 */
	FastFilterModelRequestFeature requestFeature;

	/**
	 * 模型用到的用户特征
	 */
	FastFilterModelUserFeature userFeature;

	/**
	 * 模型用到的酒店特征
	 */
	List<FastFilterModelItemFeature> filterFeature;

}
