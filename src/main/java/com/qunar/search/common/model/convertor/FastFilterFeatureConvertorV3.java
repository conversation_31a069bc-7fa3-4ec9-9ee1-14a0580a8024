package com.qunar.search.common.model.convertor;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FastFilterFeatureIndexEnum;
import com.qunar.search.common.model.feature.FastFilterModelItemFeature;
import com.qunar.search.common.model.feature.FastFilterModelRequestFeature;
import com.qunar.search.common.model.feature.FastFilterModelUserFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserRealClickFilterFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @create 2023-12-12 下午7:10
 * @DESCRIPTION 添加部分新特征V3
 **/
@Component
public class FastFilterFeatureConvertorV3 extends FastFilterFeatureConvertorV2 {

    public Map<Integer, Object> convertObject(FastFilterModelRequestFeature requestFeature,
                                              FastFilterModelUserFeature userFeature,
                                              FastFilterModelItemFeature filterFeature) {


        Map<Integer, Object> resultMap = super.convertObject(requestFeature, userFeature, filterFeature);

        Map<String, Double> queryFilterMap = requestFeature.getQueryFilterMap();
        if (MapUtils.isNotEmpty(queryFilterMap)) {
            double clickCnt = queryFilterMap.getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO);
            // query下点击次数
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_78.getIndex(), clickCnt);

            // query下点击次数占比
            double cntSum = queryFilterMap.values().stream().mapToDouble(Double::doubleValue).sum();
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_79.getIndex(), clickCnt/(cntSum+1));

            Map<String, Integer> sortByValue = sortByValueForIndex(queryFilterMap);
            int index = sortByValue.getOrDefault(filterFeature.getFilterName(), 0);
            // 排序
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_80.getIndex(), index);
            switch (index) {
                case 1:
                    resultMap.put(FastFilterFeatureIndexEnum.INDEX_81.getIndex(), FeatureConstants.ONE);
                    break;
                case 2:
                    resultMap.put(FastFilterFeatureIndexEnum.INDEX_82.getIndex(), FeatureConstants.ONE);
                    break;
                case 3:
                    resultMap.put(FastFilterFeatureIndexEnum.INDEX_83.getIndex(), FeatureConstants.ONE);
                    break;
                case 4:
                    resultMap.put(FastFilterFeatureIndexEnum.INDEX_84.getIndex(), FeatureConstants.ONE);
                    break;
                case 5:
                    resultMap.put(FastFilterFeatureIndexEnum.INDEX_85.getIndex(), FeatureConstants.ONE);
                    break;
                default:
            }
        }
        return resultMap;
    }


    /**
     * Map按照value逆序排列,获取排名
     */
    public Map<String, Integer> sortByValueForIndex(Map<String, Double> map) {
        Map<String, Integer> sortedMap = new LinkedHashMap<>();
        map.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .forEachOrdered(e -> sortedMap.put(e.getKey(), sortedMap.size()+1));
        return sortedMap;
    }
}


