package com.qunar.search.common.model.feature.operator;

import com.qunar.search.common.model.feature.*;
import com.qunar.search.common.model.memory.MemoryData;
import com.qunar.search.common.util.LibSvmUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Collections;
import java.util.Set;

import static com.qunar.search.common.model.feature.operator.DataType.*;

public enum FeatureLogOperator {

    /**
     * 用户行为数据优化
     * 1、用于与酒店就算交叉特征
     * 2、打日志时，涉及到的偏好酒店map信息只打本次列表酒店
     */
    UserBehave(USER) {
        @Override
        public void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            FeatureLogReduce.reduceUserBehave(userFeature.getUserRealtimeQueryFeature(), allLogHotels);
        }

        @Override
        public void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            // 不需要还原，使用这个数据是用户与酒店的交叉计算逻辑。
            // 只需要打印酒店有数据就能计算出这些酒店的特征
        }
    },

    /**
     * 旧版embeddding数据，日志不打印，需要离线还原
     */
    EmbeddingV1(HOTEL) {
        @Override
        public void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> s.setHotelEmbedding(Collections.emptyList()));
        }

        @Override
        public void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> s.setHotelEmbedding(MemoryData.getFunction().getHotelEmbedding(s.getHotelSeq(), requestFeature.getDataVersion())));
        }
    },

    /**
     * 新版embeddding数据，日志不打印，需要离线还原
     */
    EmbeddingV2(HOTEL) {
        @Override
        public void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> s.setHotelEmbeddingV2(Collections.emptyList()));
        }

        @Override
        public void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> s.setHotelEmbeddingV2(MemoryData.getFunction().getHotelEmbeddingV2(s.getHotelSeq(), requestFeature.getDataVersion())));
        }
    },

    /**
     * 酒店离线统计原始数据， 日志不打印，需要离线还原
     */
    OfflineFeature(HOTEL) {
        @Override
        public void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> {
                s.setOfflineFeature(LibSvmUtil.EMPTY_MAP);
                s.setOfflineFeatureLibSvm(null);
            });
        }

        @Override
        public void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> {
                // 值不为空则不还原重置
                if (MapUtils.isNotEmpty(s.getOfflineFeature())) {
                    return;
                }
                s.setOfflineFeatureLibSvm(MemoryData.getFunction().getOfflineFeatureLibSvm(s.getHotelSeq(), requestFeature.getDataVersion()));
            });
        }
    },

    /**
     * 酒店离线可变长原始数据， 日志不打印，需要离线还原
     */
    OfflineVariableFeature(HOTEL) {
        @Override
        public void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> {
                s.setOfflineVariableFeature(LibSvmUtil.EMPTY_MAP_HOTEL_VARIABLE_FEATURE);
                s.setOfflineVariableFeatureLibSvm(null);
            });
        }

        @Override
        public void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
            allLogHotels.forEach(s -> {
                // 值不为空则不还原重置
                if (MapUtils.isNotEmpty(s.getOfflineVariableFeature())) {
                    return;
                }
                s.setOfflineVariableFeatureLibSvm(MemoryData.getFunction().getOfflineVariableFeatureLibSvm(s.getHotelSeq(), requestFeature.getDataVersion()));
            });
        }
    };

    /**
     * 数据类型，用户or酒店
     */
    DataType dataType;

    FeatureLogOperator(DataType dataType) {
        this.dataType = dataType;
    }

    public abstract void reduce(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels);

    public abstract void recover(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels);

    public static void reduceAll(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels, DataType dataType) {
        if (null == requestFeature || null == userFeature){
            return;
        }

        for (FeatureLogOperator value : FeatureLogOperator.values()) {
            if (value.dataType == dataType) {
                value.reduce(requestFeature, userFeature, allLogHotels);
            }
        }
    }

    public static void recoverAll(ModelRequestFeature requestFeature, ModelUserFeature userFeature, Set<ModelHotelFeature> allLogHotels) {
        if (null == requestFeature || null == requestFeature.getDataVersion() ||
                null == userFeature || CollectionUtils.isEmpty(allLogHotels)) {
            return;
        }

        for (FeatureLogOperator value : FeatureLogOperator.values()) {
            value.recover(requestFeature, userFeature, allLogHotels);
        }
    }
}
