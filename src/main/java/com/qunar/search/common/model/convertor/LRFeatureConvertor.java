package com.qunar.search.common.model.convertor;


import com.google.common.collect.Maps;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.DateUtil;
import com.qunar.search.common.util.RangeUtil;
import com.qunar.search.common.util.ZhiXin;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.enums.FeatureType.ADR_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_SHOW_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_SHOW_PV_7_DAY;
import static com.qunar.search.common.model.convertor.ConvertorBase.getValueByPlatform;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

@Component
public class LRFeatureConvertor extends AbstractSortFeatureConvertor {

	public static final Double FEATURE_POSITIVE = 1.0D;

	private static final double LOG_2 = Math.log(2);
	private static final double[] CLICK_PRICE_DIFF_SECTIONS = {-1, -0.8, -0.6, -0.5, -0.4, -0.3, -0.25, -0.2, -0.15,
			-0.1, -0.05, 0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 0.6, 0.8, 1, 1.5, 3};
	private static final double[] HISTORY_PRICE_DISTANCE_SECTIONS = {0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 0.75,
			1, 1.5, 3, Double.MAX_VALUE};
	private static final double[] ZHIXIN_THRESHOLD = new double[]{0.002, 0.0025, 0.0035, 0.01, 0.2, 0.99, 1};
	private static final int[] KEY_WORD_COUNT_SECTIONS = {0, 1, 2, 3, 4, 5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100,
			120, 140, 160, Integer.MAX_VALUE};
	private static final double[] POI_DISTANCE_SECTIONS = {0, 500, 1000, 1500, 2000, 3000, 5000, Double.MAX_VALUE};
	private static final double[] DISTANCE_SECTIONS = new double[]{0, 100, 200, 500, 1000, 1500, 2000, 3000, 4000, 5000,
			7000, 10000, 15000, 20000, 30000, 40000, 60000, Integer.MAX_VALUE};
	private static final int[] SERVICE_SCORE_SECTIONS = new int[]{-6, -1, 0, 1, 2, 5, 1000000};

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
		return convertDouble(requestFeature, userFeature, hotelFeature);
    }

	private Map<Integer, Object> convertDouble(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
		Map<Integer, Object> featureMap = Maps.newHashMapWithExpectedSize(RankSystemConfig.getLrFeatureSize());

		if (hotelFeature == null) {
			return featureMap;
		}
		String seq = hotelFeature.getHotelSeq();

		int index = 1;
		//用户过去半年预订该酒店的次数。
		Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders();
		List<UserHotel.OrderHotel> historyOrder = historyOrders.get(seq);
		if (historyOrder != null && !historyOrder.isEmpty()) {
			int count = historyOrder.size();
			if (count > 4) {
				count = 4;
			}
			featureMap.put(index + count, FEATURE_POSITIVE);//[1,5]
		} else {
			featureMap.put(index, FEATURE_POSITIVE);
		}

		//过去半年用户在该城市最近一次预订的是否该酒店
		index += 10;//11
		int pos = StringUtils.lastIndexOf(seq, "_");
		if (pos > 0) {
			Map<String, UserShowOrderData.OrderInfo> cityLastOrder = userFeature.cityLastOrder;
			UserShowOrderData.OrderInfo orderInfo = cityLastOrder.get(StringUtils.substring(seq, 0, pos));
			if (orderInfo != null && StringUtils.equals(orderInfo.hotelSeq, seq)) {
				featureMap.put(index, FEATURE_POSITIVE);//11
			}
		}

		UserShowOrderData.PvInfo pvInfo = userFeature.hisShowSt.get(seq);
		// 近三周用户是否到达酒店的下单页
		index += 10;//21
		if (pvInfo != null) {
			if (pvInfo.isBookingPageOpened) {
				featureMap.put(index + 1, FEATURE_POSITIVE);//22
			}
			//近三周用户是否在酒店下单
			if (pvInfo.isOrdered) {
				featureMap.put(index + 2, FEATURE_POSITIVE);//23
			}
		}

		// 近三周用户浏览和点击该酒店的次数，目前组织为4*7的二维矩阵
		index += 10;//31
		long num_view = 0;
		long num_click = 0;
		if (pvInfo != null) {
			num_view = pvInfo.pv;
			num_click = Math.min(pvInfo.pv, pvInfo.cl);
		}

		int level_view = 0;
		int level_click = 0;
		if (num_view > 0) {
			level_view = Math.min(7, (int) (Math.log(num_view) / LOG_2) + 1);
		}
		if (num_click > 0) {
			level_click = Math.min(3, (int) (Math.log(num_click) / LOG_2) + 1);
		}
		featureMap.put(index + level_view * 4 + level_click, FEATURE_POSITIVE);//7*4+3=31; [30,61]

		// 用户点击档次排名
		index += 40;//71
		Map<Integer, Integer> dangciSort = userFeature.getDangciSort();
		int dangci = hotelFeature.getDangci();
		Integer danCi = dangciSort.get(dangci);
		if (danCi != null) {
			featureMap.put(index + danCi, FEATURE_POSITIVE);//[71-75]
		} else {
			featureMap.put(index + dangciSort.size(), FEATURE_POSITIVE);
		}

		//点击价格
		index += 10;//81
		// 用户实时点击价格,与用酒店格差异
		int priceThrs = 1000000;
		double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();
		int price = hotelFeature.getMobileMinAvailablePrice();
		if (clickAvgPrice > 0 && clickAvgPrice < priceThrs) {
			if (price <= 0 || price > priceThrs) {
				featureMap.put(index + CLICK_PRICE_DIFF_SECTIONS.length - 1, FEATURE_POSITIVE);
			} else {
				double rate = (price - clickAvgPrice) / clickAvgPrice;
				for (int i = CLICK_PRICE_DIFF_SECTIONS.length - 1; i >= 0; i--) {
					if (rate >= CLICK_PRICE_DIFF_SECTIONS[i]) {
						featureMap.put(index + i, FEATURE_POSITIVE);//[91,]
						break;
					}
				}
			}
		}

		index += 30;//111
		// 用户实时档次筛选
		List<String> realTimeFilterDangciTimeSort = userFeature.getRealTimeFilterDangciTimeSort();
		if (realTimeFilterDangciTimeSort != null && !realTimeFilterDangciTimeSort.isEmpty() && dangci != 0) {
			int hitIndex = 0;
			for (int i = 0; i < realTimeFilterDangciTimeSort.size(); ++i) {
				if (realTimeFilterDangciTimeSort.get(i).contains(String.valueOf(dangci))) {
					hitIndex = i + 1;
					hitIndex = hitIndex > 5 ? 5 : hitIndex;
					break;
				}
			}
			if (hitIndex > 0) {
				featureMap.put(index + hitIndex, FEATURE_POSITIVE);//121
			}
		}

		index += 10;//121
		// 用户实时品牌
		String brand = hotelFeature.getHotelBranch();
		List<String> realTimeFilterBrandTimeSort = userFeature.getRealTimeFilterBrandTimeSort();
		if (realTimeFilterBrandTimeSort != null && !realTimeFilterBrandTimeSort.isEmpty() && brand != null) {
			int hitIndex = 0;
			for (int i = 0; i < realTimeFilterBrandTimeSort.size(); ++i) {
				if (realTimeFilterBrandTimeSort.get(i).contains(brand)) {
					hitIndex = i + 1;
					hitIndex = hitIndex > 5 ? 5 : hitIndex;
					break;
				}
			}
			if (hitIndex > 0) {
				featureMap.put(index + hitIndex, FEATURE_POSITIVE);
			}
		}

		index += 10;//131
		// 用户实时商圈筛选
		String tradingArea = hotelFeature.getTradingArea();
		List<String> realTimeFilterTradingTimeSort = userFeature.getRealTimeFilterTradingTimeSort();
		if (realTimeFilterTradingTimeSort != null && !realTimeFilterTradingTimeSort.isEmpty() && tradingArea != null) {
			int hitIndex = 0;
			for (int i = 0; i < realTimeFilterTradingTimeSort.size(); ++i) {
				if (realTimeFilterTradingTimeSort.get(i).contains(tradingArea)) {
					hitIndex = i + 1;
					hitIndex = hitIndex > 5 ? 5 : hitIndex;
					break;
				}
			}
			if (hitIndex > 0) {
				featureMap.put(index + hitIndex, FEATURE_POSITIVE);
			}
		}
		index += 10;//141
		// 用户实时价格筛选
		List<double[]> realTimeFilterPriceTimeSort = userFeature.getRealTimeFilterPriceTimeSort();
		if (realTimeFilterPriceTimeSort != null && !realTimeFilterPriceTimeSort.isEmpty()) {
			int hitIndex = 0;
			for (int i = 0; i < realTimeFilterPriceTimeSort.size(); ++i) {
				double[] range = realTimeFilterPriceTimeSort.get(i);
				double low = range[0];
				double up = range[1];
				if (price >= low && price <= up) {
					hitIndex = i + 1;
					hitIndex = hitIndex > 5 ? 5 : hitIndex;
					break;
				}
			}
			if (hitIndex > 0) {
				featureMap.put(index + hitIndex, FEATURE_POSITIVE);
			}
		}
		index += 10;//151
		// 用户实时query
		List<String> realTimeQueryTimeSort = userFeature.getRealTimeQueryTimeSort();
		Map<String, Set<String>> realTimeQueryCandidates = userFeature.getRealTimeQueryCandidates();
		if (MapUtils.isNotEmpty(realTimeQueryCandidates)) {
			if (realTimeQueryTimeSort != null && !realTimeQueryTimeSort.isEmpty()) {
				int hitIndex = 0;
				for (int i = 0; i < realTimeQueryTimeSort.size(); ++i) {
					Set<String> candidates = realTimeQueryCandidates.get(realTimeQueryTimeSort.get(i));
					if (candidates != null && candidates.contains(seq)) {
						hitIndex = i + 1;
						hitIndex = hitIndex > 5 ? 5 : hitIndex;
						break;
					}
				}
				if (hitIndex > 0) {
					featureMap.put(index + hitIndex, FEATURE_POSITIVE);
				}
			}
		} else {
			if (hotelFeature.getHitKeyWordIndex() > 0) {
				featureMap.put(index + hotelFeature.getHitKeyWordIndex(), FEATURE_POSITIVE);
			}
		}

		// 酒店历史订单价格均值与用户历史订单价格均值的相对距离 abs(hm-um)/um，分13级, (正负区分)
		index += 10;//161
		double orderAvgPrice = userFeature.getOrderAvgPrice();
		if (orderAvgPrice > 0) {
			double diff = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(), ADR_AVG_PRICE_180_DAY,
					IOS_AVG_PRICE_180_DAY, hotelFeature.getPlatformOrderMeanPrice()) - orderAvgPrice;
			double disScore = Math.abs(diff) / orderAvgPrice;
			int idx = -1;
			for (int i = 1; i < HISTORY_PRICE_DISTANCE_SECTIONS.length; i++) {
				if (HISTORY_PRICE_DISTANCE_SECTIONS[i - 1] <= disScore
						&& disScore < HISTORY_PRICE_DISTANCE_SECTIONS[i]) {
					idx = i - 1;
					break;
				}
			}
			if (idx >= 0) {
				int shift = 0;
				if (diff > 0) {
					shift = HISTORY_PRICE_DISTANCE_SECTIONS.length; // 酒店均价高于用户订单均价时, 特征点右移14
				}
				featureMap.put(index + shift + idx, FEATURE_POSITIVE);
			}
		}

		// 酒店相似度 用户历史订单档次命中当前酒店, star, brand, type, trading
		index += 30;//191
		String city = getCityFromSeq(seq);
		int idx = 0;
		danCi = userFeature.getHistoryDangciMap().get(dangci);
		if (danCi != null) {
			idx = Math.min(danCi, 9);
		}

		featureMap.put(index + idx + dangci * 10, FEATURE_POSITIVE);
		index += 60;//251
		// 酒店相似度 用户历史订单星级命中当前酒店星级
		int star = hotelFeature.getStars();
		Integer historyStar = userFeature.getHistoryStarMap().get(star);
		if (historyStar != null) {
			idx = Math.min(historyStar, 9);
			featureMap.put(index + idx, FEATURE_POSITIVE);
		}

		index += 10;//261
		// 酒店相似度 用户历史订单品牌次命中当前酒店品牌
		if (StringUtils.isNotBlank(brand)) {
			Integer historyBrand = userFeature.getHistoryBrandMap().get(brand);
			if (historyBrand != null) {
				idx = Math.min(historyBrand, 9);
				featureMap.put(index + idx, FEATURE_POSITIVE);
			}
		}

		index += 10;//271
		// 酒店相似度 用户历史订单类型命中当前酒店类型
		String[] types = hotelFeature.getHotelType();
		if (types != null) {
			for (String t1 : types) {
				Integer historyType = userFeature.getHistoryTypeMap().get(t1);
				if (historyType != null) {
					idx = Math.min(historyType, 9);
					featureMap.put(index + idx, FEATURE_POSITIVE);
				}
			}
		}

		index += 10;//281
		// 酒店相似度 用户历史订单商圈命中当前酒店商圈
		if (StringUtils.isNotBlank(tradingArea)) {
			if (city != null) {
				String cityTradingArea = city + tradingArea;
				Integer historyTradingArea = userFeature.getHistoryTradingAreaMap().get(cityTradingArea);
				if (historyTradingArea != null) {
					idx = Math.min(historyTradingArea, 9);
					featureMap.put(index + idx, FEATURE_POSITIVE);
				}
			}
		}

		// 4天之前收藏过该酒店, 4天之内收藏过该酒店
		index += 10;//291

		UserHotel.FavoriteHotel favoriteHotel = userFeature.getFavoriteHotels().get(seq);
		if (favoriteHotel != null) {
			int requestDay = requestFeature.getRequestDate();
			int logDay4 = DateUtil.addDay(requestDay, -4);

			if (favoriteHotel.getFavoriteDate() < logDay4) {
				// 4天之前收藏过该酒店
				featureMap.put(index + 1, FEATURE_POSITIVE);
			} else {
				// 4天之内收藏过该酒店
				featureMap.put(index + 2, FEATURE_POSITIVE);
			}
		}

		// 酒店近3周的订单量，分8级
		index += 10;//301
		int v_num_orders = (int)getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
				ADR_ORD_PV_21_DAY, IOS_ORD_PV_21_DAY, hotelFeature.getPlatformOrderPv21());
		int v_level_num_orders = 0;
		if (v_num_orders > 0) {
			v_level_num_orders = (int) (Math.log(v_num_orders) / LOG_2 + 1);
			if (v_level_num_orders > 7) {
				v_level_num_orders = 7;
			}
		}
		featureMap.put(index + v_level_num_orders, FEATURE_POSITIVE);

		// 酒店评分，分13级 commentScore
		index += 10;//311
		double commentScore = hotelFeature.getCommentScore();
		int commentLevel = 0;
		if (commentScore > 0 && commentScore <= 1) {
			commentLevel = 1;
		} else if (commentScore > 1 && commentScore <= 2) {
			commentLevel = 2;
		} else if (commentScore > 2) {
			commentLevel = (int) ((commentScore - 2) / 0.3) + 3;
		}
		if (commentLevel > 12) {
			commentLevel = 12;
		}
		featureMap.put(index + commentLevel, FEATURE_POSITIVE);

		index += 20;//331
		// 酒店折扣
		double originalPrice = hotelFeature.getOriginalPrice();
		if (price > 0 && originalPrice > 0 && originalPrice >= price && price < 10000 && originalPrice < 10000) {
			double discountRate = (originalPrice - price) / (originalPrice + 1);
			if (discountRate != 0) {
				int step = (int) (discountRate / 0.05);
				step = Math.min(20, Math.abs(step));
				featureMap.put(index + step, FEATURE_POSITIVE);
			}
		}

		// 酒店转化率特征search to order
		index += 20;//351
		double rate = 0.0;
		double showpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
				ADR_SHOW_PV_7_DAY, IOS_SHOW_PV_7_DAY, hotelFeature.getPlatformShowPv7());
		double orderpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
				ADR_ORD_PV_7_DAY, IOS_ORD_PV_7_DAY, hotelFeature.getPlatformOrderPv7());
		if (showpv7 > 0) {
			rate = ZhiXin.getZhiXin(orderpv7 / showpv7, showpv7);
		}

		if (rate >= 0.002) {
			for (int i = 0; i < ZHIXIN_THRESHOLD.length - 1; i++) {
				if (rate >= ZHIXIN_THRESHOLD[i] && rate < ZHIXIN_THRESHOLD[i + 1]) {
					featureMap.put(index + 50 + i, FEATURE_POSITIVE);
					break;
				}
			}
		} else {
			int step = (int) (rate / 0.00004);
			featureMap.put(index + step, FEATURE_POSITIVE);
		}

		index += 70;//421
		// 酒店在关键词下订单量
		//场景信息
		SortScene scene = requestFeature.getSortScene();
		if (scene.equals(SortScene.POI_KEY)) {
			int keyWordOrderCount = hotelFeature.getKeyWordOrderCount();
			if (keyWordOrderCount >= 0) {
				idx = -1;
				for (int i = 1; i < KEY_WORD_COUNT_SECTIONS.length; i++) {
					if (KEY_WORD_COUNT_SECTIONS[i - 1] <= keyWordOrderCount
							&& keyWordOrderCount < KEY_WORD_COUNT_SECTIONS[i]) {
						idx = i - 1;
						break;
					}
				}
				if (idx >= 0) {
					featureMap.put(index + idx, FEATURE_POSITIVE);
				}
			}
		}

		// poi到酒店的距离
		index += 20;//441
		if (scene.equals(SortScene.POI_KEY)) {
			double poiDistance = hotelFeature.getPoiHotelDistance();
			//POI distance feature
			if (poiDistance >= 0) {
				idx = -1;
				for (int i = 1; i < POI_DISTANCE_SECTIONS.length; i++) {
					if (POI_DISTANCE_SECTIONS[i - 1] <= poiDistance && poiDistance < POI_DISTANCE_SECTIONS[i]) {
						idx = i - 1;
						break;
					}
				}
				if (idx >= 0) {
					featureMap.put(index + idx, FEATURE_POSITIVE);
				}
			}
		}
		index += 10;//451
		//同城或商圈场景下，酒店距离用户或者坐标的距离。
		if (scene.match(SortScene.SAME_CITY) || scene.match(SortScene.NEARBY)) {
			double coorDistance = hotelFeature.getUserHotelDistance();
			if (coorDistance >= 0) {
				double[] disthrs = DISTANCE_SECTIONS;
				idx = -1;
				for (int i = 1; i < disthrs.length; i++) {
					if (disthrs[i - 1] <= coorDistance && coorDistance < disthrs[i]) {
						idx = i - 1;
						break;
					}
				}
				if (idx >= 0) {
					featureMap.put(index + idx, FEATURE_POSITIVE);
				}
			}
		}

		// 酒店服务分 serviceScore
		index += 20;//471
		int serviceScore = hotelFeature.getServiceScore();

		int offset = RangeUtil.getWhichSectionRightClosed(serviceScore, SERVICE_SCORE_SECTIONS);
		if (offset != Integer.MIN_VALUE) {
			featureMap.put(index + offset, FEATURE_POSITIVE);
		}

		index += 20; // 491
		//实时点击档次
		dangciSort = userFeature.getRealTimeDangciSort();
		danCi = dangciSort.get(danCi);
		if (danCi != null) {
			featureMap.put(index + danCi, FEATURE_POSITIVE);//[481-4855]
		} else {
			featureMap.put(index + dangciSort.size(), FEATURE_POSITIVE);
		}

		// index += 10;//501
		// hotelQuantile - userQuantile 501-520 在子类中

		//index += 20;//521
		// commentCount 评论数特征, in subclass


		// index += 10; // 531
		// to be continue

		return featureMap;


	}
}
