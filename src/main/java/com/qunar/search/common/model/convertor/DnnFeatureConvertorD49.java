package com.qunar.search.common.model.convertor;


import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * D49 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD49 extends DnnFeatureConvertorD48 {

    private static final String PROMOTION_ID = "3488409";

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);
        // 分销提流
        if (hotelFeature.getActivitySet() != null && hotelFeature.getActivitySet().contains(PROMOTION_ID)) {
            resultMap.put(FeatureIndexEnum.D49_534.getIndex(), FeatureConstants.ONE);
        }
        // 金特
        int board = hotelFeature.getBoard();
        resultMap.put(FeatureIndexEnum.D49_535.getIndex(), board == 1 || board == 2 ? board : 0);

        return resultMap;
    }

}
