package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.qunar.search.common.util.Numbers.roundStringDouble;


/**
 * D50 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD50 extends DnnFeatureConvertorD49 {

    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;
    private static final double DEFAULT_MAX_LENGTH = 600.0;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        resultMap.put(FeatureIndexEnum.D50_536.getIndex(), roundStringDouble((double) hotelFeature.getDistanceBucketSort() / DEFAULT_MAX_LENGTH, 8));
        resultMap.put(FeatureIndexEnum.D50_537.getIndex(), roundStringDouble(hotelFeature.getDistanceBucketCdf(),8));
        resultMap.put(FeatureIndexEnum.D50_538.getIndex(), roundStringDouble((double) hotelFeature.getDistanceBucketNum() / DEFAULT_MAX_LENGTH,8));
        // poi类型id
        resultMap.put(FeatureIndexEnum.D50_539.getIndex(), requestFeature.getPoiTypeId());

        // todo 召回意圖
//        if (CollectionUtils.isNotEmpty(requestFeature.getQueryIntention())) {
//            resultMap.put(FeatureIndexEnum.D50_540.getIndex(), requestFeature.getQueryIntention());
//        }

        Map<String, UserHotelDetailStayTime> userHotelDetailStayTimeMap = userFeature.getUserHotelDetailStayTimeMap();
        UserHotelDetailStayTime userHotelDetailStayTime = userHotelDetailStayTimeMap.get(hotelFeature.getHotelSeq());
        if(userHotelDetailStayTime != null) {
            // 点击房型价格最大值
            resultMap.put(FeatureIndexEnum.D50_541.getIndex(), userHotelDetailStayTime.getHouseTypePriceMax());
            // 点击房型价格最小值
            resultMap.put(FeatureIndexEnum.D50_542.getIndex(), userHotelDetailStayTime.getHouseTypePriceMin());

            double estimatePrice = hotelFeature.getEstimatedRenderPrice();
            double houseTypeprice = userHotelDetailStayTime.getHouseTypeprice();
            if (estimatePrice > 0 && estimatePrice < DEFAULT_MAX_SUB100 && houseTypeprice > 0
                    && houseTypeprice < DEFAULT_MAX_SUB100) {
                resultMap.put(FeatureIndexEnum.D50_543.getIndex(), roundStringDouble((estimatePrice - houseTypeprice) / estimatePrice,8));
            }
            // 停留占比
            resultMap.put(FeatureIndexEnum.D50_544.getIndex(), roundStringDouble(userHotelDetailStayTime.getStayTimeRate(), 8));
            // 点击占比
            resultMap.put(FeatureIndexEnum.D50_545.getIndex(), roundStringDouble(userHotelDetailStayTime.getAllClickRate(),8));
        }

        // 同城下是否最后一次点击
        List<String> userRealTimeClickSeq24HourSameCity = userFeature.getUserRealTimeClickSeq24HourSameCity();
        if (userRealTimeClickSeq24HourSameCity != null && !userRealTimeClickSeq24HourSameCity.isEmpty()) {
            String seq = userRealTimeClickSeq24HourSameCity.get(0);
            resultMap.put(FeatureIndexEnum.D50_546.getIndex(), StringUtils.equals(seq, hotelFeature.getHotelSeq()) ? 1.0 : 0.0);
        }

        return resultMap;
    }

}
