package com.qunar.search.common.model.convertor;
import com.qunar.search.common.enums.FastFilterFeatureIndexEnum;
import com.qunar.search.common.model.feature.FastFilterModelItemFeature;
import com.qunar.search.common.model.feature.FastFilterModelRequestFeature;
import com.qunar.search.common.model.feature.FastFilterModelUserFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserRealClickFilterFeature;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * <AUTHOR>
 * @create 2023-11-16 下午7:10
 * @DESCRIPTION 添加部分新特征v2
 **/
@Component
public class FastFilterFeatureConvertorV2 extends FastFilterFeatureConvertorV1 {

    public Map<Integer, Object> convertObject(FastFilterModelRequestFeature requestFeature,
                                              FastFilterModelUserFeature userFeature,
                                              FastFilterModelItemFeature filterFeature) {

        Map<Integer, Object> resultMap = super.convertObject(requestFeature, userFeature, filterFeature);
        // 替换成新的标识
        resultMap.put(FastFilterFeatureIndexEnum.INDEX_1.getIndex(), (double)requestFeature.getIsNewUser());

        resultMap.put(FastFilterFeatureIndexEnum.INDEX_60.getIndex(), (double)requestFeature.getDayOfWeek());
        resultMap.put(FastFilterFeatureIndexEnum.INDEX_61.getIndex(),
                (requestFeature.getDayOfWeek() == 5 || requestFeature.getDayOfWeek() == 6) ? 1.0 : 0.0);
        resultMap.put(FastFilterFeatureIndexEnum.INDEX_62.getIndex(), requestFeature.isHoliday() ? 1.0 : 0.0);
        resultMap.put(FastFilterFeatureIndexEnum.INDEX_63.getIndex(), (double)requestFeature.getHourOfay());

        resultMap.put(FastFilterFeatureIndexEnum.INDEX_64.getIndex(), (double)requestFeature.getNowCheckInDiff());
        resultMap.put(FastFilterFeatureIndexEnum.INDEX_65.getIndex(), (double)requestFeature.getNowCheckOutDiff());
        // 是否同城
        if (StringUtils.isNotEmpty(requestFeature.getUserGpsCityCode())) {
            double isSameCity = StringUtils.equals(requestFeature.getCityUrl(), requestFeature.getUserGpsCityCode()) ? 1.0 : 0.0;
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_66.getIndex(), isSameCity);
        } else {
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_66.getIndex(), 1.0);
        }

        Map<String, UserRealClickFilterFeature> realFilterFeatureMap = userFeature.getUserRealClickFilterFeatureMap();
        if (realFilterFeatureMap.containsKey(filterFeature.getFilterName())) {
            UserRealClickFilterFeature realFilterFeature = realFilterFeatureMap.get(filterFeature.getFilterName());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_67.getIndex(), (double) realFilterFeature.getComplexFilterClickCnt());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_68.getIndex(), (double) realFilterFeature.getIsComplexFilterLastClick());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_69.getIndex(), (double) realFilterFeature.getComplexFilterClickSort());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_70.getIndex(), (double) realFilterFeature.getFastFilterClickCnt());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_71.getIndex(), (double) realFilterFeature.getIsFastFilterLastClick());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_72.getIndex(), (double) realFilterFeature.getFastFilterClickSort());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_73.getIndex(), (double) realFilterFeature.getComplexFastFilterClickCnt());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_74.getIndex(), (double) realFilterFeature.getIsComplexFastFilterLastClick());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_75.getIndex(), (double) realFilterFeature.getComplexFastFilterClickSort());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_76.getIndex(), (double) realFilterFeature.getHotelFilterTagClickCnt());
            resultMap.put(FastFilterFeatureIndexEnum.INDEX_77.getIndex(), (double) realFilterFeature.getHotelFilterTagFavouriteCnt());
        }

        return resultMap;
    }
}
