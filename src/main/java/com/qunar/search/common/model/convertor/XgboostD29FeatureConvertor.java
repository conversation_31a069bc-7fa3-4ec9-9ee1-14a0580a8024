package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;

/**
 * <AUTHOR>
 * @create 2020-11-02 下午7:10
 * @DESCRIPTION 添加部分新特征D27
 **/
@Component
public class XgboostD29FeatureConvertor extends XgboostD27FeatureConvertor {

    private static final Integer S2O_7_DAY_INDEX = 308;

    private static final Integer S2O_14_DAY_INDEX = 309;

    private static final Integer S2O_21_DAY_INDEX = 310;

    private static final Integer S2O_60_DAY_INDEX = 311;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featReturn = super.convert(requestFeature, userFeature, hotelFeature);
        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isEmpty(offlineFeature)) {
            return featReturn;
        }

        SortScene sortScene = requestFeature.getSortScene();
        switch (sortScene){
            case NEARBY:
                featReturn.put(S2O_7_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_7_DAY, ZERO));
                featReturn.put(S2O_14_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_14_DAY, ZERO));
                featReturn.put(S2O_21_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_21_DAY, ZERO));
                featReturn.put(S2O_60_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_60_DAY, ZERO));
                break;
            case POI_KEY:
                featReturn.put(S2O_7_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_7_DAY, ZERO));
                featReturn.put(S2O_14_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_14_DAY, ZERO));
                featReturn.put(S2O_21_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_21_DAY, ZERO));
                featReturn.put(S2O_60_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_60_DAY, ZERO));
                break;
            case SAME_CITY:
                featReturn.put(S2O_7_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_7_DAY, ZERO));
                featReturn.put(S2O_14_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_14_DAY, ZERO));
                featReturn.put(S2O_21_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_21_DAY, ZERO));
                featReturn.put(S2O_60_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_60_DAY, ZERO));
                break;
            case NOT_SAME_CITY:
                featReturn.put(S2O_7_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_7_DAY, ZERO));
                featReturn.put(S2O_14_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_14_DAY, ZERO));
                featReturn.put(S2O_21_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_21_DAY, ZERO));
                featReturn.put(S2O_60_DAY_INDEX, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_60_DAY, ZERO));
                break;
        }

        return featReturn;
    }
}
