package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.MediaChannel;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.feature.XhsDyShowHotelFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * D56 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD56 extends DnnFeatureConvertorD55 {

    private static final String MINI = "mini";
    private static final String APP = "APP";

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        double queryOrderAvgShowPrice = userFeature.getQueryOrderAvgShowPrice();
        resultMap.put(FeatureIndexEnum.D56_671.getIndex(), queryOrderAvgShowPrice);

        double hotelShowPrice = hotelFeature.getCachedPriceAfterMerchant() <= 0.0 ? hotelFeature.getMinPriceWithIdentity() : hotelFeature.getCachedPriceAfterMerchant();
        if (hotelShowPrice > 0 && hotelShowPrice < DEFAULT_MAX_SUB10 && queryOrderAvgShowPrice > 0 && queryOrderAvgShowPrice < DEFAULT_MAX_SUB10) {
            resultMap.put(FeatureIndexEnum.D56_672.getIndex(), hotelShowPrice / queryOrderAvgShowPrice);
        }

        int queryOrderTotalNum = userFeature.getQueryOrderTotalNum();
        double queryHotelOrderNum = hotelFeature.getQueryHotelOrderNum();
        if (queryOrderTotalNum > 0) {
            resultMap.put(FeatureIndexEnum.D56_673.getIndex(), queryHotelOrderNum / (double) queryOrderTotalNum);
            resultMap.put(FeatureIndexEnum.D56_674.getIndex(), (double) queryOrderTotalNum);
        }

        String requestSource = userFeature.getRequestSource();
        if (StringUtils.isNotEmpty(requestSource)) {
            switch (requestSource) {
                case APP:
                    resultMap.put(FeatureIndexEnum.D56_675.getIndex(), 1);
                    break;
                case MINI:
                    resultMap.put(FeatureIndexEnum.D56_676.getIndex(), 2);
                    break;
                default:
                    resultMap.put(FeatureIndexEnum.D56_677.getIndex(), 0);
                    break;
            }
        }

        XhsDyShowHotelFeature xhsDyShowHotelFeature = userFeature.getXhsDyShowHotelFeature();
        if (null != xhsDyShowHotelFeature) {
            String channel = xhsDyShowHotelFeature.getChannel();
            int channelIndex = MediaChannel.get(channel).getChannelIndex() + 1;
            resultMap.put(FeatureIndexEnum.D56_678.getIndex(), channelIndex);

            if (hotelShowPrice > 0 && hotelShowPrice < DEFAULT_MAX_SUB10 && xhsDyShowHotelFeature.getProductCodePrice() > 0 && xhsDyShowHotelFeature.getProductCodePrice() < DEFAULT_MAX_SUB10) {
                resultMap.put(FeatureIndexEnum.D56_679.getIndex(), hotelShowPrice / xhsDyShowHotelFeature.getProductCodePrice());
            }
        }

        return resultMap;
    }


}