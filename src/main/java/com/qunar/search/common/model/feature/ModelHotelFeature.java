package com.qunar.search.common.model.feature;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.bean.HotelCheckInInfo;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelVariableFeatureType;
import com.qunar.search.common.enums.QmPriceCompare;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.util.LibSvmUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2020-05-08 下午6:39
 * @DESCRIPTION 1、模型计算使用的酒店数据 2、特征日志打印对象
 **/
@Data
public class ModelHotelFeature implements Serializable {

    /**
     * 酒店seq
     */
    private String hotelSeq;

    /**
     * 酒店坐标
     */
    private GLatLng latLng;

    /**
     * 档次
     */
    private int dangci;

    /**
     * sort报价
     */
    private int mobileMinAvailablePrice;

    /**
     * 品牌
     */
    private String hotelBranch;

    /**
     * 商业区
     */
    private String tradingArea;

    /**
     * 酒店参加活动集合
     */
    private Set<String> activitySet;

    /**
     * 挂牌类型
     * 值参考search工程里的com.qunar.search.enums.HotelBoardType
     */
    private int board;

    /**
     * 星级
     */
    private int stars;

    /**
     * 酒店类型
     */
    private String[] hotelType;

    /**
     * 评分
     */
    private double commentScore;

    /**
     * 原始价格
     */
    private int originalPrice;

    /**
     * 酒店在关键词下的订单量
     */
    private int keyWordOrderCount;

    /**
     * 酒店命中用户搜索关键词索引位置--关键词按搜索时间排序，最近一个搜索词索引为 1
     */
    private int hitKeyWordIndex;

    /**
     * poi到酒店的距离
     */
    private double poiHotelDistance;

    /**
     * 用户到酒店的距离
     */
    double userHotelDistance;

    /**
     * adr或者ios 下订单均价
     */
    double platformOrderMeanPrice;

    /**
     * adr或者ios 下21天订单量
     */
    int platformOrderPv21;

    /**
     * adr或者ios 下7天搜索量
     */
    int platformShowPv7;

    /**
     * adr或者ios 下7天订单量
     */
    int platformOrderPv7;

    /**
     * 服务分
     */
    int serviceScore;

    /*--------------日志精简部分-start-----------------*/
    /**
     * 酒店embedding向量，数据过大，dnn实验特征，先不打印
     */
    @JsonIgnore
    List<Double> hotelEmbedding;

    /**
     * 酒店embedding向量，数据过大，dnn实验特征，先不打印
     * 20201225更新
     */
    @JsonIgnore
    List<Double> hotelEmbeddingV2;

    /**
     * convertor中使用的酒店统计数据
     * 1、线上填充内存数据 EnumMap<FeatureType, Double>
     * 2、打印日志时填充 空 map, 防止日志爆炸
     */
    @JsonIgnore
    private EnumMap<FeatureType, Double> offlineFeature;

    /**
     * 日志还原使用字段
     * 1、线上，不使用，为null
     * 2、离线还原，直接set字符串和上诉map
     */
    private String offlineFeatureLibSvm;

    private double profitPrice;

    public void setOfflineFeatureLibSvm(String offlineFeatureLibSvm) {
        this.offlineFeatureLibSvm = offlineFeatureLibSvm;
        if (MapUtils.isEmpty(this.offlineFeature)) {
            this.offlineFeature = LibSvmUtil.stringToEnumMap(offlineFeatureLibSvm);
        }
    }

    /**
     * convertor中使用的酒店标签类特征
     * 1、线上填充内存数据 EnumMap<HotelVariableFeatureType, Object>
     * 2、打印日志时填充 空 map, 防止日志爆炸
     */
    @JsonIgnore
    private EnumMap<HotelVariableFeatureType, Object> offlineVariableFeature;

    /**
     * 日志还原使用字段
     * 1、线上，不使用，为null
     * 2、离线还原，直接set字符串和上诉map
     */
    private String offlineVariableFeatureLibSvm;

    public void setOfflineVariableFeatureLibSvm(String offlineVariableFeatureLibSvm) {
        this.offlineVariableFeatureLibSvm = offlineVariableFeatureLibSvm;
        if (MapUtils.isEmpty(this.offlineVariableFeature)) {
            this.offlineVariableFeature = LibSvmUtil.stringToVariableFeatureDataMap(offlineVariableFeatureLibSvm);
        }
    }

    /*--------------日志精简部分-end-----------------*/

    /**
     * 用于标识酒店热门物理房型是否已售罄。true：已售罄，false：未售罄。
     */
    private transient boolean hotRoomSoldOut;

    /**
     * 带身份的最低价(变身份的sort报价)
     */
    private int minPriceWithIdentity;

    /**
     * 带身份的最低价（不变身份的sort报价）。
     */
    private int minPriceWithoutUpdateIdentity;

    /**
     * 该酒店展示类型，0表示未展示 ，1 表示前置请求展示，2表示当前请求返回
     */
    private int t;

    /**
     * 模型根据sort报价预估的render报价
     */
    private int estimatedRenderPrice;

    public int getEstimatedRenderPrice(){
        if(estimatedRenderPrice <= 0.1 ){
            return minPriceWithIdentity;
        }
        return estimatedRenderPrice;
    }

    /**
     * 模型预估城市s2o得分 暂时无
     */
    private List<Double> estimatedCitySceneS2o;

    /**
     * 比价情况
     * @see QmPriceCompare
     */
    private int beatInfo;

    /**
     * 酒店可售房型id
     */
    private Set<Long> vendibilityHouse;

    /**
     * 酒店可售房型订单占比
     */
    private double ordersRate;

    /**
     * 酒店装修时间距现在年份差
     */
    private Double fitmentYearDiff;

    /**
     * 酒店所在城市
     */
    private String cityCode;

    /**
     * 精排之后商业化插入之前酒店的初始排名
     */
    private int originSort;

    /**
     * 酒店在请求中的距离分桶排序
     */
    private int distanceBucketSort;

    /**
     * 酒店在请求中的距离前的酒店数cdf
     * */
    private double distanceBucketCdf;

    /**
     * 酒店在请求中的距离桶的酒店数
     * */
    private int distanceBucketNum;

    /**
     * 酒店类型 (青年旅舍、酒店公寓、民宿、客栈 等)
     * 这个集合里既有酒店类型，又有酒店主题
     * ThemeExperiment.getAcceptHotelType(); // 这样可以拿到酒店的类型
     */
    private List<String> screenHotelType;

    /**
     * 酒店近三个月单量排名, 从0开始
     */
    private int orderVolumeNumber;

    /**
     * 在query意图下的s2d以及s2o 意图主要（'subjectRoom','subjectTag','sheshi','bizZone','commentsTag','brand',
     * 'theme','group','fangxing','subway','dangci'）
     * {意图：s2d}
     */
    private Map<String, Double> intentionS2d = Collections.emptyMap();

    private Map<String, Double> intentionS2o = Collections.emptyMap();

    /**
     * 在意图下搜索top100的query词的s2d以及s2o 意图主要（'subjectRoom','subjectTag','sheshi','bizZone','commentsTag',
     * 'brand', 'theme','group','fangxing','subway','dangci'）
     * {query：s2d}
     */
    private Map<String, Double> queryS2d = Collections.emptyMap();

    private Map<String, Double> queryS2o = Collections.emptyMap();

    /**
     * 酒店在搜索query和筛选下的所有主题类型的订单量的rankgauss
     */
    private Map<String, Double> queryFilterThemeOrderErfinv = Collections.emptyMap();

    private Map<String, Double> queryFilterTypeOrderErfinv = Collections.emptyMap();

    /**
     * 酒店在该query词下的订单量
     */
    private double queryHotelOrderNum;

    private int qunarPayPrice;
    private int qunarPayPriceNewCustomer;
    private int ctripPayPrice;
    private int ctripPayPriceNewCustomer;
    private int elongPayPrice;
    private int elongPayPriceNewCustomer;

    /**
     * 酒店入住日期维度的特征
     */
    private HotelCheckInInfo hotelCheckInInfo;

    /**
     * render报价佣金
     */
    private double commission;

    /**
     * render报价佣金率
     */
    private double commissionRate;

    /**
     * sort报价佣金
     */
    private int sortPriceCommission;

    /**
     * sort报价佣金率
     */
    private int sortPriceCommissionRate;

    /**
     * 定价底价
     */
    private double apbp;

    /**
     * 定价后卖价 ： 定价后卖价=定价底价/(1-定价佣金率)+商家券因子卖价优惠
     */
    private double afterPrice;

    /**
     * 划线价 : 划线价=max(原始卖价,定价后卖价)
     */
    private double dispOrgPriceArr;

    /**
     * 商家券后卖价，报价侧多天时取平均并四舍五入取整了。
     */
    private int priceAfterMerchant;

    /**
     * 缓存的佣金
     */
    private int cachedCommission;

    /**
     * 缓存的商家券后卖价
     */
    private int cachedPriceAfterMerchant;

    /**
     * 缓存的最低价（考虑多倍积分等）
     */
    private int cachedRenderMinPrice;

    /**
     * 缓存的划线价
     */
    private int cachedDispOrgPrice;

    /**
     * 缓存的beat值
     */
    private float cachedBeat;

    /**
     * 缓存的新版beat值
     */
    private double cacheBeatV2;

    /**
     * 缓存的是否有效beat
     */
    private boolean cacheValidBeat;

    /**
     * POI驾车距离
     */
    private double driveDistance;

    /**
     * POI步行距离
     */
    private double walkDistance;

    /**
     * 报价缓存更新的时间戳
     */
    private long cachedTimestamp;
}
