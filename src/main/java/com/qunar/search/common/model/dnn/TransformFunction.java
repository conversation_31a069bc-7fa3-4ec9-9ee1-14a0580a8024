package com.qunar.search.common.model.dnn;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.qunar.search.common.bean.HotelStat;
import com.qunar.search.common.service.HotelStatDataService;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @DESCRIPTION dnn特征转换方法类
 **/
public class TransformFunction {

	public static Map<Integer, DnnValueConfig> transConfigMap(String str) {

		Map<Integer, DnnValueConfig> map = Maps.newHashMap();
		String[] strSplit = StringUtils.split(str, DnnConstants.BLANK);

		for (String item : strSplit) {
			String[] itemSplit = StringUtils.split(item, DnnConstants.COLON);
			if (itemSplit.length != 2) {
				continue;
			}

			// featureIndex
			int key = Integer.valueOf(itemSplit[0]);

			// min_0.1percent_25percent_media_75percent_99.9percent_max_bucketInfo_gaussMean_gaussStd_log2Mean_log2Std_sqrtMean_sqrtStd_pow2Mean_pow2Std
			String[] valueSplit = itemSplit[1].split(DnnConstants.UNDERLINE);
			if (valueSplit.length == 16) {
				map.put(key, new DnnValueConfig(key,
						DnnConstants.NAN_SET.contains(valueSplit[0]) ? Double.NaN : Double.valueOf(valueSplit[0]),
						DnnConstants.NAN_SET.contains(valueSplit[1]) ? Double.NaN : Double.valueOf(valueSplit[1]),
						DnnConstants.NAN_SET.contains(valueSplit[2]) ? Double.NaN : Double.valueOf(valueSplit[2]),
						DnnConstants.NAN_SET.contains(valueSplit[3]) ? Double.NaN : Double.valueOf(valueSplit[3]),
						DnnConstants.NAN_SET.contains(valueSplit[4]) ? Double.NaN : Double.valueOf(valueSplit[4]),
						DnnConstants.NAN_SET.contains(valueSplit[5]) ? Double.NaN : Double.valueOf(valueSplit[5]),
						DnnConstants.NAN_SET.contains(valueSplit[6]) ? Double.NaN : Double.valueOf(valueSplit[6]),
						DnnConstants.NAN_SET.contains(valueSplit[7]) ? "NAN" : valueSplit[7],
						DnnConstants.NAN_SET.contains(valueSplit[8]) ? Double.NaN : Double.valueOf(valueSplit[8]),
						DnnConstants.NAN_SET.contains(valueSplit[9]) ? Double.NaN : Double.valueOf(valueSplit[9]),
						DnnConstants.NAN_SET.contains(valueSplit[10]) ? Double.NaN : Double.valueOf(valueSplit[10]),
						DnnConstants.NAN_SET.contains(valueSplit[11]) ? Double.NaN : Double.valueOf(valueSplit[11]),
						DnnConstants.NAN_SET.contains(valueSplit[12]) ? Double.NaN : Double.valueOf(valueSplit[12]),
						DnnConstants.NAN_SET.contains(valueSplit[13]) ? Double.NaN : Double.valueOf(valueSplit[13]),
						DnnConstants.NAN_SET.contains(valueSplit[14]) ? Double.NaN : Double.valueOf(valueSplit[14]),
						DnnConstants.NAN_SET.contains(valueSplit[15]) ? Double.NaN : Double.valueOf(valueSplit[15])));
			} else {
				map.put(key, new DnnValueConfig(-1, Double.NaN, Double.NaN, Double.NaN, Double.NaN, Double.NaN,
						Double.NaN, Double.NaN, "NAN", Double.NaN, Double.NaN, Double.NaN, Double.NaN,
						Double.NaN, Double.NaN, Double.NaN, Double.NaN));
			}
		}
		return map;
	}

	/**
	 * 保留 len 位小数点 (2)
	 */
	public static double roundDoubleN(double value, int len) {
		double pow = Math.pow(10, len);
		return Math.round(value * pow) / pow;
	}

	public static double getBucketIndex(List<Double> bucketBoundaries, double value) {
		if (bucketBoundaries.isEmpty()) {
			return -1.0;
		}

		int bucketLength = bucketBoundaries.size();
		for (int i = 0; i < bucketLength; i++) {
			if (value <= bucketBoundaries.get(i)) {
				return i / (double) bucketLength;
			}

			if ((i + 1) == bucketLength) {
				return 1.0;
			}
		}

		return -1.0;
	}

	public static double log2MaxNorm(double value, double max) {
		if (max <= 0 || Double.isNaN(max)) {
			max = 1.0;
		}

		if (value < 0 || Double.isNaN(value)) {
			value = -1.0;
			return value / log2(max + 1);
		}

		return log2(value + 1) / log2(max + 1);
	}

	public static double log2(double value) {
		return Math.log(value) / Math.log(2.0);
	}

	public static double sqrt2MaxNorm(double value, double max) {
		if (max <= 0 || Double.isNaN(max)) {
			max = 1.0;
		}

		if (value < 0 || Double.isNaN(value)) {
			value = -1.0;
			return value / Math.sqrt(max + 1);
		}
		return Math.sqrt(value) / Math.sqrt(max + 1);
	}

	public static double gaussNorm(double value, double mean, double std) {
		if (Double.isNaN(value)) {
			return 0.0;
		}

		if (Double.isNaN(mean)) {
			mean = 0.0;
		}

		if (Double.isNaN(std) || std < DnnConstants.DOUBLE_ZEROS) {
			std = 1.0;
		}

		return (value - mean) / std;
	}

	public static double cosSim(List<Double> hotelVector, List<Double> userVector) {

		double sum = 0;
		double sumX1 = 0;
		double sumX2 = 0;

		if (hotelVector.size() != userVector.size()) {
			return 0.0;
		}

		for (int i = 0; i < hotelVector.size(); i++) {

			double x1 = hotelVector.get(i);
			double x2 = userVector.get(i);

			sum += x1 * x2;
			sumX1 += Math.pow(x1, 2);
			sumX2 += Math.pow(x2, 2);
		}

		if (sumX1 == 0.0 || sumX2 == 0.0) {
			return 0.0;
		}

		return roundDouble(sum / (Math.sqrt(sumX1) * Math.sqrt(sumX2)), 7);
	}

	/**
	 * 保留 len 位小数点
	 */
	public static double roundDouble(double value, int len) {
		return (int) (value * Math.pow(10, len)) / Math.pow(10, len);
	}

	/**
	 * 获取单个酒店embedding向量
	 * @param hotelSEQ
	 * @return
	 */
	public static List<Double> getHotelEmbedding(String hotelSEQ) {
		HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(hotelSEQ);
		if (Objects.isNull(hotelStat)) {
			return DnnConstants.DEFAULT_EMBEDDING_VECTOR;
		}

		List<Double> embeddingFeature = hotelStat.getAlgoEmbeddingFeature();
		if (CollectionUtils.isEmpty(embeddingFeature)) {
			return DnnConstants.DEFAULT_EMBEDDING_VECTOR;
		}

		return embeddingFeature;
	}

	/**
	 * 计算多个酒店embedding特征平均值
	 * @param hotelList
	 * @return
	 */
	public static List<Double> getAvgHotelEmbedding(List<String> hotelList) {

		ArrayList<Double> valueList = Lists.newArrayList();
		for (String hotelSeq : hotelList) {
			List<Double> hotelEmbedding = getHotelEmbedding(hotelSeq);
			for (int i = 0; i < hotelEmbedding.size(); i++) {
				if (valueList.size() <= i) {
					valueList.add(hotelEmbedding.get(i));
				} else {
					double newValue = valueList.get(i) + hotelEmbedding.get(i);
					valueList.set(i, newValue);
				}
			}
		}

		for (int i = 0; i < valueList.size(); i++) {
			double avgValue = valueList.get(i) / hotelList.size();
			valueList.set(i, avgValue);
		}

		return valueList;
	}
}
