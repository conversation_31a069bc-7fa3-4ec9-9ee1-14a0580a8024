package com.qunar.search.common.model.convertor;

import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.price.render.RenderPriceEstimate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;


@Component
public class DnnPriceConvertor extends AbstractSortFeatureConvertor {
    public static int FEATURE_LENGTH = 20;
    public static String DEFAULT_FEATURE_STR = "1.0";

    public Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Object> featureReturn = new HashMap<>(FEATURE_LENGTH);

        // index 1
        int newSortPrice = hotelFeature.getMinPriceWithIdentity();
        featureReturn.put(1, String.valueOf(newSortPrice));

        // 用户身份折扣
        // index 2
        featureReturn.put(2, userFeature.getUserIdentityDiscount());

        // 用户身份
        // index 3
        featureReturn.put(3, userFeature.getUserIdentityString());

        // 活动折扣
        // index range from 10 to 19
        int idx = 10;
        Set<String> activityIds = hotelFeature.getActivitySet();
        for (String activity : RenderPriceEstimate.activityDiscount.keySet()) {
            if (activityIds.contains(activity)) {
                featureReturn.put(idx, String.valueOf(RenderPriceEstimate.activityDiscount.get(activity)));
            } else {
                featureReturn.put(idx, DEFAULT_FEATURE_STR);
            }
            idx ++;
        }

        return featureReturn;
    }

}
