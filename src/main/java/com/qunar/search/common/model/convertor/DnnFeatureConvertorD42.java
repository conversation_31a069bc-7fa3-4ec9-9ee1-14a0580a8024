package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;

/**
 * D42 特征 Convertor
 */
@Component
public class DnnFeatureConvertorD42 extends DnnFeatureConvertorD41 {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();


        // 用户搜索城市是否用户常住地
        int oftenUserFlag = 0;
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && userFeature.getUserProfile() != null
                && StringUtils.isNotEmpty(userFeature.getUserProfile().getOftenCity())) {
            oftenUserFlag = StringUtils.equals(requestFeature.getRootCityCode(), userFeature.getUserProfile().getOftenCity()) ? 1 : 0;
        }

        boolean isRootCity = true;
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && StringUtils.isNotEmpty(requestFeature.getCityUrl())) {
            isRootCity = StringUtils.equals(requestFeature.getRootCityCode(), requestFeature.getCityUrl());
        }

        if (isRootCity) {
            // 430 地级市
            resultMap.put(FeatureIndexEnum.D42_430.getIndex(), 1.0);
        } else {
            // 431 县级市
            resultMap.put(FeatureIndexEnum.D42_431.getIndex(), 1.0);
        }

        if (MapUtils.isNotEmpty(offlineFeature)) {
            resultMap.put(FeatureIndexEnum.D42_432.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_OFTEN_USER_ORDER_RATE_7D, ZERO));
            resultMap.put(FeatureIndexEnum.D42_433.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_NOT_OFTEN_USER_ORDER_RATE_7D, ZERO));
            resultMap.put(FeatureIndexEnum.D42_434.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_OFTEN_USER_ORDER_NUM_7D, ZERO));
            resultMap.put(FeatureIndexEnum.D42_435.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_NOT_OFTEN_USER_ORDER_NUM_7D, ZERO));
            resultMap.put(FeatureIndexEnum.D42_436.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_OFTEN_USER_ORDER_RATE_7D, ZERO));
            resultMap.put(FeatureIndexEnum.D42_437.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_NOT_OFTEN_USER_ORDER_RATE_7D, ZERO));

            if (isRootCity) {
                resultMap.put(FeatureIndexEnum.D42_438.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_ORDER_PRICE_STD_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_439.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_ORDER_PRICE_AVG_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_440.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_ORDER_PRICE_MIN_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_441.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_ORDER_PRICE_MEDIA_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_442.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_ORDER_PRICE_MAX_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_443.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_444.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_GRADE_1_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_445.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_GRADE_2_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_446.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_GRADE_3_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_447.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_GRADE_4_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_448.getIndex(), offlineFeature.getOrDefault(FeatureType.CITY_GRADE_5_HOTEL_NUM, ZERO));
            } else {
                resultMap.put(FeatureIndexEnum.D42_438.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_ORDER_PRICE_STD_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_439.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_ORDER_PRICE_AVG_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_440.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_ORDER_PRICE_MIN_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_441.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_ORDER_PRICE_MEDIA_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_442.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_ORDER_PRICE_MAX_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_443.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_444.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_GRADE_1_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_445.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_GRADE_2_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_446.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_GRADE_3_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_447.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_GRADE_4_HOTEL_NUM, ZERO));
                resultMap.put(FeatureIndexEnum.D42_448.getIndex(), offlineFeature.getOrDefault(FeatureType.AREA_GRADE_5_HOTEL_NUM, ZERO));
            }

            if (1 == oftenUserFlag) {
                resultMap.put(FeatureIndexEnum.D42_449.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_OFTEN_USER_CTR_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_450.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_OFTEN_USER_CTCVR_7D, ZERO));
            } else {
                resultMap.put(FeatureIndexEnum.D42_451.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_NOT_OFTEN_USER_CTR_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_452.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_NOT_OFTEN_USER_CTCVR_7D, ZERO));
            }
        }

        // 是否提前订
        if (requestFeature.getDiffDaysFeature() != null) {
            resultMap.put(FeatureIndexEnum.D42_457.getIndex(), requestFeature.getDiffDaysFeature() > 1 ? 1.0 : 0.0);
        }
        if (StringUtils.isNotEmpty(requestFeature.getCityUrl())) {
            resultMap.put(FeatureIndexEnum.D42_458.getIndex(), userFeature.getSearchCityPriceAvg3D().getOrDefault(requestFeature.getCityUrl(), 0.0));
            resultMap.put(FeatureIndexEnum.D42_459.getIndex(), userFeature.getSearchCityPriceMedia3D().getOrDefault(requestFeature.getCityUrl(), 0.0));

            double priceAvg3D= userFeature.getSearchCityPriceAvg3D().getOrDefault(requestFeature.getCityUrl(), 0.0);
            Double hotelPrice = (Double) resultMap.getOrDefault(FeatureIndexEnum.DNN_INDEX_101.getIndex(), 0.0);

            if (priceAvg3D > 0 && priceAvg3D < DEFAULT_MAX_SUB10 && hotelPrice > 0 && hotelPrice < DEFAULT_MAX_SUB10) {
                resultMap.put(FeatureIndexEnum.D42_460.getIndex(), (priceAvg3D - hotelPrice) / priceAvg3D);
            }
        }

        return resultMap;
    }
}
