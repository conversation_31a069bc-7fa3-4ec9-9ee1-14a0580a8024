package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossRate;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;

/**
 * <AUTHOR>
 * @create 2020-08-19 下午7:10
 * @DESCRIPTION 广告rank 二期，添加ctr和酒店头图相关信息 pom: http://pmo.corp.qunar.com/browse/FD-9043
 **/
@Component
public class XgboostD25FeatureConvertor extends XgboostD23FeatureConvertor {

    private static final Integer COMPLAINT_RATE_INDEX = 181;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featReturn = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();
        if (MapUtils.isEmpty(feature)) {
            return featReturn;
        }

        // 删除投诉率特征
        if (featReturn.containsKey(COMPLAINT_RATE_INDEX)) {
            featReturn.remove(COMPLAINT_RATE_INDEX);
        }

        // （用户实时点击价格-酒店报价）/用户实时点击价格
        putCrossRate(featReturn, 280,
                userFeature.getRealtimeClickAvgPrice(), user -> (user != 0 && user < DEFAULT_MAX_SUB10),
                (double) hotelFeature.getMobileMinAvailablePrice(), hotel -> (hotel != 0 && hotel < DEFAULT_MAX_SUB10)
        );

        // 用户实时点击的酒店序列的，所有头图质量分的平均值
        putMap(featReturn, userFeature.getRtClickImgScoreAvg(), 281, v -> (v > 0));

        putCrossRate(featReturn, 282,
                userFeature.getRtClickImgScoreAvg(), user -> user > 0,
                feature.get(FeatureType.HEAD_IMAGE_SCORE), hotel -> (hotel != null && hotel > 0)
        );

        // 用户实时点击的酒店序列的，所有头图数量的平均值
        putMap(featReturn, userFeature.getRtClickImgNumAvg(), 283, v -> (v > 0.0d));

        putCrossRate(featReturn, 284,
                userFeature.getRtClickImgNumAvg(), user -> user > 0,
                feature.get(FeatureType.IMAGE_NUM), hotel -> (hotel != null && hotel > 0)
        );

        //酒店头图质量分
        putMap(featReturn, feature.get(FeatureType.HEAD_IMAGE_SCORE), 285, v -> (v != null && v > 0));

        //酒店头图数量
        putMap(featReturn, feature.get(FeatureType.IMAGE_NUM), 286, v -> (v != null && v > 0));

        //酒店3月内点击率
        putMap(featReturn, feature.get(FeatureType.CTR_3_MONTH), 287, v -> (v != null && v > -1));

        //酒店3月内点击率（置信）
        putMap(featReturn, feature.get(FeatureType.CTR_3_MONTH_ZHIXIN), 288, v -> (v != null && v > -1));

        //酒店7天点击率
        putMap(featReturn, feature.get(FeatureType.CTR_7_DAY), 289, v -> (v != null && v > -1));

        //酒店7天点击率（置信）
        putMap(featReturn, feature.get(FeatureType.CTR_7_DAY_ZHIXIN), 290, v -> (v != null && v > -1));

        return featReturn;
    }

}
