package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelTypeEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * D40 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD40 extends DnnFeatureConvertorD39 {

    private static final Integer ONE = 1;

    private static final Integer ZERO = 0;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 405 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);
        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        try {
            List<String> screenHotelType = hotelFeature.getScreenHotelType();// 酒店类型
            if(CollectionUtils.isNotEmpty(screenHotelType)){
                String strHt = screenHotelType.get(0);
                HotelTypeEnum hotelTypeEnum = HotelTypeEnum.parseByChHotelTypeOrTypeName(strHt);
                resultMap.put(FeatureIndexEnum.D40_410.getIndex(), hotelTypeEnum.getNumber());  // 酒店类型

            } else {
                resultMap.put(FeatureIndexEnum.D40_410.getIndex(), HotelTypeEnum.NORMAL_HOTEL.getNumber());
            }

            // 酒店是否在景区内
            if( !Objects.isNull(offlineFeature) ){
                Double orDefault = offlineFeature.getOrDefault(FeatureType.HOTEL_LOCATION_IS_SCENIC_AREA, FeatureConstants.ZERO);

                if(orDefault != null){
                    resultMap.put(FeatureIndexEnum.D40_411.getIndex(), orDefault-0.0<0.01 ? ZERO : ONE);
                } else {
                    resultMap.put(FeatureIndexEnum.D40_411.getIndex(), ZERO);
                }

            } else {
                resultMap.put(FeatureIndexEnum.D40_411.getIndex(), ZERO );
            }

        } catch (Exception e) {
            log.error("DnnFeatureConvertorD40 转换特征出错" , e);
        }

        return resultMap;
    }

}
