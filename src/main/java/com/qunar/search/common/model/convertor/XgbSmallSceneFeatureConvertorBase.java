package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.bean.UserProfile;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.*;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.math.CoordDistance;
import com.qunar.search.common.math.NumberUtils;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.DateUtil;
import com.qunar.search.common.util.Numbers;
import com.qunar.search.common.util.ZhiXin;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.qunar.search.common.constants.FeatureConstants.*;
import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.enums.FeatureType.*;
import static com.qunar.search.common.model.convertor.ConvertorBase.getValueByPlatform;
import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossDiff;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMapNotNull;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

/**
 * dnn特征转换器基类，由于dnn只用到了xgb的部分特征，这里只计算需要用的特征
 *
 * <AUTHOR>
 * @date 2022/08/19
 */
@Component
public class XgbSmallSceneFeatureConvertorBase extends AbstractSortFeatureConvertor {

    public static final int DEFAULT_PRICE_WEIGHT = 1;

    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE - 10;

    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;

    private static final String COMMA = ",";

    private static final double SAME_CITY_DISTANCE = 500000;

    private final static double THRESHOLD = 0.0000001;
    
    private static final Double DEFAULT_PRICE = 200D;

    private static final Double DEFAULT_CTR = 0.005D;

    private static final Integer ONE = 1;
    private static final Integer TWO = 2;
    private static final Integer THREE = 3;
    private static final Integer FOUR = 4;

    private static final EnumMap<FeatureType, Double> EMPTY_FEATURE_MAP = Maps.newEnumMap(FeatureType.class);

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 指定初始容量，避免扩容
        Map<Integer, Object> featureMap = Maps.newHashMapWithExpectedSize(RankSystemConfig.getDnnFeatureSize());

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        if (offlineFeature == null) {
            offlineFeature = EMPTY_FEATURE_MAP;
        }


        SortScene scene = requestFeature.getSortScene();
        Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders();

        double orderAvgPrice = userFeature.getOrderAvgPrice();

        double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();
        double price = hotelFeature.getMobileMinAvailablePrice();
        if (requestFeature.isPriceWeightDate() && MapUtils.isNotEmpty(offlineFeature)) {
            Double cityWeight = offlineFeature.get(HOLIDAY_PRICE_WEIGHT);
            if (cityWeight != null && cityWeight != DEFAULT_PRICE_WEIGHT) {
                clickAvgPrice = valueWeight(clickAvgPrice, cityWeight);
                price = valueWeight(price, cityWeight);
            }
        }

        String seq = hotelFeature.getHotelSeq();

        //用户过去半年预订该酒店的次数。
        if (historyOrders.containsKey(seq)) {
            double count = historyOrders.get(seq).size();
            if (count > 0 &&  count < DEFAULT_MAX_SUB10 ) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_1.getIndex(), count);
            }
        }

        int pos = StringUtils.lastIndexOf(seq, "_");
        if (pos > 0) {
            Map<String, UserShowOrderData.OrderInfo> cityLastOrder = userFeature.cityLastOrder;
            UserShowOrderData.OrderInfo orderInfo = cityLastOrder.get(StringUtils.substring(seq,0, pos));
            if (orderInfo != null && StringUtils.equals(orderInfo.hotelSeq, seq)) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_2.getIndex(), ONE);
            }
        }

        UserShowOrderData.PvInfo pvInfo = userFeature.hisShowSt.get(seq);
        // 近三周用户浏览和点击该酒店的次数
        double numClick = 0.0D;
        if (pvInfo != null) {
            numClick = pvInfo.cl;
        }
        if (numClick > 0 && numClick < DEFAULT_MAX_SUB10 ) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_6.getIndex(), numClick);
        }

        //点击价格
        if (clickAvgPrice > 0 && clickAvgPrice<DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_14.getIndex(), clickAvgPrice);
        }

        // 酒店历史订单价格均值与用户历史订单价格均值的相对距离[45-46]
        if (orderAvgPrice > 0 && orderAvgPrice<DEFAULT_MAX_SUB10 ) {
            double orderMeanPrice = getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_AVG_PRICE_180_DAY,
                    IOS_AVG_PRICE_180_DAY, hotelFeature.getPlatformOrderMeanPrice());
            if (orderMeanPrice > 0 && orderMeanPrice<DEFAULT_MAX_SUB10) {
                double diff = orderMeanPrice- orderAvgPrice;
                double disScore = diff / orderAvgPrice;
                featureMap.put(FeatureIndexEnum.DNN_INDEX_45.getIndex(), Numbers.roundStringDouble(disScore, FeatureConstants.decimals_num));
                featureMap.put(FeatureIndexEnum.DNN_INDEX_46.getIndex(), Numbers.roundStringDouble(diff, FeatureConstants.decimals_num));
            }
        }


        // 4天之前收藏过该酒店, 4天之内收藏过该酒店[97-98]
        if (userFeature.getFavoriteHotels().containsKey(seq)) {
            int requestDay = requestFeature.getRequestDate();
            int logDay4 = DateUtil.addDay(requestDay, -4);
            UserHotel.FavoriteHotel favoriteHotel = userFeature.getFavoriteHotels().get(seq);
            if (favoriteHotel.getFavoriteDate() < logDay4) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_97.getIndex(), ONE);
            } else {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_98.getIndex(), ONE);
            }
        }

        // 酒店近3周的订单量[99]
        double v_num_orders = getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_ORD_PV_21_DAY,
                IOS_ORD_PV_21_DAY, hotelFeature.getPlatformOrderPv21());
        if (v_num_orders<DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_99.getIndex(), v_num_orders);
        }

        //酒店评分
        double commentScore = hotelFeature.getCommentScore();
        featureMap.put(FeatureIndexEnum.DNN_INDEX_100.getIndex(), commentScore);

        //"酒店折扣"[101-102]
        double originalPrice = hotelFeature.getOriginalPrice();
        if (price > 0 && price < DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_101.getIndex(), price);
            if (originalPrice != 0 && originalPrice < DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_102.getIndex(), originalPrice - price);
            }
        }

        // 根据sort报价预估的render报价，替换sort报价, index: 101
        double estimatePrice = hotelFeature.getEstimatedRenderPrice();
        // 有效的预估价格
        if (estimatePrice > 0 && estimatePrice < DEFAULT_MAX_SUB100) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_101.getIndex(), estimatePrice);
            if (originalPrice != 0 && originalPrice < DEFAULT_MAX_SUB100) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_102.getIndex(), originalPrice - estimatePrice);
            }
        }

        // 酒店转化率特征
        double showpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_SHOW_PV_7_DAY, IOS_SHOW_PV_7_DAY, hotelFeature.getPlatformShowPv7());
        double orderpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_ORD_PV_7_DAY, IOS_ORD_PV_7_DAY, hotelFeature.getPlatformOrderPv7());
        if (showpv7 > 0) {
            double rate = ZhiXin.getZhiXin(orderpv7 / showpv7, showpv7);
            featureMap.put(FeatureIndexEnum.DNN_INDEX_103.getIndex(), rate);
        }
        //酒店在关键词下订单量
        if (scene.match(SortScene.POI_KEY)) {
            double keyWordOrderCount = hotelFeature.getKeyWordOrderCount();
            if (keyWordOrderCount > 0 && keyWordOrderCount < DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_104.getIndex(), keyWordOrderCount);
            }
        }
        //酒店到poi的距离
        if (scene.match(SortScene.POI_KEY)) {
            double poiDistance = hotelFeature.getPoiHotelDistance();
            if(poiDistance > 0 && poiDistance<DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_105.getIndex(), poiDistance);
            }
        }

        //同城或商圈场景下，酒店距离用户或者坐标的距离。
        if (scene.match(SortScene.SAME_CITY) || scene.match(SortScene.NEARBY)) {
            double coorDistance = hotelFeature.getUserHotelDistance();

            if(coorDistance > 0 && coorDistance<DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_106.getIndex(), coorDistance);
            }
        }


        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_114.getIndex(), offlineFeature.get(ORD_CNT_6_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_115.getIndex(), offlineFeature.get(ORD_CNT_3_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_119.getIndex(), offlineFeature.get(SEARCH_CNT_3_WEEK));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_123.getIndex(), offlineFeature.get(CTR_S2O_3_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_125.getIndex(), offlineFeature.get(CTR_S2O_3_WEEK));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_132.getIndex(), offlineFeature.get(COMMENT_COUNT));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_135.getIndex(), offlineFeature.get(TRAFFIC_NEG_LABEL));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_151.getIndex(), offlineFeature.get(ECRM));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_156.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_24H, NEGATIVE_ONE));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_157.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_30D, NEGATIVE_ONE));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_158.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_90D, NEGATIVE_ONE));


        // 187 - 188
        addDistanceFeat(hotelFeature.getLatLng(), requestFeature, featureMap);


        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;

        // 189 酒店档次
        featureMap.put(FeatureIndexEnum.DNN_INDEX_189.getIndex(), (double)hotelRealGrade);

        // 190 历史点击酒店次数
        featureMap.put(FeatureIndexEnum.DNN_INDEX_190.getIndex(), (double)userFeature.getUserHistoryClickFeature().getBehaveCount());

        // 192 用户历史点击当前酒店档次占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_192.getIndex(), userFeature.getUserHistoryClickFeature().getGradeRateMap().get(hotelRealGrade));

        // 197 用户点击当前酒店占比
        putMapInternal(featureMap,FeatureIndexEnum.DNN_INDEX_197.getIndex(), userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().get(seq));

        // 199 1月（31天）内收藏酒店最大档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_199.getIndex(), (double)(hotelRealGrade - userFeature.getUser31DayCollectFeature().getBehaveMaxGrade()));


        // 205 用户实时点击酒店次数
        if (userFeature.getUserRealtimeClickFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_205.getIndex(), (double)userFeature.getUserRealtimeClickFeature().getBehaveCount());
        }        

        // 206 用户实时点击次数最多的档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_206.getIndex(), (double)(hotelRealGrade - userFeature.getUserRealtimeClickFeature().getBehaveMaxGrade()));

        // 212 用户实时点击当前酒店占比
        putMapInternal(featureMap,FeatureIndexEnum.DNN_INDEX_212.getIndex(), userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().get(seq));

        // 213 实时query筛选到该酒店的次数
        if (userFeature.getUserRealtimeQueryFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_213.getIndex(), (double)userFeature.getUserRealtimeQueryFeature().getBehaveCount());
        }

        // 214 实时query筛选到该酒店的query占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_214.getIndex(), userFeature.getUserRealtimeQueryFeature().getHotelSeqRateMap().get(seq));

        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_220.getIndex(), userFeature.getUser180DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 221 用户历史180天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_221.getIndex(), userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 226 用户历史180天订单量城市中，该城市占比
        if (MapUtils.isNotEmpty(userFeature.getUser180DayOrderFeature().getHotelCityRateMap())) {
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_226.getIndex(), userFeature.getUser180DayOrderFeature().getHotelCityRateMap().get(getCityFromSeq(seq)));
        }

        // 227 用户历史365天订单量
        if (userFeature.getUser365DayOrderFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_227.getIndex(), (double)userFeature.getUser365DayOrderFeature().getBehaveCount());
        }

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_229.getIndex(), userFeature.getUser365DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 230 用户历史365天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_230.getIndex(), userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 235 用户历史365天订单量城市中，该酒店城市占比
        if (MapUtils.isNotEmpty(userFeature.getUser365DayOrderFeature().getHotelCityRateMap())) {
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_235.getIndex(), userFeature.getUser365DayOrderFeature().getHotelCityRateMap().get(getCityFromSeq(seq)));
        }

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_237.getIndex(), (double)(hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade()));

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_238.getIndex(), userFeature.getUser800DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 239 用户历史800天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_239.getIndex(), userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().get(seq));

        // embedding 特征位，246-247
        ConvertorBase.putEmbeddingFeature(userFeature, hotelFeature, featureMap);

        Double bizZoneMean = offlineFeature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
        if (bizZoneMean != null && bizZoneMean > 0.0d) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_248.getIndex(), bizZoneMean);
            double diff = estimatePrice - bizZoneMean;
            featureMap.put(FeatureIndexEnum.DNN_INDEX_249.getIndex(), diff);
            featureMap.put(FeatureIndexEnum.DNN_INDEX_250.getIndex(), Numbers.roundStringDouble(diff / bizZoneMean, FeatureConstants.decimals_num));
        }


        UserProfile userProfile = userFeature.getUserProfile();
        if (userProfile != null) {
            switch (userProfile.getSensitiveScore()) {
                case 1: // 高价格敏感度用户
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.HIGH_PRICE_SENS_PREFECT));
                    break;
                case 0: // 低价格敏感度用户
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.LOW_PRICE_SENS_PREFECT));
                    break;
                case -1: // 未知
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.PRICE_SENS_UN_KNOW_PERFECT));
                    break;
            }
        }

        //酒店3月内点击率
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_287.getIndex(), offlineFeature.get(FeatureType.CTR_3_MONTH));

        //酒店3月内点击率（置信）
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_288.getIndex(), offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN));

        //酒店7天点击率
        putMapInternal(featureMap, FeatureIndexEnum.D25_HOUTLE_7DAY_CTR_289.getIndex(), offlineFeature.get(FeatureType.CTR_7_DAY));

        //酒店7天点击率（置信）
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_290.getIndex(), offlineFeature.get(FeatureType.CTR_7_DAY_ZHIXIN));



        int checkInNum = requestFeature.getCheckInNum();
        checkInNum = Math.min(checkInNum, 8);
        // 用户入住天数
        featureMap.put(FeatureIndexEnum.DNN_INDEX_291.getIndex(), (double)checkInNum);

        if (checkInNum > 0) {

            // 这里赋值是错的，加个开关是为了和原始错的逻辑diff
            if (RankSystemConfig.isEnable292FeatureError()) {
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_292.getIndex(), offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + checkInNum - 1)));
            }

            // 用户入住天数对应的酒店不同天数的订单数 [292]
            int intervalIndex = 2*(checkInNum - 1);
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_292.getIndex(), offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + intervalIndex)));
        }

        //用户入住那天是周末时，酒店的历史周末的销售订单占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_295.getIndex(), requestFeature.isWeekend() ? FeatureConstants.ONE : FeatureConstants.ZERO);

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_296.getIndex(), requestFeature.isWeekend()
                ? offlineFeature.get(FeatureType.WEEKEND_ORDER_RATE)
                : offlineFeature.get(FeatureType.NO_WEEKEND_ORDER_RATE));

        // 用户预订日期内包含节假日和周末占比[297]
        if (requestFeature.getCheckWeekendHolidayRate() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_297.getIndex(), requestFeature.getCheckWeekendHolidayRate());
        }

        // 酒店历史订单（周末间夜+节假日间夜）占比[298]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_298.getIndex(), offlineFeature.get(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE));

        // 用户预定日期内的节假日和周末占比 - 酒店历史订单节假日和周末占比（间夜） [299]
        putCrossDiff(featureMap, FeatureIndexEnum.DNN_INDEX_299.getIndex(), requestFeature.getCheckWeekendHolidayRate(), s -> true,
                offlineFeature.getOrDefault(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE, NEGATIVE_ONE),
                v -> (v != null && v > -1.0));


        // 酒店60天内得曝光pv[300]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_300.getIndex(), offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_60_DAY));

        // 酒店7天内得曝光pv[303]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_303.getIndex(), offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_7_DAY));

        // 酒店60天内得曝光s2o[304]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_304.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_60_DAY));

        // 酒店21天内得曝光s2o[305]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_305.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_21_DAY));

        // 酒店14天内得曝光s2o[306]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_306.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_14_DAY));

        // 酒店7天内得曝光s2o[307]
        putMapInternal(featureMap, FeatureIndexEnum.D27_HOUTLE_7DAY_S2O_307.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_7_DAY));

        SortScene sortScene = requestFeature.getSortScene();
        switch (sortScene){
            case NEARBY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_60_DAY));
                break;
            case POI_KEY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_60_DAY));
                break;
            case SAME_CITY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_60_DAY));
                break;
            case NOT_SAME_CITY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_60_DAY));
                break;
        }

        // 335 -> 比价info
        featureMap.put(FeatureIndexEnum.D34_QM_PRICECOMPARE_335.getIndex(), hotelFeature.getBeatInfo()*1.0);

        // 336 -> 可售房型订单占比
        featureMap.put(FeatureIndexEnum.D34_ORDERS_RATE_336.getIndex(), Numbers.roundStringDouble(
                hotelFeature.getOrdersRate(), FeatureConstants.decimals_num));
        
        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 337 -> 7天对应场景曝光CTR    338 -> 21天对应场景曝光CTR   339 -> 60天对应场景曝光CTR
            fillSceneCtr(requestFeature.getSortScene(), offlineFeature, featureMap);

            // 340 -> 7天去位置偏置曝光CTR
            featureMap.put(FeatureIndexEnum.D34_POS_COEC_CTR_7DAY_340.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_7DAY, NEGATIVE_ONE));

            // 341 -> 14天去位置偏置曝光CTR
            featureMap.put(FeatureIndexEnum.D34_POS_COEC_CTR_14DAY_341.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_14DAY, NEGATIVE_ONE));

            // 342 -> 21天去位置偏置曝光CTR
            featureMap.put(FeatureIndexEnum.D34_POS_COEC_CTR_21DAY_342.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_21DAY, NEGATIVE_ONE));

            // 343 -> 该酒店7天非扶持曝光量占该城市非扶持曝光量占比
            featureMap.put(FeatureIndexEnum.D34_CITY_7DAY_SHOW_RAT_343.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_SHOW_RATE, NEGATIVE_ONE));

            // 344 -> 该酒店7天点击量占该城市点击量占比
            featureMap.put(FeatureIndexEnum.D34_CITY_7DAY_CLICK_RAT_344.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_CLICK_RATE, NEGATIVE_ONE));

            // 345 -> 该酒店7天订单量占该城市订单量占比
            featureMap.put(FeatureIndexEnum.D34_CITY_7DAY_ORDER_RAT_345.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_ORDER_RATE, NEGATIVE_ONE));
        }

        // 346 -> 当前时间于酒店装修或开业时间年份差
        if (hotelFeature.getFitmentYearDiff() != null) {
            featureMap.put(FeatureIndexEnum.D34_FITMENT_YEARS_DIFF_346.getIndex(), hotelFeature.getFitmentYearDiff());
        }

        // 348 -> 酒店7天的cvr (s2o / ctr) 直接用统计好的 307特征位 除以289特征位
        Double cvr7d = divisionValue(featureMap.get(FeatureIndexEnum.D25_HOUTLE_7DAY_CTR_289.getIndex()),
                featureMap.get(FeatureIndexEnum.D27_HOUTLE_7DAY_S2O_307.getIndex()), FeatureConstants.decimals_num);
        featureMap.put(FeatureIndexEnum.D34_HOTEL_7DAY_CVR_348.getIndex(), cvr7d);

        // 354 -> 酒店折扣率 （预估render 报价 / sort报价）
        Double priceDiscount = divisionValue(hotelFeature.getMobileMinAvailablePrice() * 1.0,
                hotelFeature.getEstimatedRenderPrice() * 1.0, 2);
        featureMap.put(FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex(), priceDiscount);


        if (MapUtils.isNotEmpty(offlineFeature)) {
            Double cityHotelCnt = offlineFeature.getOrDefault(FeatureType.CITY_HOTELS_CNT, FeatureConstants.ONE);
            Double geoHotelCnt = offlineFeature.getOrDefault(FeatureType.GEO_HOTELS_CNT, FeatureConstants.ONE);
            Double aroundHotelsCnt = offlineFeature.getOrDefault(FeatureType.AROUND_HOTELS_CNT, FeatureConstants.ONE);
            Double geoPv7cnt = offlineFeature.getOrDefault(FeatureType.GEO_PV_7_CNT, FeatureConstants.ONE);
            Double geoPvAvg7Price = offlineFeature.getOrDefault(FeatureType.GEO_PV_AVG_7_PRICE, DEFAULT_PRICE);
            Double geoOrder7Cnt = offlineFeature.getOrDefault(FeatureType.GEO_ORDER_7_CNT, FeatureConstants.ONE);
            Double geoClick7Cnt = offlineFeature.getOrDefault(FeatureType.GEO_CLICK_7_CNT, FeatureConstants.ONE);
            Double aroundPv7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_PV_7_CNT, FeatureConstants.ONE);
            Double aroundAvg7Price = offlineFeature.getOrDefault(FeatureType.AROUND_AVG_7_PRICE, DEFAULT_PRICE);
            Double aroundOrder7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_ORDER_7_CNT, FeatureConstants.ONE);
            Double aroundClick7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_CLICK_7_CNT, FeatureConstants.ONE);
            Double city7Pvs = offlineFeature.getOrDefault(FeatureType.CITY_7_PVS, FeatureConstants.ONE);
            Double cityPv7Price = offlineFeature.getOrDefault(FeatureType.CITY_PV_7_PRICE, DEFAULT_PRICE);
            Double city7Orders = offlineFeature.getOrDefault(FeatureType.CITY_7_ORDERS, FeatureConstants.ONE);
            Double city7Clicks = offlineFeature.getOrDefault(FeatureType.CITY_7_CLICKS, FeatureConstants.ONE);
            Double hotel7Ctr = offlineFeature.getOrDefault(FeatureType.CTR_7_DAY_ZHIXIN, DEFAULT_CTR);
            Double hotel7Orders = offlineFeature.getOrDefault(FeatureType.ORD_CNT_3_WEEK, FeatureConstants.ZERO) / 3;

            // 酒店报价
            double hotelShowPrice = hotelFeature.getEstimatedRenderPrice();
            if(hotelShowPrice <= 0.0 ){
                hotelShowPrice = hotelFeature.getMinPriceWithIdentity();
            }
            if ( hotelShowPrice >= 100000 ){
                hotelShowPrice = cityPv7Price;
            }

            // 355 酒店对应的geo_id范围内的酒店数占当前城市酒店数量的比例
            featureMap.put(FeatureIndexEnum.D35_355.getIndex(), divisionValue(cityHotelCnt, geoHotelCnt));

            // 356 酒店对应的geo_id范围内的过去7天订单量占当前城市订单的比例
            featureMap.put(FeatureIndexEnum.D35_356.getIndex(), divisionValue(city7Orders,geoOrder7Cnt));

            // 357
            featureMap.put(FeatureIndexEnum.D35_357.getIndex(), divisionValue(city7Pvs,geoPv7cnt));

            // 358
            featureMap.put(FeatureIndexEnum.D35_358.getIndex(), divisionValue(city7Clicks,geoClick7Cnt));

            // 359
            featureMap.put(FeatureIndexEnum.D35_359.getIndex(), divisionValue(cityPv7Price,geoPvAvg7Price));

            // 360
            featureMap.put(FeatureIndexEnum.D35_360.getIndex(), divisionValue(cityHotelCnt,aroundHotelsCnt));

            // 361
            featureMap.put(FeatureIndexEnum.D35_361.getIndex(), divisionValue(city7Orders,aroundOrder7Cnt));

            // 362
            featureMap.put(FeatureIndexEnum.D35_362.getIndex(), divisionValue(city7Pvs,aroundPv7Cnt));

            // 363
            featureMap.put(FeatureIndexEnum.D35_363.getIndex(), divisionValue(city7Clicks,aroundClick7Cnt));

            // 364
            featureMap.put(FeatureIndexEnum.D35_364.getIndex(), divisionValue(cityPv7Price,aroundAvg7Price));

            // 365
            featureMap.put(FeatureIndexEnum.D35_365.getIndex(), divisionValue(geoOrder7Cnt,hotel7Orders));

            // 366
            featureMap.put(FeatureIndexEnum.D35_366.getIndex(), divisionValue(geoPvAvg7Price,hotelShowPrice));

            // 367
            featureMap.put(FeatureIndexEnum.D35_367.getIndex(), divisionValue(divisionValue(geoPv7cnt,geoClick7Cnt),hotel7Ctr));

            // 368
            featureMap.put(FeatureIndexEnum.D35_368.getIndex(), divisionValue(aroundOrder7Cnt,hotel7Orders));

            // 369
            featureMap.put(FeatureIndexEnum.D35_369.getIndex(), divisionValue(aroundAvg7Price,hotelShowPrice));

            // 370
            featureMap.put(FeatureIndexEnum.D35_370.getIndex(), divisionValue(divisionValue(aroundPv7Cnt, aroundClick7Cnt), hotel7Ctr));

            // 371
            featureMap.put(FeatureIndexEnum.D35_371.getIndex(), divisionValue(cityPv7Price,hotelShowPrice));

            if (MapUtils.isNotEmpty(offlineFeature)) {
                // 372 酒店7天的uv_ctcvr
                featureMap.put(FeatureIndexEnum.D36_372.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_7D, ZERO));
                // 373 酒店21天的uv_ctcvr
                featureMap.put(FeatureIndexEnum.D36_373.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_21D, ZERO));
                // 374 酒店90天的uv_ctcvr
                featureMap.put(FeatureIndexEnum.D36_374.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_90D, ZERO));

                // 375 酒店7天的uv_ctr
                featureMap.put(FeatureIndexEnum.D36_375.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_7D, ZERO));
                // 376 酒店21天的uv_ctr
                featureMap.put(FeatureIndexEnum.D36_376.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_21D, ZERO));
                // 377 酒店90天的uv_ctr
                featureMap.put(FeatureIndexEnum.D36_377.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_90D, ZERO));

                // 378 - 383 分场景uv ctcvr
                fillSceneUvCtrCvr(requestFeature.getSortScene(), offlineFeature, featureMap);

                // 384 酒店7天内的下单用户数占该城市总下单用户总数的比
                putMapNotNull(featureMap, FeatureIndexEnum.D36_384.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_7D));

                // 385 酒店21天内的下单用户数占该城市总下单用户总数的比
                putMapNotNull(featureMap, FeatureIndexEnum.D36_385.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_21D));

                // 386 酒店90天内的下单用户数占该城市总下单用户总数的比
                putMapNotNull(featureMap, FeatureIndexEnum.D36_386.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_90D));

                // 387 酒店靠近地铁，则21天内近地铁的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_387.getIndex(), offlineFeature.get(FeatureType.SUBWAY_ORDER_RATE_21D));

                // 388 酒店靠近机场，则21天内近机场的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_388.getIndex(), offlineFeature.get(FeatureType.AIRPORT_ORDER_RATE_21D));

                // 389 酒店靠近火车站，则21天内近火车站的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_389.getIndex(), offlineFeature.get(FeatureType.RAILWAY_ORDER_RATE_21D));

                // 390 酒店靠近医院，则21天内近医院的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_390.getIndex(), offlineFeature.get(FeatureType.HOSPITAL_ORDER_RATE_21D));

                // 391 酒店靠近大学，则21天内近大学的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_391.getIndex(), offlineFeature.get(FeatureType.UNIVERSITY_ORDER_RATE_21D));

                // 392 酒店靠近景区，则21天内近景区的订单占城市所有订单占比，否则为0
                putMapNotNull(featureMap, FeatureIndexEnum.D36_392.getIndex(), offlineFeature.get(FeatureType.SPOTPLACES_ORDER_RATE_21D));
            }

            // 393 用户搜索城市是否用户常住地
            if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && userFeature.getUserProfile() != null
                    && StringUtils.isNotEmpty(userFeature.getUserProfile().getOftenCity())) {
                featureMap.put(FeatureIndexEnum.D36_393.getIndex(),
                        StringUtils.equals(requestFeature.getRootCityCode(), userFeature.getUserProfile().getOftenCity()) ? FeatureConstants.ONE : ZERO);
            }

            // 394 用户当前请求小时（0-23）one-hot处理
            featureMap.put(FeatureIndexEnum.D36_394.getIndex(), requestFeature.getRequestDateTime().getHour());

            // 395 入住和请求相差的天数（-1-8）+ 1 one-hot处理
            featureMap.put(FeatureIndexEnum.D36_395.getIndex(), requestFeature.getDiffDaysFeature() == null ? 0 : requestFeature.getDiffDaysFeature() );
        }

        Platform platform = requestFeature.getPlatform();

        // 平台 索引[404]
        featureMap.put(FeatureIndexEnum.D39_404.getIndex(), (platform != null && platform.equals(Platform.IOS)) ? ONE : TWO);

        // scene 索引 [405]
        switch (scene) {
            case NEARBY:
                featureMap.put(FeatureIndexEnum.D39_405.getIndex(), TWO);
                break;
            case POI_KEY:
                featureMap.put(FeatureIndexEnum.D39_405.getIndex(), THREE);
                break;
            case NOT_SAME_CITY:
                featureMap.put(FeatureIndexEnum.D39_405.getIndex(), FOUR);
                break;
            default: //same_city or others
                featureMap.put(FeatureIndexEnum.D39_405.getIndex(), ONE);
                break;
        }
        return featureMap;
    }

    /**
     * 计算不同场景下的距离特征
     */
    private void addDistanceFeat(GLatLng gLatLng, ModelRequestFeature requestFeature, Map<Integer, Object> featureMap) {

        if (gLatLng == null) {
            return;
        }
        featureMap.put(FeatureIndexEnum.DNN_INDEX_187.getIndex(), NEGATIVE_ONE);
        featureMap.put(FeatureIndexEnum.DNN_INDEX_188.getIndex(), NEGATIVE_ONE);

        SortScene scene = requestFeature.getSortScene();
        if (scene == SortScene.NOT_SAME_CITY) {
            double distance = calDistance(gLatLng, requestFeature.getCityUrlCenterPoint());
            if (distance != 0) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_187.getIndex(), distance);
            }
        } else if (scene.equals(SortScene.POI_KEY)) {
            double distance = calDistance(gLatLng, requestFeature.getPoiPoint());
            if (distance != 0) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_188.getIndex(), distance);
            }
        }
    }

    /**
     * 距离计算
     */
    private double calDistance(GLatLng gLatLng, String point) {

        if (null == gLatLng || StringUtils.isBlank(point)) {
            return 0;
        }

        double hotelLat = gLatLng.getLat();
        double hotelLng = gLatLng.getLng();

        String[] latLng = StringUtils.split(point, COMMA);
        if (null == latLng || latLng.length != 2) {
            return 0;
        }

        try {
            double lat = NumberUtils.parseDouble(latLng[0]);
            double lng = NumberUtils.parseDouble(latLng[1]);
            double distance = CoordDistance.distanceWithNormalized(hotelLat, hotelLng, lat, lng);
            return distance <= SAME_CITY_DISTANCE ? distance : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    private void putMapInternal(Map<Integer, Object> featureMap, Integer index, Double value) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }


    public double valueWeight(double value, double weight) {
        return Math.round(value * weight * 10) / 10.0;
    }
    
    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    private static Double divisionValue(Object divisor,Object dividend,int len){
        if (divisor == null || dividend == null || (double)divisor ==0.0 || (double)dividend ==0.0){
            return FeatureConstants.ZERO;
        } else {
            return Numbers.roundStringDouble((double)dividend / (double)divisor,len);
        }

    }

    /**
     * 根据对应场景 填充ctr 特征
     */
    private static void fillSceneCtr(SortScene sortScene,EnumMap<FeatureType, Double> offlineFeature,Map<Integer, Object> featReturn){

        switch (sortScene){
            case NEARBY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;
            case POI_KEY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;

            case NOT_SAME_CITY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;

            default: // SAME_CITY and others
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_60_DAY,NEGATIVE_ONE));

                break;

        }

    }

    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     */
    private static Double divisionValue(Double divisor, Double dividend){
        if (divisor == 0.0 || dividend == 0.0){
            return FeatureConstants.ZERO;
        } else {
            Double v = Numbers.roundStringDouble(dividend / divisor, 6);

            if (v > 4.0D) {
                return 4.0D;
            }
            return v;
        }
    }

    /**
     * 根据对应场景 填充uv ctr ctcvr 特征
     */
    private static void fillSceneUvCtrCvr(SortScene sortScene, EnumMap<FeatureType, Double> offlineFeature, Map<Integer, Object> featReturn){
        Double sceneUvCtcvr7day;
        Double sceneUvCtcvr21day;
        Double sceneUvCtcvr90day;
        Double sceneUvCtr7day;
        Double sceneUvCtr21day;
        Double sceneUvCtr90day;

        switch (sortScene) {
            case NEARBY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_90D, ZERO);
                break;
            case POI_KEY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_90D, ZERO);
                break;

            case NOT_SAME_CITY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_90D, ZERO);
                break;

            default: // SAME_CITY and others
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_90D, ZERO);
                break;
        }
        // 378 酒店分场景7天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_378.getIndex(), sceneUvCtcvr7day);
        // 379 酒店分场景21天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_379.getIndex(), sceneUvCtcvr21day);
        // 380 酒店分场景90天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_380.getIndex(), sceneUvCtcvr90day);
        // 381 酒店分场景7天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_381.getIndex(), sceneUvCtr7day);
        // 382 酒店分场景21天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_382.getIndex(), sceneUvCtr21day);
        // 383 酒店分场景90天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_383.getIndex(), sceneUvCtr90day);
    }

}
