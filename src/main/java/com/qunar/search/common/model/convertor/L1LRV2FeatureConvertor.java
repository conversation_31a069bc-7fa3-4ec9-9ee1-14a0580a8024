package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

@Component
public class L1LRV2FeatureConvertor extends D6LRFeatureConvertor {
    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE -10 ;
    public static final Double ONE = 1.0D;
    public static final Double DEFAULT_ZERO = 0.0D;

    private static final double[] HOTEL_PRICE_BUCKET = {0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 700, 800, 1000, 1500, 2000, 2500, 3000, 5000, Double.MAX_VALUE}; // 20
    private static final double[] CLICK_COUNT_BUCKET = {-1, 0, 10, 20, 40,60, 80, 100, 200, 300, 400, 500, 600, 800, 1000, 1500, 2000, 2500, 3000, 5000, Double.MAX_VALUE}; // 20
    private static final double[] COLLECT_COUNT_BUCKET = {-1, 0, 1, 3, 5, 8, 10, 20, 40, 60, 100, Double.MAX_VALUE}; // 10
    private static final double[] SMALL_COUNT_BUCKET = {-1, 0, 1, 3, 5, 8, 10, 20, 40, 60, 100, Double.MAX_VALUE}; // 10
    private static final double[] RATE = {-1.0, 0.0, 0.001, 0.003, 0.005, 0.007, 0.01, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1}; // 15
    private static final double[] RATE2 = {-1.0, 0.0, 0.0001, 0.0005, 0.001, 0.003, 0.005, 0.007, 0.01, 0.016, 0.021, 0.03, 0.05, 0.07, 0.1, 0.2, 0.3, 0.5, 0.8, 1}; // 15


    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Object> featuresMap = super.convert(requestFeature, userFeature, hotelFeature);
        featuresMap.putAll(addNewFeature(userFeature, hotelFeature));
        return featuresMap;
    }

    // 增加D20特征
    private Map<Integer, Double> addNewFeature(ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Double> featMap = Maps.newHashMap();

        String seq = hotelFeature.getHotelSeq();
        String hotelCity = getCityFromSeq(seq);
        int hotelStars = hotelFeature.getStars();
        String hotelBrand = hotelFeature.getHotelBranch();
        String hotelTrading = hotelFeature.getTradingArea();
        String[] hotelTypes = hotelFeature.getHotelType();
        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();

        int baseIndex = 950;
        int bucketIndex = 0;

        //"酒店报价"[950-980]
        if (hotelPrice != 0 && hotelPrice < DEFAULT_MAX_SUB10) {
            bucketIndex = getBucketIndexQuick(HOTEL_PRICE_BUCKET, hotelPrice);
            featMap.put(baseIndex + bucketIndex, ONE);
        }
        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);

        // 981-985 酒店档次
        baseIndex += 30; // 980
        featMap.put(bucketIndex + hotelRealGrade, ONE);


        // [986-1006] 历史点击酒店次数
        baseIndex += 6; // 986
        bucketIndex = getBucketIndexQuick(CLICK_COUNT_BUCKET, userFeature.getUserHistoryClickFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 192 用户历史点击当前酒店档次占比
        baseIndex += 20; // 1006
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserHistoryClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 193 用户历史点击当前酒店星级占比
        baseIndex += 20; // 1026
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserHistoryClickFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史点击当前酒店品牌占比
        baseIndex += 20; // 1046
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserHistoryClickFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史点击当前酒店商圈占比
        baseIndex += 20; // 1066
        double hisClickTradRate = DEFAULT_ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisClickTradRate += userFeature.getUserHistoryClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, DEFAULT_ZERO);
        }
        bucketIndex = getBucketIndexQuick(RATE2, hisClickTradRate);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史点击当前酒店类型占比
        baseIndex += 20; // 1086
        double historyClickTypeRate = DEFAULT_ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserHistoryClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyClickTypeRate += typeRateMap.getOrDefault(type, DEFAULT_ZERO);
            }
        }
        bucketIndex = getBucketIndexQuick(RATE2, hisClickTradRate);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户点击当前酒店占比
        baseIndex += 20; // 1106
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().getOrDefault(seq, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 1月（31天）内收藏次数
        baseIndex += 20; // 1126
        bucketIndex = getBucketIndexQuick(COLLECT_COUNT_BUCKET, userFeature.getUser31DayCollectFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 1月（31天）用户收藏当前酒店档次占比
        baseIndex += 10; //1136
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser31DayCollectFeature().getGradeRateMap().getOrDefault(hotelRealGrade, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 1月（31天）用户收藏当前酒店星级占比
        baseIndex += 20; //1156
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser31DayCollectFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 1月（31天）用户收藏当前酒店品牌占比
        baseIndex += 20; // 1176
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser31DayCollectFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击酒店次数
        baseIndex += 20; //1196
        bucketIndex = getBucketIndexQuick(COLLECT_COUNT_BUCKET, userFeature.getUserRealtimeClickFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店档次占比
        baseIndex += 10; //1206
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserRealtimeClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店星级占比
        baseIndex += 20;//1226
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserRealtimeClickFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店品牌占比
        baseIndex += 20;//1246
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserRealtimeClickFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店商圈占比
        baseIndex += 20;//1266
        double realClickTradRate = DEFAULT_ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            realClickTradRate += userFeature.getUserRealtimeClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, DEFAULT_ZERO);
        }
        bucketIndex = getBucketIndexQuick(RATE2, realClickTradRate);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店类型占比
        baseIndex += 20;//1286
        double realTimeClickTypeRate = DEFAULT_ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserRealtimeClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                realTimeClickTypeRate += typeRateMap.getOrDefault(type, DEFAULT_ZERO);
            }
        }
        bucketIndex = getBucketIndexQuick(RATE2, realClickTradRate);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户实时点击当前酒店占比
        baseIndex += 20;//1306
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().getOrDefault(seq, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 实时query筛选到该酒店的次数
        baseIndex += 20;//1326
        bucketIndex = getBucketIndexQuick(SMALL_COUNT_BUCKET, userFeature.getUserRealtimeQueryFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量
        baseIndex += 10;//1336
        bucketIndex = getBucketIndexQuick(SMALL_COUNT_BUCKET, userFeature.getUser180DayOrderFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量中档次最多的订单档次与当前酒店档次差
        baseIndex += 20;//1356
        featMap.put(baseIndex + hotelRealGrade - userFeature.getUser180DayOrderFeature().getBehaveMaxGrade(), ONE);

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        baseIndex += 10;//1366
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser180DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量中，该酒店占比
        baseIndex += 20;//1386
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量品牌中，该酒店品牌占比
        baseIndex += 20;//1406
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser180DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量类型中，该酒店类型占比
        baseIndex += 20;//1426
        double historyOrderTypeRate180Day = DEFAULT_ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser180DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate180Day += typeRateMap.getOrDefault(type, DEFAULT_ZERO);
            }
        }
        bucketIndex = getBucketIndexQuick(RATE2, historyOrderTypeRate180Day);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量星级中，该酒店星级占比
        baseIndex += 20;//1446
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser180DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史180天订单量商圈中，该酒店商圈占比
        baseIndex += 20;//1466
        double hisOrder180DayTradRate = DEFAULT_ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder180DayTradRate += userFeature.getUser180DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, DEFAULT_ZERO);
        }
        bucketIndex = getBucketIndexQuick(RATE2, hisOrder180DayTradRate);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史365天订单量
        baseIndex += 20;//1486
        bucketIndex = getBucketIndexQuick(SMALL_COUNT_BUCKET, userFeature.getUser365DayOrderFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 228 用户历史365天订单量中档次最多的订单档次与当前酒店档次差
        baseIndex += 20;//1506
        featMap.put(baseIndex + hotelRealGrade - userFeature.getUser365DayOrderFeature().getBehaveMaxGrade(), ONE);

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        baseIndex += 10;//1516
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser365DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 230 用户历史365天订单量中，该酒店占比
        baseIndex += 20;//1536
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 231 用户历史365天订单量品牌中，该酒店品牌占比
        baseIndex += 20;//1556
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser365DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 232 用户历史365天订单量类型中，该酒店类型占比
        baseIndex += 20;//1576
        double historyOrderTypeRate365Day = DEFAULT_ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser365DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate365Day += typeRateMap.getOrDefault(type, DEFAULT_ZERO);
            }
        }
        bucketIndex = getBucketIndexQuick(RATE2, historyOrderTypeRate365Day);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 233 用户历史365天订单量类星级中，该酒店星级占比
        baseIndex += 20;//1596
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser365DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 236 用户历史800天订单量
        baseIndex += 20;//1616
        bucketIndex = getBucketIndexQuick(SMALL_COUNT_BUCKET, userFeature.getUser800DayOrderFeature().getBehaveCount());
        featMap.put(baseIndex + bucketIndex, ONE);

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        baseIndex += 20;//1636
        featMap.put(baseIndex + hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade(), ONE);

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        baseIndex += 10;//1646
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser800DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 239 用户历史800天订单量中，该酒店占比
        baseIndex += 20;//1666
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 240 用户历史800天订单量品牌中，该酒店品牌占比
        baseIndex += 20;//1686
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser800DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史800天订单量类型中，该酒店类型占比
        baseIndex += 20;//1706
        double historyOrderTypeRate800Day = DEFAULT_ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser800DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate800Day += typeRateMap.getOrDefault(type, DEFAULT_ZERO);
            }
        }
        bucketIndex = getBucketIndexQuick(RATE2, historyOrderTypeRate800Day);
        featMap.put(baseIndex + bucketIndex, ONE);

        // 用户历史800天订单量类星级中，该酒店星级占比
        baseIndex += 20;//1726
        bucketIndex = getBucketIndexQuick(RATE2, userFeature.getUser800DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, DEFAULT_ZERO));
        featMap.put(baseIndex + bucketIndex, ONE);

        // D35 feature

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isNotEmpty(offlineFeature)) {
            //酒店3月内点击率
            baseIndex += 20; // 1746
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.CTR_3_MONTH));
            featMap.put(baseIndex + bucketIndex, ONE);

            //酒店3月内点击率（置信）
            baseIndex += 20; // 1766
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN));
            featMap.put(baseIndex + bucketIndex, ONE);

            //酒店7天点击率（置信）
            baseIndex += 20; //1786
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.CTR_7_DAY_ZHIXIN));
            featMap.put(baseIndex + bucketIndex, ONE);
        }

        // 336 -> 可售房型订单占比
        baseIndex += 20;//1806
        bucketIndex = getBucketIndexQuick(RATE2, hotelFeature.getOrdersRate());
        featMap.put(baseIndex + bucketIndex, ONE);

        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 3个星期的搜索量
            baseIndex += 20; //1826
            bucketIndex = getBucketIndexQuick(CLICK_COUNT_BUCKET, offlineFeature.get(FeatureType.SEARCH_CNT_3_WEEK));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 3个月的搜索量
            baseIndex += 20; //1846
            bucketIndex = getBucketIndexQuick(CLICK_COUNT_BUCKET, offlineFeature.get(FeatureType.SEARCH_CNT_3_MONTH));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 6个月的搜索量
            baseIndex += 20; //1866
            bucketIndex = getBucketIndexQuick(CLICK_COUNT_BUCKET, offlineFeature.get(FeatureType.SEARCH_CNT_6_MONTH));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 交通负向标签
            baseIndex += 20; //1886
            featMap.put(baseIndex + offlineFeature.getOrDefault(FeatureType.TRAFFIC_NEG_LABEL, DEFAULT_ZERO).intValue(), ONE);

            // 交通正向标签
            baseIndex += 2; //1888
            featMap.put(baseIndex + offlineFeature.getOrDefault(FeatureType.TRAFFIC_POS_LABEL, DEFAULT_ZERO).intValue(), ONE);

            // 服务负向标签
            baseIndex += 2; //1890
            featMap.put(baseIndex + offlineFeature.getOrDefault(FeatureType.SERVICE_NEG_LABEL, DEFAULT_ZERO).intValue(), ONE);

            // 服务正向标签
            baseIndex += 2; //1892
            featMap.put(baseIndex + offlineFeature.getOrDefault(FeatureType.SERVICE_POS_LABEL, DEFAULT_ZERO).intValue(), ONE);

            // 3个月的ctr
            baseIndex += 2; //1894
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.CTR_3_MONTH));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 3个月置信ctr
            baseIndex += 20;//1914
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店7天的uv_ctcvr
            baseIndex += 20;//1954
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTCVR_7D));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店21天的uv_ctcvr
            baseIndex += 20;//1974
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTCVR_21D));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店90天的uv_ctcvr
            baseIndex += 20;//1994
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTCVR_90D));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店7天的uv_ctr
            baseIndex += 20; //2014
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTR_7D));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店21天的uv_ctr
            baseIndex += 20;//2034
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTR_21D));
            featMap.put(baseIndex + bucketIndex, ONE);

            // 酒店90天的uv_ctr
            baseIndex += 20;//2054
            bucketIndex = getBucketIndexQuick(RATE2, offlineFeature.get(FeatureType.UV_CTR_90D));
            featMap.put(baseIndex + bucketIndex, ONE);
        }


        return featMap;
    }

    public int getBucketIndexQuick(double[] bucket, double value) {
        if (bucket.length <= 1 || value == Double.MAX_VALUE || value == Double.MIN_VALUE) {
            return -1;
        }
        if (value < bucket[0]) {
            return 0;
        }
        int low = 0, high = bucket.length - 1;
        while (low <= high) {
            int mid = low + ((high - low) >> 1);
            if (bucket[mid] > value) {
                high= mid - 1;
            } else if (bucket[mid] < value) {
                low = mid + 1;
            } else {
                return mid;
            }
        }
        return low - 1;
    }

    public int getBucketIndexQuick(double[] bucket, Double value) {
        if (null == value) {
            return 0;
        }

        return getBucketIndexQuick(bucket, value.doubleValue());
    }
}
