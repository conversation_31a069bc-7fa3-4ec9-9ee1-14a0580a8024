package com.qunar.search.common.model.feature;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.enums.Platform;
import com.qunar.search.common.enums.SortScene;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.qunar.search.common.feature.GroupFeature;
import com.qunar.search.common.model.memory.DataVersion;
import lombok.Data;
import org.joda.time.DateTime;

/**
 * <AUTHOR>
 * @create 2020-05-08 下午7:44
 * @DESCRIPTION
 *  1、模型计算使用的请求数据
 *  2、特征日志打印对象
 **/
@Data
public class ModelRequestFeature implements Serializable {
    /**
     * adr or ios， 平台 -- 多端统一映射平台，其他平台会
     */
    private Platform platform;

    /**
     * 请求发生当天日期-- sort新增
     */
    private int requestDate;

    /**
     * 请求发生时间（小时、分钟）
     */
    @JsonIgnore
    private LocalDateTime requestDateTime;

    /**
     * 中转打印requestDateTime使用
     */
    public long getRequestTime() {
        if (requestDateTime != null) {
            return requestDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        } else {
            return 0;
        }
    }

    public void setRequestTime(long milliSecond) {
        Instant instant = Instant.ofEpochMilli(milliSecond);
        requestDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * 城市市中心坐标
     */
    private String cityUrlCenterPoint;

    /**
     * 搜索场景
     */
    private  SortScene sortScene;

    /**
     * 是否是节假日加权日期
     */
    private boolean isPriceWeightDate;

    /**
     * poi坐标点
     */
    private String poiPoint;

    /**
     * poiTypeID
     */
    private int poiTypeId;

    /**
     * poiID
     */
    private String poiId;

    /**
     * uid
     */
    private String uid;

    /**
     * realUid
     */
    private String realUid;

    /**
     * 用户设备gid
     */
    private String gid;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 城市
     */
    private String cityUrl;

    /**
     * 坐标- 用户坐标
     * 问题：20210719之前值有问题，填充成poi坐标了，
     * 影响：1、由于convertor使用HotelItem中的值，并不影响特征与模型
     * 2、20210719发现修复，后续使用注意
     */
    private String coord;

    /**
     * 入住时间
     */
    @JsonIgnore
    private DateTime fromDate;

    /**
     * fromDate
     */
    public long getFromDateTime() {
        if (fromDate != null) {
            return fromDate.getMillis();
        } else {
            return 0;
        }
    }

    public void setFromDateTime(long milliSecond) {
        fromDate = new DateTime(milliSecond);
    }

    /**
     * 离店时间
     */
    private String toDate;

    /**
     * check_in是否是周末（周六和周日）
     */
    private boolean isWeekend;

    /**
     * check_in和check_out是否包含周末（周五、周六）
     */
    private boolean isWeekendOfCheckInOut;

    /**
     * 入住天数
     */
    private int checkInNum = 0;

    /**
     * 用户预定日期内的节假日（包含加放假前1天）和周末（这里的周末是指周五和周六）占比
     */
    private double checkWeekendHolidayRate = 0;

    /**
     * 适配原始 String 型 platform 日志
     * @param platform
     */
    public void setPlatform(String platform) {
        this.platform = Platform.match(platform).orElse(Platform.OTHER);
    }

    /**
     * 本次请求使用的内存数据版本信息
     */
    private DataVersion dataVersion;

    /**
     * 395特征位 入住和请求相差的天数（-1-8）+ 1 one-hot处理
     */
    private Integer diffDaysFeature;

    /**
     * group 维度的特征
     */
    private GroupFeature groupFeature;

    /**
     * 请求城市的地级市
     */
    private String rootCityCode;

    /**
     * 地级市#请求城市
     */
    private String rootCityAndRequestCity;

    /**
     * query和筛选是否主题类型和酒店类型的酒店
     */
    private boolean isQueryFilterThemeType;

    /**
     * 请求query的召回意图
     */
    private List<String> queryIntention = Collections.emptyList();

    /**
     * 请求query
     */
    private String searchQuery;

    /**
     * 用户筛选类型
     */
    private Set<String> filterHotelType = Collections.emptySet();

    /**
     * 用户筛选主题
     */
    private Set<String> filterHotelTheme = Collections.emptySet();

    /**
     *活动id相关
     */
    List<String> activityIdsList = Collections.emptyList();

    /**
     * 手机型号
     */
    private String phoneModel;

}
