package com.qunar.search.common.model.convertor;

import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

@Slf4j
public abstract class AbstractSortFeatureConvertor implements SortFeatureConvertor {

    @Override
    public Map<Integer, Object> convertObject(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        return convert(requestFeature, userFeature, hotelFeature);
    }

    protected abstract Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature);
}
