package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.feature.GroupFeature;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.GeneralUtil;
import com.qunar.search.common.util.RankGuassUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * D43 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD45 extends DnnFeatureConvertorD43 {


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 476 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        try {

            resultMap =  GeneralUtil.convertorFeature484To489(resultMap,userFeature,hotelFeature);

            GroupFeature groupFeature = requestFeature.getGroupFeature();
            resultMap.put(FeatureIndexEnum.D45_491.getIndex(),
                    RankGuassUtil.calRankGuassByIndex(hotelFeature.getOrderVolumeNumber(),groupFeature.getTotalNums())
            );

        } catch (Exception e) {
            log.error("DnnFeatureConvertorD45 转换特征出错" , e);
        }

        return resultMap;
    }

}
