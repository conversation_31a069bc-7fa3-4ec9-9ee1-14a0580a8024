package com.qunar.search.common.model.convertor;


import java.util.Map;
import java.util.function.Function;

public class ConvertorObject {

    /**
     * 批量移除map中的值
     */
    static void removeIndex(Map<Integer, Object> featureMap, int start, int len) {
        for (int i = start; i < start + len; i++) {
            featureMap.remove(i);
        }
    }

    /**
     * 将 value 放到 map 中，key为baseIndex + offset
     *
     * @param featureMap 要put的map
     * @param baseIndex  基础编号
     * @param offset     编号偏移量
     * @param value      值
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int baseIndex, Double offset) {
        if (null == offset) {
            return;
        }

        putMap(featureMap, value, baseIndex, offset.intValue());
    }

    /**
     * 将 value 放到 map 中，key为baseIndex + offset
     *
     * @param featureMap 要put的map
     * @param baseIndex  基础编号
     * @param offset     编号偏移量
     * @param value      值
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int baseIndex, int offset) {
        if (offset >= 0) {
            featureMap.put(baseIndex + offset, value);
        }
    }

    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param value      值
     * @param index      基础编号
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int index) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }

    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param value      值
     * @param index      基础编号
     */
    static void putMapNotNull(Map<Integer, Object> featureMap, Integer index, Object value) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }


    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param index      基础编号
     * @param value      值
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int index,
                       Function<Double, Boolean> putCondition) {
        if (putCondition.apply(value)) {
            featureMap.put(index, value);
        }
    }

    /**
     * 将 value 放到 map 中，key为baseIndex + offset
     *
     * @param featureMap 要put的map
     * @param baseIndex  基础编号
     * @param offset     编号偏移量
     * @param value      值
     */
    static void putMap(Map<Integer, Double> featureMap, Double value, int baseIndex, int offset,
                       Function<Double, Boolean> putCondition) {
        if (offset >= 0 && putCondition.apply(value)) {
            featureMap.put(baseIndex + offset, value);
        }
    }

    /**
     * 将 value 放到 map 中，key为 start-end 所有值
     */
    static void putMapInterval(Map<Integer, Object> featureMap, Double value, int start, int end) {
        for (int i = start; i <= end; i++) {
            putMap(featureMap, value, i, 0);
        }
    }

    /**
     * 酒店-用户数据交叉特征， 计算比例
     *
     * @param featureMap put 特征map
     * @param index      map key
     * @param userValue  用户value
     * @param userCheck  用户value 校验逻辑
     * @param hotelValue 酒店value
     * @param hotelCheck 酒店value 校验逻辑
     */
    static void putCrossRate(Map<Integer, Object> featureMap, int index,
                             double userValue, Function<Double, Boolean> userCheck,
                             Double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, (userValue - hotelValue) / userValue);
        }
    }

    static void putCrossRate(Map<Integer, Object> featureMap, int index,
                             double userValue, Function<Double, Boolean> userCheck,
                             double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, (userValue - hotelValue) / userValue);
        }
    }

    /**
     * 酒店-用户数据交叉特征， 计算比例
     *
     * @param featureMap put 特征map
     * @param index      map key
     * @param userValue  用户value
     * @param userCheck  用户value 校验逻辑
     * @param hotelValue 酒店value
     * @param hotelCheck 酒店value 校验逻辑
     */
    static void putCrossDiff(Map<Integer, Object> featureMap, int index,
                             double userValue, Function<Double, Boolean> userCheck,
                             Double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, userValue - hotelValue);
        }
    }
}
