package com.qunar.search.common.model.memory;

import com.qunar.search.common.bean.HotelInfo;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelVariableFeatureType;

import java.util.EnumMap;
import java.util.List;

/**
 * 内存数据管理器
 * 1、线上实现方案，工程 http://gitlab.corp.qunar.com/mobile_hotel_rank/mobile_hotel_search
 * 2、离线实现方案，工程 http://gitlab.corp.qunar.com/mobile_hotel_rank/tfrecord_hadoop
 */
public interface MemoryFunction {

    /**
     * 酒店基础信息,内存数据获取函数
     *
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelInfoHolder中的酒店基础信息
     */
    HotelInfo getHotelBaseInfo(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店特征map,内存数据获取函数
     *
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店离线特征map
     */
    EnumMap<FeatureType, Double> getOfflineFeature(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店特征原始数据，线上没有，赋值为null, 离线还原时使用
     *
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店离线特征map
     */
    String getOfflineFeatureLibSvm(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店特征map,内存数据获取函数
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店离线特征map
     */
    EnumMap<HotelVariableFeatureType, Object> getOfflineVariableFeature(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店可变长特征原始数据，线上没有，赋值为null, 离线还原时使用
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店离线特征map
     */
    String getOfflineVariableFeatureLibSvm(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店embedding v1 版本数据
     *
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店embedding数据，2019年老版数据
     */
    List<Double> getHotelEmbedding(String hotelSeq, DataVersion dataVersion);

    /**
     * 酒店embedding v1 版本数据
     *
     * @param hotelSeq 酒店seq
     * @param dataVersion  数据版本
     * @return 缓存在HotelStat中的酒店embedding数据，2021年新版数据
     */
    List<Double> getHotelEmbeddingV2(String hotelSeq, DataVersion dataVersion);
}
