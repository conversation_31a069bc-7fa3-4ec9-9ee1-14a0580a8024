package com.qunar.search.common.model.feature.realtimeuserbehavior;

import lombok.Data;

import java.util.HashSet;

/**
 * 用户在酒店 详情页的行为数据
 */
@Data
public class UserHotelDetailStayTime {

    private String hotelSeq;

    /**
     * 进入详情页次数
     */
    private int clickDetailCount;

    /**
     * 详情页总停留时长
     */
    private int totalRemainTime;

    /**
     * 详情页总停留时长占比
     */
    private double stayTimeRate;

    /**
     * 点击房型次数
     */
    private int clickHouseType;

    /**
     * 点击房型的平均价格
     */
    private double houseTypeprice;

    /**
     * 点击房型的价格最大值
     */
    private double houseTypePriceMax;

    /**
     * 点击房型的价格最小值
     */
    private double houseTypePriceMin;

    /**
     * 点击物理房型list (去重的)
     */
    private HashSet<String> rtIdList;

    /**
     * 点击主图次数
     */
    private int clickMasterMapCount;

    /**
     * 点击分享次数
     */
    private int clickShareCount;

    /**
     * 点击收藏次数
     */
    private int clickCollectCount;

    /**
     * 点击距离次数
     */
    private int clickDistanceCount;

    /**
     * 点击评论次数
     */
    private int clickCommentCount;

    /**
     * 点击总次数占比
     */
    private double allClickRate;

}
