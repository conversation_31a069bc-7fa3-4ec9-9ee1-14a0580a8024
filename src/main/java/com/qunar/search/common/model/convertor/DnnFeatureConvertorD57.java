package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.*;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.feature.XhsDyShowHotelFeature;
import com.qunar.search.common.math.data.UserBehaveSequence;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossRate;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;


/**
 * D57 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD57 extends DnnFeatureConvertorD56 {

    public static final List<Integer> priceBucketList = Arrays.asList(60, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 215, 220, 225, 230, 235, 240, 245, 250, 255, 260, 265, 270, 280, 285, 290, 300, 305, 310, 320, 325, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450, 470, 500, 520, 540, 560, 580, 600, 620, 640, 660, 680, 700, 720, 740, 760, 780, 800, 850, 900, 950, 1000, 1050, 1100, 1150, 1200, 1250, 1300, 1350, 1400, 1500, 1600, 1700, 1800, 1900, 2000);

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        if (StringUtils.isNotEmpty(hotelFeature.getHotelSeq())) {
            resultMap.put(FeatureIndexEnum.D57_680.getIndex(), getCityFromSeq(hotelFeature.getHotelSeq()));
        }

        // 酒店报价分桶
        double hotelShowPrice = hotelFeature.getCachedPriceAfterMerchant() <= 0.0 ? hotelFeature.getMinPriceWithIdentity() : hotelFeature.getCachedPriceAfterMerchant();
        resultMap.put(FeatureIndexEnum.D57_681.getIndex(), getBucketIdx(hotelShowPrice));

        // 48小时内点击价格序列过滤小于0的
        UserBehaveSequence realTimeListClickSequence = userFeature.getRealTimeListClickSequence();
        if (null != realTimeListClickSequence) {
            // 价格分桶序列
            List<Float> priceList = realTimeListClickSequence.getPriceList();
            if (CollectionUtils.isNotEmpty(priceList)) {
                List<Float> collectLimit = priceList.stream().filter(s -> s>0).collect(Collectors.toList());
                int limitSize = Math.min(collectLimit.size(), CLICK_LIST_LIMIT_SIZE);
                List<Float> limitList = collectLimit.subList(0, limitSize);
                List<Integer> collect = limitList.stream().map(this::getBucketIdx).collect(Collectors.toList());
                resultMap.put(FeatureIndexEnum.D57_682.getIndex(), collect);
            }
        }

        boolean weekendOfCheckInOut = requestFeature.isWeekendOfCheckInOut();
        Map<UserWeekDayOrderFeature, Object> userWeekDayOrderFeatureObjectMap = userFeature.getUserWeekDayOrderFeatureObjectMap();
        EnumMap<HotelVariableFeatureType, Object> offlineVariableFeature = hotelFeature.getOfflineVariableFeature();

        if (weekendOfCheckInOut) {
            // 用户相关特征
            Double userWeekOrderNum30D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_NUM_30D);
            putMap(resultMap, userWeekOrderNum30D, FeatureIndexEnum.D57_683.getIndex(), v -> (v != null && v > 0));

            Double userWeekOrderNum60D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_NUM_60D);
            putMap(resultMap, userWeekOrderNum60D, FeatureIndexEnum.D57_684.getIndex(), v -> (v != null && v > 0));

            Double userWeekOrderNum90D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_NUM_90D);
            putMap(resultMap, userWeekOrderNum90D, FeatureIndexEnum.D57_685.getIndex(), v -> (v != null && v > 0));

            Double userWeekOrderPrice30D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_PRICE_30D);
            putMap(resultMap, userWeekOrderPrice30D, FeatureIndexEnum.D57_686.getIndex(), v -> (v != null && v > 0));

            Double userWeekOrderPrice60D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_PRICE_60D);
            putMap(resultMap, userWeekOrderPrice60D, FeatureIndexEnum.D57_687.getIndex(), v -> (v != null && v > 0));

            Double userWeekOrderPrice90D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_PRICE_90D);
            putMap(resultMap, userWeekOrderPrice90D, FeatureIndexEnum.D57_688.getIndex(), v -> (v != null && v > 0));

            Object weekOrderObject = userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_ORDER_NUM_MAP);
            if (null != weekOrderObject) {
                Map<String, Double> userWeekOrderMap = (Map<String, Double>)weekOrderObject;
                Double val = userWeekOrderMap.get(hotelFeature.getHotelSeq());
                putMap(resultMap, val, FeatureIndexEnum.D57_689.getIndex(), v -> (v != null && v > 0));
            }

            Object gradeObj = userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.WEEK_GRADE_MAP);
            if (null != weekOrderObject) {
                Map<Integer, Double> userWeekOrderGradeMap = (Map<Integer, Double>) gradeObj;
                int grade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
                Double val = userWeekOrderGradeMap.get(grade);
                putMap(resultMap, val, FeatureIndexEnum.D57_690.getIndex(), v -> (v != null && v > 0));
            }

            if (MapUtils.isNotEmpty(offlineVariableFeature)) {

                Double hotelWeekOrderNum30D = (Double)offlineVariableFeature.get(HotelVariableFeatureType.WEEK_ORDER_NUM_30D);
                putMap(resultMap, hotelWeekOrderNum30D, FeatureIndexEnum.D57_691.getIndex(), v -> (v != null && v > 0));

                Double hotelWeekOrderPrice30D = (Double)offlineVariableFeature.get(HotelVariableFeatureType.WEEK_ORDER_PRICE_30D);
                putMap(resultMap, hotelWeekOrderPrice30D, FeatureIndexEnum.D57_692.getIndex(), v -> (v != null && v > 0));

                Double hotelWeekUvCtr = (Double)offlineVariableFeature.get(HotelVariableFeatureType.WEEK_UV_CTR_30D);
                putMap(resultMap, hotelWeekUvCtr, FeatureIndexEnum.D57_693.getIndex(), v -> (v != null && v > 0));

                Double hotelWeekUvCtcvr = (Double)offlineVariableFeature.get(HotelVariableFeatureType.WEEK_UV_CTCVR_30D);
                putMap(resultMap, hotelWeekUvCtcvr, FeatureIndexEnum.D57_694.getIndex(), v -> (v != null && v > 0));

                Double hotelWeekOrderRate = (Double)offlineVariableFeature.get(HotelVariableFeatureType.WEEK_ORDER_RATE_CITY_30D);
                putMap(resultMap, hotelWeekOrderRate, FeatureIndexEnum.D57_695.getIndex(), v -> (v != null && v > 0));
            }
        } else {
            // 用户相关特征
            Double userDayOrderNum30D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_NUM_30D);
            putMap(resultMap, userDayOrderNum30D, FeatureIndexEnum.D57_696.getIndex(), v -> (v != null && v > 0));

            Double userDayOrderNum60D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_NUM_60D);
            putMap(resultMap, userDayOrderNum60D, FeatureIndexEnum.D57_697.getIndex(), v -> (v != null && v > 0));

            Double userDayOrderNum90D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_NUM_90D);
            putMap(resultMap, userDayOrderNum90D, FeatureIndexEnum.D57_698.getIndex(), v -> (v != null && v > 0));

            Double userDayOrderPrice30D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_PRICE_30D);
            putMap(resultMap, userDayOrderPrice30D, FeatureIndexEnum.D57_699.getIndex(), v -> (v != null && v > 0));

            Double userDayOrderPrice60D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_PRICE_60D);
            putMap(resultMap, userDayOrderPrice60D, FeatureIndexEnum.D57_700.getIndex(), v -> (v != null && v > 0));

            Double userDayOrderPrice90D = (Double)userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_PRICE_90D);
            putMap(resultMap, userDayOrderPrice90D, FeatureIndexEnum.D57_701.getIndex(), v -> (v != null && v > 0));

            Object ouserDayOrderMapObj = userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_ORDER_NUM_MAP);
            if (null != ouserDayOrderMapObj) {
                Map<String, Double> userDayOrderMap = (Map<String, Double>)ouserDayOrderMapObj;
                Double val = userDayOrderMap.get(hotelFeature.getHotelSeq());
                putMap(resultMap, val, FeatureIndexEnum.D57_702.getIndex(), v -> (v != null && v > 0));
            }

            Object userDayOrderGradeMapObj = userWeekDayOrderFeatureObjectMap.get(UserWeekDayOrderFeature.DAY_GRADE_MAP);
            if (null != userDayOrderGradeMapObj) {
                Map<Integer, Double> userDayOrderGradeMap = (Map<Integer, Double>) userDayOrderGradeMapObj;
                int grade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
                Double val = userDayOrderGradeMap.get(grade);
                putMap(resultMap, val, FeatureIndexEnum.D57_703.getIndex(), v -> (v != null && v > 0));
            }


            if (MapUtils.isNotEmpty(offlineVariableFeature)) {

                Double hotelDayOrderNum30D = (Double)offlineVariableFeature.get(HotelVariableFeatureType.DAY_ORDER_NUM_30D);
                putMap(resultMap, hotelDayOrderNum30D, FeatureIndexEnum.D57_704.getIndex(), v -> (v != null && v > 0));

                Double hotelDayOrderPrice30D = (Double)offlineVariableFeature.get(HotelVariableFeatureType.DAY_ORDER_PRICE_30D);
                putMap(resultMap, hotelDayOrderPrice30D, FeatureIndexEnum.D57_705.getIndex(), v -> (v != null && v > 0));

                Double hotelDayUvCtr = (Double)offlineVariableFeature.get(HotelVariableFeatureType.DAY_UV_CTR_30D);
                putMap(resultMap, hotelDayUvCtr, FeatureIndexEnum.D57_706.getIndex(), v -> (v != null && v > 0));

                Double hotelDayUvCtcvr = (Double)offlineVariableFeature.get(HotelVariableFeatureType.DAY_UV_CTCVR_30D);
                putMap(resultMap, hotelDayUvCtcvr, FeatureIndexEnum.D57_707.getIndex(), v -> (v != null && v > 0));

                Double hotelDayOrderRate = (Double)offlineVariableFeature.get(HotelVariableFeatureType.DAY_ORDER_RATE_CITY_30D);
                putMap(resultMap, hotelDayOrderRate, FeatureIndexEnum.D57_708.getIndex(), v -> (v != null && v > 0));
            }

        }


        return resultMap;
    }

    private int getBucketIdx(double v) {
        for (int idx=0; idx < priceBucketList.size(); idx++) {
            if (v < priceBucketList.get(idx)) {
                return idx + 1;
            }
        }
        return 0;
    }
}