package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;


@Component
public class XgboostD20FeatureConvertor extends XgboostFeatureConvertorMoreFeat {
    //embedding 特征位，246-247
    private static final int EMBEDDING_FEATURE_INDEX_START = 246;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();

        Map<Integer, Object> featureReturn = super.convert(requestFeature, userFeature, hotelFeature);
        if (MapUtils.isEmpty(feature)) {
            return featureReturn;
        }

        addNewFeature(userFeature, hotelFeature, featureReturn);

        ConvertorBase.putEmbeddingFeature(userFeature, hotelFeature, featureReturn);


        return featureReturn;
    }

    private void addNewFeature(ModelUserFeature userFeature, ModelHotelFeature hotelFeature, Map<Integer, Object> featMap) {        

        String seq = hotelFeature.getHotelSeq();
        String hotelCity = getCityFromSeq(seq);
        int hotelStars = hotelFeature.getStars();
        String hotelBrand = hotelFeature.getHotelBranch();
        String hotelTrading = hotelFeature.getTradingArea();
        String[] hotelTypes = hotelFeature.getHotelType();
        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();

        //"酒店报价"[101]
        if (hotelPrice != 0 && hotelPrice < DEFAULT_MAX_SUB10) {
            featMap.put(101, hotelPrice);
        }

        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);
        // 189 酒店档次
        featMap.put(189, (double)hotelRealGrade);

        // 190 历史点击酒店次数
        featMap.put(190, (double)userFeature.getUserHistoryClickFeature().getBehaveCount());

        // 191 用户历史点击次数最多的档次与当前酒店档次差
        featMap.put(191, (double)(hotelRealGrade - userFeature.getUserHistoryClickFeature().getBehaveMaxGrade()));

        // 192 用户历史点击当前酒店档次占比
        featMap.put(192, userFeature.getUserHistoryClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, ZERO));

        // 193 用户历史点击当前酒店星级占比
        featMap.put(193, userFeature.getUserHistoryClickFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 194 用户历史点击当前酒店品牌占比
        featMap.put(194, userFeature.getUserHistoryClickFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));

        // 195 用户历史点击当前酒店商圈占比
        double hisClickTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisClickTradRate += userFeature.getUserHistoryClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(195, hisClickTradRate);

        // 196 用户历史点击当前酒店类型占比
        double historyClickTypeRate = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserHistoryClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyClickTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }

        featMap.put(196, historyClickTypeRate);

        // 197 用户点击当前酒店占比
        featMap.put(197, userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 198 1月（31天）内收藏次数
        featMap.put(198, (double)userFeature.getUser31DayCollectFeature().getBehaveCount());

        // 199 1月（31天）内收藏酒店最大档次与当前酒店档次差
        featMap.put(199, (double)(hotelRealGrade - userFeature.getUser31DayCollectFeature().getBehaveMaxGrade()));

        // 200 1月（31天）用户收藏当前酒店档次占比
        featMap.put(200, userFeature.getUser31DayCollectFeature().getGradeRateMap().getOrDefault(hotelRealGrade, ZERO));

        // 201 1月（31天）用户收藏当前酒店星级占比
        featMap.put(201, userFeature.getUser31DayCollectFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 202 1月（31天）用户收藏当前酒店品牌占比
        featMap.put(202, userFeature.getUser31DayCollectFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));

        // 203 1月（31天）用户收藏当前酒店商圈占比
        double hisCollectTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisCollectTradRate += userFeature.getUser31DayCollectFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(203, hisCollectTradRate);

        // 204 1月（31天）用户收藏当前酒店类型占比
        double historyFavoriteTypeRate = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser31DayCollectFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyFavoriteTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        featMap.put(204, historyFavoriteTypeRate);

        // 205 用户实时点击酒店次数
        featMap.put(205, (double)userFeature.getUserRealtimeClickFeature().getBehaveCount());

        // 206 用户实时点击次数最多的档次与当前酒店档次差
        featMap.put(206, (double)(hotelRealGrade - userFeature.getUserRealtimeClickFeature().getBehaveMaxGrade()));

        // 207 用户实时点击当前酒店档次占比
        featMap.put(207, userFeature.getUserRealtimeClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, ZERO));

        // 207 用户实时点击当前酒店星级占比
        featMap.put(208, userFeature.getUserRealtimeClickFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 209 用户实时点击当前酒店品牌占比
        featMap.put(209, userFeature.getUserRealtimeClickFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));

        // 210 用户实时点击当前酒店商圈占比
        double realClickTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            realClickTradRate += userFeature.getUserRealtimeClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(210, realClickTradRate);

        // 211 用户实时点击当前酒店类型占比
        double realTimeClickTypeRate = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserRealtimeClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                realTimeClickTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        featMap.put(211, realTimeClickTypeRate);

        // 212 用户实时点击当前酒店占比
        featMap.put(212, userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 213 实时query筛选到该酒店的次数
        featMap.put(213, (double)userFeature.getUserRealtimeQueryFeature().getBehaveCount());

        // 214 实时query筛选到该酒店的query占比
        featMap.put(214, userFeature.getUserRealtimeQueryFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 218 用户历史180天订单量
        featMap.put(218, (double)userFeature.getUser180DayOrderFeature().getBehaveCount());

        // 219 用户历史180天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(219, (double)(hotelRealGrade - userFeature.getUser180DayOrderFeature().getBehaveMaxGrade()));

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(220, userFeature.getUser180DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, ZERO));

        // 221 用户历史180天订单量中，该酒店占比
        featMap.put(221, userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 222 用户历史180天订单量品牌中，该酒店品牌占比
        featMap.put(222, userFeature.getUser180DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));

        // 223 用户历史180天订单量类型中，该酒店类型占比
        double historyOrderTypeRate180Day = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser180DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate180Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        featMap.put(223, historyOrderTypeRate180Day);

        // 224 用户历史180天订单量星级中，该酒店星级占比
        featMap.put(224, userFeature.getUser180DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 225 用户历史180天订单量商圈中，该酒店商圈占比
        double hisOrder180DayTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder180DayTradRate += userFeature.getUser180DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(225, hisOrder180DayTradRate);

        // 226 用户历史180天订单量城市中，该城市占比
        featMap.put(226, userFeature.getUser180DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, ZERO));

        // 227 用户历史365天订单量
        featMap.put(227, (double)userFeature.getUser365DayOrderFeature().getBehaveCount());

        // 228 用户历史365天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(228, (double)(hotelRealGrade - userFeature.getUser365DayOrderFeature().getBehaveMaxGrade()));

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(229, userFeature.getUser365DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, ZERO));

        // 230 用户历史365天订单量中，该酒店占比
        featMap.put(230, userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 231 用户历史365天订单量品牌中，该酒店品牌占比
        featMap.put(231, userFeature.getUser365DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));
        // 232 用户历史365天订单量类型中，该酒店类型占比
        double historyOrderTypeRate365Day = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser365DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate365Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        featMap.put(232, historyOrderTypeRate365Day);

        // 233 用户历史365天订单量类星级中，该酒店星级占比
        featMap.put(233, userFeature.getUser365DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 234 用户历史365天订单量商圈中，该酒店商圈占比
        double hisOrder365DayTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder365DayTradRate += userFeature.getUser365DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(234, hisOrder365DayTradRate);

        // 235 用户历史365天订单量城市中，该酒店城市占比
        featMap.put(235, userFeature.getUser365DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, ZERO));

        // 236 用户历史800天订单量
        featMap.put(236, (double)userFeature.getUser800DayOrderFeature().getBehaveCount());

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(237, (double)(hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade()));

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(238, userFeature.getUser800DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, ZERO));

        // 239 用户历史800天订单量中，该酒店占比
        featMap.put(239, userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, ZERO));

        // 240 用户历史800天订单量品牌中，该酒店品牌占比
        featMap.put(240, userFeature.getUser800DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, ZERO));
        // 241 用户历史800天订单量类型中，该酒店类型占比
        double historyOrderTypeRate800Day = ZERO;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser800DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate800Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        featMap.put(241, historyOrderTypeRate800Day);

        // 242 用户历史800天订单量类星级中，该酒店星级占比
        featMap.put(242, userFeature.getUser800DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 243 用户历史800天订单量商圈中，该酒店商圈占比
        double hisOrder800DayTradRate = ZERO;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder800DayTradRate += userFeature.getUser800DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        featMap.put(243, hisOrder800DayTradRate);

        // 244 用户历史800天订单量城市中，该酒店城市占比
        featMap.put(244, userFeature.getUser800DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, ZERO));
    }



}
