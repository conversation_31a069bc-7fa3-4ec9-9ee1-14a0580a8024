package com.qunar.search.common.model.feature.realtimeuserbehavior;

import lombok.Data;

import java.util.HashSet;

/**
 * 用户在综筛/快筛的行为数据
 */
@Data
public class UserRealClickFilterFeature {

    /**
     * 筛选名
     */
    private String filterName;

    /**
    * 用户24h在综筛/位置筛选里点击该筛选次数
     */
    private int complexFilterClickCnt;

    /**
     * 用户24h在综筛/位置是否是最近一次点击该筛选(0\1)
     */
     private int isComplexFilterLastClick;

    /**
     * 用户24h在综筛/位置筛选里点击筛选项的排名（1-10）
     */
    private int complexFilterClickSort;

    /**
     * 用户24h在快筛里点击筛选项次数
     */
    private int fastFilterClickCnt;

     /**
     * 用户24h在快筛是否是最近一次点击该筛选
     */
     private int isFastFilterLastClick;

     /**
     * 用户24h在快筛里点击筛选项的时间排名（1-10）
     */
     private int fastFilterClickSort;

     /**
     * 用户24h在综筛/位置/快筛里点击该筛选总次数
     */
     private int complexFastFilterClickCnt;

     /**
     * 用户24h在综筛/位置/快筛是否是最近一次点击该筛选
     */
     private int isComplexFastFilterLastClick;

     /**
     * 用户24h在综筛/位置/快筛里点击筛选项排名（1-10）
     */
     private int complexFastFilterClickSort;

    /**
     * 用户24h点击酒店的酒店标签（该筛选）次数
     */
    private int hotelFilterTagClickCnt;

    /**
     * 用户24h收藏酒店的酒店标签（该筛选）次数
     */
    private int hotelFilterTagFavouriteCnt;
}
