package com.qunar.search.common.model.convertor;

import com.qunar.search.common.bean.UserProfile;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;
import java.util.Objects;

import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossDiff;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;

/**
 * <AUTHOR>
 * @create 2020-08-03 下午7:10
 * @DESCRIPTION
 **/
@Component
public class XgboostD23FeatureConvertor extends XgboostD21FeatureConvertor {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Object> featMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();
        UserProfile userProfile = userFeature.getUserProfile();
        if (null == userProfile || MapUtils.isEmpty(feature)) {
            return featMap;
        }

        addFeature(userProfile, feature, featMap);

        return featMap;
    }

    /**
     * 计算交叉特征
     *
     * @param userProfile    用户画像数据
     * @param offlineFeature 酒店订单用户统计数据
     * @return 特征map
     */
    private void addFeature(UserProfile userProfile, EnumMap<FeatureType, Double> offlineFeature, Map<Integer, Object> map) {

        // todo 将下标改成Integer常量，避免每次都自动装箱

        switch (userProfile.getGender()) {
            case 1: // 男
                putMap(map, offlineFeature.get(FeatureType.MAN_PREFECT), 261);
                break;
            case 0: // 女
                putMap(map, offlineFeature.get(FeatureType.WOMAN_PREFECT), 261);
                break;
            case -1:// 未知
                putMap(map, offlineFeature.get(FeatureType.GENDER_UN_KNOW_PERFECT), 261);
        }

        switch (userProfile.getAge()) {
            case 0: // 0-25
                putMap(map, offlineFeature.get(FeatureType.AGE_0_25_PREFECT), 262);
                break;
            case 1: // 25-30
                putMap(map, offlineFeature.get(FeatureType.AGE_25_30_PREFECT), 262);
                break;
            case 2: // 30-35
                putMap(map, offlineFeature.get(FeatureType.AGE_30_35_PREFECT), 262);
                break;
            case 3: // 35- 40
                putMap(map, offlineFeature.get(FeatureType.AGE_35_40_PREFECT), 262);
                break;
            case 4: // 40 -100
                putMap(map, offlineFeature.get(FeatureType.AGE_40_100_PREFECT), 262);
                break;
            case -1: // 其他
                putMap(map, offlineFeature.get(FeatureType.AGE_UN_KNOW_PERFECT), 262);
        }

        switch (userProfile.getSensitiveScore()) {
            case 1: // 高价格敏感度用户
                putMap(map, offlineFeature.get(FeatureType.HIGH_PRICE_SENS_PREFECT), 264);
                break;
            case 0: // 低价格敏感度用户
                putMap(map, offlineFeature.get(FeatureType.LOW_PRICE_SENS_PREFECT), 264);
                break;
            case -1: // 未知
                putMap(map, offlineFeature.get(FeatureType.PRICE_SENS_UN_KNOW_PERFECT), 264);
                break;
        }

        switch (userProfile.getHasMeituan()) {
            case 1: // 下载美团用户
                putMap(map, offlineFeature.get(FeatureType.HAS_MEITUAN_PREFECT), 265);
                break;
            case 0: // 未下载美团用户
                putMap(map, offlineFeature.get(FeatureType.NO_MEITUAN_PREFECT), 265);
                break;
            case -1: // 未知
                putMap(map, offlineFeature.get(FeatureType.MEITUAN_UN_KNOW_PERFECT), 265);
                break;
        }

        switch (userProfile.getHasCtrip()) {
            case 1: // 下载携程用户
                putMap(map, offlineFeature.get(FeatureType.HAS_CTRIP_PREFECT), 266);
                break;
            case 0: // 未下载携程用户
                putMap(map, offlineFeature.get(FeatureType.NO_CTRIP_PREFECT), 266);
                break;
            case -1: // 未知
                putMap(map, offlineFeature.get(FeatureType.CTRIP_UN_KNOW_PERFECT), 266);
                break;
        }

        switch (userProfile.getHasElong()) {
            case 1: // 下载艺龙用户
                putMap(map, offlineFeature.get(FeatureType.HAS_ELONG_PREFECT), 267);
                break;
            case 0: // 未下载艺龙用户
                putMap(map, offlineFeature.get(FeatureType.NO_ELONG_PREFECT), 267);
                break;
            case -1: // 未知
                putMap(map, offlineFeature.get(FeatureType.ELONG_UN_KNOW_PERFECT), 267);
                break;
        }

        switch (userProfile.getHasCompet()) {
            case 1: // 下载竞品用户
                putMap(map, offlineFeature.get(FeatureType.HAS_COMPETE_PREFECT), 268);
                break;
            case 0: // 未下载竞品用户
                putMap(map, offlineFeature.get(FeatureType.NO_COMPETE_PREFECT), 268);
                break;
            case -1: // 未知
                putMap(map, offlineFeature.get(FeatureType.COMPETE_UN_KNOW_PERFECT), 268);
                break;
        }

        switch (userProfile.getFlightUser()) {
            case "TT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_FLIGHT_TT_PREFECT), 269);
                break;
            case "FT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_FLIGHT_FT_PREFECT), 269);
                break;
            case "TF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_FLIGHT_TF_PREFECT), 269);
                break;
            case "FF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_FLIGHT_FF_PREFECT), 269);
                break;
        }

        switch (userProfile.getTicketUser()) {
            case "TT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TRAIN_TT_PREFECT), 270);
                break;
            case "FT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TRAIN_FT_PREFECT), 270);
                break;
            case "TF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TRAIN_TF_PREFECT), 270);
                break;
            case "FF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TRAIN_FF_PREFECT), 270);
                break;
        }

        switch (userProfile.getFlightUser()) {
            case "TT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TICKET_TT_PREFECT), 271);
                break;
            case "FT":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TICKET_FT_PREFECT), 271);
                break;
            case "TF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TICKET_TF_PREFECT), 271);
                break;
            case "FF":
                putMap(map, offlineFeature.get(FeatureType.HOTEL_TICKET_FF_PREFECT), 271);
                break;
        }

        switch (userProfile.getVacationUser()) {
            case "TT": // 高价格敏感度用户
                putMap(map, offlineFeature.get(FeatureType.HOTEL_VACATION_TT_PREFECT), 272);
                break;
            case "FT": // 低价格敏感度用户
                putMap(map, offlineFeature.get(FeatureType.HOTEL_VACATION_FT_PREFECT), 272);
                break;
            case "TF": // 未知
                putMap(map, offlineFeature.get(FeatureType.HOTEL_VACATION_TF_PREFECT), 272);
                break;
            case "FF": // 未知
                putMap(map, offlineFeature.get(FeatureType.HOTEL_VACATION_FF_PREFECT), 272);
                break;
        }

        Object value272 = map.get(272);
        if (value272 != null) {
            map.put(273, value272);
        }

        putCrossDiff(map, 274, userProfile.getPreferLocalCity(), s -> true,
                offlineFeature.get(FeatureType.SAME_CITY_ORDER_RATE), Objects::nonNull);

        putCrossDiff(map, 275, userProfile.getPreferDiffCity(), s -> true,
                offlineFeature.get(FeatureType.NOT_SAME_CITY_ORDER_RATE), Objects::nonNull);

        putCrossDiff(map, 276, userProfile.getPreferHoliday(), s -> true,
                offlineFeature.get(FeatureType.HOLIDAY_ORDER_RATE), Objects::nonNull);

        putCrossDiff(map, 277, userProfile.getPreferWeekend(), s -> true,
                offlineFeature.get(FeatureType.WEEKEND_ORDER_RATE), Objects::nonNull);

    }
}
