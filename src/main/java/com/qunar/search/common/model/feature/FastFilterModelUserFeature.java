package com.qunar.search.common.model.feature;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.bean.*;
import com.qunar.search.common.bean.DetailShow.ListClick;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.math.data.UserBehave;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserRealClickFilterFeature;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023-09-30 上午11:30
 * @DESCRIPTION
 * 1、模型计算使用的用户数据
 * 2、特征日志打印对象
 **/

@Data
public class FastFilterModelUserFeature implements Serializable {

	/**
	 * 用户快筛画像数据
	 */
	private UserFastFilterProfile userFastFilterProfile;

	/**
	 * 用户实时行为数据
	 */
	private Map<String, UserRealClickFilterFeature> userRealClickFilterFeatureMap = Collections.emptyMap();
}
