package com.qunar.search.common.model.dnn;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * @DESCRIPTION Dnn特征转换用到的常量
 **/
public class DnnConstants {

	public static final String BLANK = " ";

	public static final String COMMA = ",";

	public static final String COLON = ":";

	public static final String UNDERLINE = "_";

	public static final Set NAN_SET = Sets.newHashSet("NAN", "nan", "NaN", "NULL", "null");

	public static final double DOUBLE_ZEROS = 0.000000001;

	// embedding 向量的长度，如线下更新，需要更改
	public static final int EMBEDDING_VECTOR_LEN = 32;

	public static List<Double> DEFAULT_EMBEDDING_VECTOR;

	static {
		List<Double> defaultList = Lists.newArrayList();
		for (int i = 0; i < EMBEDDING_VECTOR_LEN; i++) {
			defaultList.add(0.0);
		}
		DEFAULT_EMBEDDING_VECTOR = defaultList;
	}
}
