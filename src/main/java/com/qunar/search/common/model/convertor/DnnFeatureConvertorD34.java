package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.NEGATIVE_ONE;


/**
 * D34 特征 Convertor
 * 价格特征用预估的
 */
@Component
public class DnnFeatureConvertorD34 extends DnnFeatureConvertorBase {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 318 的特征索引
        Map<Integer, Object> featReturn = super.convert(requestFeature, userFeature, hotelFeature);


        // 335 -> 比价info
        featReturn.put(FeatureIndexEnum.D34_QM_PRICECOMPARE_335.getIndex(), hotelFeature.getBeatInfo()*1.0);

        // 336 -> 可售房型订单占比
        featReturn.put(FeatureIndexEnum.D34_ORDERS_RATE_336.getIndex(), Numbers.roundStringDouble(
                hotelFeature.getOrdersRate(), FeatureConstants.decimals_num));

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 337 -> 7天对应场景曝光CTR    338 -> 21天对应场景曝光CTR   339 -> 60天对应场景曝光CTR
            fillSceneCtr(requestFeature.getSortScene(), offlineFeature, featReturn);

            // 340 -> 7天去位置偏置曝光CTR
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_7DAY_340.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_7DAY, NEGATIVE_ONE));

            // 341 -> 14天去位置偏置曝光CTR
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_14DAY_341.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_14DAY, NEGATIVE_ONE));

            // 342 -> 21天去位置偏置曝光CTR
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_21DAY_342.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_21DAY, NEGATIVE_ONE));

            // 343 -> 该酒店7天非扶持曝光量占该城市非扶持曝光量占比
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_SHOW_RAT_343.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_SHOW_RATE, NEGATIVE_ONE));

            // 344 -> 该酒店7天点击量占该城市点击量占比
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_CLICK_RAT_344.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_CLICK_RATE, NEGATIVE_ONE));

            // 345 -> 该酒店7天订单量占该城市订单量占比
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_ORDER_RAT_345.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_ORDER_RATE, NEGATIVE_ONE));
        }

        // 346 -> 当前时间于酒店装修或开业时间年份差
        if (hotelFeature.getFitmentYearDiff() != null) {
            featReturn.put(FeatureIndexEnum.D34_FITMENT_YEARS_DIFF_346.getIndex(), hotelFeature.getFitmentYearDiff());
        }

        // 347 -> 酒店基础信息所在城市
        if (StringUtils.isNotEmpty(hotelFeature.getCityCode())) {
            featReturn.put(FeatureIndexEnum.D34_HOTEL_CITY_CODE_347.getIndex(), hotelFeature.getCityCode());
        }

        // 348 -> 酒店7天的cvr (s2o / ctr) 直接用统计好的 307特征位 除以289特征位
        Double cvr7d = divisionValue(featReturn.get(FeatureIndexEnum.D25_HOUTLE_7DAY_CTR_289.getIndex()),
                featReturn.get(FeatureIndexEnum.D27_HOUTLE_7DAY_S2O_307.getIndex()), FeatureConstants.decimals_num);
        featReturn.put(FeatureIndexEnum.D34_HOTEL_7DAY_CVR_348.getIndex(), cvr7d);

        // 354 -> 酒店折扣率 （预估render 报价 / sort报价）
        Double priceDiscount = divisionValue(hotelFeature.getMobileMinAvailablePrice() * 1.0,
                hotelFeature.getEstimatedRenderPrice() * 1.0, 2);
        featReturn.put(FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex(), priceDiscount);

        // 349 当前酒店seq
        if (StringUtils.isNotEmpty(hotelFeature.getHotelSeq())) {
            featReturn.put(FeatureIndexEnum.HOTEL_CURRENT.getIndex(), hotelFeature.getHotelSeq());
        }

        return featReturn;
    }


    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    private static Double divisionValue(Object divisor,Object dividend,int len){
        if (divisor == null || dividend == null || (double)divisor ==0.0 || (double)dividend ==0.0){
            return FeatureConstants.ZERO;
        } else {
            return Numbers.roundStringDouble((double)dividend / (double)divisor,len);
        }

    }

    /**
     * 根据对应场景 填充ctr 特征
     */
    private static void fillSceneCtr(SortScene sortScene,EnumMap<FeatureType, Double> offlineFeature,Map<Integer, Object> featReturn){

        switch (sortScene){
            case NEARBY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;
            case POI_KEY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;

            case NOT_SAME_CITY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_60_DAY,NEGATIVE_ONE));
                break;

            default: // SAME_CITY and others
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_7_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_21_DAY,NEGATIVE_ONE));

                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(), offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_60_DAY,NEGATIVE_ONE));

                break;

        }

    }

}
