package com.qunar.search.common.model.feature;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 广告酒店的特征
 * @Author: haohao.sun
 * @Date: 2024/11/4 17:06
 */
@Data
public class AdModelItemFeature extends ModelHotelFeature implements Serializable {

    //报价，普通佣金参考基类sortPriceCommission
    /**
     * 当前酒店的插入位次
     */
    private int pos;
    /**
     * 预估的ctr
     */
    private double pCTR;
    /**
     * 预估的ctcvr
     */
    private double pCTCVR;

    /**
     * 广告佣金
     */
    private double adCommission;

    /**
     * cpc出价
     */
    private BigDecimal bidPrice;

    /**
     * 前面酒店的特征
     */
    private AdContextHotelFeature frontHotel=new AdContextHotelFeature();
    /**
     * 后面酒店的特征
     */
    private AdContextHotelFeature behindHotel=new AdContextHotelFeature();


}
