package com.qunar.search.common.model.convertor;

import com.qunar.search.common.bean.UserActivityItem;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;


@Component
public class DnnPriceConvertorV2 extends DnnPriceConvertor {
    public static String DEFAULT_FEATURE_STR = "1.0";
    public static int OFFSET = 30;

    public Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Object> featMap = super.convert(requestFeature, userFeature, hotelFeature);

        // 用户身份、酒店活动的ID映射
        Map<String, UserActivityItem>  userActivityItemMap = userFeature.getUserActivityItemMap();

        Set<String> activityIds = hotelFeature.getActivitySet();

        // User 相关身份特征
        // index start from 200
        featMap.putAll(userFeature.getUserIdentityFeature());

        // 活动相关特征
        // index start from 30
        for (String activity : activityIds) {
            UserActivityItem userActivityItem = userActivityItemMap.get(activity);
            if (userActivityItem != null) {
                featMap.put(userActivityItem.getTypeId() + OFFSET, DEFAULT_FEATURE_STR);
            } else {
                featMap.put(OFFSET, DEFAULT_FEATURE_STR);
            }
        }

        return featMap;
    }
}
