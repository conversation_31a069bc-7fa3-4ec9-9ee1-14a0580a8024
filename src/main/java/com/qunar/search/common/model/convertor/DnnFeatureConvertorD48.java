package com.qunar.search.common.model.convertor;


import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * D43 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD48 extends DnnFeatureConvertorD47 {



    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 524 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        Map<String, UserHotelDetailStayTime> userHotelDetailStayTimeMap = userFeature.getUserHotelDetailStayTimeMap();
        UserHotelDetailStayTime userHotelDetailStayTime = userHotelDetailStayTimeMap.get(hotelFeature.getHotelSeq());

        if(userHotelDetailStayTime != null){

            resultMap.put(FeatureIndexEnum.D48_525.getIndex(), userHotelDetailStayTime.getTotalRemainTime() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_526.getIndex(), userHotelDetailStayTime.getClickDetailCount() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_527.getIndex(), userHotelDetailStayTime.getClickHouseType() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_528.getIndex(), userHotelDetailStayTime.getHouseTypeprice());
            resultMap.put(FeatureIndexEnum.D48_529.getIndex(), userHotelDetailStayTime.getClickMasterMapCount() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_530.getIndex(), userHotelDetailStayTime.getClickShareCount() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_531.getIndex(), userHotelDetailStayTime.getClickCollectCount() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_532.getIndex(), userHotelDetailStayTime.getClickDistanceCount() * 1.0);
            resultMap.put(FeatureIndexEnum.D48_533.getIndex(), userHotelDetailStayTime.getClickCommentCount() * 1.0);
        }

        return resultMap;
    }

}
