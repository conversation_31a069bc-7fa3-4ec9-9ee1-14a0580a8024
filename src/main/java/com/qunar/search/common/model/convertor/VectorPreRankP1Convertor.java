package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.bean.UserProfile;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 特征参照：
 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=421249519
 */

@Component
public class VectorPreRankP1Convertor implements SortFeatureStringConvertor {

    public Map<Integer, String> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, String> featuresMap = Maps.newHashMap();

        addEmbeddingFeature(userFeature,featuresMap);
        addUserProfileFeature(userFeature,featuresMap);

        return featuresMap;
    }


    private void addEmbeddingFeature(ModelUserFeature userFeature,Map<Integer, String> embFeatMap) {

        UserShowOrderData userShowOrderData = userFeature.getUserShowOrderData();

        List<UserShowOrderData.PvInfo> pvInfoList = userShowOrderData.getPvInfoList(); // 历史浏览以及点击
        Map<String, UserHotel.FavoriteHotel> favoriteHotels = userFeature.getFavoriteHotels(); // 历史收藏
        Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders(); // 历史订单

        List<UserHotel.RealtimeClickHotel> clickHotels = userFeature.getClickHotels(); // 实时点击酒店

        StringBuilder pvString = new StringBuilder();
        StringBuilder clickString = new StringBuilder();
        StringBuilder collectString = new StringBuilder();
        StringBuilder orderString = new StringBuilder();
        StringBuilder realtimeClickString = new StringBuilder();


        if (!pvInfoList.isEmpty()){
            for (UserShowOrderData.PvInfo pvInfo:pvInfoList) {
                if(pvInfo.getCl() >0){
                    clickString.append(",").append(pvInfo.getHotelSeq());
                }
                pvString.append(",").append(pvInfo.getHotelSeq());
            }
            embFeatMap.put(1,pvString.toString().substring(1));
            embFeatMap.put(2,clickString.toString().substring(1));
        } else {
            embFeatMap.put(1,"0");
            embFeatMap.put(2,"0");
        }

        if (!favoriteHotels.isEmpty()){
            for (String collHotelSeq :favoriteHotels.keySet()) {
                collectString.append(",").append(collHotelSeq );
            }
            embFeatMap.put(3,collectString.toString().substring(1));
        } else {
            embFeatMap.put(3,"0");
        }

        if (!historyOrders.isEmpty()){
            for (String orderHotelSeq:historyOrders.keySet()) {
                orderString.append(",").append(orderHotelSeq );
            }
            embFeatMap.put(4,orderString.toString().substring(1));
        } else {
            embFeatMap.put(4,"0");
        }

        // 实时点击酒店s
        if (!clickHotels.isEmpty()){
            for (UserHotel.RealtimeClickHotel realtimeClickHotel:clickHotels) {
                realtimeClickString.append(",").append(realtimeClickHotel.getSeq());
            }
            embFeatMap.put(5,realtimeClickString.toString().substring(1));
        } else {
            embFeatMap.put(5,"0");
        }

    }


    private void addUserProfileFeature(ModelUserFeature userFeature,Map<Integer, String> profileFeatMap) {
        UserProfile userProfile = userFeature.getUserProfile();

        if (Objects.isNull(userProfile)){
            profileFeatMap.put(6,"-1");
            profileFeatMap.put(7,"-1");
            profileFeatMap.put(8,"0.0");
            profileFeatMap.put(9,"0.0");

        } else {
            profileFeatMap.put(6,userProfile.getAge()+"");
            profileFeatMap.put(7,userProfile.getGender()+"");
            profileFeatMap.put(8,userProfile.getPreferHoliday()+"");
            profileFeatMap.put(9,userProfile.getPreferWeekend()+"");
        }


    }

}
