package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.FeatureValueTypeEnum;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.feature.GroupFeature;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import com.qunar.search.common.util.Numbers;
import com.qunar.search.common.util.RangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.enums.FeatureType.HOLIDAY_PRICE_WEIGHT;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;
import static com.qunar.search.common.util.Numbers.roundStringDouble;


/**
 * D51 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD51 extends DnnFeatureConvertorD50 {

    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;
    private static final double DEFAULT_MAX_LENGTH = 600.0;
    private static final Double DEFAULT_PRICE = 200D;
    private static final Double DEFAULT_CTR = 0.005D;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        resultMap.remove(FeatureIndexEnum.DNN_INDEX_101.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_102.getIndex());
        resultMap.remove(FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex());
        resultMap.remove(FeatureIndexEnum.D37_400.getIndex());
        resultMap.remove(FeatureIndexEnum.D50_543.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_220.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_229.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_238.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_249.getIndex());
        resultMap.remove(FeatureIndexEnum.DNN_INDEX_250.getIndex());
        resultMap.remove(FeatureIndexEnum.D35_366.getIndex());
        resultMap.remove(FeatureIndexEnum.D35_369.getIndex());
        resultMap.remove(FeatureIndexEnum.D35_371.getIndex());
        resultMap.remove(FeatureIndexEnum.D43_463.getIndex());
        resultMap.remove(FeatureIndexEnum.D43_464.getIndex());
        resultMap.remove(FeatureIndexEnum.D43_465.getIndex());


        // 用户下单价格
        double userOrderAvgPrice = userFeature.getOrderAvgPrice();
        // 用户实时点击价格
        double userRealTimeClickAvgPrice = userFeature.getRealtimeClickAvgPrice();
        // 用户实时点击房型价格
        Map<String, UserHotelDetailStayTime> userHotelDetailStayTimeMap = userFeature.getUserHotelDetailStayTimeMap();
        UserHotelDetailStayTime userHotelDetailStayTime = userHotelDetailStayTimeMap.get(hotelFeature.getHotelSeq());

        // 酒店报价
        double hotelShowPrice = hotelFeature.getCachedPriceAfterMerchant() <= 0.0 ? hotelFeature.getMinPriceWithIdentity() : hotelFeature.getCachedPriceAfterMerchant();
        // 酒店划线价格
        double hotelShowOriginPrice = hotelFeature.getCachedDispOrgPrice() <= 0.0 ? hotelFeature.getOriginalPrice() : hotelFeature.getCachedDispOrgPrice();

        // 价格相关特征
        if (hotelShowPrice > 0 && hotelShowPrice < DEFAULT_MAX_SUB10) {
            // 酒店报价
            resultMap.put(FeatureIndexEnum.D51_547.getIndex(), hotelShowPrice);
            if (hotelShowOriginPrice > 0 && hotelShowOriginPrice < DEFAULT_MAX_SUB10) {
                // 酒店原始价格 - 酒店报价
                resultMap.put(FeatureIndexEnum.D51_548.getIndex(), hotelShowOriginPrice - hotelShowPrice);
                // 酒店折扣率 （报价 / 划线价）
                Double priceDiscount = RangeUtil.divisionValue(hotelShowPrice, hotelShowOriginPrice, 4);
                resultMap.put(FeatureIndexEnum.D51_549.getIndex(), priceDiscount);
            }

            if (userOrderAvgPrice > 0 && userOrderAvgPrice < DEFAULT_MAX_SUB10) {
                double diff = userOrderAvgPrice - hotelShowPrice;
                // 用户下单价格-酒店报价
                resultMap.put(FeatureIndexEnum.D51_550.getIndex(), diff);
                // (用户下单价格-酒店报价)/酒店报价
                Double diffRate = RangeUtil.divisionValue(hotelShowPrice, diff, 4);
                resultMap.put(FeatureIndexEnum.D51_551.getIndex(), diffRate);
            }

            // 当前酒店报价 / 用户实时点击平均价格
            double featureValue400 = RangeUtil.divisionValue(userRealTimeClickAvgPrice, hotelShowPrice, 6, 10);
            if (featureValue400 > 0) {
                resultMap.put(FeatureIndexEnum.D51_552.getIndex(), featureValue400);
            }

            if(userHotelDetailStayTime != null && userHotelDetailStayTime.getHouseTypeprice() > 0 && userHotelDetailStayTime.getHouseTypeprice() < DEFAULT_MAX_SUB100) {
                double houseTypeprice = userHotelDetailStayTime.getHouseTypeprice();
                // 当前酒店价格/点击房型平均价格-1
                resultMap.put(FeatureIndexEnum.D51_553.getIndex(), roundStringDouble((hotelShowPrice - houseTypeprice) / hotelShowPrice, 8));
            }

            int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
            int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelShowPrice);
            // 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D51_554.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser180DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));
            // 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D51_555.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser365DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));
            // 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D51_556.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser800DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));

            EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
            if (MapUtils.isNotEmpty(offlineFeature)) {
                Double cityPv7Price = offlineFeature.getOrDefault(FeatureType.CITY_PV_7_PRICE, DEFAULT_PRICE);
                Double bizZoneMean = offlineFeature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
                Double geoPvAvg7Price = offlineFeature.getOrDefault(FeatureType.GEO_PV_AVG_7_PRICE, DEFAULT_PRICE);
                Double aroundAvg7Price = offlineFeature.getOrDefault(FeatureType.AROUND_AVG_7_PRICE, DEFAULT_PRICE);


                double hotelShowPriceValid = hotelShowPrice >= 100000 ? cityPv7Price : hotelShowPrice;

                if (bizZoneMean != null && bizZoneMean > 0.0d) {
                    double diff = hotelShowPriceValid - bizZoneMean;
                    // 酒店报价-商业区价格
                    resultMap.put(FeatureIndexEnum.D51_557.getIndex(), diff);
                    // (酒店报价-商业区价格)/商业区价格
                    resultMap.put(FeatureIndexEnum.D51_558.getIndex(), Numbers.roundStringDouble(diff / bizZoneMean, FeatureConstants.decimals_num));
                }

                // poi对应的geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价
                resultMap.put(FeatureIndexEnum.D51_559.getIndex(), RangeUtil.divisionValue(geoPvAvg7Price, hotelShowPriceValid));
                // poi周围9个 geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价
                resultMap.put(FeatureIndexEnum.D51_560.getIndex(), RangeUtil.divisionValue(aroundAvg7Price, hotelShowPriceValid));
                // 酒店预估价格/当前城市展示价格
                resultMap.put(FeatureIndexEnum.D51_561.getIndex(), RangeUtil.divisionValue(cityPv7Price, hotelShowPriceValid));
            }

            GroupFeature groupFeature = requestFeature.getGroupFeature();
            if (null != groupFeature) {
                // 酒店报价/组内酒店最高报价
                resultMap.put(FeatureIndexEnum.D51_562.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelMaxPrice(), hotelShowPrice, 3, 1.0)
                );
                // 酒店报价/组内酒店平均报价
                resultMap.put(FeatureIndexEnum.D51_563.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelAvgPrice(), hotelShowPrice, 3, 10.0)
                );
                // 酒店报价/组内酒店最低报价
                resultMap.put(FeatureIndexEnum.D51_564.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelMinPrice(), hotelShowPrice, 3, 1000.0)
                );
            }
            // beat值
            resultMap.put(FeatureIndexEnum.D51_565.getIndex(), Numbers.roundStringDouble((double) hotelFeature.getCachedBeat(), FeatureConstants.decimals_num));
            // 是否有缓存价格
            resultMap.put(FeatureIndexEnum.D51_566.getIndex(), hotelFeature.getCachedPriceAfterMerchant() <= 0.0 ? 0.0 : 1.0);
        }
        return resultMap;
    }

}
