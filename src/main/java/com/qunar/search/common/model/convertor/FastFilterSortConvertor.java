package com.qunar.search.common.model.convertor;


import com.qunar.search.common.model.feature.FastFilterModelItemFeature;
import com.qunar.search.common.model.feature.FastFilterModelRequestFeature;
import com.qunar.search.common.model.feature.FastFilterModelUserFeature;

import java.util.Map;

/**
 * rank 模型特征转换接口类，
 * 注意：
 * 1、线上使用spring bean 方式注入，线下jar 包中new对象使用
 * 2、 convertor 中只写特征转换方法，
 * 3不要依赖外部工具 Qmonitor，Qconfig 等
 */
public interface FastFilterSortConvertor {

    /**
     * 特征转换方法
     *
     * @param requestFeature 请求特征数据
     * @param userFeature    用户特征
     * @param filterFeature   item特征数据
     * @return 编码后的特征map ， key 为 int 类型
     */
    Map<Integer, Object> convertObject(FastFilterModelRequestFeature requestFeature, FastFilterModelUserFeature userFeature, FastFilterModelItemFeature filterFeature);
}

