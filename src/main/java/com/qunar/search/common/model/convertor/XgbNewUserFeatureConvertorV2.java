package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.GeneralUtil;
import com.qunar.search.common.util.MobileUtils;
import com.qunar.search.common.util.RangeUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.constants.FeatureConstants.ONE;

/**
 * D42 特征 Convertor
 */
@Component
public class XgbNewUserFeatureConvertorV2 extends XgbSmallSceneFeatureConvertorBase {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        // 酒店有身份报价
        double  hotelShowPrice = hotelFeature.getMinPriceWithIdentity();
        // 用户实时点击均价
        double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();

        // 400 当前酒店sort报价 / 用户实时点击平均价格
        double featureValue400 = RangeUtil.divisionValue(clickAvgPrice,hotelShowPrice,6,10);
        if (featureValue400 > 0) {
            resultMap.put(FeatureIndexEnum.D37_400.getIndex(), featureValue400);
        }

        // 401 当前酒店和同城最近点击三家酒店的平均距离
        List<GLatLng> userRealClickGLatLngMap = userFeature.getUserRealClickGLatLngMap();
        if (null != userRealClickGLatLngMap && !userRealClickGLatLngMap.isEmpty()) {
            int realClickNum = 0;
            double total_distance = 0;
            for (GLatLng gLatLng : userRealClickGLatLngMap) {
                if (gLatLng != null && hotelFeature.getLatLng() != null) {
                    double distance = gLatLng.distance(hotelFeature.getLatLng());
                    total_distance += distance;
                    realClickNum += 1;

                    if (realClickNum >= 3) {
                        break;
                    }
                }
            }
            resultMap.put(FeatureIndexEnum.D37_401.getIndex(), RangeUtil.divisionValue(realClickNum * 1.0, total_distance, 6, 1000000));
        }

        // 402 当前酒店在用户实时点击五家酒店中的平均点击-下单关联性
        Map<String, Double> mapRealTimeOrderRela = userFeature.getMapRealTimeOrderRela();
        resultMap.put(FeatureIndexEnum.D37_402.getIndex(), mapRealTimeOrderRela.getOrDefault(hotelFeature.getHotelSeq(), ZERO));


        // 用户搜索城市是否用户常住地
        int oftenUserFlag = 0;
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && userFeature.getUserProfile() != null
                && StringUtils.isNotEmpty(userFeature.getUserProfile().getOftenCity())) {
            oftenUserFlag = StringUtils.equals(requestFeature.getRootCityCode(), userFeature.getUserProfile().getOftenCity()) ? 1 : 0;
        }

        if (MapUtils.isNotEmpty(offlineFeature)) {
            if (1 == oftenUserFlag) {
                resultMap.put(FeatureIndexEnum.D42_449.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_OFTEN_USER_CTR_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_450.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_OFTEN_USER_CTCVR_7D, ZERO));
            } else {
                resultMap.put(FeatureIndexEnum.D42_451.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_NOT_OFTEN_USER_CTR_7D, ZERO));
                resultMap.put(FeatureIndexEnum.D42_452.getIndex(), offlineFeature.getOrDefault(FeatureType.HOTEL_NOT_OFTEN_USER_CTCVR_7D, ZERO));
            }
        }

        resultMap =  GeneralUtil.convertorFeature484To489(resultMap,userFeature,hotelFeature);

        return resultMap;
    }
}
