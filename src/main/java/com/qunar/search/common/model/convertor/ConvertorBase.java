package com.qunar.search.common.model.convertor;

import com.google.common.collect.Lists;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.Platform;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;

import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.function.Function;

public class ConvertorBase {

    private static final int EMBEDDING_FEATURE_LEN = 32;
    private static final Double EMBEDDING_DEFAULT_VALUE = -1.0;

    private static final Integer FEATURE_INDEX_246 = 246;

    private static final Integer FEATURE_INDEX_247 = 247;

    /**
     * 计算用户点击酒店与当前酒店的embedding特征
     */
    public static void putEmbeddingFeature(ModelUserFeature userFeature, ModelHotelFeature hotelFeature, Map<Integer, Object> embeddingFeature) {
        embeddingFeature.put(FEATURE_INDEX_246, EMBEDDING_DEFAULT_VALUE);
        embeddingFeature.put(FEATURE_INDEX_247, EMBEDDING_DEFAULT_VALUE);

        List<List<Double>> clickListHotelAvgEmbedding = userFeature.getRealTimeClickHotelEmbedding();
        /*用户没有实时点击酒店*/
        if (CollectionUtils.isEmpty(clickListHotelAvgEmbedding)) {
            return;
        }

        List<Double> curHotelVector = hotelFeature.getHotelEmbedding();
        /*当前酒店没有 embedding 向量*/
        if (CollectionUtils.isEmpty(curHotelVector) || curHotelVector.size() != EMBEDDING_FEATURE_LEN) {
            return;
        }

        List<Double> sims = Lists.newArrayList();
        for (List<Double> clickHotelVector : clickListHotelAvgEmbedding) {

            if (clickHotelVector.size() != EMBEDDING_FEATURE_LEN) {
                continue;
            }

            double sim = cosSim(curHotelVector, clickHotelVector);
            sims.add(sim);
        }

        /*点击酒店与当前酒店计算相似度值集合不为空*/
        if (CollectionUtils.isEmpty(sims)) {
            return;
        }

        double min = Double.MAX_VALUE, max = Double.MIN_VALUE;
        for (double sim : sims) {
            if (sim > max) {
                max = sim;
            }

            if (sim < min) {
                min = sim;
            }
        }

        embeddingFeature.put(FEATURE_INDEX_246, min);
        embeddingFeature.put(FEATURE_INDEX_247, max);
    }

    /**
     * 计算向量的余弦相似度
     */
    public static double cosSim(List<Double> hotelSeqVects, List<Double> clickVects) {

        double sum = 0;
        double sumX1 = 0;
        double sumX2 = 0;

        for (int i = 0; i < hotelSeqVects.size(); i++) {

            double x1 = hotelSeqVects.get(i);
            double x2 = clickVects.get(i);

            sum += x1 * x2;
            sumX1 += Math.pow(x1, 2);
            sumX2 += Math.pow(x2, 2);
        }

        return roundDouble(sum / (Math.sqrt(sumX1) * Math.sqrt(sumX2)), 7);
    }

    /**
     * 保留 len 位小数点
     */
    public static double roundDouble(double value, int len) {
        return (int) (value * Math.pow(10, len)) / Math.pow(10, len);
    }

    /**
     * 每个档次下得价格分桶
     */
    public static int getGradePriceBucketIndex(int grade, double price) {
        if (grade <= 2) {
            if (price <= 100.0) {
                return 1;
            } else if (price <= 200.0) {
                return 2;
            } else if (price < Integer.MAX_VALUE - 10) {
                return 3;
            }
            return -1;
        }

        if (grade == 3) {
            if (price <= 400.0) {
                return 4;
            } else if (price < Integer.MAX_VALUE - 10) {
                return 5;
            }
            return -1;
        }

        if (grade == 4) {
            return 6;
        }

        if (grade == 5) {
            return 7;
        }
        return 4;
    }

    static int getBucketIndex(double[] bucket, Double value) {
        if (null == value) {
            return -1;
        }

        return getBucketIndex(bucket, value.doubleValue());
    }

    /**
     * 计算分桶索引命中
     */
    static int getBucketIndex(double[] bucket, double value) {
        if (bucket.length <= 1 || value == Double.MAX_VALUE || value == Double.MIN_VALUE) {
            return -1;
        }
        if (value < bucket[0]) {
            return 0;
        }
        if (value > bucket[bucket.length - 1]) {
            return bucket.length - 1;
        }
        for (int i = 1; i < bucket.length; i++) {
            if (bucket[i - 1] <= value && value < bucket[i]) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 批量移除map中的值
     */
    static void removeIndex(Map<Integer, Double> featureMap, int start, int len) {
        for (int i = start; i < start + len; i++) {
            featureMap.remove(i);
        }
    }

    /**
     * 将 value 放到 map 中，key为baseIndex + offset
     *
     * @param featureMap 要put的map
     * @param baseIndex  基础编号
     * @param offset     编号偏移量
     * @param value      值
     */
    static void putMap(Map<Integer, Double> featureMap, double value, int baseIndex, Double offset) {
        if (null == offset) {
            return;
        }

        putMap(featureMap, value, baseIndex, offset.intValue());
    }

    /**
     * 将 value 放到 map 中，key为baseIndex + offset
     *
     * @param featureMap 要put的map
     * @param baseIndex  基础编号
     * @param offset     编号偏移量
     * @param value      值
     */
    static void putMap(Map<Integer, Double> featureMap, double value, int baseIndex, int offset) {
        if (offset >= 0) {
            featureMap.put(baseIndex + offset, value);
        }
    }

    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param value      值
     * @param index      基础编号
     */
    static void putMap(Map<Integer, Object> featureMap, double value, int index) {
        featureMap.put(index, value);
    }

    static void putMap(Map<Integer, Object> featureMap, double value, Integer index) {
        featureMap.put(index, value);
    }

    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param value      值
     * @param index      基础编号
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int index) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }


    /**
     * 将 value 放到 map 中，key为 index ,校验 value 值
     *
     * @param featureMap 要put的map
     * @param index      基础编号
     * @param value      值
     */
    static void putMap(Map<Integer, Object> featureMap, Double value, int index,
                       Function<Double, Boolean> putCondition) {
        if (putCondition.apply(value)) {
            featureMap.put(index, value);
        }
    }

    static void putMap(Map<Integer, Object> featureMap, String value, int index,
                       Function<String, Boolean> putCondition) {
        if (putCondition.apply(value)) {
            featureMap.put(index, value);
        }
    }



    /**
     * 酒店-用户数据交叉特征， 计算比例
     *
     * @param featureMap put 特征map
     * @param index      map key
     * @param userValue  用户value
     * @param userCheck  用户value 校验逻辑
     * @param hotelValue 酒店value
     * @param hotelCheck 酒店value 校验逻辑
     */
    static void putCrossRate(Map<Integer, Object> featureMap, int index,
                             double userValue, Function<Double, Boolean> userCheck,
                             Double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, (userValue - hotelValue) / userValue);
        }
    }

    static void putCrossRate(Map<Integer, Object> featureMap, Integer index,
                             Double userValue, Function<Double, Boolean> userCheck,
                             double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, roundDouble((userValue - hotelValue) / userValue, 7));
        }
    }

    static void putCrossRate(Map<Integer, Object> featureMap, int index,
                             double userValue, Function<Double, Boolean> userCheck,
                             double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, (userValue - hotelValue) / userValue);
        }
    }

    static void putCrossRate(Map<Integer, Object> featureMap, Integer index,
                             Double userValue, Function<Double, Boolean> userCheck,
                             Double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, roundDouble((userValue - hotelValue) / userValue, 7));
        }
    }

    /**
     * 酒店-用户数据交叉特征， 计算比例
     *
     * @param featureMap put 特征map
     * @param index      map key
     * @param userValue  用户value
     * @param userCheck  用户value 校验逻辑
     * @param hotelValue 酒店value
     * @param hotelCheck 酒店value 校验逻辑
     */
    static void putCrossDiff(Map<Integer, Object> featureMap, Integer index,
                             double userValue, Function<Double, Boolean> userCheck,
                             Double hotelValue, Function<Double, Boolean> hotelCheck) {
        if (userCheck.apply(userValue) && hotelCheck.apply(hotelValue)) {
            featureMap.put(index, userValue - hotelValue);
        }
    }

    /**
     *
     * @param featureMap
     * @param platform
     * @param adrType
     * @param iosType
     * @param oldValue 兼容旧日志值获取
     * @return
     */
    public static double getValueByPlatform(EnumMap<FeatureType, Double> featureMap, Platform platform, FeatureType adrType, FeatureType iosType, double oldValue){
        if (oldValue != 0) {
            return oldValue;
        }

        if(MapUtils.isEmpty(featureMap)){
            return 0;
        }
        if(platform == Platform.ADR){
            return featureMap.getOrDefault(adrType, 0.0d);
        }
        return featureMap.getOrDefault(iosType, 0.0d);
    }

    /**
     * double value 存到 String 类型的map 中
     * @param featureMap
     * @param value
     * @param key
     */
    static void putDoubleToStringMap(Map<Integer, String> featureMap, Double value, double defaultDouble, int key ) {
        if (value == null){
            featureMap.put(key,String.valueOf(defaultDouble));
        } else {
            double v = Numbers.roundStringDouble(value, FeatureConstants.decimals_num);
            featureMap.put(key,String.valueOf(v));
        }

    }

    static void putDoubleToStringMap(Map<Integer, String> featureMap, Double value, int key ) {

        double v = Numbers.roundStringDouble(value, FeatureConstants.decimals_num);
        featureMap.put(key,String.valueOf(v));
    }

    /**
     * int value 存到 String 类型的map 中
     * @param featureMap
     * @param value
     * @param key
     */
    static void putIntegetToStringMap(Map<Integer, String> featureMap, Integer value, int defaultint, int key ) {
        if (value == null){
            featureMap.put(key,String.valueOf(defaultint));
        } else {
            featureMap.put(key,String.valueOf(value));
        }

    }

}
