package com.qunar.search.common.model.convertor;

import com.qunar.search.common.bean.UserFastFilterProfile;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FastFilterFeatureIndexEnum;
import com.qunar.search.common.enums.FastFilterItemFeatureType;
import com.qunar.search.common.model.feature.FastFilterModelItemFeature;
import com.qunar.search.common.model.feature.FastFilterModelRequestFeature;
import com.qunar.search.common.model.feature.FastFilterModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMapNotNull;


/**
 * <AUTHOR>
 * @create 2023-09-22 下午7:10
 * @DESCRIPTION 添加部分新特征v1
 **/
@Component
public class FastFilterFeatureConvertorV1 implements FastFilterSortConvertor {


    public Map<Integer, Object> convertObject(FastFilterModelRequestFeature requestFeature, FastFilterModelUserFeature userFeature, FastFilterModelItemFeature filterFeature) {

        Map<Integer, Object> featReturn = new HashMap<>();


        if (null != userFeature.getUserFastFilterProfile()) {
            UserFastFilterProfile userFastFilterProfile = userFeature.getUserFastFilterProfile();
            // 新老用户
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_1.getIndex(), (double)userFastFilterProfile.getNewUserFlag());
            // 用户等级
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_2.getIndex(), (double)userFastFilterProfile.getUserLevel());
            // 是否学生
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_3.getIndex(), (double)userFastFilterProfile.getStudentFlag());
            // 性别
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_4.getIndex(), (double)userFastFilterProfile.getGender());
            // 年龄
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_5.getIndex(), (double)userFastFilterProfile.getAge());
            // 1年订单的平均价格
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_6.getIndex(), userFastFilterProfile.getOrderAvgPrice1y());
            // 1年订单的最大价格
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_7.getIndex(), userFastFilterProfile.getOrderMaxPrice1y());
            // 1年订单的最小价格
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_8.getIndex(), userFastFilterProfile.getOrderMinPrice1y());
            // 1年订单数
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_9.getIndex(), userFastFilterProfile.getOrderNum1y());
            // 用户的酒店星级偏好
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_10.getIndex(), userFastFilterProfile.getHotelGradePrefer());
            // 用户一年内的商务订单占比
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_11.getIndex(), userFastFilterProfile.getOrderBusRate1y());
            // 用户一年内的节假日订单占比
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_12.getIndex(), userFastFilterProfile.getOrderHolidaysRate1y());
            // 用户一年内的周末订单占比
            featReturn.put(FastFilterFeatureIndexEnum.INDEX_13.getIndex(), userFastFilterProfile.getOrderWdayRate1y());

            if (StringUtils.isNotEmpty(filterFeature.getFilterName())) {
                // 用户最近点击时间的排序
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_14.getIndex(), userFastFilterProfile.getClickTimeRank().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户点击筛选项的次数排序
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_15.getIndex(), userFastFilterProfile.getClickCntRank().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户7天点击筛选项的次数
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_16.getIndex(), userFastFilterProfile.getClickCnt7d().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户1年点击筛选项的次数
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_17.getIndex(), userFastFilterProfile.getClickCnt1y().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户7天浏览筛选项的次数
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_18.getIndex(), userFastFilterProfile.getShowCnt7d().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户1年浏览筛选项的次数
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_19.getIndex(), userFastFilterProfile.getShowCnt1y().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户7天对筛选项的ctr
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_20.getIndex(), userFastFilterProfile.getCtr7d().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
                // 用户1年对筛选项的ctr
                featReturn.put(FastFilterFeatureIndexEnum.INDEX_21.getIndex(), userFastFilterProfile.getCtr1y().getOrDefault(filterFeature.getFilterName(), FeatureConstants.ZERO));
            }
        }

        EnumMap<FastFilterItemFeatureType, Double> offlineFeature = filterFeature.getOfflineFeature();
        if (MapUtils.isNotEmpty(offlineFeature)) {
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_22.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_23.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_24.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_25.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_26.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_27.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_28.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_29.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_30.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_31.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_32.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_UV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_33.getIndex(), offlineFeature.get(FastFilterItemFeatureType.ALL_CLICK_PV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_34.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_35.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_36.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_37.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_38.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_39.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_40.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_41.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_42.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_43.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_44.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_UV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_45.getIndex(), offlineFeature.get(FastFilterItemFeatureType.COMPREHENSIVE_LOC_CLICK_PV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_46.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_47.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RATE_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_48.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_49.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RANK_1_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_50.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_51.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RATE_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_52.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_53.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RANK_7_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_54.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_55.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RATE_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_56.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_UV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_57.getIndex(), offlineFeature.get(FastFilterItemFeatureType.FAST_CLICK_PV_RANK_30_DAY));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_58.getIndex(), offlineFeature.getOrDefault(FastFilterItemFeatureType.COMPREHENSIVE_FILTER_OUV, FeatureConstants.ZERO));
            putMapNotNull(featReturn, FastFilterFeatureIndexEnum.INDEX_59.getIndex(), offlineFeature.getOrDefault(FastFilterItemFeatureType.LOCATION_FILTER_OUV, FeatureConstants.ZERO));
        }

        return featReturn;
    }
}
