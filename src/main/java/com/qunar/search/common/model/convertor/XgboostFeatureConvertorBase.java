package com.qunar.search.common.model.convertor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.DateUtil;
import com.qunar.search.common.util.ZhiXin;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.enums.FeatureType.ADR_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_SHOW_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.HOLIDAY_PRICE_WEIGHT;
import static com.qunar.search.common.enums.FeatureType.IOS_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_SHOW_PV_7_DAY;
import static com.qunar.search.common.model.convertor.ConvertorBase.getValueByPlatform;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

@Component
public class XgboostFeatureConvertorBase extends AbstractSortFeatureConvertor {


    //分割符号 | 下，第7位为价格权重数据,一共两位，第一位降权，第二位加权
    public static final int DEFAULT_PRICE_WEIGHT = 1;

    public static final int FEATURE_LENGTH = 126;
    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE - 10;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        return convertDouble(requestFeature, userFeature, hotelFeature);
    }

    public Map<Integer, Object> convertDouble(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        SortScene scene = requestFeature.getSortScene();
        Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders();
        Map<Integer, Integer> dangciSort = userFeature.getDangciSort();
        List<String> realTimeFilterDangciTimeSort = userFeature.getRealTimeFilterDangciTimeSort();
        List<String> realTimeFilterBrandTimeSort = userFeature.getRealTimeFilterBrandTimeSort();
        List<String> realTimeFilterTradingTimeSort = userFeature.getRealTimeFilterTradingTimeSort();
        List<String> realTimeQueryTimeSort = userFeature.getRealTimeQueryTimeSort();
        Map<String, Set<String>> realTimeQueryCandidates = userFeature.getRealTimeQueryCandidates();
        double orderAvgPrice = userFeature.getOrderAvgPrice();

        double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();
        List<double[]> realTimeFilterPriceTimeSort = userFeature.getRealTimeFilterPriceTimeSort();
        int price = hotelFeature.getMobileMinAvailablePrice();
        if (requestFeature.isPriceWeightDate() && MapUtils.isNotEmpty(offlineFeature)) {
            double cityWeight = offlineFeature.getOrDefault(HOLIDAY_PRICE_WEIGHT, 1.0d);
            if (cityWeight != DEFAULT_PRICE_WEIGHT) {
                clickAvgPrice = valueWeight(clickAvgPrice, cityWeight);
                price = (int)valueWeight(price, cityWeight);
                realTimeFilterPriceTimeSort = priceWeightFilterList(realTimeFilterPriceTimeSort, cityWeight);
            }
        }

        // 指定初始容量，避免扩容
        Map<Integer, Object> featureReturn = Maps.newHashMapWithExpectedSize(RankSystemConfig.getXgbFeatureSize());

        double featureVect[] = new double[FEATURE_LENGTH];

        if (hotelFeature == null) {
            return featureReturn;
        }
        String seq = hotelFeature.getHotelSeq();

        //用户过去半年预订该酒店的次数。
        if (historyOrders.containsKey(seq)) {
            int count = historyOrders.get(seq).size();
            if ( count<DEFAULT_MAX_SUB10 ) {
                featureVect[1] = count;
            } else {
                featureVect[1] = Double.NaN;
            }
        }

        int pos = StringUtils.lastIndexOf(seq, "_");
        if (pos > 0) {
            Map<String, UserShowOrderData.OrderInfo> cityLastOrder = userFeature.cityLastOrder;
            UserShowOrderData.OrderInfo orderInfo = cityLastOrder.get(StringUtils.substring(seq,0, pos));
            if (orderInfo != null && StringUtils.equals(orderInfo.hotelSeq, seq)) {
                featureVect[2] = 1;
            }

        }

        UserShowOrderData.PvInfo pvInfo = userFeature.hisShowSt.get(seq);
        if (pvInfo != null) {
            // 近三周用户是否到达酒店的下单页
            if (pvInfo.isBookingPageOpened) {
                featureVect[3] = 1;
            }

            //近三周用户是否在酒店下单
            if (pvInfo.isOrdered) {
                featureVect[4] = 1;
            }
        }

        // 近三周用户浏览和点击该酒店的次数
        long num_view = 0;
        long num_click = 0;
        if (pvInfo != null) {
            num_view = pvInfo.pv;
            num_click = pvInfo.cl;
        }

        if (num_view < DEFAULT_MAX_SUB10 ) {
            featureVect[5] = num_view;
        }else {
            featureVect[5] = Double.NaN;
        }

        if ( num_click<DEFAULT_MAX_SUB10 ) {
            featureVect[6] = num_click;
        }else {
            featureVect[6] = Double.NaN;
        }

        // 用户点击档次排名[7-13]
        int dangci = hotelFeature.getDangci();
        if (dangciSort.containsKey(dangci)) {
            int rank = dangciSort.get(dangci);
            rank = rank >5 ? 5 : rank;
            featureVect[7 + rank] = 1;

        } else {
            featureVect[13] = 1;
        }

        //点击价格
        if (clickAvgPrice != 0 && clickAvgPrice<DEFAULT_MAX_SUB10) {
            featureVect[14] = clickAvgPrice;
        } else {
            featureVect[14] = Double.NaN;
        }

        //[15-20]
        if (realTimeFilterDangciTimeSort != null && !realTimeFilterDangciTimeSort.isEmpty() && dangci != 0) {
            int hitIndex = 0;
            for (int i = 0; i < realTimeFilterDangciTimeSort.size(); ++i) {
                if (realTimeFilterDangciTimeSort.get(i).contains(String.valueOf(dangci))) {
                    hitIndex = i + 1;
                    hitIndex = hitIndex > 5 ? 5 : hitIndex;
                    break;
                }
            }
            if (hitIndex > 0) {
                featureVect[15 + hitIndex] = 1;
            }
        }

        //用户实时品牌[21-26]
        String brand = hotelFeature.getHotelBranch();
        if (realTimeFilterBrandTimeSort != null && !realTimeFilterBrandTimeSort.isEmpty() && brand != null) {
            int hitIndex = 0;
            for (int i = 0; i < realTimeFilterBrandTimeSort.size(); ++i) {
                if (realTimeFilterBrandTimeSort.get(i).contains(brand)) {
                    hitIndex = i + 1;
                    hitIndex = hitIndex > 5 ? 5 : hitIndex;
                    break;
                }
            }
            if (hitIndex > 0) {
                featureVect[21 + hitIndex] = 1;
            }
        }

        //用户实时商圈筛选[27-32]
        String tradingArea = hotelFeature.getTradingArea();
        if (realTimeFilterTradingTimeSort != null && !realTimeFilterTradingTimeSort.isEmpty() && tradingArea != null) {
            int hitIndex = 0;
            for (int i = 0; i < realTimeFilterTradingTimeSort.size(); ++i) {
                if (realTimeFilterTradingTimeSort.get(i).contains(tradingArea)) {
                    hitIndex = i + 1;
                    hitIndex = hitIndex > 5 ? 5 : hitIndex;
                    break;
                }
            }
            if (hitIndex > 0) {
                featureVect[27 + hitIndex] = 1;
            }
        }

        //用户实时价格筛选[33-38]

        if (realTimeFilterPriceTimeSort != null && !realTimeFilterPriceTimeSort.isEmpty()) {
            int hitIndex = 0;
            for (int i = 0; i < realTimeFilterPriceTimeSort.size(); ++i) {
                double[] range = realTimeFilterPriceTimeSort.get(i);
                double low = range[0];
                double up = range[1];
                if (price >= low && price <= up) {
                    hitIndex = i + 1;
                    hitIndex = hitIndex > 5 ? 5 : hitIndex;
                    break;
                }
            }
            if (hitIndex > 0) {
                featureVect[33 + hitIndex] = 1;
            }
        }

        //用户实时query[39-44]
        // 用户实时query
        if (MapUtils.isNotEmpty(realTimeQueryCandidates)) {
            if (realTimeQueryTimeSort != null && !realTimeQueryTimeSort.isEmpty()) {
                int hitIndex = 0;
                for (int i = 0; i < realTimeQueryTimeSort.size(); ++i) {
                    Set<String> candidates = realTimeQueryCandidates.get(realTimeQueryTimeSort.get(i));
                    if (candidates != null && candidates.contains(seq)) {
                        hitIndex = i + 1;
                        hitIndex = hitIndex > 5 ? 5 : hitIndex;
                        break;
                    }
                }
                if (hitIndex > 0) {
                    featureVect[39 + hitIndex] = 1;
                }
            }
        } else {
            if (hotelFeature.getHitKeyWordIndex() > 0) {
                featureVect[39 + hotelFeature.getHitKeyWordIndex()] = 1;
            }
        }

        // 酒店历史订单价格均值与用户历史订单价格均值的相对距离[45-46]
        if (orderAvgPrice > 0 && orderAvgPrice<DEFAULT_MAX_SUB10 ) {
            double orderMeanPrice = getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_AVG_PRICE_180_DAY,
                    IOS_AVG_PRICE_180_DAY, hotelFeature.getPlatformOrderMeanPrice());
            if (orderMeanPrice > 0 && orderMeanPrice<DEFAULT_MAX_SUB10) {
                double diff = orderMeanPrice- orderAvgPrice;
                double disScore = diff / orderAvgPrice;
                featureVect[45] = disScore;
                featureVect[45 + 1] = diff;
            } else {
                featureVect[45] = Double.NaN;
                featureVect[45 + 1] = Double.NaN;
            }
        } else {
            featureVect[45] = Double.NaN;
            featureVect[45 + 1] = Double.NaN;
        }

        // 酒店相似度 用户历史订单档次命中当前酒店[47-56]
        String city = getCityFromSeq(seq);
        int idx = 0;
        if (userFeature.getHistoryDangciMap().containsKey(dangci)) {
            idx = Math.min(userFeature.getHistoryDangciMap().get(dangci), 9);
            featureVect[47 + idx] = 1;
        }

        //酒店相似度 用户历史订单星级命中当前酒店星级[57-66]
        int star = hotelFeature.getStars();
        if (userFeature.getHistoryStarMap().containsKey(star)) {
            idx = Math.min(userFeature.getHistoryStarMap().get(star), 9);
            featureVect[57 + idx] = 1;
        }

        //酒店相似度 用户历史订单品牌次命中当前酒店品牌[67-76]
        if (StringUtils.isNotBlank(brand)) {
            if (userFeature.getHistoryBrandMap().containsKey(brand)) {
                idx = Math.min(userFeature.getHistoryBrandMap().get(brand), 9);
                featureVect[67 + idx] = 1;
            }
        }

        //酒店相似度 用户历史订单类型命中当前酒店类型[77-86]
        String[] types = hotelFeature.getHotelType();
        if (types != null) {
            for (String t1 : types) {
                if (userFeature.getHistoryTypeMap().containsKey(t1)) {
                    idx = Math.min(userFeature.getHistoryTypeMap().get(t1), 9);
                    featureVect[77 + idx] = 1;
                }
            }
        }

        //酒店相似度 用户历史订单商圈命中当前酒店商圈[87-96]
        if (StringUtils.isNotBlank(tradingArea)) {
            if (city != null) {
                String cityTradingArea = city + tradingArea;
                if (userFeature.getHistoryTradingAreaMap().containsKey(cityTradingArea)) {
                    idx = Math.min(userFeature.getHistoryTradingAreaMap().get(cityTradingArea), 9);
                    featureVect[87 + idx] = 1;
                }
            }
        }

        // 4天之前收藏过该酒店, 4天之内收藏过该酒店[97-98]
        if (userFeature.getFavoriteHotels().containsKey(seq)) {
            int requestDay = requestFeature.getRequestDate();
            int logDay4 = DateUtil.addDay(requestDay, -4);
            UserHotel.FavoriteHotel favoriteHotel = userFeature.getFavoriteHotels().get(seq);
            if (favoriteHotel.getFavoriteDate() < logDay4) {
                featureVect[97] = 1;
            } else {
                featureVect[98] = 1;
            }
        }

        // 酒店近3周的订单量[99]
        int v_num_orders = (int)getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_ORD_PV_21_DAY,
                IOS_ORD_PV_21_DAY, hotelFeature.getPlatformOrderPv21());
        if (v_num_orders<DEFAULT_MAX_SUB10) {
            featureVect[99] = v_num_orders;
        }else {
            featureVect[99] = Double.NaN;
        }

        //酒店评分
        double commentScore = hotelFeature.getCommentScore();
        featureVect[100] = commentScore;

        //"酒店折扣"[101-102]
        double originalPrice = hotelFeature.getOriginalPrice();
        double discountRate = (originalPrice - price) / (originalPrice + 1);
        if (price != 0 && originalPrice != 0 &&
                originalPrice<DEFAULT_MAX_SUB10 &&
                price<DEFAULT_MAX_SUB10) {
            featureVect[101] = price;
            featureVect[101 + 1] = originalPrice - price;
        } else {
            featureVect[101] = Double.NaN;
            featureVect[101 + 1] = Double.NaN;
        }

        // 酒店转化率特征
        double showpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_SHOW_PV_7_DAY, IOS_SHOW_PV_7_DAY, hotelFeature.getPlatformShowPv7());
        double orderpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_ORD_PV_7_DAY, IOS_ORD_PV_7_DAY, hotelFeature.getPlatformOrderPv7());
        if (showpv7 > 0) {
            double rate = ZhiXin.getZhiXin(orderpv7 / showpv7, showpv7);
            featureVect[103] = rate;
        } else {
            featureVect[103] = Double.NaN;
        }

        //酒店在关键词下订单量
        if (scene.match(SortScene.POI_KEY)) {
            int keyWordOrderCount = hotelFeature.getKeyWordOrderCount();
            if( keyWordOrderCount<DEFAULT_MAX_SUB10) {
                featureVect[104] = keyWordOrderCount;
            } else {
                featureVect[104] = Double.NaN;
            }
        } else {
            featureVect[104] = Double.NaN;
        }

        //酒店到poi的距离
        if (scene.match(SortScene.POI_KEY)) {
            double poiDistance = hotelFeature.getPoiHotelDistance();
            if( poiDistance<DEFAULT_MAX_SUB10) {
                featureVect[105] = poiDistance;
            } else {
                featureVect[105] = Double.NaN;
            }
        } else {
            featureVect[105] = Double.NaN;
        }

        //同城或商圈场景下，酒店距离用户或者坐标的距离。
        if (scene.match(SortScene.SAME_CITY) || scene.match(SortScene.NEARBY)) {
            double coorDistance = hotelFeature.getUserHotelDistance();

            if( coorDistance<DEFAULT_MAX_SUB10) {
                featureVect[106] = coorDistance;
            } else {
                featureVect[106] = Double.NaN;
            }
        } else {
            featureVect[106] = Double.NaN;
        }

        //实时点击档次排序[107-113]
        dangciSort = userFeature.getRealTimeDangciSort();
        if (dangciSort.containsKey(dangci)) {
            int rank = dangciSort.get(dangci);

            rank = rank > 5 ? 5 : rank;
            featureVect[107 + rank] = 1;
        } else {
            featureVect[113] = 1;
        }

        for (int i=1; i<featureVect.length; i++) {
            double value = featureVect[i];

            //删除null值
            if (!Double.isNaN(value)){
                featureReturn.put(i, value);
            }
        }

        return featureReturn;
    }


    public double valueWeight(double value, double weight) {
        return Math.round(value * weight * 10) / 10.0;
    }

    public List<double[]> priceWeightFilterList(List<double[]> realTimeFilterPriceTimeSort, double weight) {
        if (CollectionUtils.isEmpty(realTimeFilterPriceTimeSort)) {
            return Lists.newArrayList();
        }

        List<double[]> realTimeFilterPriceTimeSortWeight = new ArrayList<>(realTimeFilterPriceTimeSort.size());
        for (int i = 0; i < realTimeFilterPriceTimeSort.size(); ++i) {
            double[] range = realTimeFilterPriceTimeSort.get(i);
            double low = range[0];
            double up = range[1];

            low = valueWeight(low, weight);
            up = valueWeight(up, weight);

            double[] rangeWeight = new double[]{low, up};

            realTimeFilterPriceTimeSortWeight.add(rangeWeight);
        }
        return realTimeFilterPriceTimeSortWeight;
    }
}
