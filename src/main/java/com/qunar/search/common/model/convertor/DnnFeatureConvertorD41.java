package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.feature.GroupFeature;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;



/**
 * D41 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD41 extends DnnFeatureConvertorD40 {



    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 411 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        try {
            GroupFeature groupFeature = requestFeature.getGroupFeature();

            if (groupFeature == null){

                groupFeature = new GroupFeature();
                requestFeature.setGroupFeature(groupFeature);
            }
            resultMap.put(FeatureIndexEnum.D41_412.getIndex(), groupFeature.getUserClickHotel3WeekMaxCount());
            resultMap.put(FeatureIndexEnum.D41_413.getIndex(), groupFeature.getUserClickHotelRealTimeMaxRate());
            resultMap.put(FeatureIndexEnum.D41_414.getIndex(), groupFeature.getHotelMaxPrice());
            resultMap.put(FeatureIndexEnum.D41_415.getIndex(), groupFeature.getHotelAvgPrice());
            resultMap.put(FeatureIndexEnum.D41_416.getIndex(), groupFeature.getHotelMinPrice());
            resultMap.put(FeatureIndexEnum.D41_417.getIndex(), groupFeature.getPoiHotelMinDistance());
            resultMap.put(FeatureIndexEnum.D41_418.getIndex(), groupFeature.getUserHotelMinDistance());
            resultMap.put(FeatureIndexEnum.D41_419.getIndex(), groupFeature.getHotelMinSellPriceDivideOrgPrice());
            resultMap.put(FeatureIndexEnum.D41_420.getIndex(), groupFeature.getHotel24hOrderMedianPrice());
            resultMap.put(FeatureIndexEnum.D41_421.getIndex(), groupFeature.getHotelSearchMaxCount6week());
            resultMap.put(FeatureIndexEnum.D41_422.getIndex(), groupFeature.getHotelMaxCommentCount());
            resultMap.put(FeatureIndexEnum.D41_423.getIndex(), groupFeature.getHotelMaxClickCount());
            resultMap.put(FeatureIndexEnum.D41_424.getIndex(), groupFeature.getHotel7DayMaxCtr());
            resultMap.put(FeatureIndexEnum.D41_425.getIndex(), groupFeature.getHotel3MonthOrderCount());
            resultMap.put(FeatureIndexEnum.D41_426.getIndex(), groupFeature.getHotel3MonthMaxS2o());
            resultMap.put(FeatureIndexEnum.D41_427.getIndex(), groupFeature.getHotel60DayMaxPVS2o());

        } catch (Exception e) {
            log.error("DnnFeatureConvertorD41 转换特征出错" , e);
        }

        return resultMap;
    }

}
