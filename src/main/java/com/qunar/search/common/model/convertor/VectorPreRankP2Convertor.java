package com.qunar.search.common.model.convertor;

import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 特征参照：
 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=421249519
 * 增加 以下特征：
 * 10:历史订单平均价格
 * 11:当前城市历史订单集合
 * 12:历史订单档次集合
 * 13:历史订单品牌集合
 */

@Component
public class VectorPreRankP2Convertor extends VectorPreRankP1Convertor {

    public Map<Integer, String> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {


        Map<Integer, String> featuresMap = super.convert(requestFeature, userFeature, hotelFeature);

        addListFeature(requestFeature,userFeature,featuresMap);


        return featuresMap;
    }

    /**
     * orderHotels put 默认值
     * @param requestFeature
     * @param userFeature
     * @param embFeatMap
     */


    private void addListFeature(ModelRequestFeature requestFeature,ModelUserFeature userFeature,Map<Integer, String> embFeatMap) {

        // 初始化
        embFeatMap.put(10,"0");
        embFeatMap.put(11,"0");
        embFeatMap.put(12,"0");
        embFeatMap.put(13,"0");

        double historyOrderPrice = 0.0;    // 历史订单平均价格

        StringBuilder currentCityHistoryOrders = new StringBuilder(); // 当前城市历史订单
        StringBuilder historyOrdersDangcis = new StringBuilder(); // 历史订单档次集合
        StringBuilder historyOrderBrands = new StringBuilder(); // 历史订单品牌集合

        String cityUrl = requestFeature.getCityUrl(); // 当前城市

        Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders(); // 历史订单


        // 历史订单
        if (!historyOrders.isEmpty()){
            int orderNum = 0;
            double sumprice = 0;
            for (String orderHotelSeq:historyOrders.keySet()) {
                List<UserHotel.OrderHotel> orderHotels = historyOrders.get(orderHotelSeq);

                for (UserHotel.OrderHotel orderHotel:orderHotels) {
                    sumprice += orderHotel.getPrice()/orderHotel.getRoomNum();

                    historyOrdersDangcis.append(",").append(orderHotel.getDangci());
                    if (orderHotel.getBrand() != null && !StringUtils.equals(orderHotel.getBrand(),"null") ){
                        historyOrderBrands.append(",").append(orderHotel.getBrand());
                    }

                    if(orderHotelSeq.contains(cityUrl)){
                        // 当前城市
                        currentCityHistoryOrders.append(",").append(orderHotelSeq);
                    }

                    orderNum ++;
                }

            }

            historyOrderPrice = sumprice/orderNum;


            embFeatMap.put(10,historyOrderPrice + "");
            if (currentCityHistoryOrders.length()>1){
                embFeatMap.put(11,currentCityHistoryOrders.toString().substring(1));
            }
            if (historyOrdersDangcis.length()>1){
                embFeatMap.put(12,historyOrdersDangcis.toString().substring(1));
            }
            if ( historyOrderBrands.length()>1 ){
                embFeatMap.put(13,historyOrderBrands.toString().substring(1));
            }

        }


    }



}
