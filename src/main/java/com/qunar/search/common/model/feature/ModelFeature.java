package com.qunar.search.common.model.feature;

import java.io.Serializable;
import java.util.*;

import com.google.common.collect.Sets;
import com.qunar.search.common.bean.PageInfo;
import com.qunar.search.common.model.feature.operator.FeatureLogOperator;
import static com.qunar.search.common.model.feature.operator.DataType.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-05-14 下午9:30
 * @DESCRIPTION 模型使用到的特征对象，用户特征转换和日志打印
 **/
@Data
public class ModelFeature implements Serializable {

    /**
     * 该请求对应的首屏请求trace
     */
    String requestTime;

    /**
     * 请求屏信息
     */
    PageInfo pageInfo;

    /**
     * 模型用到的请求特征
     */
    ModelRequestFeature requestFeature;

    /**
     * 模型用到的用户特征
     */
    ModelUserFeature userFeature;

    /**
     * 模型用到的酒店特征
     */
    List<ModelHotelFeature> hotelFeature;

    /**
     * 头部打印一部分酒店，开关控制打印本次返回酒店还是top n
     */
    List<ModelHotelFeature> topInOrder;

    /**
     * 基于 topInOrder ，之后顺序打印一批酒店
     */
    List<ModelHotelFeature> middleInOrder;

    /**
     * 基于 middleInOrder, 之后随机打印一批酒店
     */
    List<ModelHotelFeature> bottomRandom;

    /**
     * 缩减数据，减少日志量
     */
    public void reduce(boolean reduceHotel) {
        FeatureLogOperator.reduceAll(requestFeature, userFeature, allHotels(), USER);
        if (reduceHotel) {
            FeatureLogOperator.reduceAll(requestFeature, userFeature, allHotels(), HOTEL);
        }
    }

    /**
     * 数据还原
     */
    public void recover() {
        FeatureLogOperator.recoverAll(requestFeature, userFeature, allHotels());
    }

    /**
     * 多个酒店列表合并
     */
    private Set<ModelHotelFeature> allHotels() {
        Set<ModelHotelFeature> logHotels = Sets.newHashSet();
        logHotels.addAll(Optional.ofNullable(hotelFeature).orElse(Collections.emptyList()));
        logHotels.addAll(Optional.ofNullable((topInOrder)).orElse(Collections.emptyList()));
        logHotels.addAll(Optional.ofNullable((middleInOrder)).orElse(Collections.emptyList()));
        logHotels.addAll(Optional.ofNullable((bottomRandom)).orElse(Collections.emptyList()));
        return logHotels;
    }
}

