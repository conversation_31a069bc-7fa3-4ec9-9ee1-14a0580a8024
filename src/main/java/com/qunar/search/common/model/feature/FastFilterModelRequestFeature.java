package com.qunar.search.common.model.feature;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.model.memory.DataVersion;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-09-15
 * @DESCRIPTION 算分使用的请求数据
 **/
@Data
public class FastFilterModelRequestFeature implements Serializable {
    /**
     * 城市 -- 来自searchParam
     */
    private String cityUrl;

    /**
     * 用户名
     * -- 来自searchParam
     */
    private String userName;

    /**
     * 用户realUid
     */
    private String realUid;

    /**
     * 用户gid
     */
    private String gid;

    /**
     * 入住日期
     */
    private String checkIn;

    /**
     * 离店日期
     */
    private String checkOut;

    /**
     * 用户所在城市code
     */
    private String userGpsCityCode;

    /**
     * 用户搜索query
     */
    private String query;

    /**
     * 是否新客
     */
    private int isNewUser;

    /**
     * 请求发生当天日期
     */
    private int requestDate;

    /**
     * query词下的筛选项点击次数
     */
    Map<String, Double> queryFilterMap = Collections.emptyMap();

    /**
     * 请求时间
     */
    @JsonIgnore
    private DateTime requestDateTime;

    /**
     * requestDateTime
     */
    public long getRequestTime() {
        if (requestDateTime != null) {
            return requestDateTime.getMillis();
        } else {
            return 0;
        }
    }

    public void setRequestTime(long milliSecond) {
        requestDateTime = new DateTime(milliSecond);
    }

    /**
     * 请求时间是周几
     */
    private int dayOfWeek;

    /**
     * 请求小时
     */
    private int hourOfay;

    /**
     * 本次请求使用的快筛内存数据版本信息
     */
    private String filterOfflineFeatureVersion;

    /**
     * 入住和请求相差的天数
     */
    private int nowCheckInDiff;

    /**
     * 请求和离店相差的天数
     */
    private int nowCheckOutDiff;

    /**
     * 入住和离店相差的天数
     */
    private int checkInOutDiff = 1;

    /**
     * 是否节假日
     */
    private boolean isHoliday;
}
