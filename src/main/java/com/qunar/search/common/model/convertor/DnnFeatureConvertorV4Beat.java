package com.qunar.search.common.model.convertor;

import com.google.common.collect.Sets;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.model.convertor.ConvertorObject.putCrossDiff;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMap;

/**
 *
 * 基于预估的报价
 */
@Component
public class DnnFeatureConvertorV4Beat extends XgboostD29FeatureConvertor {
    private final static Integer[] XGB_FEATURE_INDEX = {1, 2, 6, 14, 45, 46, 97, 98, 99,
            100, 101, 102, 103, 104, 105, 106, 114, 115, 119, 123, 125, 132,
            135, 151, 156, 157, 158, 187, 188, 189, 190, 192, 197, 199, 205, 206, 212,
            213, 214, 220, 221, 226, 227, 229, 230, 235, 237, 238, 239, 246, 247, 248,
            249, 250, 264, 287, 288, 289, 290, 291, 292, 298, 299, 300, 303, 304, 305, 306, 307, 308,
            309, 310, 311, 316, 317, 318, 295, 296, 297};

    private final static Set MISS_FEATURE_INDEX = Sets.newHashSet(14, 46, 100, 101, 105,
            189, 246, 247, 248);//156, 157, 158, 187, 188, 106,

    private final static double THRESHOLD = 0.0000001;
    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        if (MapUtils.isNotEmpty(offlineFeature)) {
            //酒店3月内点击率
            putMap(featMap, offlineFeature.get(FeatureType.CTR_3_MONTH), 287);

            //酒店3月内点击率（置信）
            putMap(featMap, offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN), 288);

            //酒店7天点击率
            putMap(featMap, offlineFeature.get(FeatureType.CTR_7_DAY), 289);

            //酒店7天点击率（置信）
            putMap(featMap, offlineFeature.get(FeatureType.CTR_7_DAY_ZHIXIN), 290);

            // 修复D26部分特征错位问题  [292-294]
            Map<Integer, Double> checkInOrderRate = userFeature.getCheckInOrderRate();
            if (null == checkInOrderRate) {
                checkInOrderRate = Collections.emptyMap();
            }

            int checkInNum = requestFeature.getCheckInNum();
            // 用户入住天数
            checkInNum = Math.min(checkInNum, 8);
            if (checkInNum > 0) {
                // 用户入住天数对应的酒店不同天数的订单数 [292]
                int intervalIndex = 2*(checkInNum - 1);
                putMap(featMap,
                        offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + intervalIndex)),
                        292, v -> (v != null && v > 0));
                // 用户入住天数对应的酒店不同天数的订单占比 [293]
                putMap(featMap,
                        offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_RATE_1_DAY.getIndex() + intervalIndex + 1)),
                        293, v -> (v != null && v > 0));
                // 用户入住天数对应的用户过去入住天数的订单占比-酒店入住天数的占比 [299]
                putCrossDiff(featMap, 294, checkInOrderRate.getOrDefault(checkInNum, 0.0), s -> s > 0,
                        offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_RATE_1_DAY.getIndex() + intervalIndex + 1)),
                        v -> (v != null && v > 0));
            }
        }

        // 根据sort报价预估的render报价，替换sort报价, index: 101
        double estimatePrice = hotelFeature.getEstimatedRenderPrice();

        //"酒店折扣"[101-102]
        double originalPrice = hotelFeature.getOriginalPrice();

        // 有效的预估价格
        if (estimatePrice > 0 && estimatePrice < DEFAULT_MAX_SUB100) {
            featMap.replace(101, estimatePrice);
            if (originalPrice != 0 && originalPrice < DEFAULT_MAX_SUB100) {
                featMap.replace(102, originalPrice - estimatePrice);
            }
        }

        // 249 酒店价格-商业区价格
        // 250 (酒店价格-商业区价格)/商业区价格
        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();
        if (!MapUtils.isEmpty(feature)) {
            Double bizZoneMean = feature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
            if (null != bizZoneMean && bizZoneMean > 0.0d) {
                double diff = estimatePrice - bizZoneMean;
                featMap.replace(249, diff);
                featMap.replace(250, Numbers.roundStringDouble(diff / bizZoneMean, FeatureConstants.decimals_num));
            }
        }

        for (Integer index : XGB_FEATURE_INDEX) {
            Object value = featMap.getOrDefault(index, FeatureConstants.ZERO);

            featMap.put(index, value);

            if (MISS_FEATURE_INDEX.contains(index)) {
                double v = (double) value;
                if (index == 246 || index == 247) {
                    featMap.put(-1 * index, Math.abs(v - 0.0) < THRESHOLD || v == -1 ? FeatureConstants.ONE : FeatureConstants.ZERO);
                } else {
                    featMap.put(-1 * index, Math.abs(v - 0.0) < THRESHOLD ? FeatureConstants.ONE : FeatureConstants.ZERO);
                }
            }
        }
        return featMap;
    }



}
