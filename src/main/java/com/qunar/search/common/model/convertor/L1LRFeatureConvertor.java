package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

@Component
public class L1LRFeatureConvertor extends D6LRFeatureConvertor {
    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE -10 ;

    private static final Integer LR_INDEX_951 = 951;

    private static final Integer LR_INDEX_1039 = 1039;

    private static final Integer LR_INDEX_1040 = 1040;

    private static final Integer LR_INDEX_1041 = 1041;

    private static final Integer LR_INDEX_1042 = 1042;

    private static final Integer LR_INDEX_1043 = 1043;

    private static final Integer LR_INDEX_1044 = 1044;

    private static final Integer LR_INDEX_1045 = 1045;

    private static final Integer LR_INDEX_1046 = 1046;

    private static final Integer LR_INDEX_1047 = 1047;

    private static final Integer LR_INDEX_1048 = 1048;

    private static final Integer LR_INDEX_1049 = 1049;

    private static final Integer LR_INDEX_1050 = 1050;

    private static final Integer LR_INDEX_1051 = 1051;

    private static final Integer LR_INDEX_1052 = 1052;

    private static final Integer LR_INDEX_1053 = 1053;

    private static final Integer LR_INDEX_1054 = 1054;

    private static final Integer LR_INDEX_1055 = 1055;

    private static final Integer LR_INDEX_1056 = 1056;

    private static final Integer LR_INDEX_1057 = 1057;

    private static final Integer LR_INDEX_1058 = 1058;

    private static final Integer LR_INDEX_1059 = 1059;

    private static final Integer LR_INDEX_1060 = 1060;

    private static final Integer LR_INDEX_1061 = 1061;

    private static final Integer LR_INDEX_1062 = 1062;

    private static final Integer LR_INDEX_1063 = 1063;

    private static final Integer LR_INDEX_1064 = 1064;

    private static final Integer LR_INDEX_1068 = 1068;

    private static final Integer LR_INDEX_1069 = 1069;

    private static final Integer LR_INDEX_1070 = 1070;

    private static final Integer LR_INDEX_1071 = 1071;

    private static final Integer LR_INDEX_1072 = 1072;

    private static final Integer LR_INDEX_1073 = 1073;

    private static final Integer LR_INDEX_1074 = 1074;

    private static final Integer LR_INDEX_1075 = 1075;

    private static final Integer LR_INDEX_1076 = 1076;

    private static final Integer LR_INDEX_1077 = 1077;

    private static final Integer LR_INDEX_1078 = 1078;

    private static final Integer LR_INDEX_1079 = 1079;

    private static final Integer LR_INDEX_1080 = 1080;

    private static final Integer LR_INDEX_1081 = 1081;

    private static final Integer LR_INDEX_1082 = 1082;

    private static final Integer LR_INDEX_1083 = 1083;

    private static final Integer LR_INDEX_1084 = 1084;

    private static final Integer LR_INDEX_1085 = 1085;

    private static final Integer LR_INDEX_1086 = 1086;

    private static final Integer LR_INDEX_1087 = 1087;

    private static final Integer LR_INDEX_1088 = 1088;

    private static final Integer LR_INDEX_1089 = 1089;

    private static final Integer LR_INDEX_1090 = 1090;

    private static final Integer LR_INDEX_1091 = 1091;

    private static final Integer LR_INDEX_1092 = 1092;

    private static final Integer LR_INDEX_1093 = 1093;

    private static final Integer LR_INDEX_1094 = 1094;


    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        if (RankSystemConfig.isEnableL1LrFeatureConvertOptimize()) {
            Map<Integer, Object> featuresMap = super.convert(requestFeature, userFeature, hotelFeature);
            addNewFeature(userFeature, hotelFeature, featuresMap);
            return featuresMap;
        } else {
            Map<Integer, Object> featuresMap = super.convert(requestFeature, userFeature, hotelFeature);

            featuresMap.putAll(addNewFeature(userFeature, hotelFeature));

            return featuresMap;
        }
    }

    // 增加D20特征
    private Map<Integer, Double> addNewFeature(ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        Map<Integer, Double> featMap = Maps.newHashMap();

        String seq = hotelFeature.getHotelSeq();
        String hotelCity = getCityFromSeq(seq);
        int hotelStars = hotelFeature.getStars();
        String hotelBrand = hotelFeature.getHotelBranch();
        String hotelTrading = hotelFeature.getTradingArea();
        String[] hotelTypes = hotelFeature.getHotelType();
        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();

        int baseIndex = 850;

        //"酒店报价"[951]
        if (hotelPrice != 0 && hotelPrice < DEFAULT_MAX_SUB10) {
            featMap.put(baseIndex + 101, hotelPrice);
        }

        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);

        // 189 酒店档次
        featMap.put(baseIndex + 189, (double)hotelRealGrade);

        // 190 历史点击酒店次数
        featMap.put(baseIndex + 190, (double)userFeature.getUserHistoryClickFeature().getBehaveCount());

        // 191 用户历史点击次数最多的档次与当前酒店档次差
        featMap.put(baseIndex + 191, (double)(hotelRealGrade - userFeature.getUserHistoryClickFeature().getBehaveMaxGrade()));

        // 192 用户历史点击当前酒店档次占比
        featMap.put(baseIndex + 192, userFeature.getUserHistoryClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, 0.0));

        // 193 用户历史点击当前酒店星级占比
        featMap.put(baseIndex + 193, userFeature.getUserHistoryClickFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 194 用户历史点击当前酒店品牌占比
        featMap.put(baseIndex + 194, userFeature.getUserHistoryClickFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));

        // 195 用户历史点击当前酒店商圈占比
        double hisClickTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisClickTradRate += userFeature.getUserHistoryClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 195, hisClickTradRate);

        // 196 用户历史点击当前酒店类型占比
        double historyClickTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserHistoryClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyClickTypeRate += typeRateMap.getOrDefault(type, 0.0);
            }
        }

        featMap.put(baseIndex + 196, historyClickTypeRate);

        // 197 用户点击当前酒店占比
        featMap.put(baseIndex + 197, userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 198 1月（31天）内收藏次数
        featMap.put(baseIndex + 198, (double)userFeature.getUser31DayCollectFeature().getBehaveCount());

        // 199 1月（31天）内收藏酒店最大档次与当前酒店档次差
        featMap.put(baseIndex + 199, (double)(hotelRealGrade - userFeature.getUser31DayCollectFeature().getBehaveMaxGrade()));

        // 200 1月（31天）用户收藏当前酒店档次占比
        featMap.put(baseIndex + 200, userFeature.getUser31DayCollectFeature().getGradeRateMap().getOrDefault(hotelRealGrade, 0.0));

        // 201 1月（31天）用户收藏当前酒店星级占比
        featMap.put(baseIndex + 201, userFeature.getUser31DayCollectFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 202 1月（31天）用户收藏当前酒店品牌占比
        featMap.put(baseIndex + 202, userFeature.getUser31DayCollectFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));

        // 203 1月（31天）用户收藏当前酒店商圈占比
        double hisCollectTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisCollectTradRate += userFeature.getUser31DayCollectFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 203, hisCollectTradRate);

        // 204 1月（31天）用户收藏当前酒店类型占比
        double historyFavoriteTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser31DayCollectFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyFavoriteTypeRate += typeRateMap.getOrDefault(type, 0.0);
            }
        }
        featMap.put(baseIndex + 204, historyFavoriteTypeRate);

        // 205 用户实时点击酒店次数
        featMap.put(baseIndex + 205, (double)userFeature.getUserRealtimeClickFeature().getBehaveCount());

        // 206 用户实时点击次数最多的档次与当前酒店档次差
        featMap.put(baseIndex + 206, (double)(hotelRealGrade - userFeature.getUserRealtimeClickFeature().getBehaveMaxGrade()));

        // 207 用户实时点击当前酒店档次占比
        featMap.put(baseIndex + 207, userFeature.getUserRealtimeClickFeature().getGradeRateMap().getOrDefault(hotelRealGrade, 0.0));

        // 207 用户实时点击当前酒店星级占比
        featMap.put(baseIndex + 208, userFeature.getUserRealtimeClickFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 209 用户实时点击当前酒店品牌占比
        featMap.put(baseIndex + 209, userFeature.getUserRealtimeClickFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));

        // 210 用户实时点击当前酒店商圈占比
        double realClickTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            realClickTradRate += userFeature.getUserRealtimeClickFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 210, realClickTradRate);

        // 211 用户实时点击当前酒店类型占比
        double realTimeClickTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserRealtimeClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                realTimeClickTypeRate += typeRateMap.getOrDefault(type, 0.0);
            }
        }
        featMap.put(baseIndex + 211, realTimeClickTypeRate);

        // 212 用户实时点击当前酒店占比
        featMap.put(baseIndex + 212, userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 213 实时query筛选到该酒店的次数
        featMap.put(baseIndex + 213, (double)userFeature.getUserRealtimeQueryFeature().getBehaveCount());

        // 214 实时query筛选到该酒店的query占比
        featMap.put(baseIndex + 214, userFeature.getUserRealtimeQueryFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 218 用户历史180天订单量
        featMap.put(baseIndex + 218, (double)userFeature.getUser180DayOrderFeature().getBehaveCount());

        // 219 用户历史180天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(baseIndex + 219, (double)(hotelRealGrade - userFeature.getUser180DayOrderFeature().getBehaveMaxGrade()));

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(baseIndex + 220, userFeature.getUser180DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, 0.0));

        // 221 用户历史180天订单量中，该酒店占比
        featMap.put(baseIndex + 221, userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 222 用户历史180天订单量品牌中，该酒店品牌占比
        featMap.put(baseIndex + 222, userFeature.getUser180DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));

        // 223 用户历史180天订单量类型中，该酒店类型占比
        double historyOrderTypeRate180Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser180DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate180Day += typeRateMap.getOrDefault(type, 0.0);
            }
        }
        featMap.put(baseIndex + 223, historyOrderTypeRate180Day);

        // 224 用户历史180天订单量星级中，该酒店星级占比
        featMap.put(baseIndex + 224, userFeature.getUser180DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 225 用户历史180天订单量商圈中，该酒店商圈占比
        double hisOrder180DayTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder180DayTradRate += userFeature.getUser180DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 225, hisOrder180DayTradRate);

        // 226 用户历史180天订单量城市中，该城市占比
        featMap.put(baseIndex + 226, userFeature.getUser180DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, 0.0));

        // 227 用户历史365天订单量
        featMap.put(baseIndex + 227, (double)userFeature.getUser365DayOrderFeature().getBehaveCount());

        // 228 用户历史365天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(baseIndex + 228, (double)(hotelRealGrade - userFeature.getUser365DayOrderFeature().getBehaveMaxGrade()));

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(baseIndex + 229, userFeature.getUser365DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, 0.0));

        // 230 用户历史365天订单量中，该酒店占比
        featMap.put(baseIndex + 230, userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 231 用户历史365天订单量品牌中，该酒店品牌占比
        featMap.put(baseIndex + 231, userFeature.getUser365DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));
        // 232 用户历史365天订单量类型中，该酒店类型占比
        double historyOrderTypeRate365Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser365DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate365Day += typeRateMap.getOrDefault(type, 0.0);
            }
        }
        featMap.put(baseIndex + 232, historyOrderTypeRate365Day);

        // 233 用户历史365天订单量类星级中，该酒店星级占比
        featMap.put(baseIndex + 233, userFeature.getUser365DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 234 用户历史365天订单量商圈中，该酒店商圈占比
        double hisOrder365DayTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder365DayTradRate += userFeature.getUser365DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 234, hisOrder365DayTradRate);

        // 235 用户历史365天订单量城市中，该酒店城市占比
        featMap.put(baseIndex + 235, userFeature.getUser365DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, 0.0));

        // 236 用户历史800天订单量
        featMap.put(baseIndex + 236, (double)userFeature.getUser800DayOrderFeature().getBehaveCount());

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(baseIndex + 237, (double)(hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade()));

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        featMap.put(baseIndex + 238, userFeature.getUser800DayOrderFeature().getGradePriceRateMap().getOrDefault(priceBucket, 0.0));

        // 239 用户历史800天订单量中，该酒店占比
        featMap.put(baseIndex + 239, userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().getOrDefault(seq, 0.0));

        // 240 用户历史800天订单量品牌中，该酒店品牌占比
        featMap.put(baseIndex + 240, userFeature.getUser800DayOrderFeature().getBrandRateMap().getOrDefault(hotelBrand, 0.0));
        // 241 用户历史800天订单量类型中，该酒店类型占比
        double historyOrderTypeRate800Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser800DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate800Day += typeRateMap.getOrDefault(type, 0.0);
            }
        }
        featMap.put(baseIndex + 241, historyOrderTypeRate800Day);

        // 242 用户历史800天订单量类星级中，该酒店星级占比
        featMap.put(baseIndex + 242, userFeature.getUser800DayOrderFeature().getStarRateMap().getOrDefault(hotelStars, 0.0));

        // 243 用户历史800天订单量商圈中，该酒店商圈占比
        double hisOrder800DayTradRate = 0.0;
        if (StringUtils.isNotBlank(hotelTrading)) {
            hisOrder800DayTradRate += userFeature.getUser800DayOrderFeature().getTradingAreaRateMap().getOrDefault(hotelTrading, 0.0);
        }
        featMap.put(baseIndex + 243, hisOrder800DayTradRate);

        // 244 用户历史800天订单量城市中，该酒店城市占比
        featMap.put(baseIndex + 244, userFeature.getUser800DayOrderFeature().getHotelCityRateMap().getOrDefault(hotelCity, 0.0));

        return featMap;
    }


    // 增加D20特征
    private void addNewFeature(ModelUserFeature userFeature, ModelHotelFeature hotelFeature, Map<Integer, Object> featMap) {
        String seq = hotelFeature.getHotelSeq();
        String hotelCity = getCityFromSeq(seq);
        int hotelStars = hotelFeature.getStars();
        String hotelBrand = hotelFeature.getHotelBranch();
        String hotelTrading = hotelFeature.getTradingArea();
        String[] hotelTypes = hotelFeature.getHotelType();
        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();

        //"酒店报价"[951]
        if (hotelPrice != 0 && hotelPrice < DEFAULT_MAX_SUB10) {
            featMap.put(LR_INDEX_951, hotelPrice);
        }

        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);

        // 189 酒店档次
        featMap.put(LR_INDEX_1039, (double)hotelRealGrade);

        // 190 历史点击酒店次数
        featMap.put(LR_INDEX_1040, (double)userFeature.getUserHistoryClickFeature().getBehaveCount());

        // 191 用户历史点击次数最多的档次与当前酒店档次差
        featMap.put(LR_INDEX_1041, (double)(hotelRealGrade - userFeature.getUserHistoryClickFeature().getBehaveMaxGrade()));

        // 192 用户历史点击当前酒店档次占比
        putMapInternal(featMap, LR_INDEX_1042, userFeature.getUserHistoryClickFeature().getGradeRateMap().get(hotelRealGrade));

        // 193 用户历史点击当前酒店星级占比
        putMapInternal(featMap, LR_INDEX_1043, userFeature.getUserHistoryClickFeature().getStarRateMap().get(hotelStars));

        // 194 用户历史点击当前酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1044, userFeature.getUserHistoryClickFeature().getBrandRateMap().get(hotelBrand));

        // 195 用户历史点击当前酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1045, userFeature.getUserHistoryClickFeature().getTradingAreaRateMap().get(hotelTrading));
        }

        // 196 用户历史点击当前酒店类型占比
        double historyClickTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserHistoryClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyClickTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        if (historyClickTypeRate > 0.0) {
            featMap.put(LR_INDEX_1046, historyClickTypeRate);
        }

        // 197 用户点击当前酒店占比
        putMapInternal(featMap, LR_INDEX_1047, userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().get(seq));

        if (userFeature.getUser31DayCollectFeature().getBehaveCount() > 0) {
            // 198 1月（31天）内收藏次数
            featMap.put(LR_INDEX_1048, (double)userFeature.getUser31DayCollectFeature().getBehaveCount());
        }

        // 199 1月（31天）内收藏酒店最大档次与当前酒店档次差
        featMap.put(LR_INDEX_1049, (double)(hotelRealGrade - userFeature.getUser31DayCollectFeature().getBehaveMaxGrade()));

        // 200 1月（31天）用户收藏当前酒店档次占比
        putMapInternal(featMap, LR_INDEX_1050, userFeature.getUser31DayCollectFeature().getGradeRateMap().get(hotelRealGrade));

        // 201 1月（31天）用户收藏当前酒店星级占比
        putMapInternal(featMap, LR_INDEX_1051, userFeature.getUser31DayCollectFeature().getStarRateMap().get(hotelStars));

        // 202 1月（31天）用户收藏当前酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1052, userFeature.getUser31DayCollectFeature().getBrandRateMap().get(hotelBrand));

        // 203 1月（31天）用户收藏当前酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1053, userFeature.getUser31DayCollectFeature().getTradingAreaRateMap().get(hotelTrading));
        }

        // 204 1月（31天）用户收藏当前酒店类型占比
        double historyFavoriteTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser31DayCollectFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyFavoriteTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }

        if (historyFavoriteTypeRate > 0.0) {
            featMap.put(LR_INDEX_1054, historyFavoriteTypeRate);
        }


        // 205 用户实时点击酒店次数
        if (userFeature.getUserRealtimeClickFeature().getBehaveCount() > 0) {
            featMap.put(LR_INDEX_1055, (double)userFeature.getUserRealtimeClickFeature().getBehaveCount());
        }


        // 206 用户实时点击次数最多的档次与当前酒店档次差
        featMap.put(LR_INDEX_1056, (double)(hotelRealGrade - userFeature.getUserRealtimeClickFeature().getBehaveMaxGrade()));

        // 207 用户实时点击当前酒店档次占比
        putMapInternal(featMap, LR_INDEX_1057, userFeature.getUserRealtimeClickFeature().getGradeRateMap().get(hotelRealGrade));

        // 208 用户实时点击当前酒店星级占比
        putMapInternal(featMap, LR_INDEX_1058, userFeature.getUserRealtimeClickFeature().getStarRateMap().get(hotelStars));

        // 209 用户实时点击当前酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1059, userFeature.getUserRealtimeClickFeature().getBrandRateMap().get(hotelBrand));

        // 210 用户实时点击当前酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1060, userFeature.getUserRealtimeClickFeature().getTradingAreaRateMap().get(hotelTrading));
        }

        // 211 用户实时点击当前酒店类型占比
        double realTimeClickTypeRate = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserRealtimeClickFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                realTimeClickTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
        }

        if (realTimeClickTypeRate > 0.0) {
            featMap.put(LR_INDEX_1061, realTimeClickTypeRate);
        }


        // 212 用户实时点击当前酒店占比
        putMapInternal(featMap, LR_INDEX_1062, userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().get(seq));

        // 213 实时query筛选到该酒店的次数
        if (userFeature.getUserRealtimeQueryFeature().getBehaveCount() > 0) {
            featMap.put(LR_INDEX_1063, (double)userFeature.getUserRealtimeQueryFeature().getBehaveCount());
        }


        // 214 实时query筛选到该酒店的query占比
        putMapInternal(featMap, LR_INDEX_1064, userFeature.getUserRealtimeQueryFeature().getHotelSeqRateMap().get(seq));

        // 218 用户历史180天订单量
        if (userFeature.getUser180DayOrderFeature().getBehaveCount() > 0) {
            featMap.put(LR_INDEX_1068, (double)userFeature.getUser180DayOrderFeature().getBehaveCount());
        }

        // 219 用户历史180天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(LR_INDEX_1069, (double)(hotelRealGrade - userFeature.getUser180DayOrderFeature().getBehaveMaxGrade()));

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featMap, LR_INDEX_1070, userFeature.getUser180DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 221 用户历史180天订单量中，该酒店占比
        putMapInternal(featMap, LR_INDEX_1071, userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 222 用户历史180天订单量品牌中，该酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1072, userFeature.getUser180DayOrderFeature().getBrandRateMap().get(hotelBrand));

        // 223 用户历史180天订单量类型中，该酒店类型占比
        double historyOrderTypeRate180Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser180DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate180Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        if (historyOrderTypeRate180Day > 0.0) {
            featMap.put(LR_INDEX_1073, historyOrderTypeRate180Day);
        }


        // 224 用户历史180天订单量星级中，该酒店星级占比
        putMapInternal(featMap, LR_INDEX_1074, userFeature.getUser180DayOrderFeature().getStarRateMap().get(hotelStars));

        // 225 用户历史180天订单量商圈中，该酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1075, userFeature.getUser180DayOrderFeature().getTradingAreaRateMap().get(hotelTrading));
        }

        // 226 用户历史180天订单量城市中，该城市占比
        putMapInternal(featMap, LR_INDEX_1076, userFeature.getUser180DayOrderFeature().getHotelCityRateMap().get(hotelCity));

        // 227 用户历史365天订单量
        if (userFeature.getUser365DayOrderFeature().getBehaveCount() > 0) {
            featMap.put(LR_INDEX_1077, (double)userFeature.getUser365DayOrderFeature().getBehaveCount());
        }

        // 228 用户历史365天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(LR_INDEX_1078, (double)(hotelRealGrade - userFeature.getUser365DayOrderFeature().getBehaveMaxGrade()));

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featMap, LR_INDEX_1079, userFeature.getUser365DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 230 用户历史365天订单量中，该酒店占比
        putMapInternal(featMap, LR_INDEX_1080, userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 231 用户历史365天订单量品牌中，该酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1081, userFeature.getUser365DayOrderFeature().getBrandRateMap().get(hotelBrand));


        // 232 用户历史365天订单量类型中，该酒店类型占比
        double historyOrderTypeRate365Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser365DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate365Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        if (historyOrderTypeRate365Day > 0) {
            featMap.put(LR_INDEX_1082, historyOrderTypeRate365Day);
        }


        // 233 用户历史365天订单量类星级中，该酒店星级占比
        putMapInternal(featMap, LR_INDEX_1083, userFeature.getUser365DayOrderFeature().getStarRateMap().get(hotelStars));

        // 234 用户历史365天订单量商圈中，该酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1084, userFeature.getUser365DayOrderFeature().getTradingAreaRateMap().get(hotelTrading));
        }


        // 235 用户历史365天订单量城市中，该酒店城市占比
        putMapInternal(featMap, LR_INDEX_1085, userFeature.getUser365DayOrderFeature().getHotelCityRateMap().get(hotelCity));

        // 236 用户历史800天订单量
        if (userFeature.getUser800DayOrderFeature().getBehaveCount() > 0) {
            featMap.put(LR_INDEX_1086, (double)userFeature.getUser800DayOrderFeature().getBehaveCount());
        }

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        featMap.put(LR_INDEX_1087, (double)(hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade()));

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featMap, LR_INDEX_1088, userFeature.getUser800DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 239 用户历史800天订单量中，该酒店占比
        putMapInternal(featMap, LR_INDEX_1089, userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 240 用户历史800天订单量品牌中，该酒店品牌占比
        putMapInternal(featMap, LR_INDEX_1090, userFeature.getUser800DayOrderFeature().getBrandRateMap().get(hotelBrand));


        // 241 用户历史800天订单量类型中，该酒店类型占比
        double historyOrderTypeRate800Day = 0.0;
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUser800DayOrderFeature().getTypeRateMap();
            for (String type : hotelTypes) {
                historyOrderTypeRate800Day += typeRateMap.getOrDefault(type, ZERO);
            }
        }
        if (historyOrderTypeRate800Day > 0) {
            featMap.put(LR_INDEX_1091, historyOrderTypeRate800Day);
        }


        // 242 用户历史800天订单量类星级中，该酒店星级占比
        putMapInternal(featMap, LR_INDEX_1092, userFeature.getUser800DayOrderFeature().getStarRateMap().get(hotelStars));

        // 243 用户历史800天订单量商圈中，该酒店商圈占比
        if (StringUtils.isNotBlank(hotelTrading)) {
            putMapInternal(featMap, LR_INDEX_1093, userFeature.getUser800DayOrderFeature().getTradingAreaRateMap().get(hotelTrading));
        }

        // 244 用户历史800天订单量城市中，该酒店城市占比
        putMapInternal(featMap, LR_INDEX_1094, userFeature.getUser800DayOrderFeature().getHotelCityRateMap().get(hotelCity));
    }

    private void putMapInternal(Map<Integer, Object> featureMap, Integer index, Double value) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }



}
