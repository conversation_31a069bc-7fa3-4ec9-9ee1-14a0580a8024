package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_24H;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_30D;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_400D;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_90D;
import static com.qunar.search.common.model.convertor.ConvertorBase.getBucketIndex;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMap;

@Component
public class D6LRFeatureConvertor extends D4LRFeatureConvertor {


    private static final double[] PRICE_DIFF_BUCKET = {Double.MIN_VALUE, -1000, -500, -200, -100, -80, -60, -40, -20, -10, 0, 1, 10, 20, 40, 60, 80, 100, 200, 500, 1000, 20000, Double.MAX_VALUE};

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        EnumMap<FeatureType, Double> hotelOffline = hotelFeature.getOfflineFeature();

        Map<Integer, Object> featuresMap = super.convert(requestFeature, userFeature, hotelFeature);
        if (MapUtils.isEmpty(hotelOffline)) {
            return featuresMap;
        }

        double price = hotelFeature.getMobileMinAvailablePrice();
        // 根据加载的特征设置特征位
        double priceDiff = getPriceDiff(price, hotelOffline.get(ORD_MEDIAN_24H));
        putMap(featuresMap, ONE, 801, getBucketIndex(PRICE_DIFF_BUCKET, priceDiff));

        priceDiff = getPriceDiff(price, hotelOffline.get(ORD_MEDIAN_30D));
        putMap(featuresMap, ONE, 831, getBucketIndex(PRICE_DIFF_BUCKET, priceDiff));

        priceDiff = getPriceDiff(price, hotelOffline.get(ORD_MEDIAN_90D));
        putMap(featuresMap, ONE, 861, getBucketIndex(PRICE_DIFF_BUCKET, priceDiff));

        priceDiff = getPriceDiff(price, hotelOffline.get(ORD_MEDIAN_400D));
        putMap(featuresMap, ONE, 891, getBucketIndex(PRICE_DIFF_BUCKET, priceDiff));

        priceDiff = getPriceDiff(price, userFeature.getCurrentCityOrder400DAvg());
        putMap(featuresMap, ONE, 921, getBucketIndex(PRICE_DIFF_BUCKET, priceDiff));

        return featuresMap;
    }

    /**
     * 计算价格差
     * @param hotelPrice 酒店sort 报价
     * @param historyPrice 离线统计价格数据
     * @return 价格差
     */
    private double getPriceDiff(double hotelPrice, Double historyPrice) {
        if (null == historyPrice) {
            return 0;
        }

        if (historyPrice == 0 || hotelPrice == 0) {
            return 0;
        }

        return hotelPrice - historyPrice;
    }
}
