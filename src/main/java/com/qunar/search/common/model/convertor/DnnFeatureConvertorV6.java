package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * D34 特征 Convertor
 * 价格特征用预估的
 */
@Component
public class DnnFeatureConvertorV6 extends DnnFeatureConvertorV4Beat {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featReturn = Maps.newHashMap();

        // 返回 1 - 318 的特征索引
        Map<Integer, Object> featMap = super.convert(requestFeature, userFeature, hotelFeature);
        if(MapUtils.isNotEmpty(featMap)) {
            for (Integer key : featMap.keySet()) {
                double v = Numbers.roundStringDouble((double)featMap.get(key), FeatureConstants.decimals_num);
                featReturn.put(key, v);
            }
        }

        // 335 -> 比价info
        featReturn.put(FeatureIndexEnum.D34_QM_PRICECOMPARE_335.getIndex(), hotelFeature.getBeatInfo()*1.0);

        // 336 -> 可售房型订单占比
        featReturn.put(FeatureIndexEnum.D34_ORDERS_RATE_336.getIndex(), Numbers.roundStringDouble(
                hotelFeature.getOrdersRate(), FeatureConstants.decimals_num));

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 337 -> 7天对应场景曝光CTR    338 -> 21天对应场景曝光CTR   339 -> 60天对应场景曝光CTR
            fillSceneCtr(requestFeature.getSortScene(), offlineFeature, featReturn);

            // 340 -> 7天去位置偏置曝光CTR
            double coecCtr7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_7DAY, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_7DAY_340.getIndex(), coecCtr7d);
            // 341 -> 14天去位置偏置曝光CTR

            double coecCtr14d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_14DAY, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_14DAY_341.getIndex(), coecCtr14d);
            // 342 -> 21天去位置偏置曝光CTR
            double coecCtr21d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_21DAY, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_POS_COEC_CTR_21DAY_342.getIndex(), coecCtr21d);

            // 343 -> 该酒店7天非扶持曝光量占该城市非扶持曝光量占比
            double cityShowRate7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_SHOW_RATE, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_SHOW_RAT_343.getIndex(), cityShowRate7d);

            // 344 -> 该酒店7天点击量占该城市点击量占比
            double cityClickRate7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_CLICK_RATE, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_CLICK_RAT_344.getIndex(), cityClickRate7d);

            // 345 -> 该酒店7天订单量占该城市订单量占比
            double cityOrderRate7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_ORDER_RATE, -1.0),
                    FeatureConstants.decimals_num);
            featReturn.put(FeatureIndexEnum.D34_CITY_7DAY_ORDER_RAT_345.getIndex(), cityOrderRate7d);
        }

        // 346 -> 当前时间于酒店装修或开业时间年份差
        if (hotelFeature.getFitmentYearDiff() != null) {
            featReturn.put(FeatureIndexEnum.D34_FITMENT_YEARS_DIFF_346.getIndex(), hotelFeature.getFitmentYearDiff());
        }

        // 347 -> 酒店基础信息所在城市
        if (StringUtils.isNotEmpty(hotelFeature.getCityCode())) {
            featReturn.put(FeatureIndexEnum.D34_HOTEL_CITY_CODE_347.getIndex(), hotelFeature.getCityCode());
        }

        // 348 -> 酒店7天的cvr (s2o / ctr) 直接用统计好的 307特征位 除以289特征位
        double cvr7d = divisionValue(featMap.get(FeatureIndexEnum.D25_HOUTLE_7DAY_CTR_289),
                featMap.get(FeatureIndexEnum.D27_HOUTLE_7DAY_S2O_307), FeatureConstants.decimals_num);
        featReturn.put(FeatureIndexEnum.D34_HOTEL_7DAY_CVR_348.getIndex(),
                Numbers.roundStringDouble(cvr7d, FeatureConstants.decimals_num));

        // 354 -> 酒店折扣率 （预估render 报价 / sort报价）
        double priceDiscount = divisionValue(hotelFeature.getMobileMinAvailablePrice() * 1.0,
                hotelFeature.getEstimatedRenderPrice() * 1.0, 2);
        featReturn.put(FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex(),
                Numbers.roundStringDouble(priceDiscount, FeatureConstants.decimals_num));

        // 349 当前酒店seq
        if (StringUtils.isNotEmpty(hotelFeature.getHotelSeq())) {
            featReturn.put(FeatureIndexEnum.HOTEL_CURRENT.getIndex(), hotelFeature.getHotelSeq());
        }

        // 350 实时点击酒店id  宽口径 24h
        List<String> userRealTimeClickSeqsMerge24Hour = userFeature.getUserRealTimeClickSeq24Hour();
        if (userRealTimeClickSeqsMerge24Hour != null && !userRealTimeClickSeqsMerge24Hour.isEmpty()) {
            Map<String, Integer> actionCount = new HashMap<>();
            ArrayList<String> actionList = new ArrayList<>();
            for (String en: userRealTimeClickSeqsMerge24Hour) {
                int count = actionCount.getOrDefault(en, 0);
                if (count < 5) {
                    actionList.add(en);
                }
                actionCount.put(en, count+1);
            }
            featReturn.put(FeatureIndexEnum.HOTEL_LIST_24H_REALTIME_CLICK.getIndex(), actionList);
        }

        // 351 实时点击酒店id  宽口径 24 同城
        List<String> userRealTimeClickSeq24HourSameCity = userFeature.getUserRealTimeClickSeq24HourSameCity();
        if (userRealTimeClickSeq24HourSameCity != null && !userRealTimeClickSeq24HourSameCity.isEmpty()) {
            Map<String, Integer> actionCount = new HashMap<>();
            ArrayList<String> actionList = new ArrayList<>();
            for (String en: userRealTimeClickSeq24HourSameCity) {
                int count = actionCount.getOrDefault(en, 0);
                if (count < 5) {
                    actionList.add(en);
                }
                actionCount.put(en, count+1);
            }
            featReturn.put(FeatureIndexEnum.HOTEL_LIST_24H_REALTIME_CLICK_CITY.getIndex(), actionList);
        }


        for (Map.Entry<Integer, Object> entry : featReturn.entrySet()) {
            if (null == entry.getValue()) {
                featReturn.remove(entry.getKey());
            }
        }

        return featReturn;
    }


    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    private static double divisionValue(Object divisor,Object dividend,int len){
        if (divisor == null || dividend == null || (double)divisor ==0.0 || (double)dividend ==0.0){
            return 0.0;
        } else {
            return Numbers.roundStringDouble((double)dividend / (double)divisor,len);
        }

    }

    /**
     * 根据对应场景 填充ctr 特征
     */
    private static void fillSceneCtr(SortScene sortScene,EnumMap<FeatureType, Double> offlineFeature,Map<Integer, Object> featReturn){

        switch (sortScene){
            case NEARBY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_7_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_21_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_CTR_60_DAY,-1.0),
                                FeatureConstants.decimals_num));
                break;
            case POI_KEY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_7_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_21_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.POI_SCENE_CTR_60_DAY,-1.0),
                                FeatureConstants.decimals_num));
                break;

            case NOT_SAME_CITY:
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_7_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_21_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_CTR_60_DAY,-1.0),
                                FeatureConstants.decimals_num));
                break;

            default: // SAME_CITY and others
                featReturn.put(FeatureIndexEnum.D34_SCENE_7DAY_CTR_337.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_7_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_21DAY_CTR_338.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_21_DAY,-1.0),
                                FeatureConstants.decimals_num));
                featReturn.put(FeatureIndexEnum.D34_SCENE_60DAY_CTR_339.getIndex(),
                        Numbers.roundStringDouble(
                                offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_CTR_60_DAY,-1.0),
                                FeatureConstants.decimals_num));
                break;

        }

    }

}
