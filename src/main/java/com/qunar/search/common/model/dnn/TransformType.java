package com.qunar.search.common.model.dnn;

/**
 * @DESCRIPTION dnn特征转换类型
 **/
public enum TransformType {
	/**
	 * 不处理的特位偏移量
	 */
	NOT_DEAL_WITH_NEW_IDX("not_deal", 0.1),

	/**
	 * 累积分布特征位偏移量
	 */
	CUM_DIST_NORM_NEW_IDX("cum_dist_norm", 0.2),

	/**
	 * log转化特征位偏移量
	 */
	LOG_TRANSFORM_NEW_IDX("log_transform", 0.3),

	/**
	 * 排序转换特位偏移量
	 */
	SQRT_TRANSFORM_NEW_IDX("sqrt_transform", 0.4),

	/**
	 * 高斯转换特征偏移量
	 */
	GAUSSIAN_NORM_NEW_IDX("gaussian_norm", 0.6),

	/**
	 * 缺失值特征位
	 */
	MISS_NAN_COMPLETION_NEW_IDX("miss_nan", 0.7);


	public String type;
	public double offset;

	TransformType(String type, double offset) {
		this.type = type;
		this.offset = offset;
	}
}
