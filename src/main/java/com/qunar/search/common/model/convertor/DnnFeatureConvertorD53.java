package com.qunar.search.common.model.convertor;

import com.google.common.base.Joiner;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.math.data.UserBehaveSequence;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * D53 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD53 extends DnnFeatureConvertorD52 {

    private static final String ABNORMAL_STR = "fromForLog";
    private static final String POUND = "#";
    public static final Joiner POUND_JOINER = Joiner.on("#").skipNulls();
    public static final String NULL = "NULL";
    public static final String HOTEL_TYPE_DEFAULT = "NORMAL_HOTEL";
    public static final int CLICK_LIST_LIMIT_SIZE = 50;
    public static final int SUBMIT_ORDER_LIST_LIMIT_SIZE = 100;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        // 48小时内点击行为序列
        UserBehaveSequence realTimeListClickSequence = userFeature.getRealTimeListClickSequence();
        if (null != realTimeListClickSequence) {

            List<Float> priceList = realTimeListClickSequence.getPriceList();
            if (CollectionUtils.isNotEmpty(priceList)) {
                List<Float> collectLimit = priceList.stream().filter(s -> s>0).collect(Collectors.toList());
                int limitSize = Math.min(collectLimit.size(), CLICK_LIST_LIMIT_SIZE);
                List<Float> limitList = collectLimit.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_577.getIndex(), limitList);
                resultMap.put(FeatureIndexEnum.D53_587.getIndex(), limitList.size());
            }

            List<String> gradeList = realTimeListClickSequence.getGradeList();
            if (CollectionUtils.isNotEmpty(gradeList)) {
                int limitSize = Math.min(gradeList.size(), CLICK_LIST_LIMIT_SIZE);
                List<String> limitList = gradeList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_578.getIndex(), limitList);
            }

            List<String> brandList = realTimeListClickSequence.getBrandList();
            if (CollectionUtils.isNotEmpty(brandList)) {
                int limitSize = Math.min(brandList.size(), CLICK_LIST_LIMIT_SIZE);
                List<String> limitList = brandList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_579.getIndex(), limitList);
            }

            List<List<String>> typeList = realTimeListClickSequence.getTypeList();
            if (CollectionUtils.isNotEmpty(typeList)) {
                int limitSize = Math.min(typeList.size(), CLICK_LIST_LIMIT_SIZE);
                List<List<String>> limitList = typeList.subList(0, limitSize);
                List<String> collect = limitList.stream().filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                resultMap.put(FeatureIndexEnum.D53_580.getIndex(), collect);
            }

            List<String> hotelSeqList = realTimeListClickSequence.getHotelSeqList();
            if (CollectionUtils.isNotEmpty(hotelSeqList)) {
                int limitSize = Math.min(hotelSeqList.size(), CLICK_LIST_LIMIT_SIZE);
                List<String> limitList = hotelSeqList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_581.getIndex(), limitList);
            }
        }

        // 400天内提交单行为序列
        UserBehaveSequence submitOrderSequence = userFeature.getSubmitOrderSequence();
        if (null != submitOrderSequence) {

            List<Float> priceList = submitOrderSequence.getPriceList();
            if (CollectionUtils.isNotEmpty(priceList)) {
                List<Float> collectLimit = priceList.stream().filter(s -> s>0).collect(Collectors.toList());
                int limitSize = Math.min(collectLimit.size(), SUBMIT_ORDER_LIST_LIMIT_SIZE);
                List<Float> limitList = collectLimit.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_582.getIndex(), limitList);
                resultMap.put(FeatureIndexEnum.D53_588.getIndex(), limitList.size());
            }

            List<String> gradeList = submitOrderSequence.getGradeList();
            if (CollectionUtils.isNotEmpty(gradeList)) {
                int limitSize = Math.min(gradeList.size(), SUBMIT_ORDER_LIST_LIMIT_SIZE);
                List<String> limitList = gradeList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_583.getIndex(), limitList);
            }

            List<String> brandList = submitOrderSequence.getBrandList();
            if (CollectionUtils.isNotEmpty(brandList)) {
                int limitSize = Math.min(brandList.size(), SUBMIT_ORDER_LIST_LIMIT_SIZE);
                List<String> limitList = brandList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_584.getIndex(), limitList);
            }

            List<List<String>> typeList = submitOrderSequence.getTypeList();
            if (CollectionUtils.isNotEmpty(typeList)) {
                int limitSize = Math.min(typeList.size(), SUBMIT_ORDER_LIST_LIMIT_SIZE);
                List<List<String>> limitList = typeList.subList(0, limitSize);
                List<String> collect = limitList.stream().filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                resultMap.put(FeatureIndexEnum.D53_585.getIndex(), collect);
            }

            List<String> hotelSeqList = submitOrderSequence.getHotelSeqList();
            if (CollectionUtils.isNotEmpty(hotelSeqList)) {
                int limitSize = Math.min(hotelSeqList.size(), SUBMIT_ORDER_LIST_LIMIT_SIZE);
                List<String> limitList = hotelSeqList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D53_586.getIndex(), limitList);
            }
        }

        return resultMap;
    }

}