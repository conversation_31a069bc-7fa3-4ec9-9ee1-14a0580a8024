package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.enums.cUserDatas.CUserClickBookCollectData;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.enums.cUserDatas.UserFlightHotelTrainBnbOrderData;
import com.qunar.search.common.enums.cUserDatas.UserPoiOrderDistancePriceData;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.feature.GroupFeature;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.feature.XhsDyShowHotelFeature;
import com.qunar.search.common.model.feature.realtimeuserbehavior.UserHotelDetailStayTime;
import com.qunar.search.common.util.Numbers;
import com.qunar.search.common.util.RangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.util.Numbers.roundStringDouble;
import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossRate;


/**
 * D59 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD59 extends DnnFeatureConvertorD58 {

    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;
    private static final Double DEFAULT_PRICE = 200D;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_101.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_102.getIndex());
        // resultMap.remove(FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex());
        // resultMap.remove(FeatureIndexEnum.D37_400.getIndex());
        // resultMap.remove(FeatureIndexEnum.D50_543.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_220.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_229.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_238.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_249.getIndex());
        // resultMap.remove(FeatureIndexEnum.DNN_INDEX_250.getIndex());
        // resultMap.remove(FeatureIndexEnum.D35_366.getIndex());
        // resultMap.remove(FeatureIndexEnum.D35_369.getIndex());
        // resultMap.remove(FeatureIndexEnum.D35_371.getIndex());
        // resultMap.remove(FeatureIndexEnum.D43_463.getIndex());
        // resultMap.remove(FeatureIndexEnum.D43_464.getIndex());
        // resultMap.remove(FeatureIndexEnum.D43_465.getIndex());

        // 用户积分的缓存报价，加入用户券的酒店缓存报价-考虑多倍积分（替换酒店报价）
        double cachedPriceAfterMerchantPrice = hotelFeature.getCachedPriceAfterMerchant() > 0.0 && hotelFeature.getCachedPriceAfterMerchant() < DEFAULT_MAX_SUB100? hotelFeature.getCachedPriceAfterMerchant() : hotelFeature.getMinPriceWithIdentity();
        double cachedRenderMinPrice = hotelFeature.getCachedRenderMinPrice() > 0.0 && hotelFeature.getCachedRenderMinPrice() < DEFAULT_MAX_SUB100? hotelFeature.getCachedRenderMinPrice() : cachedPriceAfterMerchantPrice;
        resultMap.put(FeatureIndexEnum.D54_654.getIndex(), cachedRenderMinPrice); // D54已经定义但是未使用

        /*  
         * D51 依赖特征更新
         */

        // 用户下单价格
        double userOrderAvgPrice = userFeature.getOrderAvgPrice();
        // 用户实时点击价格
        double userRealTimeClickAvgPrice = userFeature.getRealtimeClickAvgPrice();
        // 用户实时点击房型价格
        Map<String, UserHotelDetailStayTime> userHotelDetailStayTimeMap = userFeature.getUserHotelDetailStayTimeMap();
        UserHotelDetailStayTime userHotelDetailStayTime = userHotelDetailStayTimeMap.get(hotelFeature.getHotelSeq());

        // 酒店划线价格
        double hotelShowOriginPrice = hotelFeature.getCachedDispOrgPrice() <= 0.0 ? hotelFeature.getOriginalPrice() : hotelFeature.getCachedDispOrgPrice();

        // 价格相关特征
        if (cachedRenderMinPrice > 0 && cachedRenderMinPrice < DEFAULT_MAX_SUB10) {
            // 酒店报价
            if (hotelShowOriginPrice > 0 && hotelShowOriginPrice < DEFAULT_MAX_SUB10) {
                // 酒店原始价格 - 酒店报价
                resultMap.put(FeatureIndexEnum.D59_715.getIndex(), hotelShowOriginPrice - cachedRenderMinPrice);
                // 酒店折扣率 （报价 / 划线价）
                Double priceDiscount = RangeUtil.divisionValue(cachedRenderMinPrice, hotelShowOriginPrice, 4);
                resultMap.put(FeatureIndexEnum.D59_716.getIndex(), priceDiscount);
            }

            if (userOrderAvgPrice > 0 && userOrderAvgPrice < DEFAULT_MAX_SUB10) {
                double diff = userOrderAvgPrice - cachedRenderMinPrice;
                // 用户下单价格-酒店报价
                resultMap.put(FeatureIndexEnum.D59_717.getIndex(), diff);
                // (用户下单价格-酒店报价)/酒店报价
                Double diffRate = RangeUtil.divisionValue(cachedRenderMinPrice, diff, 4);
                resultMap.put(FeatureIndexEnum.D59_718.getIndex(), diffRate);
            }

            // 当前酒店报价 / 用户实时点击平均价格
            double featureValue400 = RangeUtil.divisionValue(userRealTimeClickAvgPrice, cachedRenderMinPrice, 6, 10);
            if (featureValue400 > 0) {
                resultMap.put(FeatureIndexEnum.D59_719.getIndex(), featureValue400);
            }

            if(userHotelDetailStayTime != null && userHotelDetailStayTime.getHouseTypeprice() > 0 && userHotelDetailStayTime.getHouseTypeprice() < DEFAULT_MAX_SUB100) {
                double houseTypeprice = userHotelDetailStayTime.getHouseTypeprice();
                // 当前酒店价格/点击房型平均价格-1
                resultMap.put(FeatureIndexEnum.D59_720.getIndex(), roundStringDouble((cachedRenderMinPrice - houseTypeprice) / cachedRenderMinPrice, 8));
            }

            int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;
            int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, cachedRenderMinPrice);
            // 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D59_721.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser180DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));
            // 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D59_722.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser365DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));
            // 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
            putMapInternal(resultMap, FeatureIndexEnum.D59_723.getIndex(), Numbers.roundStringDouble(
                    userFeature.getUser800DayOrderFeature().getGradePriceRateMap().get(priceBucket), FeatureConstants.decimals_num));

            EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
            if (MapUtils.isNotEmpty(offlineFeature)) {
                Double cityPv7Price = offlineFeature.getOrDefault(FeatureType.CITY_PV_7_PRICE, DEFAULT_PRICE);
                Double bizZoneMean = offlineFeature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
                Double geoPvAvg7Price = offlineFeature.getOrDefault(FeatureType.GEO_PV_AVG_7_PRICE, DEFAULT_PRICE);
                Double aroundAvg7Price = offlineFeature.getOrDefault(FeatureType.AROUND_AVG_7_PRICE, DEFAULT_PRICE);


                double hotelShowPriceValid = cachedRenderMinPrice >= 100000 ? cityPv7Price : cachedRenderMinPrice;

                if (bizZoneMean != null && bizZoneMean > 0.0d) {
                    double diff = hotelShowPriceValid - bizZoneMean;
                    // 酒店报价-商业区价格
                    resultMap.put(FeatureIndexEnum.D59_724.getIndex(), diff);
                    // (酒店报价-商业区价格)/商业区价格
                    resultMap.put(FeatureIndexEnum.D59_725.getIndex(), Numbers.roundStringDouble(diff / bizZoneMean, FeatureConstants.decimals_num));
                }

                // poi对应的geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价
                resultMap.put(FeatureIndexEnum.D59_726.getIndex(), RangeUtil.divisionValue(geoPvAvg7Price, hotelShowPriceValid));
                // poi周围9个 geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价
                resultMap.put(FeatureIndexEnum.D59_727.getIndex(), RangeUtil.divisionValue(aroundAvg7Price, hotelShowPriceValid));
                // 酒店预估价格/当前城市展示价格
                resultMap.put(FeatureIndexEnum.D59_728.getIndex(), RangeUtil.divisionValue(cityPv7Price, hotelShowPriceValid));
            }

            GroupFeature groupFeature = requestFeature.getGroupFeature();
            if (null != groupFeature) {
                // 酒店报价/组内酒店最高报价
                resultMap.put(FeatureIndexEnum.D59_729.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelMaxPrice(), cachedRenderMinPrice, 3, 1.0)
                );
                // 酒店报价/组内酒店平均报价
                resultMap.put(FeatureIndexEnum.D59_730.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelAvgPrice(), cachedRenderMinPrice, 3, 10.0)
                );
                // 酒店报价/组内酒店最低报价
                resultMap.put(FeatureIndexEnum.D59_731.getIndex(),
                        RangeUtil.divisionValue(groupFeature.getHotelMinPrice(), cachedRenderMinPrice, 3, 1000.0)
                );
            }

            // 是否有缓存价格
            resultMap.put(FeatureIndexEnum.D59_732.getIndex(), cachedRenderMinPrice <= 0.0 ? 0.0 : 1.0);
        }

        /*
         * D54 依赖特征更新
         */

        // 用户在C的下单特征
        Map<CUserOrderData, Object> userCOrderAction = userFeature.getUserCOrderAction();
        if (MapUtils.isNotEmpty(userCOrderAction)) {
            // 用户近一年c最大间夜价格和酒店价格diff比
            Double cOrderMaxPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_MAX_ROOM_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D59_733.getIndex(), cOrderMaxPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> hotel > 0);

            // 用户近一年C平均间夜价格和酒店价格diff比
            Double cOrderAvgPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_AVG_ROOM_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D59_734.getIndex(), cOrderAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> hotel > 0);

            // 用户近一年c最小间夜价格和酒店价格diff比
            Double cOrderMinPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_MIN_ROOM_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D59_735.getIndex(), cOrderMinPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> hotel > 0);
        }

        // 用户在C的点击收藏特征
        Map<CUserClickBookCollectData, Object> userCtripClickBookCollectAction = userFeature.getUserCtripClickBookCollectAction();
        if (MapUtils.isNotEmpty(userCtripClickBookCollectAction)) {
            if (userCtripClickBookCollectAction.containsKey(CUserClickBookCollectData.USER_C_CLICK_AVG_PRICE)) {
                Double cClickAvgPrice = (Double)userCtripClickBookCollectAction.get(CUserClickBookCollectData.USER_C_CLICK_AVG_PRICE);
                // 用户在C的点击平均价格和该酒店的价格diff比
                putCrossRate(resultMap, FeatureIndexEnum.D59_736.getIndex(), cClickAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> hotel > 0);
            }
        }

        // 机酒火民特征
        Map<UserFlightHotelTrainBnbOrderData, Object> userFlightHotelTrainBnbOrderData = userFeature.getUserFlightHotelTrainBnbOrderData();
        if (MapUtils.isNotEmpty(userFlightHotelTrainBnbOrderData)) {
            // 酒店特征
            Double hotelOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.hotel_avg_order_price);
            putCrossRate(resultMap, FeatureIndexEnum.D59_737.getIndex(), hotelOrderAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel != null && hotel > 0));

            // 机票特征
            Double flightOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.flight_avg_order_price);
            putCrossRate(resultMap, FeatureIndexEnum.D59_738.getIndex(), flightOrderAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel != null && hotel > 0));

            // 火车特征
            Double trainOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.train_avg_order_price);
            putCrossRate(resultMap, FeatureIndexEnum.D59_739.getIndex(), trainOrderAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel != null && hotel > 0));

            // 民宿特征
            Double bnbOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.bnb_avg_order_price);
            putCrossRate(resultMap, FeatureIndexEnum.D59_740.getIndex(), bnbOrderAvgPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel != null && hotel > 0));
        }

        // poi下的距离价格特征
        Map<UserPoiOrderDistancePriceData, Object> userPoiOrderDistancePriceData = userFeature.getUserPoiOrderDistancePriceData();
        if (MapUtils.isNotEmpty(userPoiOrderDistancePriceData) && requestFeature.getSortScene() != null
                && requestFeature.getSortScene() == SortScene.POI_KEY) {
            Double avgClickPrice = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_CLICK_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D59_741.getIndex(), avgClickPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double clickPrice9 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.CLICK_PRICE_9);
            putCrossRate(resultMap, FeatureIndexEnum.D59_742.getIndex(), clickPrice9, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double avgOrderPrice = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_ORDER_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D59_743.getIndex(), avgOrderPrice, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double orderPrice9 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.ORDER_PRICE_9);
            putCrossRate(resultMap, FeatureIndexEnum.D59_744.getIndex(), orderPrice9, user -> (user != null && user > 0), cachedRenderMinPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

        }

        /*
         * D56 依赖特征更新
         */

        double queryOrderAvgShowPrice = userFeature.getQueryOrderAvgShowPrice();
        if (cachedRenderMinPrice > 0 && cachedRenderMinPrice < DEFAULT_MAX_SUB10 && queryOrderAvgShowPrice > 0 && queryOrderAvgShowPrice < DEFAULT_MAX_SUB10) {
            resultMap.put(FeatureIndexEnum.D59_745.getIndex(), cachedRenderMinPrice / queryOrderAvgShowPrice);
        }

        XhsDyShowHotelFeature xhsDyShowHotelFeature = userFeature.getXhsDyShowHotelFeature();
        if (null != xhsDyShowHotelFeature) {
            if (cachedRenderMinPrice > 0 && cachedRenderMinPrice < DEFAULT_MAX_SUB10 && xhsDyShowHotelFeature.getProductCodePrice() > 0 && xhsDyShowHotelFeature.getProductCodePrice() < DEFAULT_MAX_SUB10) {
                resultMap.put(FeatureIndexEnum.D59_746.getIndex(), cachedRenderMinPrice / xhsDyShowHotelFeature.getProductCodePrice());
            }
        }

        /*
         * D57 依赖特征更新
         */

        resultMap.put(FeatureIndexEnum.D59_747.getIndex(), getBucketIdx(cachedRenderMinPrice));
        
        return resultMap;
    }

    private int getBucketIdx(double v) {
        for (int idx=0; idx < priceBucketList.size(); idx++) {
            if (v < priceBucketList.get(idx)) {
                return idx + 1;
            }
        }
        return 0;
    }
}
