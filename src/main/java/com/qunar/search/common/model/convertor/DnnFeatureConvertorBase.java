package com.qunar.search.common.model.convertor;

import com.google.common.collect.Maps;
import com.qunar.search.common.bean.UserProfile;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.math.CoordDistance;
import com.qunar.search.common.math.NumberUtils;
import com.qunar.search.common.math.data.UserShowOrderData;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.DateUtil;
import com.qunar.search.common.util.GeneralUtil;
import com.qunar.search.common.util.Numbers;
import com.qunar.search.common.util.ZhiXin;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.NEGATIVE_ONE;
import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.enums.FeatureType.ADR_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_SHOW_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.COMMENT_COUNT;
import static com.qunar.search.common.enums.FeatureType.CTR_S2O_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.CTR_S2O_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.ECRM;
import static com.qunar.search.common.enums.FeatureType.HOLIDAY_PRICE_WEIGHT;
import static com.qunar.search.common.enums.FeatureType.IOS_AVG_PRICE_180_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_21_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_SHOW_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.ORD_CNT_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.ORD_CNT_6_MONTH;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_24H;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_30D;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_90D;
import static com.qunar.search.common.enums.FeatureType.SEARCH_CNT_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.TRAFFIC_NEG_LABEL;
import static com.qunar.search.common.model.convertor.ConvertorBase.getValueByPlatform;
import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossDiff;
import static com.qunar.search.common.util.MobileUtils.getCityFromSeq;

/**
 * dnn特征转换器基类，由于dnn只用到了xgb的部分特征，这里只计算需要用的特征
 *
 * <AUTHOR>
 * @date 2022/08/19
 */
@Component
public class DnnFeatureConvertorBase extends AbstractSortFeatureConvertor {

    public static final int DEFAULT_PRICE_WEIGHT = 1;

    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE - 10;

    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;

    private static final String COMMA = ",";

    private static final double SAME_CITY_DISTANCE = 500000;

    private final static Integer[] MISS_FEATURE_INDEX = {14, 46, 100, 101, 105, 189, 246, 247, 248};

    private final static double THRESHOLD = 0.0000001;

    private static final EnumMap<FeatureType, Double> EMPTY_FEATURE_MAP = Maps.newEnumMap(FeatureType.class);

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 指定初始容量，避免扩容
        Map<Integer, Object> featureMap = Maps.newHashMapWithExpectedSize(RankSystemConfig.getDnnFeatureSize());

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        if (offlineFeature == null) {
            offlineFeature = EMPTY_FEATURE_MAP;
        }


        SortScene scene = requestFeature.getSortScene();
        Map<String, List<UserHotel.OrderHotel>> historyOrders = userFeature.getHistoryOrders();

        double orderAvgPrice = userFeature.getOrderAvgPrice();

        double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();
        double price = hotelFeature.getMobileMinAvailablePrice();
        if (requestFeature.isPriceWeightDate() && MapUtils.isNotEmpty(offlineFeature)) {
            Double cityWeight = offlineFeature.get(HOLIDAY_PRICE_WEIGHT);
            if (cityWeight != null && cityWeight != DEFAULT_PRICE_WEIGHT) {
                clickAvgPrice = valueWeight(clickAvgPrice, cityWeight);
                price = valueWeight(price, cityWeight);
            }
        }

        String seq = hotelFeature.getHotelSeq();

        //用户过去半年预订该酒店的次数。
        if (historyOrders.containsKey(seq)) {
            double count = historyOrders.get(seq).size();
            if (count > 0 &&  count < DEFAULT_MAX_SUB10 ) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_1.getIndex(), count);
            }
        }

        int pos = StringUtils.lastIndexOf(seq, "_");
        if (pos > 0) {
            Map<String, UserShowOrderData.OrderInfo> cityLastOrder = userFeature.cityLastOrder;
            UserShowOrderData.OrderInfo orderInfo = cityLastOrder.get(StringUtils.substring(seq,0, pos));
            if (orderInfo != null && StringUtils.equals(orderInfo.hotelSeq, seq)) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_2.getIndex(), ONE);
            }
        }

        UserShowOrderData.PvInfo pvInfo = userFeature.hisShowSt.get(seq);
        // 近三周用户浏览和点击该酒店的次数
        double numClick = 0.0D;
        if (pvInfo != null) {
            numClick = pvInfo.cl;
        }
        if (numClick > 0 && numClick < DEFAULT_MAX_SUB10 ) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_6.getIndex(), numClick);
        }

        //点击价格
        if (clickAvgPrice > 0 && clickAvgPrice<DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_14.getIndex(), clickAvgPrice);
        }

        // 酒店历史订单价格均值与用户历史订单价格均值的相对距离[45-46]
        if (orderAvgPrice > 0 && orderAvgPrice<DEFAULT_MAX_SUB10 ) {
            double orderMeanPrice = getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_AVG_PRICE_180_DAY,
                    IOS_AVG_PRICE_180_DAY, hotelFeature.getPlatformOrderMeanPrice());
            if (orderMeanPrice > 0 && orderMeanPrice<DEFAULT_MAX_SUB10) {
                double diff = orderMeanPrice- orderAvgPrice;
                double disScore = diff / orderAvgPrice;
                featureMap.put(FeatureIndexEnum.DNN_INDEX_45.getIndex(), Numbers.roundStringDouble(disScore, FeatureConstants.decimals_num));
                featureMap.put(FeatureIndexEnum.DNN_INDEX_46.getIndex(), Numbers.roundStringDouble(diff, FeatureConstants.decimals_num));
            }
        }


        // 4天之前收藏过该酒店, 4天之内收藏过该酒店[97-98]
        if (userFeature.getFavoriteHotels().containsKey(seq)) {
            int requestDay = requestFeature.getRequestDate();
            int logDay4 = DateUtil.addDay(requestDay, -4);
            UserHotel.FavoriteHotel favoriteHotel = userFeature.getFavoriteHotels().get(seq);
            if (favoriteHotel.getFavoriteDate() < logDay4) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_97.getIndex(), ONE);
            } else {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_98.getIndex(), ONE);
            }
        }

        // 酒店近3周的订单量[99]
        double v_num_orders = getValueByPlatform(offlineFeature, requestFeature.getPlatform(), ADR_ORD_PV_21_DAY,
                IOS_ORD_PV_21_DAY, hotelFeature.getPlatformOrderPv21());
        if (v_num_orders<DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_99.getIndex(), v_num_orders);
        }

        //酒店评分
        double commentScore = hotelFeature.getCommentScore();
        featureMap.put(FeatureIndexEnum.DNN_INDEX_100.getIndex(), commentScore);

        //"酒店折扣"[101-102]
        double originalPrice = hotelFeature.getOriginalPrice();
        if (price > 0 && price < DEFAULT_MAX_SUB10) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_101.getIndex(), price);
            if (originalPrice != 0 && originalPrice < DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_102.getIndex(), originalPrice - price);
            }
        }

        // 根据sort报价预估的render报价，替换sort报价, index: 101
        double estimatePrice = hotelFeature.getEstimatedRenderPrice();
        // 有效的预估价格
        if (estimatePrice > 0 && estimatePrice < DEFAULT_MAX_SUB100) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_101.getIndex(), estimatePrice);
            if (originalPrice != 0 && originalPrice < DEFAULT_MAX_SUB100) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_102.getIndex(), originalPrice - estimatePrice);
            }
        }

        // 酒店转化率特征
        double showpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_SHOW_PV_7_DAY, IOS_SHOW_PV_7_DAY, hotelFeature.getPlatformShowPv7());
        double orderpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_ORD_PV_7_DAY, IOS_ORD_PV_7_DAY, hotelFeature.getPlatformOrderPv7());
        if (showpv7 > 0) {
            double rate = ZhiXin.getZhiXin(orderpv7 / showpv7, showpv7);
            featureMap.put(FeatureIndexEnum.DNN_INDEX_103.getIndex(), rate);
        }
        //酒店在关键词下订单量
        if (scene.match(SortScene.POI_KEY)) {
            double keyWordOrderCount = hotelFeature.getKeyWordOrderCount();
            if (keyWordOrderCount > 0 && keyWordOrderCount < DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_104.getIndex(), keyWordOrderCount);
            }
        }
        //酒店到poi的距离
        if (scene.match(SortScene.POI_KEY) && !GeneralUtil.isHaveCtripScenicZone(requestFeature)) {
            double poiDistance = hotelFeature.getPoiHotelDistance();
            if(poiDistance > 0 && poiDistance<DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_105.getIndex(), poiDistance);
            }
        }

        //同城或商圈场景下，酒店距离用户或者坐标的距离。
        if (scene.match(SortScene.SAME_CITY) || scene.match(SortScene.NEARBY)) {
            double coorDistance = hotelFeature.getUserHotelDistance();

            if(coorDistance > 0 && coorDistance<DEFAULT_MAX_SUB10) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_106.getIndex(), coorDistance);
            }
        }


        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_114.getIndex(), offlineFeature.get(ORD_CNT_6_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_115.getIndex(), offlineFeature.get(ORD_CNT_3_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_119.getIndex(), offlineFeature.get(SEARCH_CNT_3_WEEK));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_123.getIndex(), offlineFeature.get(CTR_S2O_3_MONTH));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_125.getIndex(), offlineFeature.get(CTR_S2O_3_WEEK));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_132.getIndex(), offlineFeature.get(COMMENT_COUNT));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_135.getIndex(), offlineFeature.get(TRAFFIC_NEG_LABEL));

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_151.getIndex(), offlineFeature.get(ECRM));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_156.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_24H, NEGATIVE_ONE));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_157.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_30D, NEGATIVE_ONE));

        featureMap.put(FeatureIndexEnum.DNN_INDEX_158.getIndex(), offlineFeature.getOrDefault(ORD_MEDIAN_90D, NEGATIVE_ONE));


        // 187 - 188
        addDistanceFeat(hotelFeature.getLatLng(), requestFeature, featureMap);


        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;

        // 189 酒店档次
        featureMap.put(FeatureIndexEnum.DNN_INDEX_189.getIndex(), (double)hotelRealGrade);

        // 190 历史点击酒店次数
        featureMap.put(FeatureIndexEnum.DNN_INDEX_190.getIndex(), (double)userFeature.getUserHistoryClickFeature().getBehaveCount());

        // 192 用户历史点击当前酒店档次占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_192.getIndex(), userFeature.getUserHistoryClickFeature().getGradeRateMap().get(hotelRealGrade));

        // 197 用户点击当前酒店占比
        putMapInternal(featureMap,FeatureIndexEnum.DNN_INDEX_197.getIndex(), userFeature.getUserHistoryClickFeature().getHotelSeqRateMap().get(seq));

        // 199 1月（31天）内收藏酒店最大档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_199.getIndex(), (double)(hotelRealGrade - userFeature.getUser31DayCollectFeature().getBehaveMaxGrade()));


        // 205 用户实时点击酒店次数
        if (userFeature.getUserRealtimeClickFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_205.getIndex(), (double)userFeature.getUserRealtimeClickFeature().getBehaveCount());
        }        

        // 206 用户实时点击次数最多的档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_206.getIndex(), (double)(hotelRealGrade - userFeature.getUserRealtimeClickFeature().getBehaveMaxGrade()));

        // 212 用户实时点击当前酒店占比
        putMapInternal(featureMap,FeatureIndexEnum.DNN_INDEX_212.getIndex(), userFeature.getUserRealtimeClickFeature().getHotelSeqRateMap().get(seq));

        // 213 实时query筛选到该酒店的次数
        if (userFeature.getUserRealtimeQueryFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_213.getIndex(), (double)userFeature.getUserRealtimeQueryFeature().getBehaveCount());
        }

        // 214 实时query筛选到该酒店的query占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_214.getIndex(), userFeature.getUserRealtimeQueryFeature().getHotelSeqRateMap().get(seq));

        double hotelPrice = hotelFeature.getMobileMinAvailablePrice();
        int priceBucket = ConvertorBase.getGradePriceBucketIndex(hotelRealGrade, hotelPrice);

        // 220 用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_220.getIndex(), userFeature.getUser180DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 221 用户历史180天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_221.getIndex(), userFeature.getUser180DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 226 用户历史180天订单量城市中，该城市占比
        if (MapUtils.isNotEmpty(userFeature.getUser180DayOrderFeature().getHotelCityRateMap())) {
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_226.getIndex(), userFeature.getUser180DayOrderFeature().getHotelCityRateMap().get(getCityFromSeq(seq)));
        }

        // 227 用户历史365天订单量
        if (userFeature.getUser365DayOrderFeature().getBehaveCount() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_227.getIndex(), (double)userFeature.getUser365DayOrderFeature().getBehaveCount());
        }

        // 229 用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_229.getIndex(), userFeature.getUser365DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 230 用户历史365天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_230.getIndex(), userFeature.getUser365DayOrderFeature().getHotelSeqRateMap().get(seq));

        // 235 用户历史365天订单量城市中，该酒店城市占比
        if (MapUtils.isNotEmpty(userFeature.getUser365DayOrderFeature().getHotelCityRateMap())) {
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_235.getIndex(), userFeature.getUser365DayOrderFeature().getHotelCityRateMap().get(getCityFromSeq(seq)));
        }

        // 237 用户历史800天订单量中档次最多的订单档次与当前酒店档次差
        featureMap.put(FeatureIndexEnum.DNN_INDEX_237.getIndex(), (double)(hotelRealGrade - userFeature.getUser800DayOrderFeature().getBehaveMaxGrade()));

        // 238 用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_238.getIndex(), userFeature.getUser800DayOrderFeature().getGradePriceRateMap().get(priceBucket));

        // 239 用户历史800天订单量中，该酒店占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_239.getIndex(), userFeature.getUser800DayOrderFeature().getHotelSeqRateMap().get(seq));

        // embedding 特征位，246-247
        ConvertorBase.putEmbeddingFeature(userFeature, hotelFeature, featureMap);

        Double bizZoneMean = offlineFeature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
        if (bizZoneMean != null && bizZoneMean > 0.0d) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_248.getIndex(), bizZoneMean);
            double diff = estimatePrice - bizZoneMean;
            featureMap.put(FeatureIndexEnum.DNN_INDEX_249.getIndex(), diff);
            featureMap.put(FeatureIndexEnum.DNN_INDEX_250.getIndex(), Numbers.roundStringDouble(diff / bizZoneMean, FeatureConstants.decimals_num));
        }


        UserProfile userProfile = userFeature.getUserProfile();
        if (userProfile != null) {
            switch (userProfile.getSensitiveScore()) {
                case 1: // 高价格敏感度用户
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.HIGH_PRICE_SENS_PREFECT));
                    break;
                case 0: // 低价格敏感度用户
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.LOW_PRICE_SENS_PREFECT));
                    break;
                case -1: // 未知
                    putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_264.getIndex(), offlineFeature.get(FeatureType.PRICE_SENS_UN_KNOW_PERFECT));
                    break;
            }
        }

        //酒店3月内点击率
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_287.getIndex(), offlineFeature.get(FeatureType.CTR_3_MONTH));

        //酒店3月内点击率（置信）
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_288.getIndex(), offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN));

        //酒店7天点击率
        putMapInternal(featureMap, FeatureIndexEnum.D25_HOUTLE_7DAY_CTR_289.getIndex(), offlineFeature.get(FeatureType.CTR_7_DAY));

        //酒店7天点击率（置信）
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_290.getIndex(), offlineFeature.get(FeatureType.CTR_7_DAY_ZHIXIN));



        int checkInNum = requestFeature.getCheckInNum();
        checkInNum = Math.min(checkInNum, 8);
        // 用户入住天数
        featureMap.put(FeatureIndexEnum.DNN_INDEX_291.getIndex(), (double)checkInNum);

        if (checkInNum > 0) {

            // 这里赋值是错的，加个开关是为了和原始错的逻辑diff
            if (RankSystemConfig.isEnable292FeatureError()) {
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_292.getIndex(), offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + checkInNum - 1)));
            }

            // 用户入住天数对应的酒店不同天数的订单数 [292]
            int intervalIndex = 2*(checkInNum - 1);
            putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_292.getIndex(), offlineFeature.get(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + intervalIndex)));
        }

        //用户入住那天是周末时，酒店的历史周末的销售订单占比
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_295.getIndex(), requestFeature.isWeekend() ? FeatureConstants.ONE : FeatureConstants.ZERO);

        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_296.getIndex(), requestFeature.isWeekend()
                ? offlineFeature.get(FeatureType.WEEKEND_ORDER_RATE)
                : offlineFeature.get(FeatureType.NO_WEEKEND_ORDER_RATE));

        // 用户预订日期内包含节假日和周末占比[297]
        if (requestFeature.getCheckWeekendHolidayRate() > 0) {
            featureMap.put(FeatureIndexEnum.DNN_INDEX_297.getIndex(), requestFeature.getCheckWeekendHolidayRate());
        }

        // 酒店历史订单（周末间夜+节假日间夜）占比[298]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_298.getIndex(), offlineFeature.get(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE));

        // 用户预定日期内的节假日和周末占比 - 酒店历史订单节假日和周末占比（间夜） [299]
        putCrossDiff(featureMap, FeatureIndexEnum.DNN_INDEX_299.getIndex(), requestFeature.getCheckWeekendHolidayRate(), s -> true,
                offlineFeature.getOrDefault(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE, NEGATIVE_ONE),
                v -> (v != null && v > -1.0));


        // 酒店60天内得曝光pv[300]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_300.getIndex(), offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_60_DAY));

        // 酒店7天内得曝光pv[303]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_303.getIndex(), offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_7_DAY));

        // 酒店60天内得曝光s2o[304]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_304.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_60_DAY));

        // 酒店21天内得曝光s2o[305]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_305.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_21_DAY));

        // 酒店14天内得曝光s2o[306]
        putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_306.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_14_DAY));

        // 酒店7天内得曝光s2o[307]
        putMapInternal(featureMap, FeatureIndexEnum.D27_HOUTLE_7DAY_S2O_307.getIndex(), offlineFeature.get(FeatureType.DISPLAY_PV_S2O_7_DAY));

        SortScene sortScene = requestFeature.getSortScene();
        switch (sortScene){
            case NEARBY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.NEARBY_SCENE_S2O_60_DAY));
                break;
            case POI_KEY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.POIKEY_SCENE_S2O_60_DAY));
                break;
            case SAME_CITY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.SAMECITY_SCENE_S2O_60_DAY));
                break;
            case NOT_SAME_CITY:
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_308.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_7_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_309.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_14_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_310.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_21_DAY));
                putMapInternal(featureMap, FeatureIndexEnum.DNN_INDEX_311.getIndex(), offlineFeature.get(FeatureType.NOTSAMECITY_SCENE_S2O_60_DAY));
                break;
        }

        for (Integer index  : MISS_FEATURE_INDEX) {
            double value = (Double)featureMap.getOrDefault(index, FeatureConstants.ZERO);
            if (index == 246 || index == 247) {
                featureMap.put(-1 * index, Math.abs(value - 0.0) < THRESHOLD || value == -1 ? FeatureConstants.ONE : FeatureConstants.ZERO);
            } else {
                featureMap.put(-1 * index, Math.abs(value - 0.0) < THRESHOLD ? FeatureConstants.ONE : FeatureConstants.ZERO);
            }
        }

        return featureMap;
    }

    /**
     * 计算不同场景下的距离特征
     */
    private void addDistanceFeat(GLatLng gLatLng, ModelRequestFeature requestFeature, Map<Integer, Object> featureMap) {

        if (gLatLng == null) {
            return;
        }
        featureMap.put(FeatureIndexEnum.DNN_INDEX_187.getIndex(), NEGATIVE_ONE);
        featureMap.put(FeatureIndexEnum.DNN_INDEX_188.getIndex(), NEGATIVE_ONE);

        SortScene scene = requestFeature.getSortScene();
        if (scene == SortScene.NOT_SAME_CITY) {
            double distance = calDistance(gLatLng, requestFeature.getCityUrlCenterPoint());
            if (distance != 0) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_187.getIndex(), distance);
            }
        } else if (scene.equals(SortScene.POI_KEY)) {
            double distance = calDistance(gLatLng, requestFeature.getPoiPoint());
            if (distance != 0) {
                featureMap.put(FeatureIndexEnum.DNN_INDEX_188.getIndex(), distance);
            }
        }
    }

    /**
     * 距离计算
     */
    private double calDistance(GLatLng gLatLng, String point) {

        if (null == gLatLng || StringUtils.isBlank(point)) {
            return 0;
        }

        double hotelLat = gLatLng.getLat();
        double hotelLng = gLatLng.getLng();

        String[] latLng = StringUtils.split(point, COMMA);
        if (null == latLng || latLng.length != 2) {
            return 0;
        }

        try {
            double lat = NumberUtils.parseDouble(latLng[0]);
            double lng = NumberUtils.parseDouble(latLng[1]);
            double distance = CoordDistance.distanceWithNormalized(hotelLat, hotelLng, lat, lng);
            return distance <= SAME_CITY_DISTANCE ? distance : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    public void putMapInternal(Map<Integer, Object> featureMap, Integer index, Double value) {
        if (null != value) {
            featureMap.put(index, value);
        }
    }


    public double valueWeight(double value, double weight) {
        return Math.round(value * weight * 10) / 10.0;
    }

}
