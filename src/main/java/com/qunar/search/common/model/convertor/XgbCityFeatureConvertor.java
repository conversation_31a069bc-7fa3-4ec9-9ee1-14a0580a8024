package com.qunar.search.common.model.convertor;

import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * D36 特征 Convertor
 */
@Component
public class XgbCityFeatureConvertor extends DnnFeatureConvertorD36 {

    private static final int CITY_CODE_INDEX = 347;
    private static final String DEFAULT_CITY_CODE = "other_city";

    private final static int[] XGB_FEATURE_INDEX = {1, 2, 6, 14, 45, 46, 97, 98, 99, 100, 101,
            102, 103, 104, 105, 106, 114, 115, 119, 123, 125, 132, 135, 151, 156, 157, 158,
            187, 188, 189, 190, 192, 197, 199, 205, 206, 212, 213, 214, 220, 221, 226, 227,
            229, 230, 235, 237, 238, 239, 246, 247, 248, 249, 250, 264, 287, 288, 289, 290,
            291, 292, 295, 296, 297, 298, 299, 300, 303, 304, 305, 306, 307, 308, 309, 310,
            311, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 348, 356, 361,
            362, 363, 364, 365, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378,
            379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395};


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        Map<Integer, Object> featureMap = new HashMap<>();

        for (Integer index : XGB_FEATURE_INDEX) {
            Object val = resultMap.getOrDefault(index, 0.0);
            if (val instanceof Number) {
                featureMap.put(index, ((Number)val).doubleValue());
            }
        }

        featureMap.put(CITY_CODE_INDEX, resultMap.getOrDefault(CITY_CODE_INDEX, DEFAULT_CITY_CODE));

        return featureMap;
    }
}
