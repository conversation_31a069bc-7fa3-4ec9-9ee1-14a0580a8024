package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.HotelVariableFeatureType;
import com.qunar.search.common.enums.UserQCOrderClickFeature;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;


/**
 * D58 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD58 extends DnnFeatureConvertorD57 {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);
        boolean isNewUser = MapUtils.isNotEmpty(userFeature.getUserIdentityInfoMap()) && userFeature.getUserIdentityInfoMap().containsKey("newUser");
        resultMap.put(FeatureIndexEnum.D58_709.getIndex(), isNewUser? 1.0: 0.0);
        String phoneModel = requestFeature.getPhoneModel();
        if (Objects.nonNull(phoneModel)) {
            resultMap.put(FeatureIndexEnum.D58_710.getIndex(), phoneModel);
        }
        Map<UserQCOrderClickFeature, Object> userQCOrderClickMap = userFeature.getUserQCOrderClickMap();
        if (MapUtils.isNotEmpty(userQCOrderClickMap)) {
            Double userQcOrderPrice = (Double)userQCOrderClickMap.get(UserQCOrderClickFeature.C_1Y_AVG_RN_PRICE);
            putMap(resultMap, userQcOrderPrice, FeatureIndexEnum.D58_711.getIndex(), v -> (v != null && v > 0));

            Double userQcClickPrice = (Double)userQCOrderClickMap.get(UserQCOrderClickFeature.C_1M_AVG_HOTEL_PRICE);
            putMap(resultMap, userQcClickPrice, FeatureIndexEnum.D58_712.getIndex(), v -> (v != null && v > 0));

            List<String> userQcClickSeq = (List<String>)userQCOrderClickMap.get(UserQCOrderClickFeature.C_6M_CLK_HOTELSEQ_LIST);
            if (null != userQcClickSeq && userQcClickSeq.contains(hotelFeature.getHotelSeq())) {
                resultMap.put(FeatureIndexEnum.D58_714.getIndex(), 1.0);
            } else {
                resultMap.put(FeatureIndexEnum.D58_714.getIndex(), 0.0);
            }
        }

        EnumMap<HotelVariableFeatureType, Object> offlineVariableFeature = hotelFeature.getOfflineVariableFeature();
        if (MapUtils.isNotEmpty(offlineVariableFeature)) {
            Double hotelCanUseCoupons = (Double)offlineVariableFeature.get(HotelVariableFeatureType.HOTEL_NO_COUPONS);
            putMap(resultMap, hotelCanUseCoupons, FeatureIndexEnum.D58_713.getIndex(), Objects::nonNull);
        }

        return resultMap;
    }
}