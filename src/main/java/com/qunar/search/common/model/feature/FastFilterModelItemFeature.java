package com.qunar.search.common.model.feature;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.enums.FastFilterItemFeatureType;
import com.qunar.search.common.util.LibSvmUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2023-09-14
 * @DESCRIPTION 1、模型计算使用的酒店数据 2、特征日志打印对象
 **/
@Data
public class FastFilterModelItemFeature implements Serializable {

    /**
     * 筛选项name
     */
    private String filterName;

    /**
     * convertor中使用的酒店统计数据
     * 1、线上填充内存数据 EnumMap<FeatureType, Double>
     * 2、打印日志时填充 空 map, 防止日志爆炸
     */
    @JsonIgnore
    private EnumMap<FastFilterItemFeatureType, Double> offlineFeature;

    /**
     * 日志还原使用字段
     * 1、线上，不使用，为null
     * 2、离线还原，直接set字符串和上诉map
     */
    private String offlineFeatureLibSvm;


    public void setOfflineFeatureLibSvm(String offlineFeatureLibSvm) {
        this.offlineFeatureLibSvm = offlineFeatureLibSvm;
        if (MapUtils.isEmpty(this.offlineFeature)) {
            this.offlineFeature = LibSvmUtil.stringToFastFilterEnumMap(offlineFeatureLibSvm);
        }
    }

}
