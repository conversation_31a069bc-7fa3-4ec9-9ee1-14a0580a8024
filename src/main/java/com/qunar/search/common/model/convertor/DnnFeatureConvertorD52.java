package com.qunar.search.common.model.convertor;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.enums.HotelTypeEnum;
import com.qunar.search.common.feature.UserHotel;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * D52 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD52 extends DnnFeatureConvertorD51 {

    private static final String ABNORMAL_STR = "fromForLog";
    private static final String POUND = "#";
    public static final Joiner POUND_JOINER = Joiner.on("#").skipNulls();
    public static final String NULL = "NULL";
    public static final String HOTEL_TYPE_DEFAULT = "NORMAL_HOTEL";

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        // 召回意图
        if (CollectionUtils.isNotEmpty(requestFeature.getQueryIntention())) {
            resultMap.put(FeatureIndexEnum.D50_540.getIndex(), requestFeature.getQueryIntention());
        }

        // 品牌
        String hotelBranch = hotelFeature.getHotelBranch();
        String brand = StringUtils.isNotEmpty(hotelBranch) ? hotelBranch : NULL;
        resultMap.put(FeatureIndexEnum.D52_567.getIndex(), brand);

        // 酒店类型
        String[] hotelType = hotelFeature.getHotelType(); // 酒店类型
        List<String> typeList = new ArrayList<>();
        if (hotelType != null && hotelType.length > 0) {
            for (String en : hotelType) {
                if (StringUtils.isNotEmpty(en)) {
                    typeList.add(en);
                }
            }
        }
        List<String> tmpList = CollectionUtils.isEmpty(typeList) ? Lists.newArrayList(HOTEL_TYPE_DEFAULT) : typeList;
        resultMap.put(FeatureIndexEnum.D52_568.getIndex(), tmpList);

        // 酒店档次
        resultMap.put(FeatureIndexEnum.D52_569.getIndex(), String.valueOf(HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10));

        // 请求城市
        if (StringUtils.isNotEmpty(requestFeature.getCityUrl())) {
            resultMap.put(FeatureIndexEnum.D52_570.getIndex(), requestFeature.getCityUrl());
        }

        // 地级市
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode())) {
            resultMap.put(FeatureIndexEnum.D52_571.getIndex(), requestFeature.getRootCityCode());
        }

        // 地级市_请求城市
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && StringUtils.isNotEmpty(requestFeature.getCityUrl())) {
            resultMap.put(FeatureIndexEnum.D52_572.getIndex(), requestFeature.getRootCityAndRequestCity());
        }

        // 24h点击交叉行为序列
        List<String> userRealTimeClickSeqsMerge24Hour = userFeature.getUserRealTimeClickSeq24Hour();
        if (CollectionUtils.isNotEmpty(userRealTimeClickSeqsMerge24Hour)) {
            ArrayList<String> actionList = new ArrayList<>();
            ArrayList<String> crossList = new ArrayList<>();
            for (int i = 0;  i < userRealTimeClickSeqsMerge24Hour.size(); i++) {
                String seq = userRealTimeClickSeqsMerge24Hour.get(i).trim();
                if (i < 50 && StringUtils.isNotEmpty(seq) && !seq.contains(ABNORMAL_STR)) {
                    String joinSeq = hotelFeature.getHotelSeq() + POUND + seq;
                    actionList.add(seq);
                    crossList.add(joinSeq);
                }
            }
            resultMap.put(FeatureIndexEnum.D52_573.getIndex(), crossList);
            resultMap.put(FeatureIndexEnum.D52_575.getIndex(), actionList);
        }

        return resultMap;
    }

}