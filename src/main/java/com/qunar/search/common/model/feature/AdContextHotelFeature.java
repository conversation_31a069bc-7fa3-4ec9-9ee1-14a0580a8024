package com.qunar.search.common.model.feature;

import lombok.Data;

import java.io.Serializable;

/**
 * 广告酒店的上下文特征
 * @Author: haohao.sun
 * @Date: 2024/11/4 17:09
 */
@Data
public class AdContextHotelFeature implements Serializable {
    /**
     * poi到酒店的距离
     */
    private double poiHotelDistance;
    /**
     * 用户到酒店的距离
     */
    double userHotelDistance;
    /**
     * sort报价
     */
    private int mobileMinAvailablePrice;
    /**
     * 评分
     */
    private double commentScore;
    /**
     * 星级
     */
    private int stars;
    /**
     * 月订单量
     */
    private int order1M;

    /**
     * 预估的ctr
     */
    private double pCTR;
    /**
     * 预估的ctcvr
     */
    private double pCTCVR;
}
