package com.qunar.search.common.model.convertor;

import com.google.common.base.Joiner;
import com.qunar.hotel.common.util.MapUtil;
import com.qunar.search.common.enums.*;
import com.qunar.search.common.enums.cUserDatas.CUserClickBookCollectData;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.enums.cUserDatas.UserFlightHotelTrainBnbOrderData;
import com.qunar.search.common.enums.cUserDatas.UserPoiOrderDistancePriceData;
import com.qunar.search.common.math.data.UserBehaveSequence;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossRate;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;


/**
 * D54 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD54 extends DnnFeatureConvertorD53 {
    private static final int DEFAULT_MAX_SUB100 = Integer.MAX_VALUE - 100;
    private static final String ABNORMAL_STR = "fromForLog";
    private static final String POUND = "#";
    public static final Joiner POUND_JOINER = Joiner.on("#").skipNulls();
    public static final String NULL = "NULL";
    public static final String HOTEL_TYPE_DEFAULT = "NORMAL_HOTEL";
    public static final int LIST_LIMIT_SIZE = 50;
    public static final int SUBMIT_ORDER_LIST_LIMIT_SIZE = 100;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);
        double hotelShowPrice = hotelFeature.getCachedPriceAfterMerchant() <= 0.0 ? hotelFeature.getMinPriceWithIdentity() : hotelFeature.getCachedPriceAfterMerchant();

        // 用户在C的下单特征
        Map<CUserOrderData, Object> userCOrderAction = userFeature.getUserCOrderAction();
        if (MapUtils.isNotEmpty(userCOrderAction)) {
            // 用户近一年c订单量
            putMap(resultMap, (Double) userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_CNT), FeatureIndexEnum.D54_589.getIndex(), v -> (v != null && v > 0));

            // 用户近一年C订单酒店数
            putMap(resultMap, (Double) userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_HOTEL_CNT), FeatureIndexEnum.D54_590.getIndex(), v -> (v != null && v > 0));

            // 用户近一年c最大间夜价格
            Double cOrderMaxPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_MAX_ROOM_PRICE);
            putMap(resultMap, cOrderMaxPrice, FeatureIndexEnum.D54_591.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_592.getIndex(), cOrderMaxPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> hotel > 0);

            // 用户近一年c平均间夜价格
            Double cOrderAvgPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_AVG_ROOM_PRICE);
            putMap(resultMap, cOrderAvgPrice, FeatureIndexEnum.D54_593.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_594.getIndex(), cOrderAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> hotel > 0);

            // 用户近一年c最小间夜价格
            Double cOrderMinPrice = (Double)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_MIN_ROOM_PRICE);
            putMap(resultMap, cOrderMinPrice, FeatureIndexEnum.D54_595.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_596.getIndex(), cOrderMinPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> hotel > 0);

            // 用户近一年c最近下单时间距离当前时间天数
            resultMap.put(FeatureIndexEnum.D54_597.getIndex(), userFeature.getCLastOrderDaysDiff());

            // 用户近一年下单酒店序列
            if (userCOrderAction.containsKey(CUserOrderData.USER_C_ONE_YEAR_ORDER_HOTEL_LISTS)) {
                List<String> hotelList= (List<String>)userCOrderAction.get(CUserOrderData.USER_C_ONE_YEAR_ORDER_HOTEL_LISTS);
                if (CollectionUtils.isNotEmpty(hotelList)) {
                    resultMap.put(FeatureIndexEnum.D54_598.getIndex(), hotelList.subList(0, Math.min(LIST_LIMIT_SIZE, hotelList.size())));
                    resultMap.put(FeatureIndexEnum.D54_599.getIndex(), hotelList.contains(hotelFeature.getHotelSeq()) ? 1.0 : 0.0);
                }
            }
        }

        // 用户在C的点击收藏特征
        Map<CUserClickBookCollectData, Object> userCtripClickBookCollectAction = userFeature.getUserCtripClickBookCollectAction();
        if (MapUtils.isNotEmpty(userCtripClickBookCollectAction)) {
            if (userCtripClickBookCollectAction.containsKey(CUserClickBookCollectData.USER_C_CLICK_HOTEL_LIST)) {
                List<String> hotelList= (List<String>)userCtripClickBookCollectAction.get(CUserClickBookCollectData.USER_C_CLICK_HOTEL_LIST);
                if (CollectionUtils.isNotEmpty(hotelList)) {
                    resultMap.put(FeatureIndexEnum.D54_600.getIndex(), hotelList.subList(0, Math.min(LIST_LIMIT_SIZE, hotelList.size())));
                    resultMap.put(FeatureIndexEnum.D54_601.getIndex(), hotelList.contains(hotelFeature.getHotelSeq()) ? 1.0 : 0.0);
                }
            }
            if (userCtripClickBookCollectAction.containsKey(CUserClickBookCollectData.USER_C_BOOK_HOTEL_LIST)) {
                List<String> hotelList= (List<String>)userCtripClickBookCollectAction.get(CUserClickBookCollectData.USER_C_BOOK_HOTEL_LIST);
                if (CollectionUtils.isNotEmpty(hotelList)) {
                    resultMap.put(FeatureIndexEnum.D54_602.getIndex(), hotelList.subList(0, Math.min(LIST_LIMIT_SIZE, hotelList.size())));
                    resultMap.put(FeatureIndexEnum.D54_603.getIndex(), hotelList.contains(hotelFeature.getHotelSeq()) ? 1.0 : 0.0);
                }
            }
            if (userCtripClickBookCollectAction.containsKey(CUserClickBookCollectData.USER_C_COLLECT_HOTEL_LIST)) {
                List<String> hotelList= (List<String>)userCtripClickBookCollectAction.get(CUserClickBookCollectData.USER_C_COLLECT_HOTEL_LIST);
                if (CollectionUtils.isNotEmpty(hotelList)) {
                    resultMap.put(FeatureIndexEnum.D54_604.getIndex(), hotelList.subList(0, Math.min(LIST_LIMIT_SIZE, hotelList.size())));
                    resultMap.put(FeatureIndexEnum.D54_605.getIndex(), hotelList.contains(hotelFeature.getHotelSeq()) ? 1.0 : 0.0);
                }
            }
            if (userCtripClickBookCollectAction.containsKey(CUserClickBookCollectData.USER_C_CLICK_AVG_PRICE)) {
                Double cClickAvgPrice = (Double)userCtripClickBookCollectAction.get(CUserClickBookCollectData.USER_C_CLICK_AVG_PRICE);
                putMap(resultMap, cClickAvgPrice, FeatureIndexEnum.D54_606.getIndex(), v -> (v != null && v > 0));
                putCrossRate(resultMap, FeatureIndexEnum.D54_607.getIndex(), cClickAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> hotel > 0);
            }
        }

        // 机酒火民特征
        Map<UserFlightHotelTrainBnbOrderData, Object> userFlightHotelTrainBnbOrderData = userFeature.getUserFlightHotelTrainBnbOrderData();
        if (MapUtils.isNotEmpty(userFlightHotelTrainBnbOrderData)) {
            // 酒店特征
            Double hotelAllOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.hotel_all_avg_order_price);
            Double hotelOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.hotel_avg_order_price);
            Double hotelOrderNum = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.hotel_order_num);
            putMap(resultMap, hotelAllOrderAvgPrice, FeatureIndexEnum.D54_608.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, hotelOrderAvgPrice, FeatureIndexEnum.D54_609.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, hotelOrderNum, FeatureIndexEnum.D54_610.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_611.getIndex(), hotelAllOrderAvgPrice, user -> (user != null && user > 0), hotelOrderAvgPrice, hotel -> (hotel != null && hotel > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_612.getIndex(), hotelOrderAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel != null && hotel > 0));

            // 机票特征
            Double flightAllOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.flight_all_avg_order_price);
            Double flightOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.flight_avg_order_price);
            Double flightOrderNum = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.flight_order_num);
            putMap(resultMap, flightAllOrderAvgPrice, FeatureIndexEnum.D54_613.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, flightOrderAvgPrice, FeatureIndexEnum.D54_614.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, flightOrderNum, FeatureIndexEnum.D54_615.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_616.getIndex(), flightAllOrderAvgPrice, user -> (user != null && user > 0), flightOrderAvgPrice, hotel -> (hotel != null && hotel > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_617.getIndex(), flightOrderAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel != null && hotel > 0));

            // 火车特征
            Double trainAllOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.train_all_avg_order_price);
            Double trainOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.train_avg_order_price);
            Double trainOrderNum = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.train_order_num);
            putMap(resultMap, trainAllOrderAvgPrice, FeatureIndexEnum.D54_618.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, trainOrderAvgPrice, FeatureIndexEnum.D54_619.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, trainOrderNum, FeatureIndexEnum.D54_620.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_621.getIndex(), trainAllOrderAvgPrice, user -> (user != null && user > 0), trainOrderAvgPrice, hotel -> (hotel != null && hotel > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_622.getIndex(), trainOrderAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel != null && hotel > 0));

            // 民宿特征
            Double bnbAllOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.bnb_all_avg_order_price);
            Double bnbOrderAvgPrice = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.bnb_avg_order_price);
            Double bnbOrderNum = (Double)userFlightHotelTrainBnbOrderData.get(UserFlightHotelTrainBnbOrderData.bnb_order_num);
            putMap(resultMap, bnbAllOrderAvgPrice, FeatureIndexEnum.D54_623.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, bnbOrderAvgPrice, FeatureIndexEnum.D54_624.getIndex(), v -> (v != null && v > 0));
            putMap(resultMap, bnbOrderNum, FeatureIndexEnum.D54_625.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_626.getIndex(), bnbAllOrderAvgPrice, user -> (user != null && user > 0), bnbOrderAvgPrice, hotel -> (hotel != null && hotel > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_627.getIndex(), bnbOrderAvgPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel != null && hotel > 0));
        }

        // poi下的距离价格特征
        Map<UserPoiOrderDistancePriceData, Object> userPoiOrderDistancePriceData = userFeature.getUserPoiOrderDistancePriceData();
        if (MapUtils.isNotEmpty(userPoiOrderDistancePriceData) && requestFeature.getSortScene() != null
                && requestFeature.getSortScene() == SortScene.POI_KEY) {

            Double avgClickDistance = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_CLICK_DISTANCE);
            putMap(resultMap, avgClickDistance, FeatureIndexEnum.D54_628.getIndex(), v -> (v != null && v > 0));
            putCrossRate(resultMap, FeatureIndexEnum.D54_629.getIndex(), avgClickDistance, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));
            Double relatedClickDistance99 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.RELATED_CLICK_DISTANCE_99);
            putMap(resultMap, relatedClickDistance99, FeatureIndexEnum.D54_630.getIndex(), v -> (v != null && v > 0));

            Double clickDistance99 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.CLICK_DISTANCE_99);
            putCrossRate(resultMap, FeatureIndexEnum.D54_631.getIndex(), clickDistance99, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double avgOrderDistance = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_ORDER_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_632.getIndex(), avgOrderDistance, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double relatedOrderDistance99 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.RELATED_ORDER_DISTANCE_99);
            putCrossRate(resultMap, FeatureIndexEnum.D54_633.getIndex(), relatedOrderDistance99, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double orderDistance99 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.ORDER_DISTANCE_99);
            putCrossRate(resultMap, FeatureIndexEnum.D54_634.getIndex(), orderDistance99, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double avgClickPrice = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_CLICK_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_635.getIndex(), avgClickPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double clickPrice9 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.CLICK_PRICE_9);
            putCrossRate(resultMap, FeatureIndexEnum.D54_636.getIndex(), clickPrice9, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double avgOrderPrice = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.AVG_ORDER_PRICE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_637.getIndex(), avgOrderPrice, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            Double orderPrice9 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.ORDER_PRICE_9);
            putCrossRate(resultMap, FeatureIndexEnum.D54_638.getIndex(), orderPrice9, user -> (user != null && user > 0), hotelShowPrice, hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            String top1HotelSeq = (String)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP1_HOTEL_SEQ);
            putMap(resultMap, top1HotelSeq, FeatureIndexEnum.D54_639.getIndex(), v -> StringUtils.isNotEmpty(v));
            putMap(resultMap, StringUtils.equals(hotelFeature.getHotelSeq(), top1HotelSeq) ? 1.0 : 0.0, FeatureIndexEnum.D54_640.getIndex());

            Double orderHotelDistanceTop1 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP1_ORDER_hotel_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_641.getIndex(), orderHotelDistanceTop1, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            String top2HotelSeq = (String)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP2_HOTEL_SEQ);
            putMap(resultMap, top2HotelSeq, FeatureIndexEnum.D54_642.getIndex(), v -> StringUtils.isNotEmpty(v));
            putMap(resultMap, StringUtils.equals(hotelFeature.getHotelSeq(), top2HotelSeq) ? 1.0 : 0.0, FeatureIndexEnum.D54_643.getIndex());

            Double orderHotelDistanceTop2 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP2_ORDER_hotel_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_644.getIndex(), orderHotelDistanceTop2, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            String top3HotelSeq = (String)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP3_HOTEL_SEQ);
            putMap(resultMap, top3HotelSeq, FeatureIndexEnum.D54_645.getIndex(), v -> StringUtils.isNotEmpty(v));
            putMap(resultMap, StringUtils.equals(hotelFeature.getHotelSeq(), top3HotelSeq) ? 1.0 : 0.0, FeatureIndexEnum.D54_646.getIndex());

            Double orderHotelDistanceTop3 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP3_ORDER_hotel_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_647.getIndex(), orderHotelDistanceTop3, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            String top4HotelSeq = (String)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP4_HOTEL_SEQ);
            putMap(resultMap, top4HotelSeq, FeatureIndexEnum.D54_648.getIndex(), v -> StringUtils.isNotEmpty(v));
            putMap(resultMap, StringUtils.equals(hotelFeature.getHotelSeq(), top4HotelSeq) ? 1.0 : 0.0, FeatureIndexEnum.D54_649.getIndex());

            Double orderHotelDistanceTop4 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP4_ORDER_hotel_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_650.getIndex(), orderHotelDistanceTop4, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));

            String top5HotelSeq = (String)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP5_HOTEL_SEQ);
            putMap(resultMap, top5HotelSeq, FeatureIndexEnum.D54_651.getIndex(), v -> StringUtils.isNotEmpty(v));
            putMap(resultMap, StringUtils.equals(hotelFeature.getHotelSeq(), top5HotelSeq) ? 1.0 : 0.0, FeatureIndexEnum.D54_652.getIndex());

            Double orderHotelDistanceTop5 = (Double)userPoiOrderDistancePriceData.get(UserPoiOrderDistancePriceData.TOP5_ORDER_hotel_DISTANCE);
            putCrossRate(resultMap, FeatureIndexEnum.D54_653.getIndex(), orderHotelDistanceTop5, user -> (user != null && user > 0), hotelFeature.getPoiHotelDistance(), hotel -> (hotel > 0 && hotel<DEFAULT_MAX_SUB10));
        }

        return resultMap;
    }

}