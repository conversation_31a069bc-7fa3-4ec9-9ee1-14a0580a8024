package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;

/**
 * <AUTHOR>
 * @create 2020-11-02 下午7:10
 * @DESCRIPTION 添加部分新特征D27
 **/
@Component
public class XgboostD27FeatureConvertor extends XgboostD26FeatureConvertor {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featReturn = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isEmpty(offlineFeature)) {
            return featReturn;
        }

        // 酒店60天内得曝光pv[300]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_60_DAY), 300);

        // 酒店21天内得曝光pv[301]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_21_DAY), 301);

        // 酒店14天内得曝光pv[302]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_14_DAY), 302);

        // 酒店7天内得曝光pv[303]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_SEARCH_PV_7_DAY), 303);

        // 酒店60天内得曝光s2o[304]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_PV_S2O_60_DAY), 304);

        // 酒店21天内得曝光s2o[305]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_PV_S2O_21_DAY), 305);

        // 酒店14天内得曝光s2o[306]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_PV_S2O_14_DAY), 306);

        // 酒店7天内得曝光s2o[307]
        putMap(featReturn, offlineFeature.get(FeatureType.DISPLAY_PV_S2O_7_DAY), 307);

        return featReturn;
    }
}
