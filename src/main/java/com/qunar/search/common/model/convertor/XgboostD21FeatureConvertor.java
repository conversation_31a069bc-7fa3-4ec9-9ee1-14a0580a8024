package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;


@Component
public class XgboostD21FeatureConvertor extends XgboostD20FeatureConvertor {


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();

        Map<Integer, Object> featureReturn = super.convert(requestFeature, userFeature, hotelFeature);
        if (MapUtils.isEmpty(feature)) {
            return featureReturn;
        }


        Double bizZoneMean = feature.get(FeatureType.BIZ_ZONE_MEDIAN_PRICE);
        if (null == bizZoneMean || bizZoneMean <= 0.0d) {
            return featureReturn;
        }

        featureReturn.put(248, bizZoneMean);

        double diff = hotelFeature.getMobileMinAvailablePrice() - bizZoneMean;
        featureReturn.put(249, diff);
        featureReturn.put(250, diff / bizZoneMean);

        return featureReturn;
    }
}
