package com.qunar.search.common.model.convertor;


import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.math.CoordDistance;
import com.qunar.search.common.math.NumberUtils;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.enums.FeatureType.ANTI_NOISE_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.ANTI_NOISE_POS_LABEL;
import static com.qunar.search.common.enums.FeatureType.CLEAN_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.CLEAN_POS_LABEL;
import static com.qunar.search.common.enums.FeatureType.COMMENT_COUNT;
import static com.qunar.search.common.enums.FeatureType.COMMENT_SCORE;
import static com.qunar.search.common.enums.FeatureType.COMPLAIN_RATE;
import static com.qunar.search.common.enums.FeatureType.COST_PERFORM_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.COST_PERFORM_POS_LABEL;
import static com.qunar.search.common.enums.FeatureType.CTR_S2O_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.CTR_S2O_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.CTR_S2O_6_MONTH;
import static com.qunar.search.common.enums.FeatureType.ECRM;
import static com.qunar.search.common.enums.FeatureType.FACILITY_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.FACILITY_POS_LABEL;
import static com.qunar.search.common.enums.FeatureType.HOLIDAY_PRICE_WEIGHT;
import static com.qunar.search.common.enums.FeatureType.MEITUAN_ORIGIN_ROOM_PRICE;
import static com.qunar.search.common.enums.FeatureType.MEITUAN_ROOM_PRICE;
import static com.qunar.search.common.enums.FeatureType.ORD_CNT_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.ORD_CNT_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.ORD_CNT_6_MONTH;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_24H;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_30D;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_400D;
import static com.qunar.search.common.enums.FeatureType.ORD_MEDIAN_90D;
import static com.qunar.search.common.enums.FeatureType.RAW_S2O_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.RAW_S2O_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.RAW_S2O_6_MONTH;
import static com.qunar.search.common.enums.FeatureType.SEARCH_CNT_3_MONTH;
import static com.qunar.search.common.enums.FeatureType.SEARCH_CNT_3_WEEK;
import static com.qunar.search.common.enums.FeatureType.SEARCH_CNT_6_MONTH;
import static com.qunar.search.common.enums.FeatureType.SERVICE_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.SERVICE_POS_LABEL;
import static com.qunar.search.common.enums.FeatureType.TRAFFIC_NEG_LABEL;
import static com.qunar.search.common.enums.FeatureType.TRAFFIC_POS_LABEL;

@Component
public class XgboostFeatureConvertorMoreFeat extends XgboostFeatureConvertorBase {

    private static final String COMMA = ",";
    private static final Double DEFAULT_NAN = 1.0;
    private static final Double DEFAULT_ZERO = 0.0;
    private static final double SAME_CITY_DISTANCE = 500000;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featureReturn = super.convert(requestFeature, userFeature, hotelFeature);
        addHotelFeat(requestFeature, hotelFeature, featureReturn);
        addUserFeat(userFeature, featureReturn);
        addDistanceFeat(hotelFeature.getLatLng(), requestFeature, featureReturn);

        return featureReturn;
    }

    /**
     * 计算不同场景下的距离特征
     */
    private void addDistanceFeat(GLatLng gLatLng, ModelRequestFeature requestFeature, Map<Integer, Object> featureReturn) {

        if (gLatLng == null) {
            return;
        }
        featureReturn.put(187, FeatureConstants.NEGATIVE_ONE);
        featureReturn.put(188, FeatureConstants.NEGATIVE_ONE);

        SortScene scene = requestFeature.getSortScene();
        if (scene == SortScene.NOT_SAME_CITY) {
            double distance = calDistance(gLatLng, requestFeature.getCityUrlCenterPoint());
            if (distance != 0) {
                featureReturn.put(187, distance);
            }
        } else if (scene.equals(SortScene.POI_KEY)) {
            double distance = calDistance(gLatLng, requestFeature.getPoiPoint());
            if (distance != 0) {
                featureReturn.put(188, distance);
            }
        }
    }

    /**
     * 距离计算
     */
    private double calDistance(GLatLng gLatLng, String point) {

        if (null == gLatLng || StringUtils.isBlank(point)) {
            return 0;
        }

        double hotelLat = gLatLng.getLat();
        double hotelLng = gLatLng.getLng();

        String[] latLng = StringUtils.split(point, COMMA);
        if (null == latLng || latLng.length != 2) {
            return 0;
        }

        try {
            double lat = NumberUtils.parseDouble(latLng[0]);
            double lng = NumberUtils.parseDouble(latLng[1]);
            double distance = CoordDistance.distanceWithNormalized(hotelLat, hotelLng, lat, lng);
            return distance <= SAME_CITY_DISTANCE ? distance : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 添加用户特征
     */
    private void addUserFeat(ModelUserFeature userFeature, Map<Integer, Object> featureReturn) {

        double order400DAvg = userFeature.getCurrentCityOrder400DAvg();

        // 161 - 165
        putMapInternalInterval(featureReturn, 161, 165);
        if (order400DAvg != 0.0d) {
            putMapInternal(featureReturn, order400DAvg, 161);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 165);
        }
    }

    /**
     * 添加酒店相关特征
     */
    private void addHotelFeat(ModelRequestFeature requestFeature, ModelHotelFeature hotelFeature, Map<Integer, Object> featureReturn) {

        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();
        if (MapUtils.isEmpty(feature)) {
            return;
        }

        int price = hotelFeature.getMobileMinAvailablePrice();
        if (requestFeature.isPriceWeightDate()) {
            double cityWeight = feature.getOrDefault(HOLIDAY_PRICE_WEIGHT, 1.0d);
            if (cityWeight != DEFAULT_PRICE_WEIGHT) {
                price = (int) valueWeight(price, cityWeight);
            }
        }

        // 订单相关特征 114 - 130
        putMapInternalInterval(featureReturn, 114, 130);
        if (feature.containsKey(SEARCH_CNT_6_MONTH)) {
            putMapInternal(featureReturn, feature.getOrDefault(ORD_CNT_6_MONTH, DEFAULT_ZERO), 114);
            putMapInternal(featureReturn, feature.getOrDefault(ORD_CNT_3_MONTH, DEFAULT_ZERO), 115);
            putMapInternal(featureReturn, feature.getOrDefault(ORD_CNT_3_WEEK, DEFAULT_ZERO), 116);
            putMapInternal(featureReturn, feature.getOrDefault(SEARCH_CNT_6_MONTH, DEFAULT_ZERO), 117);
            putMapInternal(featureReturn, feature.getOrDefault(SEARCH_CNT_3_MONTH, DEFAULT_ZERO), 118);
            putMapInternal(featureReturn, feature.getOrDefault(SEARCH_CNT_3_WEEK, DEFAULT_ZERO), 119);
            putMapInternal(featureReturn, feature.getOrDefault(RAW_S2O_6_MONTH, DEFAULT_ZERO), 120);
            putMapInternal(featureReturn, feature.getOrDefault(CTR_S2O_6_MONTH, DEFAULT_ZERO), 121);
            putMapInternal(featureReturn, feature.getOrDefault(RAW_S2O_3_MONTH, DEFAULT_ZERO), 122);
            putMapInternal(featureReturn, feature.getOrDefault(CTR_S2O_3_MONTH, DEFAULT_ZERO), 123);
            putMapInternal(featureReturn, feature.getOrDefault(RAW_S2O_3_WEEK, DEFAULT_ZERO), 124);
            putMapInternal(featureReturn, feature.getOrDefault(CTR_S2O_3_WEEK, DEFAULT_ZERO), 125);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 130);
        }

        // 评论相关特征 131-150
        putMapInternalInterval(featureReturn, 131, 150);
        if (feature.containsKey(COMMENT_COUNT)) {
            putMapInternal(featureReturn, feature.getOrDefault(COMMENT_SCORE, DEFAULT_ZERO), 131);
            putMapInternal(featureReturn, feature.getOrDefault(COMMENT_COUNT, DEFAULT_ZERO), 132);
            putMapInternal(featureReturn, feature.getOrDefault(CLEAN_NEG_LABEL, DEFAULT_ZERO), 133);
            putMapInternal(featureReturn, feature.getOrDefault(CLEAN_POS_LABEL, DEFAULT_ZERO), 134);
            putMapInternal(featureReturn, feature.getOrDefault(TRAFFIC_NEG_LABEL, DEFAULT_ZERO), 135);
            putMapInternal(featureReturn, feature.getOrDefault(TRAFFIC_POS_LABEL, DEFAULT_ZERO), 136);
            putMapInternal(featureReturn, feature.getOrDefault(COST_PERFORM_NEG_LABEL, DEFAULT_ZERO), 137);
            putMapInternal(featureReturn, feature.getOrDefault(COST_PERFORM_POS_LABEL, DEFAULT_ZERO), 138);
            putMapInternal(featureReturn, feature.getOrDefault(SERVICE_NEG_LABEL, DEFAULT_ZERO), 139);
            putMapInternal(featureReturn, feature.getOrDefault(SERVICE_POS_LABEL, DEFAULT_ZERO), 140);
            putMapInternal(featureReturn, feature.getOrDefault(FACILITY_NEG_LABEL, DEFAULT_ZERO), 141);
            putMapInternal(featureReturn, feature.getOrDefault(FACILITY_POS_LABEL, DEFAULT_ZERO), 142);
            putMapInternal(featureReturn, feature.getOrDefault(ANTI_NOISE_NEG_LABEL, DEFAULT_ZERO), 143);
            putMapInternal(featureReturn, feature.getOrDefault(ANTI_NOISE_POS_LABEL, DEFAULT_ZERO), 144);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 150);
        }

        // 评论相关特征 151-155
        putMapInternalInterval(featureReturn, 151, 155);
        Double ecrm = feature.get(ECRM);
        if (null != ecrm) {
            putMapInternal(featureReturn, ecrm, 151);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 155);
        }

        // 酒店价格中位数相关特征 156-160
        putMapInternal(featureReturn, feature.getOrDefault(ORD_MEDIAN_24H, FeatureConstants.NEGATIVE_ONE), 156);
        putMapInternal(featureReturn, feature.getOrDefault(ORD_MEDIAN_30D, FeatureConstants.NEGATIVE_ONE), 157);
        putMapInternal(featureReturn, feature.getOrDefault(ORD_MEDIAN_90D, FeatureConstants.NEGATIVE_ONE), 158);
        putMapInternal(featureReturn, feature.getOrDefault(ORD_MEDIAN_400D, FeatureConstants.NEGATIVE_ONE), 159);
        Double value = feature.containsKey(ORD_MEDIAN_400D) ? DEFAULT_ZERO : DEFAULT_NAN;
        putMapInternal(featureReturn, value, 160);

        // 携程优享会--线下数据一直为空
        putMapInternalInterval(featureReturn, 166, 174);
        putMapInternal(featureReturn, DEFAULT_NAN, 175);

        // 美团价格中位数相关特征
        putMapInternalInterval(featureReturn, 176, 180);
        if (feature.containsKey(MEITUAN_ROOM_PRICE)) {
            putMapInternal(featureReturn, feature.get(MEITUAN_ROOM_PRICE), 176);
            putMapInternal(featureReturn, feature.getOrDefault(MEITUAN_ORIGIN_ROOM_PRICE, DEFAULT_ZERO), 177);
            putMapInternal(featureReturn, price - feature.get(MEITUAN_ROOM_PRICE), 186);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 180);
            putMapInternal(featureReturn, DEFAULT_ZERO, 186);
        }

        // 投诉率
        putMapInternalInterval(featureReturn, 181, 185);
        Double complainRate = feature.get(COMPLAIN_RATE);
        if (null != complainRate) {
            putMapInternal(featureReturn, complainRate, 181);
        } else {
            putMapInternal(featureReturn, DEFAULT_NAN, 185);
        }
    }

    private void putMapInternalInterval(Map<Integer, Object> featureMap, int start, int end) {
        for (int i = start; i <= end; i++) {
            featureMap.put(i, DEFAULT_ZERO);
        }
    }

    private void putMapInternal(Map<Integer, Object> featureMap, Double value, int index) {
        if (value != null) {
            featureMap.put(index, value);
        }

    }
}
