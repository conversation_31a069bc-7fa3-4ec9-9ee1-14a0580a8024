package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * D43 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD46 extends DnnFeatureConvertorD45 {

    public static final int DEFAULT_MAX_SUB10 = Integer.MAX_VALUE - 10;
    private static final String SEMI_COLON = ";";
    private static final Map<String, Integer> USER_HOTEL_IDENTITY_MAP = new HashMap<String, Integer>() {{
        put("memberInfoR1", 1);
        put("memberInfoR2", 2);
        put("memberInfoR3", 3);
        put("memberInfoR4", 4);
    }};

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 405 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        if (StringUtils.isNotEmpty(userFeature.getUserIdentityString())) {
            String[] identitySplit = userFeature.getUserIdentityString().split(SEMI_COLON);
            // 用户酒店身份
            for (String identity : identitySplit) {
                if (USER_HOTEL_IDENTITY_MAP.containsKey(identity)) {
                    resultMap.put(FeatureIndexEnum.D46_492.getIndex(), USER_HOTEL_IDENTITY_MAP.get(identity));
                }
            }
        }

        if (hotelFeature.getQunarPayPrice() > 0 && hotelFeature.getQunarPayPrice() < DEFAULT_MAX_SUB10) {
            if (hotelFeature.getCtripPayPrice() > 0 && hotelFeature.getCtripPayPrice() < DEFAULT_MAX_SUB10) {
                int qcPriceDiff = hotelFeature.getQunarPayPrice() - hotelFeature.getCtripPayPrice();
                // qc差价
                resultMap.put(FeatureIndexEnum.D46_493.getIndex(), (double) qcPriceDiff);
                // "Q是否beatC"
                resultMap.put(FeatureIndexEnum.D46_494.getIndex(), qcPriceDiff > 0 ? 1.0 : 0.0);
                // beat率 ：(c价格-q价格)/ q价格
                double qcBeatRate = ((hotelFeature.getCtripPayPrice() - hotelFeature.getQunarPayPrice()) * 1.0) / (1.0 * hotelFeature.getQunarPayPrice());
                resultMap.put(FeatureIndexEnum.D46_495.getIndex(), Numbers.roundStringDouble(qcBeatRate, FeatureConstants.decimals_num));
            }
            if (hotelFeature.getElongPayPrice() > 0 && hotelFeature.getElongPayPrice() < DEFAULT_MAX_SUB10) {
                int qePriceDiff = hotelFeature.getQunarPayPrice() - hotelFeature.getElongPayPrice();
                // qe差价
                resultMap.put(FeatureIndexEnum.D46_496.getIndex(), (double) qePriceDiff);
                // "Q是否beatE"
                resultMap.put(FeatureIndexEnum.D46_497.getIndex(), qePriceDiff > 0 ? 1.0 : 0.0);
                // beat率 ：(e价格-q价格)/ q价格
                double qeBeatRate = ((hotelFeature.getElongPayPrice() - hotelFeature.getQunarPayPrice()) * 1.0) / (1.0 * hotelFeature.getQunarPayPrice());
                resultMap.put(FeatureIndexEnum.D46_498.getIndex(), Numbers.roundStringDouble(qeBeatRate, FeatureConstants.decimals_num));
            }
        }
        return resultMap;
    }

}
