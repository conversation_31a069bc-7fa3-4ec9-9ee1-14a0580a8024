package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.RangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * D43 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD43 extends DnnFeatureConvertorD42 {



    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 427 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        try {
            resultMap.put(FeatureIndexEnum.D43_461.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_412.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_6.getIndex()),
                            3,1.0 )
            );

            resultMap.put(FeatureIndexEnum.D43_462.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_413.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_212.getIndex()),
                            3,1.0 )
            );

            // 酒店报价/组内酒店最高报价
            resultMap.put(FeatureIndexEnum.D43_463.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_414.getIndex()),
                            hotelFeature.getMinPriceWithIdentity() * 1.0,
                            3,1.0 )
            );
            // 酒店报价/组内酒店平均报价
            resultMap.put(FeatureIndexEnum.D43_464.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_415.getIndex()),
                            hotelFeature.getMinPriceWithIdentity() * 1.0,
                            3,10.0 )
            );
            // 酒店报价/组内酒店最低报价
            resultMap.put(FeatureIndexEnum.D43_465.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_416.getIndex()),
                            hotelFeature.getMinPriceWithIdentity() * 1.0,
                            3,1000.0 )
            );
            // poi到酒店的距离 / 组内对应特征的最小值
            resultMap.put(FeatureIndexEnum.D43_466.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_417.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_105.getIndex()),
                            3,100.0 )
            );
            // 用户到酒店的距离 / 组内对应特征的最小值
            resultMap.put(FeatureIndexEnum.D43_467.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_418.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_106.getIndex()),
                            3,100.0 )
            );
            // (酒店卖价/原始价)/(酒店卖价/原始价最小值)
            resultMap.put(FeatureIndexEnum.D43_468.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_419.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D34_HOTEL_DISCOUNTRATE_354.getIndex()),
                            3,100.0 )
            );
            // 酒店24h订单价格中位数 / 组内对应特征的最大值
            resultMap.put(FeatureIndexEnum.D43_469.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_420.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_156.getIndex()),
                            3,1.0 )
            );
            // 酒店6周搜索量 / 组内酒店6周最大搜索量
            resultMap.put(FeatureIndexEnum.D43_470.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_421.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_119.getIndex()),
                            3,1.0 )
            );
            // 酒店评论数 / 组内酒店最大评论数
            resultMap.put(FeatureIndexEnum.D43_471.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_412.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_132.getIndex()),
                            3,1.0 )
            );
            // 酒店点击量占比 / 组内酒店点击量占比最大值
            resultMap.put(FeatureIndexEnum.D43_472.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_423.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D34_CITY_7DAY_CLICK_RAT_344.getIndex()),
                            3,1.0 )
            );
            // 酒店近7天曝光点击率 / 组内酒店7天曝光点击率最大值
            resultMap.put(FeatureIndexEnum.D43_473.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_424.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_290.getIndex()),
                            3,1.0 )
            );
            // 酒店三个月单量 / 组内酒店三个月最大单量
            resultMap.put(FeatureIndexEnum.D43_474.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_425.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_115.getIndex()),
                            3,1.0 )
            );
            // 酒店3个月转化率 / 组内酒店3个月最大转化率
            resultMap.put(FeatureIndexEnum.D43_475.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_426.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_123.getIndex()),
                            3,1.0 )
            );
            // 酒店60天得pv_s2o / 组内酒店60天得pv_s2o最大值
            resultMap.put(FeatureIndexEnum.D43_476.getIndex(),
                    RangeUtil.divisionValue(
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.D41_427.getIndex()),
                            RangeUtil.getValueByResultMap(resultMap,FeatureIndexEnum.DNN_INDEX_304.getIndex()),
                            3,1.0 )
            );

        } catch (Exception e) {
            log.error("DnnFeatureConvertorD43 转换特征出错" , e);
        }

        return resultMap;
    }

}
