package com.qunar.search.common.model.convertor;

import com.qunar.search.common.bean.HotelCheckInInfo;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.RangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;


/**
 * D43 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD47 extends DnnFeatureConvertorD46 {



    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 498 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        LocalDateTime requestDateTime = requestFeature.getRequestDateTime();
        if(requestDateTime != null){
            resultMap.put(FeatureIndexEnum.D47_500.getIndex(), requestDateTime.getHour() * 1.0);
        }

        HotelCheckInInfo hotelCheckInInfo = hotelFeature.getHotelCheckInInfo();
        if(hotelCheckInInfo != null){
            resultMap.put(FeatureIndexEnum.D47_501.getIndex(), hotelCheckInInfo.getOrderCnt() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_502.getIndex(), hotelCheckInInfo.getBeforeHourNum() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_503.getIndex(), hotelCheckInInfo.getHourRate());
            resultMap.put(FeatureIndexEnum.D47_504.getIndex(), hotelCheckInInfo.getDayAvgNightFee());
            resultMap.put(FeatureIndexEnum.D47_505.getIndex(), hotelCheckInInfo.getHourAvgNightFee());
            resultMap.put(FeatureIndexEnum.D47_506.getIndex(), hotelCheckInInfo.getHourDayNightFee());

            resultMap.put(FeatureIndexEnum.D47_507.getIndex(), hotelCheckInInfo.getCityDayOrderNum() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_508.getIndex(), hotelCheckInInfo.getHotelCityOrderDayRate() ) ;
            resultMap.put(FeatureIndexEnum.D47_509.getIndex(), hotelCheckInInfo.getCityBeforeHourOrderNum() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_510.getIndex(), hotelCheckInInfo.getHotelCityOrderHourRate() );
            resultMap.put(FeatureIndexEnum.D47_511.getIndex(), hotelCheckInInfo.getCityDayAvgNightFee());
            resultMap.put(FeatureIndexEnum.D47_512.getIndex(), hotelCheckInInfo.getHotelCityNightFeeDayRate());
            resultMap.put(FeatureIndexEnum.D47_513.getIndex(), hotelCheckInInfo.getCityHourAvgNightFee());
            resultMap.put(FeatureIndexEnum.D47_514.getIndex(), hotelCheckInInfo.getHotelCityNightFeeHourRate());

            resultMap.put(FeatureIndexEnum.D47_515.getIndex(), hotelCheckInInfo.getGeoidDayOrderNum() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_516.getIndex(), hotelCheckInInfo.getHotelGeoidOrderDayRate());
            resultMap.put(FeatureIndexEnum.D47_517.getIndex(), hotelCheckInInfo.getGeoidCityOrdeRdaRate());
            resultMap.put(FeatureIndexEnum.D47_518.getIndex(), hotelCheckInInfo.getGeoidBeforeHourOrderNum() * 1.0);
            resultMap.put(FeatureIndexEnum.D47_519.getIndex(), hotelCheckInInfo.getHotelGeoidOrderHourRate());
            resultMap.put(FeatureIndexEnum.D47_520.getIndex(), hotelCheckInInfo.getGeoidCityOrderHourRate());
            resultMap.put(FeatureIndexEnum.D47_521.getIndex(), hotelCheckInInfo.getGeoidDayAvgNightFee());
            resultMap.put(FeatureIndexEnum.D47_522.getIndex(), hotelCheckInInfo.getHotelGeoidNightFeeDayRate());
            resultMap.put(FeatureIndexEnum.D47_523.getIndex(), hotelCheckInInfo.getGeoidCityNightFeeDayRate());
            resultMap.put(FeatureIndexEnum.D47_524.getIndex(), RangeUtil.divisionValue(  hotelCheckInInfo.getGeoidDayOrderNum(),hotelCheckInInfo.getGeoidBeforeHourOrderNum(),5,1.0 ));
        }

        return resultMap;
    }

}
