package com.qunar.search.common.model.dnn;


import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @DESCRIPTION dnn特征转换值配置
 **/
public class DnnValueConfig {

	public static final DnnValueConfig DEFAULT = new DnnValueConfig(-1, Double.NaN, Double.NaN, Double.NaN, Double.NaN,
			Double.NaN, Double.NaN, Double.NaN, "NAN", Double.NaN, Double.NaN, Double.NaN, Double.NaN, Double.NaN,
			Double.NaN, Double.NaN, Double.NaN);

	/**
	 * 原始特征索引，（未经过归一化之前得索引）
	 */
	public int featureIndex;

	/**
	 * 特征的最小值
	 */
	public double featureMin;

	/**
	 * 特征的0.1分位数
	 */
	double featurePercentOne;

	/**
	 * 特征的1分位数
	 */
	double featureOneDigit;

	/**
	 * 特征的中位数
	 */
	public double featureMedia;

	/**
	 * 特征的3分位数
	 */
	double featureThreeDigit;

	/**
	 * 特征的99分位数
	 */
	double featurePercentNine;

	/**
	 * 特征的最大值
	 */
	public double featureMax;

	/**
	 * 特征的分桶边界信息
	 */
	public String bucketInfo;

	/**
	 * bucketInfo分桶边界信息转化List
	 */
	public List<Double> bucketBoundaries;

	/**
	 * 特征的均值(默认高斯分布)
	 */
	public double gaussMean;

	/**
	 * 特征的标准差(默认高斯分布)
	 */
	public double gaussStd;

	/**
	 * 特征log2变换之后的均值
	 */
	public double log2Mean;

	/**
	 * 特征log2变换之后的标准差
	 */
	public double log2Std;

	/**
	 * 特征sqrt变换之后的均值
	 */
	public double sqrtMean;

	/**
	 * 特征sqrt变换之后的均值
	 */
	public double sqrtStd;

	/**
	 * 特征平方变换之后的均值
	 */
	public double pow2Mean;

	/**
	 * 特征平方变换之后的标准差
	 */
	public double pow2Std;

	public DnnValueConfig(int featureIndex, double featureMin, double featurePercentOne, double featureOneDigit, double featureMedia,
						  double featureThreeDigit, double featurePercentNine, double featureMax, String bucketInfo, double gaussMean,
						  double gaussStd, double log2Mean, double log2Std, double sqrtMean, double sqrtStd, double pow2Mean,
						  double pow2Std) {
		this.featureIndex = featureIndex;
		this.featureMin = featureMin;
		this.featurePercentOne = featurePercentOne;
		this.featureOneDigit = featureOneDigit;
		this.featureMedia = featureMedia;
		this.featureThreeDigit = featureThreeDigit;
		this.featurePercentNine = featurePercentNine;
		this.featureMax = featureMax;
		this.bucketInfo = bucketInfo;
		this.bucketBoundaries = computeBucketBoundaries(bucketInfo);
		this.gaussMean = gaussMean;
		this.gaussStd = gaussStd;
		this.log2Mean = log2Mean;
		this.log2Std = log2Std;
		this.sqrtMean = sqrtMean;
		this.sqrtStd = sqrtStd;
		this.pow2Mean = pow2Mean;
		this.pow2Std = pow2Std;

	}

	private List<Double> computeBucketBoundaries(String bucketInfo) {
		if (DnnConstants.NAN_SET.contains(bucketInfo)) {
			return new ArrayList<>();
		}

		String[] bucketBound = StringUtils.split(bucketInfo.replace("\"", ""), DnnConstants.COMMA);
		List<Double> boundDouble = new ArrayList<>();
		for (String s : bucketBound) {
			Double parseDouble = Double.parseDouble(s);
			boundDouble.add(parseDouble);
		}
		Collections.sort(boundDouble);
		return boundDouble;
	}
}
