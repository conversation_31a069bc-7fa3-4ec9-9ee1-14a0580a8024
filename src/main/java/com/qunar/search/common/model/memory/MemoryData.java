package com.qunar.search.common.model.memory;

import lombok.Getter;

/**
 * 线上内存数据获取方法
 * 1、线上使用静态方式设置默认的 线上内存对象
 * 2、离线使用需要实现 MemoryFunction 接口，实现离线数据获取的方法，rank后调用setFunction
 */
public class MemoryData {

    /**
     * 缓存内存获取数据方法
     */
    @Getter
    private static MemoryFunction function;

    static {
        // 默认线上使用 OnlineMemory
        setFunction(new OnlineMemory());
    }

    /**
     * 1、设置内存数据获取函数
     * 2、public 只要是提供给udf 工程实现
     *
     * @param memoryFunction
     */
    public static void setFunction(MemoryFunction memoryFunction) {
        MemoryData.function = memoryFunction;
    }

}
