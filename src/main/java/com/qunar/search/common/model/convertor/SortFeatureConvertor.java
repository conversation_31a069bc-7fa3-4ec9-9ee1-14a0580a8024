package com.qunar.search.common.model.convertor;


import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;

import java.util.Map;

/**
 * rank 模型特征转换接口类，
 * 注意：
 * 1、线上使用spring bean 方式注入，线下jar 包中new对象使用
 * 2、 convertor 中只写特征转换方法，
 * 3不要依赖外部工具 Qmonitor，Qconfig 等
 */
public interface SortFeatureConvertor {

    /**
     * 特征转换方法
     *
     * @param requestFeature 请求特征数据
     * @param userFeature    用户特征
     * @param hotelFeature   酒店特征数据
     * @return 编码后的特征map ， key 为 int 类型
     */
    Map<Integer, Object> convertObject(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature);
}

