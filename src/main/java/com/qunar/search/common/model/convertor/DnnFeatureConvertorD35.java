package com.qunar.search.common.model.convertor;


import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ONE;

/**
 * D35 特征 Convertor
 * 和v7 是一样的
 */
@Component
public class DnnFeatureConvertorD35 extends DnnFeatureConvertorD34 {

    private static final Double DEFAULT_PRICE = 200D;

    private static final Double DEFAULT_CTR = 0.005D;

    private static final Double FOUR = 4.0D;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 354 的特征索引
        Map<Integer, Object> featureMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        if (MapUtils.isNotEmpty(offlineFeature)) {

            Double cityHotelCnt = offlineFeature.getOrDefault(FeatureType.CITY_HOTELS_CNT, ONE);
            Double geoHotelCnt = offlineFeature.getOrDefault(FeatureType.GEO_HOTELS_CNT, ONE);
            Double aroundHotelsCnt = offlineFeature.getOrDefault(FeatureType.AROUND_HOTELS_CNT, ONE);
            Double geoPv7cnt = offlineFeature.getOrDefault(FeatureType.GEO_PV_7_CNT, ONE);
            Double geoPvAvg7Price = offlineFeature.getOrDefault(FeatureType.GEO_PV_AVG_7_PRICE, DEFAULT_PRICE);
            Double geoOrder7Cnt = offlineFeature.getOrDefault(FeatureType.GEO_ORDER_7_CNT, ONE);
            Double geoClick7Cnt = offlineFeature.getOrDefault(FeatureType.GEO_CLICK_7_CNT, ONE);
            Double aroundPv7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_PV_7_CNT, ONE);
            Double aroundAvg7Price = offlineFeature.getOrDefault(FeatureType.AROUND_AVG_7_PRICE, DEFAULT_PRICE);
            Double aroundOrder7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_ORDER_7_CNT, ONE);
            Double aroundClick7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_CLICK_7_CNT, ONE);
            Double city7Pvs = offlineFeature.getOrDefault(FeatureType.CITY_7_PVS, ONE);
            Double cityPv7Price = offlineFeature.getOrDefault(FeatureType.CITY_PV_7_PRICE, DEFAULT_PRICE);
            Double city7Orders = offlineFeature.getOrDefault(FeatureType.CITY_7_ORDERS, ONE);
            Double city7Clicks = offlineFeature.getOrDefault(FeatureType.CITY_7_CLICKS, ONE);
            Double hotel7Ctr = offlineFeature.getOrDefault(FeatureType.CTR_7_DAY_ZHIXIN, DEFAULT_CTR);
            Double hotel7Orders = offlineFeature.getOrDefault(FeatureType.ORD_CNT_3_WEEK, FeatureConstants.ZERO) / 3;

            // 酒店报价
            double hotelShowPrice = hotelFeature.getEstimatedRenderPrice();
            if(hotelShowPrice <= 0.0 ){
                hotelShowPrice = hotelFeature.getMinPriceWithIdentity();
            }
            if ( hotelShowPrice >= 100000 ){
                hotelShowPrice = cityPv7Price;
            }

            // 355 酒店对应的geo_id范围内的酒店数占当前城市酒店数量的比例
            featureMap.put(FeatureIndexEnum.D35_355.getIndex(), divisionValue(cityHotelCnt, geoHotelCnt));

            // 356 酒店对应的geo_id范围内的过去7天订单量占当前城市订单的比例
            featureMap.put(FeatureIndexEnum.D35_356.getIndex(), divisionValue(city7Orders,geoOrder7Cnt));

            // 357
            featureMap.put(FeatureIndexEnum.D35_357.getIndex(), divisionValue(city7Pvs,geoPv7cnt));

            // 358
            featureMap.put(FeatureIndexEnum.D35_358.getIndex(), divisionValue(city7Clicks,geoClick7Cnt));

            // 359
            featureMap.put(FeatureIndexEnum.D35_359.getIndex(), divisionValue(cityPv7Price,geoPvAvg7Price));

            // 360
            featureMap.put(FeatureIndexEnum.D35_360.getIndex(), divisionValue(cityHotelCnt,aroundHotelsCnt));

            // 361
            featureMap.put(FeatureIndexEnum.D35_361.getIndex(), divisionValue(city7Orders,aroundOrder7Cnt));

            // 362
            featureMap.put(FeatureIndexEnum.D35_362.getIndex(), divisionValue(city7Pvs,aroundPv7Cnt));

            // 363
            featureMap.put(FeatureIndexEnum.D35_363.getIndex(), divisionValue(city7Clicks,aroundClick7Cnt));

            // 364
            featureMap.put(FeatureIndexEnum.D35_364.getIndex(), divisionValue(cityPv7Price,aroundAvg7Price));

            // 365
            featureMap.put(FeatureIndexEnum.D35_365.getIndex(), divisionValue(geoOrder7Cnt,hotel7Orders));

            // 366
            featureMap.put(FeatureIndexEnum.D35_366.getIndex(), divisionValue(geoPvAvg7Price,hotelShowPrice));

            // 367
            featureMap.put(FeatureIndexEnum.D35_367.getIndex(), divisionValue(divisionValue(geoPv7cnt,geoClick7Cnt),hotel7Ctr));

            // 368
            featureMap.put(FeatureIndexEnum.D35_368.getIndex(), divisionValue(aroundOrder7Cnt,hotel7Orders));

            // 369
            featureMap.put(FeatureIndexEnum.D35_369.getIndex(), divisionValue(aroundAvg7Price,hotelShowPrice));

            // 370
            featureMap.put(FeatureIndexEnum.D35_370.getIndex(), divisionValue(divisionValue(aroundPv7Cnt, aroundClick7Cnt), hotel7Ctr));

            // 371
            featureMap.put(FeatureIndexEnum.D35_371.getIndex(), divisionValue(cityPv7Price,hotelShowPrice));
        }

        return featureMap;
    }


    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     */
    private static Double divisionValue(Double divisor, Double dividend){
        if (divisor == 0.0 || dividend == 0.0){
            return FeatureConstants.ZERO;
        } else {
            Double v = Numbers.roundStringDouble(dividend / divisor, 6);

            if (v > 4.0D) {
                return FOUR;
            }
            return v;
        }
    }



}
