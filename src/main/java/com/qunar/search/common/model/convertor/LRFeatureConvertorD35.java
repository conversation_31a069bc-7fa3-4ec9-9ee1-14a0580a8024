package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMap;


@Component
public class LRFeatureConvertorD35 extends L1LRFeatureConvertor {
    private static final int[] LOG_2_NORM_INDEX = {287, 288, 290, 309, 310, 311, 336, 340, 343, 345, 356, 363, 365, 368};

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featMap = super.convert(requestFeature, userFeature, hotelFeature);
        Map<Integer, Object> newMap = addNewFeature(requestFeature, userFeature, hotelFeature);

        int baseIndex = 1095;

        for (int i = 0; i < LOG_2_NORM_INDEX.length; i++) {
            Double value = (Double) newMap.getOrDefault(LOG_2_NORM_INDEX[i], FeatureConstants.ZERO);
            featMap.put(baseIndex + i, log2Norm(value));
        }

        return featMap;
    }

    private Map<Integer, Object> addNewFeature(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featMap = new HashMap<>();

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isNotEmpty(offlineFeature)) {
            //酒店3月内点击率
            putMap(featMap, offlineFeature.get(FeatureType.CTR_3_MONTH), 287, v -> (v != null && v > -1));

            //酒店3月内点击率（置信）
            putMap(featMap, offlineFeature.get(FeatureType.CTR_3_MONTH_ZHIXIN), 288, v -> (v != null && v > -1));

            //酒店7天点击率（置信）
            putMap(featMap, offlineFeature.get(FeatureType.CTR_7_DAY_ZHIXIN), 290, v -> (v != null && v > -1));


            SortScene sortScene = requestFeature.getSortScene();
            switch (sortScene) {
                case NEARBY:
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_14_DAY, -1.0), 309,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_21_DAY, -1.0), 310,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NEARBY_SCENE_S2O_60_DAY, -1.0), 311,
                            v -> (v != null && v > -1.0));
                    break;
                case POI_KEY:
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_14_DAY, -1.0), 309,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_21_DAY, -1.0), 310,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.POIKEY_SCENE_S2O_60_DAY, -1.0), 311,
                            v -> (v != null && v > -1.0));
                    break;
                case SAME_CITY:
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_14_DAY, -1.0), 309,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_21_DAY, -1.0), 310,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.SAMECITY_SCENE_S2O_60_DAY, -1.0), 311,
                            v -> (v != null && v > -1.0));
                    break;
                case NOT_SAME_CITY:
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_14_DAY, -1.0), 309,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_21_DAY, -1.0), 310,
                            v -> (v != null && v > -1.0));
                    putMap(featMap, offlineFeature.getOrDefault(FeatureType.NOTSAMECITY_SCENE_S2O_60_DAY, -1.0), 311,
                            v -> (v != null && v > -1.0));
                    break;
            }
        }

        // 336 -> 可售房型订单占比
        featMap.put(FeatureIndexEnum.D34_ORDERS_RATE_336.getIndex(), Numbers.roundStringDouble(
                hotelFeature.getOrdersRate(), FeatureConstants.decimals_num));

        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 340 -> 7天去位置偏置曝光CTR
            double coecCtr7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_POS_COEC_CTR_7DAY, -1.0),
                    FeatureConstants.decimals_num);
            featMap.put(FeatureIndexEnum.D34_POS_COEC_CTR_7DAY_340.getIndex(), coecCtr7d);

            // 343 -> 该酒店7天非扶持曝光量占该城市非扶持曝光量占比
            double cityShowRate7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_SHOW_RATE, -1.0),
                    FeatureConstants.decimals_num);
            featMap.put(FeatureIndexEnum.D34_CITY_7DAY_SHOW_RAT_343.getIndex(), cityShowRate7d);

            // 345 -> 该酒店7天订单量占该城市订单量占比
            double cityOrderRate7d = Numbers.roundStringDouble(
                    offlineFeature.getOrDefault(FeatureType.HOTEL_CITY_ORDER_RATE, -1.0),
                    FeatureConstants.decimals_num);
            featMap.put(FeatureIndexEnum.D34_CITY_7DAY_ORDER_RAT_345.getIndex(), cityOrderRate7d);


            Double geoOrder7Cnt = offlineFeature.getOrDefault(FeatureType.GEO_ORDER_7_CNT, ONE);
            Double aroundOrder7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_ORDER_7_CNT, ONE);
            Double aroundClick7Cnt = offlineFeature.getOrDefault(FeatureType.AROUND_CLICK_7_CNT, ONE);
            Double city7Orders = offlineFeature.getOrDefault(FeatureType.CITY_7_ORDERS, ONE);
            Double city7Clicks = offlineFeature.getOrDefault(FeatureType.CITY_7_CLICKS, ONE);
            Double hotel7Orders = offlineFeature.getOrDefault(FeatureType.ORD_CNT_3_WEEK, ZERO) / 3;

            // 356 酒店对应的geo_id范围内的过去7天订单量占当前城市订单的比例
            putMap(featMap, divisionValue(city7Orders, geoOrder7Cnt, 6, 4),
                    FeatureIndexEnum.D35_356.getIndex());
            // 363
            putMap(featMap, divisionValue(city7Clicks, aroundClick7Cnt, 6, 4),
                    FeatureIndexEnum.D35_363.getIndex());
            // 365
            putMap(featMap, divisionValue(geoOrder7Cnt, hotel7Orders, 6, 4),
                    FeatureIndexEnum.D35_365.getIndex());
            // 368
            putMap(featMap, divisionValue(aroundOrder7Cnt, hotel7Orders, 6, 4),
                    FeatureIndexEnum.D35_368.getIndex());
        }

        return featMap;
    }

    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    private static double divisionValue(Double divisor,Double dividend,int len, double maxValue){
        if (divisor == null || dividend == null || divisor ==0.0 || dividend ==0.0){
            return 0.0;
        } else {
            double v = Numbers.roundStringDouble(dividend / divisor, len);

            if(v > maxValue){
                v = maxValue;
            }
            return v;

        }

    }

    private static double log2Norm(double x) {
        if (x < 0.0) {
            return 0.0;
        }
        return Math.log(1+x) / Math.log(2);
    }
}
