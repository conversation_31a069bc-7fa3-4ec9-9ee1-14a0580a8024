package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;

/**
 * D42 特征 Convertor
 */
@Component
public class XgbNewUserFeatureConvertor extends XgbSmallSceneFeatureConvertorBase {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        return super.convert(requestFeature, userFeature, hotelFeature);
    }
}
