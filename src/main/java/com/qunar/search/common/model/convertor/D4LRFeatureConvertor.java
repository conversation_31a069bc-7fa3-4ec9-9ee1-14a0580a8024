package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.enums.FeatureType.ADR_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.ADR_SHOW_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_ORD_PV_7_DAY;
import static com.qunar.search.common.enums.FeatureType.IOS_SHOW_PV_7_DAY;
import static com.qunar.search.common.model.convertor.ConvertorBase.getBucketIndex;
import static com.qunar.search.common.model.convertor.ConvertorBase.getValueByPlatform;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMap;
import static com.qunar.search.common.model.convertor.ConvertorObject.removeIndex;

@Component
public class D4LRFeatureConvertor extends LRFeatureConvertor {


    private static final double[] S2O_7D_BUCKET = {0, 0.0011, 0.0016, 0.0022, 0.0027, 0.0033, 0.0041, 0.0051, 0.0068, 0.01, Double.MAX_VALUE};

    @Override
    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {
        EnumMap<FeatureType, Double> feature = hotelFeature.getOfflineFeature();

        Map<Integer, Object> featureMap = super.convert(requestFeature, userFeature, hotelFeature);
        if (MapUtils.isEmpty(feature)) {
            return featureMap;
        }

        // 添加新特征
        putMap(featureMap, ONE, 601, feature.get(FeatureType.ORD_CNT_6_MONTH_INDEX));
        
        putMap(featureMap, ONE, 621, feature.get(FeatureType.ORD_CNT_3_MONTH_INDEX));
        putMap(featureMap, ONE, 641, feature.get(FeatureType.ORD_CNT_3_WEEK_INDEX));
        putMap(featureMap, ONE, 661, feature.get(FeatureType.S2O_6_MONTH_INDEX));
        putMap(featureMap, ONE, 681, feature.get(FeatureType.S2O_3_MONTH_INDEX));
        putMap(featureMap, ONE, 701, feature.get(FeatureType.S2O_3_WEEK_INDEX));
        putMap(featureMap, ONE, 721, feature.get(FeatureType.COMMENT_CNT_INDEX));
        putMap(featureMap, ONE, 741, feature.get(FeatureType.COMMENT_SCORE_INDEX));
        putMap(featureMap, ONE, 761, feature.get(FeatureType.COMMENT_GOOD_RATE_INDEX));

        // 对酒店评分重新分段，将原来评分的特征置0
        removeIndex(featureMap, 311, 13);

        // 将hotel最近1周S2O原特征位置0
        removeIndex(featureMap, 351, 60);

        // 将hotel最近3周订单数原特征位置0
        removeIndex(featureMap, 301, 8);

        // 将7天S2O重新分段
        double showpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_SHOW_PV_7_DAY, IOS_SHOW_PV_7_DAY, hotelFeature.getPlatformShowPv7());
        double orderpv7 = getValueByPlatform(hotelFeature.getOfflineFeature(), requestFeature.getPlatform(),
                ADR_ORD_PV_7_DAY, IOS_ORD_PV_7_DAY, hotelFeature.getPlatformOrderPv7());

        if (showpv7 == 0.0 || orderpv7 == 0.0) {
            putMap(featureMap, ONE, 781, 0);
        } else {
            int bucketIndex = getBucketIndex(S2O_7D_BUCKET, orderpv7 / showpv7);
            putMap(featureMap, ONE, 781, bucketIndex);
        }

        return featureMap;
    }
}
