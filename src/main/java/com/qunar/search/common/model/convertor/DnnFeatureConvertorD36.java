package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.ZERO;
import static com.qunar.search.common.model.convertor.ConvertorObject.putMapNotNull;

/**
 * D36 特征 Convertor
 */
@Component
public class DnnFeatureConvertorD36 extends DnnFeatureConvertorD35 {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 371 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();

        if (MapUtils.isNotEmpty(offlineFeature)) {
            // 372 酒店7天的uv_ctcvr
            resultMap.put(FeatureIndexEnum.D36_372.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_7D, ZERO));
            // 373 酒店21天的uv_ctcvr
            resultMap.put(FeatureIndexEnum.D36_373.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_21D, ZERO));
            // 374 酒店90天的uv_ctcvr
            resultMap.put(FeatureIndexEnum.D36_374.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTCVR_90D, ZERO));

            // 375 酒店7天的uv_ctr
            resultMap.put(FeatureIndexEnum.D36_375.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_7D, ZERO));
            // 376 酒店21天的uv_ctr
            resultMap.put(FeatureIndexEnum.D36_376.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_21D, ZERO));
            // 377 酒店90天的uv_ctr
            resultMap.put(FeatureIndexEnum.D36_377.getIndex(), offlineFeature.getOrDefault(FeatureType.UV_CTR_90D, ZERO));

            // 378 - 383 分场景uv ctcvr
            fillSceneUvCtrCvr(requestFeature.getSortScene(), offlineFeature, resultMap);

            // 384 酒店7天内的下单用户数占该城市总下单用户总数的比
            putMapNotNull(resultMap, FeatureIndexEnum.D36_384.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_7D));

            // 385 酒店21天内的下单用户数占该城市总下单用户总数的比
            putMapNotNull(resultMap, FeatureIndexEnum.D36_385.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_21D));

            // 386 酒店90天内的下单用户数占该城市总下单用户总数的比
            putMapNotNull(resultMap, FeatureIndexEnum.D36_386.getIndex(), offlineFeature.get(FeatureType.UV_ORDER_IN_CITY_RATE_90D));

            // 387 酒店靠近地铁，则21天内近地铁的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_387.getIndex(), offlineFeature.get(FeatureType.SUBWAY_ORDER_RATE_21D));

            // 388 酒店靠近机场，则21天内近机场的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_388.getIndex(), offlineFeature.get(FeatureType.AIRPORT_ORDER_RATE_21D));

            // 389 酒店靠近火车站，则21天内近火车站的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_389.getIndex(), offlineFeature.get(FeatureType.RAILWAY_ORDER_RATE_21D));

            // 390 酒店靠近医院，则21天内近医院的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_390.getIndex(), offlineFeature.get(FeatureType.HOSPITAL_ORDER_RATE_21D));

            // 391 酒店靠近大学，则21天内近大学的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_391.getIndex(), offlineFeature.get(FeatureType.UNIVERSITY_ORDER_RATE_21D));

            // 392 酒店靠近景区，则21天内近景区的订单占城市所有订单占比，否则为0
            putMapNotNull(resultMap, FeatureIndexEnum.D36_392.getIndex(), offlineFeature.get(FeatureType.SPOTPLACES_ORDER_RATE_21D));
        }

        // 393 用户搜索城市是否用户常住地
        if (StringUtils.isNotEmpty(requestFeature.getRootCityCode()) && userFeature.getUserProfile() != null
                && StringUtils.isNotEmpty(userFeature.getUserProfile().getOftenCity())) {
            resultMap.put(FeatureIndexEnum.D36_393.getIndex(),
                    StringUtils.equals(requestFeature.getRootCityCode(), userFeature.getUserProfile().getOftenCity()) ? FeatureConstants.ONE : ZERO);
        }

        // 394 用户当前请求小时（0-23）one-hot处理
        resultMap.put(FeatureIndexEnum.D36_394.getIndex(), requestFeature.getRequestDateTime().getHour());

        // 395 入住和请求相差的天数（-1-8）+ 1 one-hot处理
        resultMap.put(FeatureIndexEnum.D36_395.getIndex(), requestFeature.getDiffDaysFeature() == null ? 0 : requestFeature.getDiffDaysFeature() );

        return resultMap;
    }

    /**
     * 根据对应场景 填充uv ctr ctcvr 特征
     */
    private static void fillSceneUvCtrCvr(SortScene sortScene, EnumMap<FeatureType, Double> offlineFeature, Map<Integer, Object> featReturn){
        Double sceneUvCtcvr7day;
        Double sceneUvCtcvr21day;
        Double sceneUvCtcvr90day;
        Double sceneUvCtr7day;
        Double sceneUvCtr21day;
        Double sceneUvCtr90day;

        switch (sortScene) {
            case NEARBY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.NEARBY_UV_CTR_90D, ZERO);
                break;
            case POI_KEY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.POI_KEY_UV_CTR_90D, ZERO);
                break;

            case NOT_SAME_CITY:
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.NOT_SAME_CITY_UV_CTR_90D, ZERO);
                break;

            default: // SAME_CITY and others
                sceneUvCtcvr7day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_7D, ZERO);
                sceneUvCtcvr21day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_21D, ZERO);
                sceneUvCtcvr90day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTCVR_90D, ZERO);
                sceneUvCtr7day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_7D, ZERO);
                sceneUvCtr21day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_21D, ZERO);
                sceneUvCtr90day = offlineFeature.getOrDefault(FeatureType.SAME_CITY_UV_CTR_90D, ZERO);
                break;
        }
        // 378 酒店分场景7天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_378.getIndex(), sceneUvCtcvr7day);
        // 379 酒店分场景21天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_379.getIndex(), sceneUvCtcvr21day);
        // 380 酒店分场景90天的uv_ctcvr
        featReturn.put(FeatureIndexEnum.D36_380.getIndex(), sceneUvCtcvr90day);
        // 381 酒店分场景7天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_381.getIndex(), sceneUvCtr7day);
        // 382 酒店分场景21天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_382.getIndex(), sceneUvCtr21day);
        // 383 酒店分场景90天的uv_ctr
        featReturn.put(FeatureIndexEnum.D36_383.getIndex(), sceneUvCtr90day);
    }

}
