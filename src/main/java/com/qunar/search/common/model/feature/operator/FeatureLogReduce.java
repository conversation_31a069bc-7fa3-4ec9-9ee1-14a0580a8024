package com.qunar.search.common.model.feature.operator;

import com.qunar.search.common.math.data.UserBehave;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线上数据打印日志时，数据缩减方法类
 */
class FeatureLogReduce {

    /**
     * 缩减userBehave中部分特征
     */
    static void reduceUserBehave(UserBehave userBehave, Set<ModelHotelFeature> hotelSet) {
        if (null == userBehave || CollectionUtils.isEmpty(hotelSet)) {
            return;
        }

        Map<String, Double> seqMap = userBehave.getHotelSeqRateMap();
        if (MapUtils.isEmpty(seqMap)) {
            return;
        }

        Map<String, Double> reduceMap = hotelSet.stream().filter(s -> seqMap.containsKey(s.getHotelSeq()))
                .collect(Collectors.toMap(ModelHotelFeature::getHotelSeq, s -> seqMap.get(s.getHotelSeq())));
        userBehave.setHotelSeqRateMap(reduceMap);
    }
}
