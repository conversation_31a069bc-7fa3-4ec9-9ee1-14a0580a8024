package com.qunar.search.common.model.convertor;

import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * D38 特征 Convertor
 */
@Component
public class DnnFeatureConvertorD38 extends DnnFeatureConvertorD36 {

    private final static int CITY_SCENE_S2O_LEN = 4;

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 395 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        List<Double> estimatedCitySceneS2o = hotelFeature.getEstimatedCitySceneS2o();
        if (estimatedCitySceneS2o != null && !estimatedCitySceneS2o.isEmpty()) {
            int baseIdx = 396;
            for (int i = 0; i < estimatedCitySceneS2o.size(); i++) {
                if (i < CITY_SCENE_S2O_LEN) {
                    resultMap.put(baseIdx + i, estimatedCitySceneS2o.get(i));
                }
            }
        }
        return resultMap;
    }
}
