package com.qunar.search.common.model.memory;

import com.qunar.search.common.bean.HotelInfo;
import com.qunar.search.common.bean.HotelStat;
import com.qunar.search.common.dc.HotelInfoHolder;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelVariableFeatureType;
import com.qunar.search.common.service.HotelStatDataService;
import com.qunar.search.common.util.LibSvmUtil;

import java.util.EnumMap;
import java.util.List;

/**
 * 线上内存数据获取方法
 */
public class OnlineMemory implements MemoryFunction {

    @Override
    public HotelInfo getHotelBaseInfo(String hotelSeq, DataVersion dataVersion) {
        return HotelInfoHolder.getHotel(hotelSeq);
    }

    @Override
    public EnumMap<FeatureType, Double> getOfflineFeature(String hotelSeq, DataVersion dataVersion) {
        HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(hotelSeq);
        if (null == hotelStat) {
            return null;
        }
        return hotelStat.getOfflineFeature();
    }

    @Override
    public String getOfflineFeatureLibSvm(String hotelSeq, DataVersion dataVersion) {
        return LibSvmUtil.enumMapToString(getOfflineFeature(hotelSeq, dataVersion));
    }

    @Override
    public EnumMap<HotelVariableFeatureType, Object> getOfflineVariableFeature(String hotelSeq, DataVersion dataVersion) {
        HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(hotelSeq);
        if (null == hotelStat) {
            return null;
        }
        return hotelStat.getOfflineVariableFeature();
    }

    @Override
    public String getOfflineVariableFeatureLibSvm(String hotelSeq, DataVersion dataVersion) {
        return LibSvmUtil.variableEnumMapToString(getOfflineVariableFeature(hotelSeq, dataVersion));
    }

    @Override
    public List<Double> getHotelEmbedding(String hotelSeq, DataVersion dataVersion) {
        HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(hotelSeq);
        if (null == hotelStat) {
            return null;
        }
        return hotelStat.getAlgoEmbeddingFeature();
    }

    @Override
    public List<Double> getHotelEmbeddingV2(String hotelSeq, DataVersion dataVersion) {
        HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(hotelSeq);
        if (null == hotelStat) {
            return null;
        }
        return hotelStat.getAlgoEmbeddingFeatureV2();
    }
}
