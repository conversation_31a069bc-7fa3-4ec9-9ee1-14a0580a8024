package com.qunar.search.common.model.convertor;


import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import com.qunar.search.common.util.RangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * D37 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD37 extends DnnFeatureConvertorD36 {


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 397 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);


        try {

            // 酒店有身份报价
            double  hotelShowPrice = hotelFeature.getMinPriceWithIdentity();
            // 用户实时点击均价
            double clickAvgPrice = userFeature.getRealtimeClickAvgPrice();

            // 400 当前酒店sort报价 / 用户实时点击平均价格
            double featureValue400 = RangeUtil.divisionValue(clickAvgPrice,hotelShowPrice,6,10);
            if (featureValue400 > 0) {
                resultMap.put(FeatureIndexEnum.D37_400.getIndex(), featureValue400);
            }

            // 401 当前酒店和同城最近点击三家酒店的平均距离
            List<GLatLng> userRealClickGLatLngMap = userFeature.getUserRealClickGLatLngMap();
            int realClickNum = 0;
            double total_distance = 0;
            for (GLatLng gLatLng: userRealClickGLatLngMap) {
                if(gLatLng != null && hotelFeature.getLatLng() != null ){
                    double distance = gLatLng.distance(hotelFeature.getLatLng());
                    total_distance += distance;
                    realClickNum += 1;

                    if (realClickNum >= 3){
                        break;
                    }
                }
            }
            resultMap.put(FeatureIndexEnum.D37_401.getIndex(), RangeUtil.divisionValue(realClickNum*1.0,total_distance,6,1000000) );

            // 402 当前酒店在用户实时点击五家酒店中的平均点击-下单关联性
            Map<String, Double> mapRealTimeOrderRela = userFeature.getMapRealTimeOrderRela();
            Double clickOrderReal = mapRealTimeOrderRela.get(hotelFeature.getHotelSeq());
            if (clickOrderReal != null) {
                resultMap.put(FeatureIndexEnum.D37_402.getIndex(), clickOrderReal);
            }
        } catch (Exception e){
            log.error("DnnFeatureConvertorD37 转换特征出错",e);
        }

        return resultMap;
    }

}
