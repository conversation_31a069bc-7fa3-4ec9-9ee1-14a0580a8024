package com.qunar.search.common.model.convertor;

import com.qunar.search.common.constants.FeatureConstants;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.EnumMap;
import java.util.Map;

import static com.qunar.search.common.constants.FeatureConstants.NEGATIVE_ONE;
import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossDiff;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;

/**
 * <AUTHOR>
 * @create 2020-09-22 下午7:10
 * @DESCRIPTION 添加部分新特征D26
 **/
@Component
public class XgboostD26FeatureConvertor extends XgboostD23FeatureConvertor {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> featReturn = super.convert(requestFeature, userFeature, hotelFeature);

        EnumMap<FeatureType, Double> offlineFeature = hotelFeature.getOfflineFeature();
        if (MapUtils.isEmpty(offlineFeature)) {
            return featReturn;
        }
        
        Map<Integer, Double> checkInOrderRate = userFeature.getCheckInOrderRate();
        if (null == checkInOrderRate) {
            checkInOrderRate = Collections.emptyMap();
        }

        int checkInNum = requestFeature.getCheckInNum();
        checkInNum = Math.min(checkInNum, 8);
        // 用户入住天数
        putMap(featReturn, checkInNum * 1.0, 291);

        if (checkInNum > 0) {
            // 用户入住天数对应的酒店不同天数的订单数 [292]  特征取得有问题
            putMap(featReturn,
                    offlineFeature.getOrDefault(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_1_DAY.getIndex() + checkInNum - 1), NEGATIVE_ONE),
                    292, v -> (v != null && v > 0));
            // 用户入住天数对应的酒店不同天数的订单占比 [293]   特征取得有问题
            putMap(featReturn,
                    offlineFeature.getOrDefault(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_5_DAY.getIndex() + checkInNum - 1), NEGATIVE_ONE),
                    293, v -> (v != null && v > 0));
            // 用户入住天数对应的用户过去入住天数的订单占比-酒店入住天数的占比 [299]  特征取得有问题
            putCrossDiff(featReturn, 294, checkInOrderRate.getOrDefault(checkInNum, FeatureConstants.ZERO), s -> s > 0,
                    offlineFeature.getOrDefault(FeatureType.matchIndex(FeatureType.CHECK_IN_ORDER_NUM_5_DAY.getIndex() + checkInNum - 1), NEGATIVE_ONE),
                    v -> (v != null && v > 0));
        }

        //用户入住那天是周末时，酒店的历史周末的销售订单占比
        putMap(featReturn, requestFeature.isWeekend() ? FeatureConstants.ONE : FeatureConstants.ZERO, 295);

        putMap(featReturn, requestFeature.isWeekend()
                        ? offlineFeature.getOrDefault(FeatureType.WEEKEND_ORDER_RATE, NEGATIVE_ONE)
                        : offlineFeature.getOrDefault(FeatureType.NO_WEEKEND_ORDER_RATE, NEGATIVE_ONE),
                296, v -> (v != null && v > -1.0));

        // 用户预订日期内包含节假日和周末占比[297]
        putMap(featReturn, requestFeature.getCheckWeekendHolidayRate(), 297);

        // 酒店历史订单（周末间夜+节假日间夜）占比[298]
        putMap(featReturn, offlineFeature.getOrDefault(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE, NEGATIVE_ONE), 298,
                v -> (v != null && v > -1.0));

        // 用户预定日期内的节假日和周末占比 - 酒店历史订单节假日和周末占比（间夜） [299]
        putCrossDiff(featReturn, 299, requestFeature.getCheckWeekendHolidayRate(), s -> true,
                offlineFeature.getOrDefault(FeatureType.WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE, NEGATIVE_ONE),
                v -> (v != null && v > -1.0));


        return featReturn;
    }
}
