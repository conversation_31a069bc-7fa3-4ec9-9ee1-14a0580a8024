package com.qunar.search.common.model.convertor;

import com.google.common.base.Joiner;
import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.HotelVariableFeatureType;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.enums.cUserDatas.CUserClickBookCollectData;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.enums.cUserDatas.UserFlightHotelTrainBnbOrderData;
import com.qunar.search.common.enums.cUserDatas.UserPoiOrderDistancePriceData;
import com.qunar.search.common.math.data.UserBehaveSequence;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.qunar.search.common.model.convertor.ConvertorBase.putCrossRate;
import static com.qunar.search.common.model.convertor.ConvertorBase.putMap;


/**
 * D55 特征 Convertor
 */
@Component
@Slf4j
public class DnnFeatureConvertorD55 extends DnnFeatureConvertorD54 {

    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);
        List<String> userActivityIdsList = requestFeature.getActivityIdsList();
        List<String> userCouponList = userFeature.getUserCouponList();

        if (CollectionUtils.isNotEmpty(userActivityIdsList)) {
            resultMap.put(FeatureIndexEnum.D55_655.getIndex(), userActivityIdsList);
        }

        if (CollectionUtils.isNotEmpty(userCouponList)) {
            resultMap.put(FeatureIndexEnum.D55_656.getIndex(), userCouponList);
        }

        EnumMap<HotelVariableFeatureType, Object> offlineVariableFeature = hotelFeature.getOfflineVariableFeature();
        if (MapUtils.isNotEmpty(offlineVariableFeature)) {

            Object hotelActivityObj = offlineVariableFeature.get(HotelVariableFeatureType.ACTIVITY_ID_SET);
            if (null != hotelActivityObj) {
                resultMap.put(FeatureIndexEnum.D55_657.getIndex(), (List<String>) hotelActivityObj);
                getUserHotelActivityIntersection(resultMap, FeatureIndexEnum.D55_658.getIndex(), userActivityIdsList, (List<String>) hotelActivityObj);
            }

            Object hotelActivityBaoMingTypeObj = offlineVariableFeature.get(HotelVariableFeatureType.ACTIVITY_BAOMING_TYPE_MAP);
            if (null != hotelActivityBaoMingTypeObj) {
                getUserHotelMapInfoIntersection(resultMap, FeatureIndexEnum.D55_659.getIndex(), userActivityIdsList, (Map<String, String>) hotelActivityBaoMingTypeObj);
            }

            Object hotelActivityClassObj = offlineVariableFeature.get(HotelVariableFeatureType.ACTIVITY_CLASSIFICATION_MAP);
            if (null != hotelActivityClassObj) {
                getUserHotelMapInfoIntersection(resultMap, FeatureIndexEnum.D55_660.getIndex(), userActivityIdsList, (Map<String, String>) hotelActivityClassObj);
            }

            Object hotelActivitOrderObj = offlineVariableFeature.get(HotelVariableFeatureType.ACTIVITY_ORDER_CNT_MAP);
            if (null != hotelActivitOrderObj) {
                getUserHotelIntersectionSumRate(resultMap, FeatureIndexEnum.D55_661.getIndex(), FeatureIndexEnum.D55_662.getIndex(), userActivityIdsList, (Map<String, Double>) hotelActivitOrderObj);
            }

            Object hotelPreferIdListObj = offlineVariableFeature.get(HotelVariableFeatureType.PREFER_ID_SET);
            if (null != hotelPreferIdListObj) {
                resultMap.put(FeatureIndexEnum.D55_663.getIndex(), (List<String>) hotelPreferIdListObj);
                getUserHotelActivityIntersection(resultMap, FeatureIndexEnum.D55_664.getIndex(), userCouponList, (List<String>) hotelPreferIdListObj);
            }

            Object hotelPreferWayObj = offlineVariableFeature.get(HotelVariableFeatureType.PREFER_WAY_MAP);
            if (null != hotelPreferWayObj) {
                getUserHotelMapInfoIntersection(resultMap, FeatureIndexEnum.D55_665.getIndex(), userCouponList, (Map<String, String>) hotelPreferWayObj);
            }

            Object hotelPreferVoucheruseObj = offlineVariableFeature.get(HotelVariableFeatureType.PREFER_VOUCHERUSE_MAP);
            if (null != hotelPreferVoucheruseObj) {
                getUserHotelMapInfoIntersection(resultMap, FeatureIndexEnum.D55_666.getIndex(), userCouponList, (Map<String, String>) hotelPreferVoucheruseObj);
            }

            Object hotelPreferOrderObj = offlineVariableFeature.get(HotelVariableFeatureType.PREFER_ORDER_CNT_MAP);
            if (null != hotelPreferOrderObj) {
                getUserHotelIntersectionSumRate(resultMap, FeatureIndexEnum.D55_667.getIndex(), FeatureIndexEnum.D55_668.getIndex(), userCouponList, (Map<String, Double>) hotelPreferOrderObj);
            }
        }

        // 48小时内点击行为序列
        UserBehaveSequence realTimeListClickSequence = userFeature.getRealTimeListClickSequence();
        if (null != realTimeListClickSequence) {

            List<String> cityList = realTimeListClickSequence.getHotelCityList();
            if (CollectionUtils.isNotEmpty(cityList)) {
                int limitSize = Math.min(cityList.size(), CLICK_LIST_LIMIT_SIZE);
                List<String> limitList = cityList.subList(0, limitSize);
                resultMap.put(FeatureIndexEnum.D55_669.getIndex(), limitList);
            }

            List<String> timeList = realTimeListClickSequence.getTimeList();
            if (CollectionUtils.isNotEmpty(timeList)) {
                int limitSize = Math.min(timeList.size(), CLICK_LIST_LIMIT_SIZE);
                List<String> limitList = timeList.subList(0, limitSize);
                List<Integer> collect = IntStream.range(1, limitList.size()+1).boxed().collect(Collectors.toList());
                resultMap.put(FeatureIndexEnum.D55_670.getIndex(), collect);
            }
        }

        return resultMap;
    }

    /**
     * 获取list交集
     * @param resultMap
     * @param index
     * @param userInfoIdsList
     * @param hotelInfoList
     */
    private void getUserHotelActivityIntersection(Map<Integer, Object> resultMap, int index,
                                                     List<String> userInfoIdsList, List<String> hotelInfoList) {
        if (CollectionUtils.isNotEmpty(userInfoIdsList) && CollectionUtils.isNotEmpty(hotelInfoList)) {
            List<String> intersection = userInfoIdsList.stream().filter(hotelInfoList::contains).collect(Collectors.toList());
            resultMap.put(index, intersection);
        }
    }

    /**
     * 获取map的交集
     * @param resultMap
     * @param index
     * @param userActivityIdsList
     * @param hotelInfoMap
     */
    private void getUserHotelMapInfoIntersection(Map<Integer, Object> resultMap, int index,
                                                     List<String> userActivityIdsList, Map<String, String> hotelInfoMap) {
        if (MapUtils.isNotEmpty(hotelInfoMap) && CollectionUtils.isNotEmpty(userActivityIdsList)) {
            List<String> intersection = userActivityIdsList.stream()
                    .map(s -> hotelInfoMap.getOrDefault(s, null))
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            resultMap.put(index, intersection);
        }
    }

    /**
     * 获取map的和比值
     * @param resultMap
     * @param sumIndex
     * @param rateIndex
     * @param userActivityIdsList
     * @param hotelInfoMap
     */
    private void getUserHotelIntersectionSumRate(Map<Integer, Object> resultMap, int sumIndex, int rateIndex,
                                                         List<String> userActivityIdsList, Map<String, Double> hotelInfoMap) {
        if (MapUtils.isNotEmpty(hotelInfoMap) && CollectionUtils.isNotEmpty(userActivityIdsList)) {
            double hSum = hotelInfoMap.values().stream().mapToDouble(Double::doubleValue).sum();
            double interSum = userActivityIdsList.stream()
                    .map(s -> hotelInfoMap.getOrDefault(s,0.0)).mapToDouble(Double::doubleValue).sum();
            resultMap.put(sumIndex, hSum);
            resultMap.put(rateIndex, interSum / (hSum + 1.0));
        }

    }
}