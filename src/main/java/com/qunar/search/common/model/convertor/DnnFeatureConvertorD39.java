package com.qunar.search.common.model.convertor;

import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.Platform;
import com.qunar.search.common.enums.SortScene;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * D37 特征 Convertor
 */
@Component
public class DnnFeatureConvertorD39 extends DnnFeatureConvertorD37 {

    private static final Integer ONE = 1;
    private static final Integer TWO = 2;
    private static final Integer THREE = 3;
    private static final Integer FOUR = 4;


    protected Map<Integer, Object> convert(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature) {

        // 返回 1 - 402 的特征索引
        Map<Integer, Object> resultMap = super.convert(requestFeature, userFeature, hotelFeature);

        SortScene scene = requestFeature.getSortScene();
        Platform platform = requestFeature.getPlatform();

        // 平台 索引[404]
        resultMap.put(FeatureIndexEnum.D39_404.getIndex(), (platform != null && platform.equals(Platform.IOS)) ? ONE : TWO);

        // scene 索引 [405]
        switch (scene) {
            case NEARBY:
                resultMap.put(FeatureIndexEnum.D39_405.getIndex(), TWO);
                break;
            case POI_KEY:
                resultMap.put(FeatureIndexEnum.D39_405.getIndex(), THREE);
                break;
            case NOT_SAME_CITY:
                resultMap.put(FeatureIndexEnum.D39_405.getIndex(), FOUR);
                break;
            default: //same_city or others
                resultMap.put(FeatureIndexEnum.D39_405.getIndex(), ONE);
                break;
        }

        return resultMap;
    }
}
