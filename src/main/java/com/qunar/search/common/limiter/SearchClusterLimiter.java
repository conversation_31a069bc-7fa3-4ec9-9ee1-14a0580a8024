package com.qunar.search.common.limiter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @description: 搜索监控注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SearchClusterLimiter {
    /**
     * 资源名称，对应captain中的资源限流接口
     */
    String resource();

    /**
     * 是否热点限流, 其实本可以从LimiterClient中的limiter对象中获取，但组件未将该对象暴露给外部使用，故而需要配置
     */
    boolean isHotspot() default false;

}
