package com.qunar.search.common.limiter;

import com.qunar.corp.tcdev.limiter.client.LimiterClient;
import com.qunar.corp.tcdev.limiter.client.LimiterStatus;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.base.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 *
 */
@Aspect
@Component
@Slf4j
public class SearchClusterLimiterAspect {

    private static final String DEFAULT_APP_CODE = "default";

    @Around("@annotation(SearchClusterLimiter)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前方法的签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取方法上的 SearchClusterLimiter 注解
        SearchClusterLimiter searchClusterLimiter = methodSignature.getMethod().getAnnotation(SearchClusterLimiter.class);
        if (searchClusterLimiter == null) {
            // 如果方法上没有 SearchClusterLimiter 注解，则直接放行，理论上不会走到这，因为切面的定义就是有注解
            log.warn("无注解方法，进入到切面");
            return joinPoint.proceed();
        }
        String appCode = "";
        if (searchClusterLimiter.isHotspot()) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            appCode = request.getHeader("QApp-Name");
            if(StringUtils.isBlank(appCode)) {
                appCode = DEFAULT_APP_CODE;
            }
        }
        LimiterStatus limiterStatus = getLimiterStatus(searchClusterLimiter, appCode);
        //触发限流 captain配置 限流开关为 "打开"
        if (LimiterStatus.BLOCK.equals(limiterStatus)) {
            log.warn("触发集群限流, method: {}, resource: {}", methodSignature.getMethod().getName(), searchClusterLimiter.resource());
            QMonitor.recordOne( "block_" + searchClusterLimiter.resource() + (StringUtils.isBlank(appCode) ? "" : "_" + appCode));
            return ApiResponse.returnFail("触发集群限流, resource: " + searchClusterLimiter.resource());
        }
        if (LimiterStatus.BLOCK_ONLY_MONITOR.equals(limiterStatus)) {
            QMonitor.recordOne( "block_monitor_" + searchClusterLimiter.resource() + (StringUtils.isBlank(appCode) ? "" : "_" + appCode));
            log.warn("达到集群限流上限 but only monitor, method: {}, resource: {}", methodSignature.getMethod().getName(), searchClusterLimiter.resource());
        }
        return joinPoint.proceed();
    }

    /**
     *
     * @param searchClusterLimiter 注解
     * @return 限流状态
     */
    private static LimiterStatus getLimiterStatus(SearchClusterLimiter searchClusterLimiter, String appCode) {
        LimiterClient limiterClient = LimiterClient.getInstance();
        LimiterStatus limiterStatus;
        if (searchClusterLimiter.isHotspot()) {
            limiterStatus = limiterClient.acquireHotspotWithFlag(searchClusterLimiter.resource(), appCode, 1);
        } else {
            limiterStatus = limiterClient.acquireWithFlag(searchClusterLimiter.resource(), 1);
        }
        return limiterStatus;
    }
}
