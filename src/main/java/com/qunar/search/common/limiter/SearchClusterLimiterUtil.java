package com.qunar.search.common.limiter;

import com.qunar.corp.tcdev.limiter.client.LimiterClient;
import com.qunar.corp.tcdev.limiter.client.LimiterStatus;
import com.qunar.hotel.qmonitor.QMonitor;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;
import java.util.function.Runnable;

/**
 * 集群限流工具类
 */
@Slf4j
public class SearchClusterLimiterUtil {

    /**
     * 热点限流（一个资源对每个appCode都存在对应的qps限制）,不适用这个方法，使用注解就好，所以这里没有考虑热点限流
     * 这里考虑的项目内部对某一资源的集群限流
     *
     * @param resource 资源名称
     * @param supplier 业务逻辑
     * @return 执行结果
     */
    public static <T> T executeWithLimit(String resource, Supplier<T> supplier) {
        LimiterClient limiterClient = LimiterClient.getInstance();
        LimiterStatus limiterStatus = limiterClient.acquireWithFlag(resource, 1);
        
        // 触发限流
        if (LimiterStatus.BLOCK.equals(limiterStatus)) {
            log.warn("触发集群限流, resource: {}", resource);
            QMonitor.recordOne("block_" + resource);
            throw new RuntimeException("触发集群限流, resource: " + resource);
        }
        if (LimiterStatus.BLOCK_ONLY_MONITOR.equals(limiterStatus)) {
            QMonitor.recordOne("block_monitor_");
            log.warn("达到集群限流上限 but only monitor, resource: {}", resource);
        }
        
        return supplier.get();
    }

    /**
     * 集群限流执行方法（无返回值版本）
     * 热点限流（一个资源对每个appCode都存在对应的qps限制）,不适用这个方法，使用注解就好，所以这里没有考虑热点限流
     * 这里考虑的项目内部对某一资源的集群限流
     *
     * @param resource 资源名称
     * @param runnable 业务逻辑（无返回值）
     */
    public static void executeWithLimitVoid(String resource, Runnable runnable) {
        LimiterClient limiterClient = LimiterClient.getInstance();
        LimiterStatus limiterStatus = limiterClient.acquireWithFlag(resource, 1);

        // 触发限流
        if (LimiterStatus.BLOCK.equals(limiterStatus)) {
            log.warn("触发集群限流, resource: {}", resource);
            QMonitor.recordOne("block_" + resource);
            throw new RuntimeException("触发集群限流, resource: " + resource);
        }
        if (LimiterStatus.BLOCK_ONLY_MONITOR.equals(limiterStatus)) {
            QMonitor.recordOne("block_monitor_");
            log.warn("达到集群限流上限 but only monitor, resource: {}", resource);
        }

        runnable.run();
    }
}