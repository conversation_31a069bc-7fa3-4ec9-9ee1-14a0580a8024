package com.qunar.search.common.price.render;

import com.qunar.search.common.bean.UserIdentityInfo;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

public class RenderPriceEstimate {
    // 酒店活动id
    public static Map<String, Float> activityDiscount = new HashMap<String, Float>() {{
        put("8013", 0.83f);
        put("8002", 0.82f);
        put("8026", 0.92f);
        put("8003", 0.85f);
        put("8006", 0.81f);
        put("8012", 0.78f);
        put("8011", 0.78f);
        put("4529", 0.81f);
        put("9057", 0.82f);
        put("null", 1.0f);
    }};

    // 用户身份打折信息
    public static Map<String, Float> userDiscount = new HashMap<String, Float>() {
        {
            put("memberInfoR1", 1.0f);
            put("memberInfoR2", 0.9f);
            put("memberInfoR3", 0.85f);
            put("memberInfoR4", 0.83f);
        }
    };

    public static Set<String> getUserIdentity(ModelUserFeature userFeature) {
        // 用户身份信息
        Map<String, UserIdentityInfo> userIdentity = userFeature.getUserIdentityInfoMap();
        Set<String> userIdentitySet = Collections.emptySet();
        if (null != userIdentity && !CollectionUtils.isEmpty(Collections.singleton(userIdentity))) {
            userIdentitySet = userIdentity.keySet();
        }

        return userIdentitySet;
    }


    public static double estimate(ModelRequestFeature requestFeature, ModelUserFeature userFeature, ModelHotelFeature hotelFeature, boolean userNewPrice) {
        Set<String> userIdentitySet = getUserIdentity(userFeature);
        float userDiscount = getRDiscount(userIdentitySet);
        int sortPrice = userNewPrice ? hotelFeature.getMinPriceWithIdentity() : hotelFeature.getMobileMinAvailablePrice();

        float estimatePrice;

        if (sortPrice <= 0 || sortPrice > 100000) {
            return sortPrice;
        }

        Set<String> activityIds = Collections.emptySet();
        if (CollectionUtils.isNotEmpty(hotelFeature.getActivitySet())) {
            activityIds = hotelFeature.getActivitySet();
        }

        // 用户身份打折
        estimatePrice = sortPrice * userDiscount;

        // 酒店活动打折
        estimatePrice = estimatePrice * getActivitiesDiscount(activityIds);

        // 券折扣
        estimatePrice = getCouponAmount(estimatePrice, userIdentitySet);

        return Math.floor(estimatePrice);
    }

    /**
     * 用户身份打折
     * R4（钻石）8折、R3（铂金）85折、R2（黄金）9折、R1（大众）不打折
     * R1: memberInfoR1
     * R2: memberInfoR2
     * R3: memberInfoR3
     * R4: memberInfoR4
     * @return 折扣比例
     */
    public static float getRDiscount(Set<String> userIdentitySet) {
        for (String identity : userIdentitySet) {
            if (userDiscount.containsKey(identity)) {
                return userDiscount.get(identity);
            }
        }

        return 1.0f;
    }

    /**
     * 酒店活动折扣
     * @param activityIds
     * @return 折扣比例
     */
    public static float getActivitiesDiscount(Set<String> activityIds) {
        float discount = 1.0f;
        if (null == activityIds) {
            return discount;
        }

        for (String activity : activityIds) {
            if (activityDiscount.containsKey(activity)) {
                discount *= activityDiscount.get(activity);
            }
        }

        return discount;
    }

    /**
     * 券
     * 市场---平台新客券（hades身份marketNewUser）：100-20，200-40
     * 机酒---机酒新用户（hades身份newUser、机酒用户大额券等级D: highValueLevelD）：100-30，200-60
     * 机酒-火酒老客（tripTrainUser）：100-5，300-10，500-20，800-30
     * @param currentEstimate
     * @param userIdentity
     * @return 券后价格
     */
    public static float getCouponAmount(float currentEstimate, Set<String> userIdentity) {
        // 平台券
        if (userIdentity.contains("marketNewUser")) {
            if (currentEstimate >= 100 && currentEstimate < 200) {
                currentEstimate -= 20;
            }
            if (currentEstimate >= 200) {
                currentEstimate -= 40;
            }
        }
        if (userIdentity.contains("newUser") && userIdentity.contains("highValueLevelD")){
            if (currentEstimate >= 100 && currentEstimate < 200) {
                currentEstimate -= 30;
            }
            if (currentEstimate >= 200) {
                currentEstimate -= 60;
            }
        }
        if (userIdentity.contains("tripTrainUser")) {
            if (currentEstimate >= 100 && currentEstimate < 300) {
                currentEstimate -= 5;
            }
            if (currentEstimate >= 300 && currentEstimate < 500) {
                currentEstimate -= 10;
            }
            if (currentEstimate >= 500 && currentEstimate < 800) {
                currentEstimate -= 30;
            }
        }

        return currentEstimate;
    }
}
