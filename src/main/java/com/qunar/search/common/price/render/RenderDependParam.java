package com.qunar.search.common.price.render;

import com.qunar.search.common.bean.HotelItem;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/1
 */
public interface RenderDependParam {

    /**
     * 如果是true，则去获取该参数对应的值，否则忽略该参数。
     */
    boolean enable(HttpServletRequest request);

    /**
     * 调render接口时传的参数名称
     */
    String paramName();

    /**
     * 获取参数值
     *
     * @param hotelItemMap hotelItem映射
     * @param request 请求信息
     * @return <酒店seq, 参数值>
     */
    Map<String, String> paramValue(Map<String, HotelItem> hotelItemMap, HttpServletRequest request);

}
