package com.qunar.search.common.price.render;

import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.UserActiveRole;
import com.qunar.search.common.enums.BusinessDependOnHPrice;
import com.qunar.search.common.util.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;
import com.qunar.search.common.conf.RenderPriceTimeoutConfig;
import com.qunar.search.common.conf.RenderPriceTimeoutConfig.TimeoutConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.*;

/**
 * render报价服务。
 *
 * render_price_config.properties这个配置文件，search和discover系统独立，可以分别配置超时时间等。
 *
 * render_price_common.properties这个配置文件，search和discover系统共享。
 *
 * <AUTHOR>
 * @date 2021/8/23
 */
@Component
@Slf4j
public class RenderPriceService {

    private static final Logger PRICE_REQUEST_LOG = LoggerFactory.getLogger("priceMonitor");

    /**
     * 是否打印render请求参数日志的开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "render.enablePrintRequestParamLog", defaultValue = "false")
    private static volatile boolean enablePrintRequestParamLog;

    /**
     * 是否打印render异常日志的开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "render.enablePrintErrorLog", defaultValue = "true")
    @Getter
    private static volatile boolean enablePrintErrorLog;

    /**
     * 提交异步请求的耗时阈值，用于控制是否打日志
     */
    @QMapConfig(value = "render_price_config.properties", key = "render.asyncRequestTimeThreshold", defaultValue = "100")
    private static volatile int asyncRequestTimeThreshold;

    /**
     * 调render报价接口的开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "render.enable", defaultValue = "true")
    private static volatile boolean enable;

    /**
     * 酒店数量阈值
     */
    @QMapConfig(value = "render_price_config.properties", key = "render.sizeThreshold", defaultValue = "20")
    private static volatile int sizeThreshold;


    /**
     * render接口urlFormat
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.urlFormat")
    private static volatile String urlFormat;

    /**
     * rank抓取使用的render接口urlFormat
     */
    @QMapConfig(value = "render_price_common.properties", key = "rank.crawl.render.urlFormat")
    private static volatile String rankCrawlUrlFormat;

    /**
     * 报价抓取消息限流
     */
    @Getter
    private static final RateLimiter rateLimiter = RateLimiter.create(10);

    @QMapConfig(value = "render_price_config.properties", key = "render.info.send.msg.limit.qps", defaultValue = "500")
    private void setRateLimiterQps(double qps) {
        rateLimiter.setRate(qps);
    }

    /**
     * 透传参数名称集合
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.extendParam")
    private static volatile Set<String> extendParamSet;

    /**
     * 触底推荐的searchType集合
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.stickySearchTypeSet", defaultValue = "8,15")
    private static volatile Set<Integer> stickySearchTypeSet;

    /**
     * 触底推荐场景调render报价需要置空的参数
     */
    @QMapConfig(value = "render_price_common.properties", key = "stickyRecommend.needRemovedParamSet", defaultValue = "bedType,specialRoomCode")
    private static volatile Set<String> needRemovedParamSet;

    private static final RenderPriceResult EMPTY_RESULT = new RenderPriceResult(Collections.emptyMap(), Collections.emptySet());

    private static final Joiner.MapJoiner PARAM_JOINER = Joiner.on("&").withKeyValueSeparator("=");


    private static QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    @QConfig("renderPriceAsyncClientConfig.json")
    public static void onChanged(AsyncClientConfig config) {
        QunarAsyncClient client = new QunarAsyncClient();
        client.setMaximumConnectionsTotal(config.getMaxConnections());
        client.setMaximumConnectionsPerHost(config.maxConnectionsPerHost);
        client.setConnectionTimeoutInMs(config.connectionTimeoutInMs);
        client.setWebSocketIdleTimeoutInMs(config.webSocketIdleTimeoutInMs);
        client.setRequestTimeoutInMs(config.requestTimeoutInMs);
        client.setReadTimeoutInMs(config.readTimeoutInMs);

        setQunarAsyncClient(client);
    }

    private static void setQunarAsyncClient(QunarAsyncClient qunarAsyncClient) {
        RenderPriceService.qunarAsyncClient = qunarAsyncClient;
    }

    @Data
    @NoArgsConstructor
    static final class AsyncClientConfig {
        private int maxConnections;
        private int maxConnectionsPerHost;
        private int connectionTimeoutInMs;
        private int requestTimeoutInMs;
        private int readTimeoutInMs;
        private int webSocketIdleTimeoutInMs;
    }

    public static RenderPriceApiQuery getQuery(String fromDate, String toDate, Set<String> seqSet,
            String isvipuser, UserActiveRole userActiveRole, HttpServletRequest request, BusinessDependOnHPrice businessDependOnHPrice) {
        RenderPriceApiQuery query = new RenderPriceApiQuery();

        query.setFromDate(fromDate);
        query.setToDate(toDate);
        query.setSeqSet(seqSet);

        long logicBit = userActiveRole.userIdentityBit;
        logicBit = GainPriceApiQueryUtil.parseLogicBit(userActiveRole, logicBit);
        query.setLogicBit(logicBit);

        query.setUserBit(GainPriceApiQueryUtil.parseMinPriceBit(userActiveRole, isvipuser));

        StringBuilder apikeySb = new StringBuilder();
        if(userActiveRole.touch){
            apikeySb.append("tc-");
        } else {
            apikeySb.append("mob-");
        }
        apikeySb.append(businessDependOnHPrice.getLabel()).append("-").append("render");
        query.setApikey(apikeySb.toString());

        query.setExtendMap(buildExtendMap(request));

        return query;
    }

    private static Map<String, String> buildExtendMap(HttpServletRequest request) {
        Map<String, String> map = Maps.newLinkedHashMap();

        // 透传参数
        for (String name : extendParamSet) {
            map.put(name, Strings.nullToEmpty(request.getParameter(name)));
        }

        map.put("latLon", Strings.nullToEmpty(request.getParameter("currCoord")));
        map.put("qunarUserName", Strings.nullToEmpty(request.getParameter("userName")));
        map.put("breakFast", Strings.nullToEmpty(request.getParameter("breakfast")));
        map.put("paymentType", Strings.nullToEmpty(request.getParameter("payType")));

        // 版控信息
        Map<String, String> versionControlMap = JsonUtils.fromJson(request.getParameter("versionControl"),
                JsonUtils.buildMapType(Map.class, String.class, String.class));
        int searchType = NumberUtils.toInt(request.getParameter("searchType"));
        // 有价格筛选的版控标识且不是触底推荐才传价格区间参数。
        if (hasVersionControl("priceFilterControl", versionControlMap) && !(stickySearchTypeSet.contains(searchType))) {
            map.put("p0", Strings.nullToEmpty(request.getParameter("p0")));
            map.put("p1", Strings.nullToEmpty(request.getParameter("p1")));
        }
        // 触底推荐场景下将影响报价的参数置空，避免推荐列表酒店无报价
        if (stickySearchTypeSet.contains(searchType)) {
            for (String param : needRemovedParamSet) {
                map.put(param, StringUtils.EMPTY);
            }
        }

        return map;
    }

    public static boolean hasVersionControl(String key, Map<String, String> versionControlMap) {
        if (StringUtils.isBlank(key) || MapUtils.isEmpty(versionControlMap)) {
            return false;
        }
        return Boolean.parseBoolean(versionControlMap.get(key));
    }

    public static RenderPriceResult getPrice(RenderPriceApiQuery query, TimeoutConfig timeoutConfig, RenderPriceSceneEnum scene){
        return getPrice(query, timeoutConfig, scene, null);
    }

    public static RenderPriceResult getPrice(RenderPriceApiQuery query, TimeoutConfig timeoutConfig, RenderPriceSceneEnum scene, RateLimiter rateLimiter) {
        if (!enable || query == null || CollectionUtils.isEmpty(query.getSeqSet())) {
            return EMPTY_RESULT;
        }

        long start = System.currentTimeMillis();
        String sceneCode = Objects.nonNull(scene) ? scene.getSceneCode() : "default";

        try {
            RenderPriceResult renderPriceResult = getPrice(query, timeoutConfig.getInnerMinTimeout(), timeoutConfig.getInnerMaxTimeout(), timeoutConfig.getInnerFinishRatio(), rateLimiter);

            int totalCount = query.getSeqSet().size();
            int successCount = renderPriceResult.getRenderPriceItemMap().size();
            int timeoutCount = renderPriceResult.getTimeoutHotels().size();
            int failCount = totalCount - successCount - timeoutCount;

            QMonitor.recordValue("renderApiTotalCount", totalCount);
            QMonitor.recordValue("renderApiSuccessCount", successCount);
            QMonitor.recordValue("renderApiTimeoutCount", timeoutCount);
            QMonitor.recordValue("renderApiFailCount", failCount);
            if (timeoutCount > 0) {
                QMonitor.recordOne("renderPriceServiceGetPriceHasTimeout");
            }

            // 分场景记录调取报价接口指标
            QMonitor.recordValue(StringUtils.join("renderApiTotalCount_", sceneCode), totalCount);
            QMonitor.recordValue(StringUtils.join("renderApiSuccessCount_", sceneCode), successCount);
            QMonitor.recordValue(StringUtils.join("renderApiTimeoutCount_", sceneCode), timeoutCount);
            QMonitor.recordValue(StringUtils.join("renderApiFailCount_", sceneCode), failCount);
            if (timeoutCount > 0) {
                QMonitor.recordOne(StringUtils.join("renderPriceServiceGetPriceHasTimeout_", sceneCode));
            }

            return renderPriceResult;
        } catch (Exception e) {
            log.error("获取render报价出错", e);
            QMonitor.recordOne("renderPriceServiceGetPriceError");
            QMonitor.recordOne(StringUtils.join("renderPriceServiceGetPriceError_", sceneCode));
        } finally {
            QMonitor.recordQuantile("renderPriceServiceGetPrice", System.currentTimeMillis() - start);
            QMonitor.recordQuantile(StringUtils.join("renderPriceServiceGetPrice_", sceneCode), System.currentTimeMillis() - start);
        }

        return EMPTY_RESULT;
    }

    /**
     * 获取render报价
     *
     * @param query 查询参数
     * @param minTimeout 最小超时时间，单位是ms
     * @param maxTimeout 最大超时时间，单位是ms
     * @param finishRatio 可接受的render完成比例
     * @return render报价结果
     */
    private static RenderPriceResult getPrice(RenderPriceApiQuery query,
                                              int minTimeout, int maxTimeout,
                                              float finishRatio, RateLimiter rateLimiter) throws Exception {
        long nanos = TimeUnit.MILLISECONDS.toNanos(minTimeout);
        final long deadline = System.nanoTime() + nanos;
        Set<String> timeoutHotels = Sets.newHashSet();
        Map<String, RenderPriceItem> renderPriceItemMap = Maps.newHashMapWithExpectedSize(query.getSeqSet().size());
        Map<String, Future<RenderPriceItem>> timeoutFutureMap = Maps.newHashMap();

        // 获取闭锁，用于控制等待render接口请求完成的时间
        CountDownLatch latch = getLatch(query.getSeqSet().size(), finishRatio);

        // 提交异步请求，返回future
        Map<String, Future<RenderPriceItem>> futureMap = submitAsyncRequest(query, latch, rateLimiter);

        for (Map.Entry<String, Future<RenderPriceItem>> entry : futureMap.entrySet()) {
            String seq = entry.getKey();
            Future<RenderPriceItem> future = entry.getValue();
            nanos = deadline - System.nanoTime();
            try {
                RenderPriceItem renderPriceItem = future.get(nanos, TimeUnit.NANOSECONDS);
                if (renderPriceItem != null) {
                    renderPriceItemMap.put(seq.intern(), renderPriceItem);
                }
            } catch (TimeoutException e) {
                if (latch == null) {
                    //绝对超时
                    timeoutHotels.add(seq);
                    log.warn("{} absolutely render超时", seq);
                } else {
                    //还有机会
                    timeoutFutureMap.put(seq, future);
                    log.warn("{} maybe render超时", seq);
                }
            } catch (Exception e) {
                QMonitor.recordOne("renderFutureGetException");
                if (enablePrintErrorLog) {
                    log.error("{} render异常", seq, e);
                }
            }
        }

        if (latch == null || MapUtils.isEmpty(timeoutFutureMap)) {
            return new RenderPriceResult(renderPriceItemMap, timeoutHotels);
        }

        // 在最小超时时间内，有些请求还没完成；最多再等maxTimeout - minTimeout
        return awaitLatchThenGetResult(latch, timeoutFutureMap, renderPriceItemMap, maxTimeout - minTimeout, timeoutHotels);
    }

    private static RenderPriceResult awaitLatchThenGetResult(CountDownLatch latch,
            Map<String, Future<RenderPriceItem>> timeoutFutureMap, Map<String, RenderPriceItem> renderPriceItemMap,
            int timeout, Set<String> timeoutHotels) {
        long start = System.currentTimeMillis();
        try {
            if (latch.await(timeout, TimeUnit.MILLISECONDS)) {
                QMonitor.recordOne("renderLatchAwaitSuccess");
            }
        } catch (Exception e) {
            log.error("renderLatchAwaitException", e);
            QMonitor.recordOne("renderLatchAwaitException");
        } finally {
            QMonitor.recordQuantile("renderLatchAwait", System.currentTimeMillis() - start);
        }

        for (Map.Entry<String, Future<RenderPriceItem>> entry : timeoutFutureMap.entrySet()) {
            String seq = entry.getKey();
            Future<RenderPriceItem> future = entry.getValue();
            try {
                RenderPriceItem renderPriceItem = future.get(0, TimeUnit.NANOSECONDS);
                if (renderPriceItem != null) {
                    renderPriceItemMap.put(seq.intern(), renderPriceItem);
                }
            } catch (TimeoutException e) {
                //最后的超时
                timeoutHotels.add(seq);
                log.warn("{} awaitLatch render超时", seq);
            } catch (Exception e) {
                QMonitor.recordOne("renderLatchFutureGetException");
                if (enablePrintErrorLog) {
                    log.error("{} renderLatch异常, {}", seq, System.currentTimeMillis() - start);
                }
            }
        }
        return new RenderPriceResult(renderPriceItemMap, timeoutHotels);
    }

    /**
     * 获取闭锁
     *
     * @param seqCount 酒店seq的数量
     * @param ratio 可接受的render请求完成比例
     * @return latch
     */
    private static CountDownLatch getLatch(int seqCount, float ratio) {
        if (ratio <= 0 || ratio >= 1) {
            return null;
        }

        if (seqCount < sizeThreshold) {
            return null;
        }

        int count = Math.round(seqCount * ratio);
        return new CountDownLatch(count);
    }

    private static Map<String, Future<RenderPriceItem>> submitAsyncRequest(RenderPriceApiQuery query,
                                                                           CountDownLatch latch,
                                                                           RateLimiter rateLimiter) throws Exception {
        long start = System.currentTimeMillis();
        Set<String> seqSet = query.getSeqSet();
        Map<String, Future<RenderPriceItem>> futureMap = Maps.newHashMapWithExpectedSize(seqSet.size());
        for (String seq : seqSet) {
            if (rateLimiter != null) {
                rateLimiter.acquire();
            }
            // 由于调用方传的cityurl可能和酒店实际所在城市不一致(比如在北京搜三亚，cityurl传的是beijng_city，实际召回的是三亚的酒店)，所以这里通过seq来得到cityurl最靠谱。
            String cityurl = CommonTools.getCityFromHotel(seq);
            String renderUrl = getRenderUrl(cityurl, query.getFromDate(), query.getToDate(), seq, query.isRankCrawl());

            Map<String, String> paramMap = buildParamMap(query, seq);
            QHttpOption httpOption = new QHttpOption();
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                httpOption.addPostFormData(entry.getKey(), Strings.nullToEmpty(entry.getValue()));
            }

            Future<RenderPriceItem> future = qunarAsyncClient.post(renderUrl, httpOption,
                    new AsyncHttpUtil.MonitorHandler<>(renderUrl, "render",
                            new RenderPriceAsyncHandler(latch, seq, enablePrintRequestParamLog)));
            futureMap.put(seq, future);

            if (enablePrintRequestParamLog) {
                UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(renderUrl);
                paramMap.forEach(builder::queryParam);
                PRICE_REQUEST_LOG.info("RenderReq hotelSeq:{} renderUrl:{}", seq, builder.build().encode().toString());
            }
        }

        long time = System.currentTimeMillis() - start;
        QMonitor.recordQuantile("renderSubmitAsyncRequest", time);

        if (time > asyncRequestTimeThreshold) {
            log.info("renderSubmitAsyncRequest耗时过长:{}ms", time);
        }

        return futureMap;
    }

    private static Map<String, String> buildParamMap(RenderPriceApiQuery query, String seq) {
        Map<String, String> paramMap = Maps.newLinkedHashMap();

        if (MapUtils.isNotEmpty(query.getExtendMap())) {
            // 调用方透传参数
            paramMap.putAll(query.getExtendMap());
        }

        // 酒店维度参数
        Map<String, Map<String, String>> hotelExtendMap = query.getHotelExtendMap();
        if (MapUtils.isNotEmpty(hotelExtendMap)) {
            Map<String, String> map = hotelExtendMap.getOrDefault(seq, Collections.emptyMap());
            paramMap.putAll(map);
        }

        paramMap.put("userBit", String.valueOf(query.getUserBit()));
        paramMap.put("logicBit", String.valueOf(query.getLogicBit()));
        paramMap.put("preAcquireInfo", query.isPreAcquireInfo() ? "1" : "0");
        paramMap.put("apikey", query.getApikey());

        return paramMap;
    }

    /**
     * 拼接调render的url。由于历史原因，报价那边用cityurl、fromDate和toDate这3个参数来做负载均衡了，因此这3个参数不能放到postFormData里。
     *
     * 为了方便查问题，把酒店seq也拼接在url上
     */
    private static String getRenderUrl(String cityurl, String fromDate, String toDate, String hotelSeq, boolean rankCrawl) {
        return rankCrawl ?
                String.format(rankCrawlUrlFormat, cityurl, fromDate, toDate, hotelSeq) :
                String.format(urlFormat, cityurl, fromDate, toDate, hotelSeq);
    }

    /**
     * 根据搜索场景获取render接口超时时间配置
     * @param scene 场景
     * @return 超时时间配置
     */
    public static TimeoutConfig getRenderPriceTimeoutConfig(RenderPriceSceneEnum scene) {
        if (scene == null) {
            log.error("获取renderPriceTimeoutConfig出错, 使用默认配置, scene为null");
            QMonitor.recordOne("getRenderPriceTimeoutConfigErrorSceneIsNull");
            return getDefaultTimeoutConfig();
        }
        TimeoutConfig config = RenderPriceTimeoutConfig.getConfig(scene.getSceneCode());
        if (config == null) {
            config = getDefaultTimeoutConfig();
            log.error("获取renderPriceTimeoutConfig为null, 使用默认配置, scene:{}", scene.getSceneCode());
            QMonitor.recordOne("getRenderPriceTimeoutConfigIsNullAndUseDefault");
        }
        return config;
    }

    /**
     * 获取默认的超时配置，极端情况下未获取到配置时使用硬编码的配置
     * @return 超时时间配置
     */
    private static TimeoutConfig getDefaultTimeoutConfig() {
        TimeoutConfig config = RenderPriceTimeoutConfig.getConfig(RenderPriceSceneEnum.DEFAULT.getSceneCode());
        if (config != null) {
            return config;
        }
        // 硬编码超时配置
        config = new TimeoutConfig();
        config.setFetchRenderPriceTimeout(600L);
        config.setInnerMinTimeout(400);
        config.setInnerMaxTimeout(500);
        config.setInnerFinishRatio(0.8f);
        return config;
    }
}
