package com.qunar.search.common.price.render;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ning.http.client.Response;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.util.AsyncHttpUtil;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Component;
import qunar.api.pojo.json.JsonV2;
import qunar.metrics.Metrics;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * torch优享会升降级。http://pmo.corp.qunar.com/browse/FD-47957
 *
 * <AUTHOR>
 * @date 2021/8/12
 */
@Component
@Slf4j
public class TorchLevelUpParam implements RenderDependParam {

    private static final JavaType RESPONSE_TYPE = JsonUtils.buildParametricType(JsonV2.class, JsonUtils.buildCollectionType(List.class, Map.class));

    /**
     * 开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "torchLevelUp.enable", defaultValue = "false")
    private volatile boolean enable;

    /**
     * 开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "torchLevelUp.request.log.enable", defaultValue = "false")
    private volatile boolean requestLogEnable;

    /**
     * 接口url
     */
    @QMapConfig(value = "render_price_config.properties", key = "torchLevelUp.url")
    private volatile String url;

    /**
     * 调接口的超时时间，单位是ms
     */
    @QMapConfig(value = "render_price_config.properties", key = "torchLevelUp.timeout", defaultValue = "50")
    private volatile int timeout;

    /**
     * 调torch的透传参数
     */
    @QMapConfig(value = "render_price_config.properties", key = "torchLevelUp.extendParam")
    private volatile Set<String> extendParamSet;

    @Override
    public boolean enable(HttpServletRequest request) {
        if (!enable) {
            return false;
        }

        String userId = request.getParameter("userId");
        return StringUtils.isNotBlank(userId);
    }

    @Override
    public String paramName() {
        return "torchInfoMap";
    }

    @Override
    public Map<String, String> paramValue(Map<String, HotelItem> hotelItemMap, HttpServletRequest request) {
        if (MapUtils.isEmpty(hotelItemMap)) {
            return null;
        }
        long startTime = System.currentTimeMillis();

        String paramBody = "";
        String responseBody = "";
        try {
            Map<String, Object> params = Maps.newHashMap();

            for (String name : extendParamSet) {
                // 放透传参数
                params.put(name, Strings.nullToEmpty(request.getParameter(name)));
            }

            params.put("identityJson", Strings.nullToEmpty(request.getParameter("userTotalIdentity")));

            List<Map<String, Object>> hotels = Lists.newArrayListWithCapacity(hotelItemMap.size());
            for (HotelItem item : hotelItemMap.values()) {
                hotels.add(ImmutableMap.of("hotelSeq", item.getHotelSEQ(), "dangCi", item.info.getDangci()));
            }
            params.put("hotels", hotels);
            params.put("source", Strings.nullToEmpty(request.getParameter("source")));

            paramBody = JsonUtils.toJson(params);
            Future<Response> responseFuture = AsyncHttpUtil.asyncPost(this.url, paramBody, this.paramName());
            if (responseFuture == null) {
                return null;
            }

            Response response = responseFuture.get(this.timeout, TimeUnit.MILLISECONDS);

            int statusCode = response.getStatusCode();
            if (response.getStatusCode() != HttpStatus.SC_OK) {
                Metrics.counter( "renderTorchLevelUpStatusCodeNotOk").delta().tag("statusCode", String.valueOf(statusCode)).get().inc();
                log.error("torch异常状态码:{}", statusCode);
                return null;
            }

            responseBody = response.getResponseBody();
            if (requestLogEnable) {
                log.info("torch requestUrl -> {}, requestParam -> {} , statusCode -> {} responseBody:{}", this.url, paramBody, statusCode, responseBody);
            }
            return parse(responseBody);
        } catch (TimeoutException e) {
            log.warn("调torch优享会升级接口超时");
            QMonitor.recordOne("renderTorchLevelUpTimeout");
        } catch (Exception e) {
            QMonitor.recordOne("renderTorchLevelUpError");
            log.error("调torch接口进行优享会升降级失败, url:{}, paramBody:{}, responseBody:{}", this.url, paramBody, responseBody, e);
        } finally {
            QMonitor.recordQuantile("renderTorchLevelUp", System.currentTimeMillis() - startTime);
        }

        return null;
    }

    /**
     * 解析接口返回结果。接口文档：https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=475867292
     *
     * @param responseBody 接口返回结果
     * @return <酒店seq, 该酒店的优享会升降级信息(json字符串)>
     */
    private Map<String, String> parse(String responseBody) throws Exception {
        JsonV2<List<Map<String, Object>>> result = JsonUtils.toBean(responseBody, RESPONSE_TYPE);
        if (result.status != 0) {
            throw new IllegalStateException("torch接口返回的状态码不为0");
        }

        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(result.data.size());
        for (Map<String, Object> info : result.data) {
            String hotelSeq = (String)info.get("hotelSeq");
            // 透传torch接口返回的数据
            resultMap.put(hotelSeq, JsonUtils.toJson(info));
        }
        return resultMap;
    }
}