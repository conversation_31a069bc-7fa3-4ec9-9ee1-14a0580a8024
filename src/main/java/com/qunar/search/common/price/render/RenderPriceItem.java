package com.qunar.search.common.price.render;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
@Data
@NoArgsConstructor
public class RenderPriceItem {

    /**
     * 酒店seq
     */
    private String seq;

    /**
     * 满房的时候，kylin用的是这个价格返给前端
     */
    private int price;

    /**
     * 房态
     *
     * @see com.qunar.search.common.enums.MobileRoomStatus
     */
    private int mobileRoomStatus;

    /**
     * 是否是团购
     */
    private boolean hasGroupBuy;

    /**
     * 钟点房标识等
     */
    private int wapInfo;

    /**
     * 无线手机专享需求加上的字段 String-wrapperId Integer-活动标识
     */
    private Map<String, Integer> promotionFlags;

    /**
     * 移动最低卖价折扣
     */
    private int priceRatio;

    /**
     * 报价更新时间
     */
    private long updateTime;

    /**
     * 按位存，每一位代表一种活动
     */
    private long activity;

    /**
     * 报价特征
     */
    private long logicBit;

    /**
     * 特殊报价特征
     */
    private long specialPriceBit;

    /**
     * 活动id列表
     */
    private List<String> activityIds;

    /**
     * 移动最低卖价对应的原价（不考虑房态）
     */
    private int originalPrice = Integer.MAX_VALUE;

    /**
     * 移动最低卖价
     */
    private int customMinAvailablePrice = Integer.MAX_VALUE;

    /**
     * 移动最低卖价对应的原价
     */
    private int customOriginPriceForMin = Integer.MAX_VALUE;

    /**
     * 酒店list报价的一些trace属性, 用于传递带到detail的服务里面, 辅助数据分析
     */
    private String priceTraceStr;

    /**
     * 优惠明细
     */
    private Map<String, Integer> discountType;

    /**
     * wrapperId
     */
    private String codebase;

    /**
     * wrapper中文名
     */
    private String codename;

    /**
     * 撒币活动最高返现
     */
    private int sabiMaxAmount;


    /**
     * 商家大促折扣信息 优享会 门店新客 天天特价
     * Integer代表活动Id String表示打折内容90|20，90表示打9折，20表示直减20
     */
    private Map<String, Integer> discountArr;

    /**
     *  替补报价标记 1代表是 0代表非
     */
    private int makeUp;

    /**
     * 最低卖价对应的税
     */
    private int minPriceTax;

    /**
     * 报价是否刷新完成
     */
    private boolean lmUpdated;

    /**
     * 商促折扣
     */
    private Map<String, Double> saleDiscount;

    /**
     * 唯一id
     */
    private String tw;

    /**
     * qtrace id
     */
    private String trd;

    /**
     * 为智行露出的现付预付标识
     *     NONE(-1),
     *     PAY_ON_ARRIVAL(0),
     *     PAY_IN_ADVANCE(1);
     */
    private int ppbPayment;

    /**
     * 扩展字段
     */
    private Map<String, String>  extendsMap;

    /**
     * 酒店的支付方式集合(用英文逗号分割)
     */
    private String paymentType = "";

    /**
     * 酒店的退订政策类型集合(用英文逗号分割)
     */
    private String cancelPolicy = "";

    /**
     * 小时房最低卖价
     */
    private int customMinAvailableHourlyPrice = Integer.MAX_VALUE;

    /**
     * 小时房可预订时间
     */
    private String hourlyPriceServiceTime;

    /**
     *  小时房可以住时长
     */
    private double timeLength;

    /**
     * 报价类型。0：全日房，1：钟点房
     */
    private int roomType;

    /**
     * 产品房型
     */
    private String roomId;

    /**
     * 标签相关信息
     */
    private Map<String, Object> conditions;

    /**
     * 用于存储优惠加回后的价格字段存储
     */
    private Map<String, String> showPriceInfo;

    /**
     * 组合报价相关信息。http://pmo.corp.qunar.com/browse/FD-337688
     */
    private Map<String, Object> combine;

}
