package com.qunar.search.common.price.render;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.ning.http.client.Response;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.util.AsyncHttpUtil;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Component;
import qunar.api.pojo.json.JsonV2;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;

/**
 * QCPS广告增加平台促补贴限制: https://wiki.corp.qunar.com/pages/viewpage.action?pageId=847494014
 */
@Component
@Slf4j
public class QcpsAdInfoParam implements RenderDependParam {

    private static final JavaType RESPONSE_TYPE = JsonUtils.buildParametricType(JsonV2.class, Map.class);

    /**
     * 系统调取render报价时，是否增加qcps广告信息参数
     */
    @QMapConfig(value = "render_price_config.properties", key = "refreshPrice.qCpsAdInfo.enable", defaultValue = "false")
    private volatile boolean enable;

    /**
     * 接口url
     */
    @QMapConfig(value = "render_price_config.properties", key = "qCpsAdInfo.url", defaultValue = "http://hotelsearch.wap.qunar.com/ad/getHotelQcpsAdInfo")
    private volatile String url;

    /**
     * 调接口的超时时间，单位是ms
     */
    @QMapConfig(value = "render_price_config.properties", key = "qCpsAdInfo.timeout", defaultValue = "50")
    private volatile int timeout;

    /**
     * 透传参数
     */
    @QMapConfig(value = "render_price_config.properties", key = "qCpsAdInfo.extendParam", defaultValue = "fromDate,toDate")
    private volatile Set<String> extendParamSet;

    @Override
    public boolean enable(HttpServletRequest request) {
        return enable;
    }

    @Override
    public String paramName() {
        return "qCpsAdInfo";
    }

    @Override
    public Map<String, String> paramValue(Map<String, HotelItem> hotelItemMap, HttpServletRequest request) {
        if (MapUtils.isEmpty(hotelItemMap)) {
            return Collections.emptyMap();
        }

        long start = System.currentTimeMillis();
        try {
            Map<String, String> params = Maps.newHashMap();

            for (String name : extendParamSet) {
                params.put(name, Strings.nullToEmpty(request.getParameter(name)));
            }

            String hotelSeqList = hotelItemMap.values().stream().map(HotelItem::getHotelSEQ).collect(Collectors.joining(","));
            params.put("hotelSeqs", hotelSeqList);

            Future<Response> responseFuture = AsyncHttpUtil.asyncPost(this.url, params, this.paramName());
            if (responseFuture == null) {
                return null;
            }

            Response response = responseFuture.get(this.timeout, TimeUnit.MILLISECONDS);
            int statusCode = response.getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                QMonitor.recordOne("qCpsAdInfoApiStatusCodeNotOk");
                log.error("qCpsAdInfo接口异常状态码:{}", statusCode);
                return null;
            }

            return parse(response.getResponseBody());
        } catch (TimeoutException e) {
            log.warn("调qCpsAdInfo接口超时");
            QMonitor.recordOne(" invokeQCpsAdInfoApiTimeout");
        } catch (Exception e) {
            log.error("调qCpsAdInfo接口异常", e);
            QMonitor.recordOne(" invokeQCpsAdInfoApiError");
        } finally {
            QMonitor.recordOne(" invokeQCpsAdInfoApi", System.currentTimeMillis() - start);
        }

        return Collections.emptyMap();
    }

    private Map<String, String> parse(String responseBody) throws Exception {
        JsonV2<Map<String, Object>> result = JsonUtils.toBean(responseBody, RESPONSE_TYPE);
        if (result.status != 0) {
            throw new IllegalStateException("qCpsAdInfo接口返回的状态码不为0");
        }

        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(result.data.size());
        for (Map.Entry<String, Object> entry : result.data.entrySet()) {
            resultMap.put(entry.getKey(), JsonUtils.toJson(entry.getValue()));
        }
        return resultMap;
    }
}
