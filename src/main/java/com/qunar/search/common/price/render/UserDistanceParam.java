package com.qunar.search.common.price.render;

import com.google.common.collect.Maps;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.gis.GLatLng;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;

@Component
@Slf4j
public class UserDistanceParam implements RenderDependParam {
    @QMapConfig(value = "render_price_config.properties", key = "userDistanceParam.enable", defaultValue = "false")
    private boolean enable;

    @Override
    public boolean enable(HttpServletRequest request) {
        return enable;
    }

    @Override
    public String paramName() {
        return "userDistance";
    }

    @Override
    public Map<String, String> paramValue(Map<String, HotelItem> hotelItemMap, HttpServletRequest request) {
        try {
            String currCoord = request.getParameter("currCoord");
            if (StringUtils.isBlank(currCoord)) {
                return Collections.emptyMap();
            }

            GLatLng userCoord = GLatLng.getLatLng(currCoord);
            if (userCoord == null) {
                return Collections.emptyMap();
            }

            Map<String, String> map = Maps.newHashMapWithExpectedSize(hotelItemMap.size());
            for (HotelItem hotelItem : hotelItemMap.values()) {
                if (hotelItem.info.getLatLng() == null) {
                    continue;
                }

                double distance = userCoord.distance(hotelItem.info.getLatLng());
                map.put(hotelItem.getHotelSEQ(), distance >= 0 ? String.valueOf(distance) : "");
            }

            return map;
        } catch (Exception e) {
            log.error("UserDistanceParam", e);
        }

        return Collections.emptyMap();
    }
}
