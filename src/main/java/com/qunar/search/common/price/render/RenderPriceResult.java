package com.qunar.search.common.price.render;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RenderPriceResult {

    /**
     * <酒店seq, 该酒店的render报价信息>
     */
    private Map<String, RenderPriceItem> renderPriceItemMap;

    /**
     * 调render接口超时的酒店seq集合
     */
    private Set<String> timeoutHotels;

}
