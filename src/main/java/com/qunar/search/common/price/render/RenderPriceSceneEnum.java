package com.qunar.search.common.price.render;

import javax.servlet.http.HttpServletRequest;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.util.JsonUtils;
import com.qunar.search.common.util.MobileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import java.util.Map;

import static com.qunar.search.common.price.render.RenderPriceService.hasVersionControl;

/**
 * 请求Render报价场景类型枚举
 * 用于区分不同的业务场景，设置不同的超时时间
 */
@Slf4j
public enum RenderPriceSceneEnum {

    /**
     * 默认场景
     */
    DEFAULT("default"),

    /**
     * 多间场景
     */
    MULTI_ROOMS("multiRooms"),

    /**
     * 多晚(多入离)场景
     */
    MULTI_NIGHTS("multiNights"),

    /**
     *  多间 + 多晚场景
     */
    MULTI_ROOMS_AND_NIGHTS("multiRoomsAndNights");


    private final String sceneCode;

    RenderPriceSceneEnum(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    /**
     * 多间版控key
     */
    public static final String MULTI_ROOMS_VERSION_CONTROL_KEY = "multirooms_new";

    /**
     * 多晚版控key
     */
    public static final String MULTI_NIGHTS_VERSION_CONTROL_KEY = "multinights";

    /**
     * 判断请求报价的场景，调取报价接口时针对不同的场景设置不同的超时时间
     * @param request 请求参数
     * @return 场景
     */
    public static RenderPriceSceneEnum parseScene(HttpServletRequest request) {
        // 版控信息
        Map<String, String> versionControlMap = JsonUtils.fromJson(request.getParameter("versionControl"), JsonUtils.buildMapType(Map.class, String.class, String.class));
        if (MapUtils.isEmpty(versionControlMap)) {
            return DEFAULT;
        }

        // 是否有多间的版控信息
        boolean hasMultiRoomsVC = hasVersionControl(MULTI_ROOMS_VERSION_CONTROL_KEY, versionControlMap);
        // 是否有多晚的版控信息
        boolean hasMultiNightsVC = hasVersionControl(MULTI_NIGHTS_VERSION_CONTROL_KEY, versionControlMap);
        // 入住的房间数
        int selectRoomCount = NumberUtils.toInt(request.getParameter("selectRoomCount"));
        // 入住的天数
        int stayDays = calculateStayDays(request);

        // 判断是否是多间场景
        boolean isMultiRooms = isMultiRooms(hasMultiRoomsVC, selectRoomCount);
        // 判断是否是多晚场景
        boolean isMultiNights = isMultiNights(hasMultiNightsVC, stayDays);

        // 判断是否是多间 + 多晚场景
        if (isMultiRooms && isMultiNights) {
            return MULTI_ROOMS_AND_NIGHTS;
        }
        if (isMultiRooms) {
            return MULTI_ROOMS;
        }
        if (isMultiNights) {
            return MULTI_NIGHTS;
        }
        return DEFAULT;
    }

    /**
     * 是否是多间场景，有版控信息且入住的房间数大于等于2
     *
     * @param hasVersionControl 是否有多间的版控信息
     * @param selectRoomCount 入住的房间数
     * @return 是否是多间场景
     */
    public static boolean isMultiRooms(boolean hasVersionControl, int selectRoomCount) {
        return hasVersionControl && selectRoomCount >= 2;
    }

    /**
     * 是否是多晚场景，有版控信息且2<=用户入住晚数<=5
     *
     * @param hasVersionControl 是否有多晚的版控信息
     * @param stayDays 入住的天数
     * @return 是否是多晚场景
     */
    public static boolean isMultiNights(boolean hasVersionControl, int stayDays) {
        return hasVersionControl && stayDays >= 2 && stayDays <= 5;
    }

    /**
     * 计算入住的天数
     */
    private static int calculateStayDays(HttpServletRequest request) {
        String fromDateStr = request.getParameter("fromDate");
        String toDateStr = request.getParameter("toDate");

        DateTime fromDate = MobileUtils.toDateTime(fromDateStr);
        DateTime toDate = MobileUtils.toDateTime(toDateStr);

        if (fromDate == null || toDate == null) {
            QMonitor.recordOne("calculateStayDaysError");
            log.error("calculateStayDaysError, fromDate:{}, toDate:{}", fromDateStr, toDateStr);
            return -1;
        }
        return Days.daysBetween(fromDate, toDate).getDays();
    }
}