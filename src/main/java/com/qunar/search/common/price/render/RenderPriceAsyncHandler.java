package com.qunar.search.common.price.render;

import com.ning.http.client.AsyncCompletionHandler;
import com.ning.http.client.Response;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.util.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.metrics.Metrics;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;

/**
 * render报价接口异步回调处理器
 *
 * <AUTHOR>
 * @date 2021/10/25
 */
@Slf4j
public class RenderPriceAsyncHandler extends AsyncCompletionHandler<RenderPriceItem> {

    private static final Logger logger = LoggerFactory.getLogger("priceMonitor");

    private final CountDownLatch latch;

    private final String hotelSeq;

    private final Boolean printResponseLog;

    public RenderPriceAsyncHandler(CountDownLatch latch, String hotelSeq, boolean printResponseLog) {
        this.latch = latch;
        this.hotelSeq = hotelSeq;
        this.printResponseLog = printResponseLog;
    }

    @Override
    public RenderPriceItem onCompleted(Response response) throws Exception {
        try {
            int statusCode = response.getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                Metrics.counter( "renderApiStatusCodeNotOk").delta().tag("statusCode", String.valueOf(statusCode)).get().inc();
                if (RenderPriceService.isEnablePrintErrorLog()) {
                    log.error("Render接口状态异常，[hotelSeq={}, http_status={}]", hotelSeq, statusCode);
                }
                return null;
            }
            return parse(response.getResponseBody());
        } catch (Exception e) {
            if (RenderPriceService.isEnablePrintErrorLog()) {
                log.error("解析render返回数据失败, [hotelSeq={}, responseBody={}]", hotelSeq, response.getResponseBody(), e);
            }
            QMonitor.recordOne("renderApiParseError");
        } finally {
            if (latch != null) {
                latch.countDown();
            }
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    private RenderPriceItem parse(String responseBody) throws Exception {
        if (printResponseLog) {
            logger.info("RenderReq render接口返回数据: hotelSeq={}, responseBody={}", hotelSeq, responseBody);
        }
        RenderApiResponse apiResponse = JsonUtils.toBean(responseBody, RenderApiResponse.class);
        // render接口目前只支持查单个酒店，取列表的第一个元素即可。
        List<Object> list = apiResponse.getHotels().get(0);
        Map<String, Integer> meta = apiResponse.getMeta();
        RenderPriceItem item = new RenderPriceItem();

        Integer id = (Integer) list.get(meta.get("id"));
        String seq = StringUtils.join(apiResponse.getCityurl(), "_", id);
        item.setSeq(seq);

        getValue(meta, list, "price").ifPresent(value -> item.setPrice((Integer) value));
        getValue(meta, list, "mobileRoomStatus").ifPresent(value -> item.setMobileRoomStatus((Integer) value));
        getValue(meta, list, "hasGroupBuy").ifPresent(value -> item.setHasGroupBuy((Boolean) value));
        getValue(meta, list, "wapInfo").ifPresent(value -> item.setWapInfo((Integer) value));
        getValue(meta, list, "promotionFlags").ifPresent(value -> item.setPromotionFlags((Map<String, Integer>) value));
        getValue(meta, list, "priceRatio").ifPresent(value -> item.setPriceRatio((Integer) value));
        getValue(meta, list, "updateTime").ifPresent(value -> item.setUpdateTime(((Number) value).longValue()));
        getValue(meta, list, "activity").ifPresent(value -> item.setActivity(((Number) value).longValue()));
        getValue(meta, list, "logicBit").ifPresent(value -> item.setLogicBit(((Number) value).longValue()));
        getValue(meta, list, "specialPriceBit").ifPresent(value -> item.setSpecialPriceBit(((Number) value).longValue()));
        getValue(meta, list, "activityIds").ifPresent(value -> item.setActivityIds((List<String>) value));
        getValue(meta, list, "originalPrice").ifPresent(value -> item.setOriginalPrice((Integer) value));
        getValue(meta, list, "customMinAvailablePrice").ifPresent(value -> item.setCustomMinAvailablePrice((Integer) value));
        getValue(meta, list, "customOriginPriceForMin").ifPresent(value -> item.setCustomOriginPriceForMin((Integer) value));
        getValue(meta, list, "priceTraceStr").ifPresent(value -> item.setPriceTraceStr((String) value));
        getValue(meta, list, "discountType").ifPresent(value -> item.setDiscountType((Map<String, Integer>) value));
        getValue(meta, list, "codebase").ifPresent(value -> item.setCodebase((String) value));
        getValue(meta, list, "codename").ifPresent(value -> item.setCodename((String) value));
        getValue(meta, list, "sabiMaxAmount").ifPresent(value -> item.setSabiMaxAmount((Integer) value));
        getValue(meta, list, "discountArr").ifPresent(value -> item.setDiscountArr((Map<String, Integer>) value));
        getValue(meta, list, "makeUp").ifPresent(value -> item.setMakeUp((Integer) value));
        getValue(meta, list, "minPriceTax").ifPresent(value -> item.setMinPriceTax((Integer) value));
        getValue(meta, list, "lmUpdated").ifPresent(value -> item.setLmUpdated((Boolean) value));
        getValue(meta, list, "saleDiscount").ifPresent(value -> item.setSaleDiscount((Map<String, Double>) value));
        getValue(meta, list, "tw").ifPresent(value -> item.setTw((String) value));
        getValue(meta, list, "trd").ifPresent(value -> item.setTrd((String) value));
        getValue(meta, list, "ppbPayment").ifPresent(value -> item.setPpbPayment((Integer) value));
        getValue(meta, list, "extendsMap").ifPresent(value -> item.setExtendsMap((Map<String, String>) value));
        getValue(meta, list, "paymentType").ifPresent(value -> item.setPaymentType((String) value));
        getValue(meta, list, "cancelPolicy").ifPresent(value -> item.setCancelPolicy((String) value));
        getValue(meta, list, "customMinAvailableHourlyPrice").ifPresent(value -> item.setCustomMinAvailableHourlyPrice((Integer) value));
        getValue(meta, list, "hourlyPriceServiceTime").ifPresent(value -> item.setHourlyPriceServiceTime((String) value));
        getValue(meta, list, "timeLength").ifPresent(value -> item.setTimeLength((Double) value));
        getValue(meta, list, "roomType").ifPresent(value -> item.setRoomType((Integer) value));
        getValue(meta, list, "roomId").ifPresent(value -> item.setRoomId((String) value));
        getValue(meta, list, "conditions").ifPresent(value -> item.setConditions((Map<String, Object>) value));
        getValue(meta, list, "showPriceInfo").ifPresent(value -> item.setShowPriceInfo((Map<String, String>) value));
        getValue(meta, list, "combine").ifPresent(value -> item.setCombine((Map<String, Object>) value));

        return item;
    }

    private Optional<Object> getValue(Map<String, Integer> meta, List<Object> list, String name) {
        Integer index = meta.get(name);
        if (index == null) {
            Metrics.counter( "renderApiNoMetaIndex").delta().tag("name", name).get().inc();
            return Optional.empty();
        }

        if (index < 0 || index >= list.size()) {
            Metrics.counter( "renderApiMetaIndexOutOfBounds").delta().tag("name", name).get().inc();
            return Optional.empty();
        }

        Object value = list.get(index);
        return Optional.ofNullable(value);
    }

    @Data
    @NoArgsConstructor
    public static final class RenderApiResponse {
        private String cityurl;
        private Map<String, Integer> meta;
        private List<List<Object>> hotels;
    }

}
