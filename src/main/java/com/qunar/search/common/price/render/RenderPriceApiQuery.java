package com.qunar.search.common.price.render;

import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/8/23
 */
@Data
public class RenderPriceApiQuery {

    private String fromDate;

    private String toDate;

    private long logicBit;

    private long userBit;

    private String apikey;

    /**
     * 调用方透传到报价的参数。<参数名, 参数值>
     */
    private Map<String, String> extendMap;

    /**
     * 调render接口时，需要传递的酒店维度的参数。
     *
     * <酒店seq, <参数名, 参数值>>
     */
    private Map<String, Map<String, String>> hotelExtendMap;

    /**
     * 酒店seq集合
     */
    private Set<String> seqSet;

    /**
     * 是否提前获取到了用户相关信息，如果提前获取到了则要设置为true
     */
    private boolean preAcquireInfo = false;

    /**
     * 请求是否是 rank 抓取
     */
    private boolean rankCrawl = false;

}
