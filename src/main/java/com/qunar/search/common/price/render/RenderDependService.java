package com.qunar.search.common.price.render;

import com.google.common.collect.Maps;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.metrics.Metrics;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2021/2/1
 */
@Component
@Slf4j
public class RenderDependService {

    /**
     * 调用render依赖服务的总开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "renderDependService.enable", defaultValue = "true")
    private static volatile boolean enable;

    /**
     * 调用render依赖服务的总的超时时间，单位是ms
     */
    @QMapConfig(value = "render_price_config.properties", key = "renderDependService.timeout", defaultValue = "50")
    private static volatile int timeout;

    private static List<RenderDependParam> renderDependParams;

    /**
     * 自动注入实现了RenderDependParam接口的bean
     */
    @Autowired(required = false)
    public RenderDependService(List<RenderDependParam> renderDependParams) {
        RenderDependService.renderDependParams = renderDependParams;
    }

    /**
     * 构建调render接口时酒店维度需要透传的参数
     *
     * @param hotelItemMap <酒店seq, HotelItem>
     * @param request 请求信息
     * @return 酒店维度透传参数map <酒店seq, <参数名, 参数值>>
     */
    public static Map<String, Map<String, String>> buildHotelExtendMap(Map<String, HotelItem> hotelItemMap, HttpServletRequest request, ThreadPoolExecutor executor) {
        if (!enable || CollectionUtils.isEmpty(renderDependParams)) {
            return null;
        }

        long start = System.currentTimeMillis();
        try {
            Map<String, Future<Map<String, String>>> futureMap = Maps.newHashMap();
            for (RenderDependParam dependParam : renderDependParams) {
                if (!dependParam.enable(request)) {
                    continue;
                }
                Future<Map<String, String>> future = executor.submit(() -> dependParam.paramValue(hotelItemMap, request));
                futureMap.put(dependParam.paramName(), future);
            }

            if (MapUtils.isEmpty(futureMap)) {
                return null;
            }

            long nanos = TimeUnit.MILLISECONDS.toNanos(timeout);
            final long deadline = System.nanoTime() + nanos;

            // <酒店seq, <参数名, 参数值>>
            Map<String, Map<String, String>> hotelExtendMap = Maps.newHashMap();

            for (Map.Entry<String, Future<Map<String, String>>> entry : futureMap.entrySet()) {
                String paramName = entry.getKey();
                Future<Map<String, String>> future = entry.getValue();
                try {
                    // <酒店seq, 参数值>
                    Map<String, String> map = future.get(nanos, TimeUnit.NANOSECONDS);

                    // 填充酒店维度透传参数
                    fillHotelExtendMap(hotelExtendMap, map, paramName);

                } catch (TimeoutException e) {
                    Metrics.meter("render_depend_service_timeout").tag("param", paramName).get().mark();
                    log.warn("调用render依赖服务超时, paramName:{}, timeout:{}", paramName, timeout);
                } catch (InterruptedException | ExecutionException e) {
                    Metrics.meter("render_depend_service_error").tag("param", paramName).get().mark();
                    log.error("调用render依赖服务失败, paramName:{}", paramName, e);
                }

                nanos = deadline - System.nanoTime();
            }

            return hotelExtendMap;
        } catch (Exception e) {
            QMonitor.recordOne("buildHotelExtendMapFailed");
            log.error("构建render酒店维度透传参数失败", e);
        } finally {
            QMonitor.recordQuantile("buildHotelExtendMap", System.currentTimeMillis() - start);
        }

        return null;
    }

    /**
     * 填充酒店维度透传参数
     *
     * @param hotelExtendMap <酒店seq, <参数名, 参数值>>
     * @param map <酒店seq, 参数值>
     * @param paramName 参数名
     */
    private static void fillHotelExtendMap(Map<String, Map<String, String>> hotelExtendMap, Map<String, String> map, String paramName) {
        if (MapUtils.isEmpty(map)) {
            return;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String hotelSeq = entry.getKey();
            String value = entry.getValue();
            Map<String, String> extendMap = hotelExtendMap.computeIfAbsent(hotelSeq, k -> Maps.newHashMap());
            extendMap.put(paramName, value);
        }
    }

}
