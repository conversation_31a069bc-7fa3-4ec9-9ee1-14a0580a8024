package com.qunar.search.common.price.render;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ning.http.client.Response;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.constants.CommonConstants;
import com.qunar.search.common.util.AsyncHttpUtil;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2021/2/1
 */
@Component
@Slf4j
public class AlitaUserIdentityParam implements RenderDependParam {

    /**
     * 开关
     */
    @QMapConfig(value = "render_price_config.properties", key = "alitaUserIdentity.enable", defaultValue = "false")
    private volatile boolean enable;

    /**
     * 调alita接口的url
     */
    @QMapConfig(value = "render_price_config.properties", key = "alitaUserIdentity.url", defaultValue = "http://alitaraiser.corp.qunar.com/identity/user?from=m_hotelrank_search")
    private volatile String url;

    /**
     * 调alita接口的超时时间，单位是ms
     */
    @QMapConfig(value = "render_price_config.properties", key = "alitaUserIdentity.timeout", defaultValue = "50")
    private volatile int timeout;

    /**
     * 需要查询的身份code
     */
    @QMapConfig(value = "render_price_config.properties", key = "alitaUserIdentity.identities", defaultValue = "newCustomerInStoreActivity")
    private volatile String identities;


    @Override
    public boolean enable(HttpServletRequest request) {
        if (!enable) {
            return false;
        }

        String uid = request.getParameter("uid");
        String userId = request.getParameter("userId");

        return StringUtils.isNotBlank(uid) && StringUtils.isNotBlank(userId);
    }

    @Override
    public String paramName() {
        return "alitaUserIdentity";
    }

    @Override
    public Map<String, String> paramValue(Map<String, HotelItem> hotelItemMap,
                                          HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String userId = request.getParameter("userId");
        return fetchAlitaUserIdentity(hotelItemMap.keySet(), uid, userId);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> fetchAlitaUserIdentity(Set<String> seqs, String uid, String userId) {
        long startTime = System.currentTimeMillis();
        String paramBody = "";
        String responseBody = "";
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("uid", uid);
            params.put("user_id", userId);
            params.put("identities", this.identities);
            params.put("params", CommonConstants.COMMA_JOINER.join(seqs));
            paramBody = JsonUtils.toJson(params);

            Future<Response> responseFuture = AsyncHttpUtil.asyncPost(this.url, paramBody, this.paramName());
            if (responseFuture == null) {
                return null;
            }
            responseBody = responseFuture.get(this.timeout, TimeUnit.MILLISECONDS).getResponseBody();
            return parse(responseBody);
        } catch (TimeoutException e) {
            QMonitor.recordOne("fetchAlitaUserIdentityTimeout");
        } catch (Exception e) {
            QMonitor.recordOne("fetchAlitaUserIdentityError");
            log.error("调alita接口查询用户身份失败, url:{}, paramBody:{}, responseBody:{}", this.url, paramBody, responseBody, e);
        } finally {
            QMonitor.recordQuantile("fetchAlitaUserIdentity", System.currentTimeMillis() - startTime);
        }

        return null;
    }

    /**
     * 解析接口返回的结果。接口wiki：https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=331086811
     *
     * @param responseBody 接口返回结果
     * @return <酒店seq, 酒店命中的身份列表(是一个json字符串)>
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> parse(String responseBody) throws Exception {
        Map<String, Object> response = JsonUtils.toBean(responseBody, Map.class);
        Map<String, Object> data = (Map<String, Object>)response.get("data");
        if (MapUtils.isEmpty(data)) {
            return null;
        }
        List<Map<String, Object>> hitIdentityList = (List<Map<String, Object>>)data.get("hitIdentity");
        if (CollectionUtils.isEmpty(hitIdentityList)) {
            return null;
        }

        // <酒店seq, 命中的身份列表>
        Map<String, List<String>> seqHitIdentityListMap = Maps.newHashMap();

        for (Map<String, Object> map : hitIdentityList) {
            String identity = (String)map.get("identity");
            Map<String, Object> ext = (Map<String, Object>)map.get("ext");
            if (StringUtils.isBlank(identity) || MapUtils.isEmpty(ext)) {
                continue;
            }
            List<String> hitHotelList = (List<String>)ext.get("hitHotel");
            if (CollectionUtils.isEmpty(hitHotelList)) {
                continue;
            }
            for (String seq : hitHotelList) {
                seqHitIdentityListMap.computeIfAbsent(seq, s -> Lists.newArrayList()).add(identity);
            }
        }

        if (MapUtils.isEmpty(seqHitIdentityListMap)) {
            return null;
        }
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(seqHitIdentityListMap.size());
        seqHitIdentityListMap.forEach((seq, identityList) -> resultMap.put(seq, JsonUtils.toJson(identityList)));
        return resultMap;
    }

}
