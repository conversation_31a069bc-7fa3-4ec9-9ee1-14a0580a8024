package com.qunar.search.common.task.automatic;

import com.qunar.search.common.base.SearchDataTask;
import com.qunar.search.common.base.UpdaterInfo;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.concurrent.ScheduledFuture;

/**
 * <AUTHOR>
 * @date 2018/6/12
 */
public class AutomaticTaskInfo {

	private Object bean;
	private Method method;
	private SearchDataTask task;
	private ScheduledFuture future;
	private UpdaterInfo updaterInfo;

	public AutomaticTaskInfo(Object bean, Method method) {
		this.bean = bean;
		this.method = method;
	}

	public SearchDataTask getTask() {
		return task;
	}

	public void setTask(SearchDataTask task) {
		this.task = task;
	}

	public ScheduledFuture getFuture() {
		return future;
	}

	public void setFuture(ScheduledFuture future) {
		this.future = future;
	}

	public Object getBean() {
		return bean;
	}

	public void setBean(Object bean) {
		this.bean = bean;
	}

	public Method getMethod() {
		return method;
	}

	public void setMethod(Method method) {
		this.method = method;
	}

    public UpdaterInfo getUpdaterInfo() {
        return updaterInfo;
    }

    public void setUpdaterInfo(UpdaterInfo updaterInfo) {
        this.updaterInfo = updaterInfo;
    }

    public void run() {
		try {
			ReflectionUtils.makeAccessible(this.method);
			this.method.invoke(this.bean);
		} catch (InvocationTargetException ex) {
			ReflectionUtils.rethrowRuntimeException(ex.getTargetException());
		} catch (IllegalAccessException ex) {
			throw new UndeclaredThrowableException(ex);
		}
	}
}
