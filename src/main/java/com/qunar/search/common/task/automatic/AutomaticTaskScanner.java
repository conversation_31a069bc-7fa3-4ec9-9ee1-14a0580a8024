package com.qunar.search.common.task.automatic;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.search.common.base.SearchDataTask;
import com.qunar.search.common.base.UpdaterInfo;
import com.qunar.search.common.enums.Env;
import com.qunar.search.common.update.BaseUpdateTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringValueResolver;
import qunar.metrics.Metrics;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2018/6/11
 */
@Slf4j
@Component
public class AutomaticTaskScanner implements BeanPostProcessor, DisposableBean, Ordered, EmbeddedValueResolverAware {
    public static final String CACHE_MONITOR_KEY = "cacheMonitor";
    private final Set<Class<?>> nonAnnotatedClasses = Collections.newSetFromMap(new ConcurrentHashMap<>(64));

    private AtomicBoolean isInitFinish = new AtomicBoolean(false);
    /**
     * 常规Updater
     */
    private List<AutomaticTaskInfo> initList = Lists.newArrayList();
    /**
     * 对城市、酒店有依赖的Updater
     */
    private List<AutomaticTaskInfo> afterBaseDataList = Lists.newArrayList();

    private static final Map<String, AutomaticTaskInfo> taskMap = Maps.newConcurrentMap();

    private StringValueResolver embeddedValueResolver;

    private Set<String> blockUpdaters;

    public List<AutomaticTaskInfo> getInitList() {
        return initList;
    }

    public List<AutomaticTaskInfo> getAfterBaseDataList() {
        return afterBaseDataList;
    }


    public void setIsInitFinish() {
        isInitFinish.set(true);
    }

    private final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(10);

    @Override
    public Object postProcessBeforeInitialization(Object o, String s) throws BeansException {
        return o;
    }

    @Override
    public Object postProcessAfterInitialization(final Object bean, String beanName) throws BeansException {
        Class<?> targetClass = AopUtils.getTargetClass(bean);
        if (!this.nonAnnotatedClasses.contains(targetClass)) {
            final Set<Method> annotatedMethods = new LinkedHashSet<>(1);
            ReflectionUtils.doWithMethods(targetClass, method -> {
                for (SearchDataTask task : AnnotationUtils.getRepeatableAnnotations(method, SearchDataTask.class)) {
                    processScheduled(task, method, bean, beanName);
                    annotatedMethods.add(method);
                }
            });
            if (annotatedMethods.isEmpty()) {
                this.nonAnnotatedClasses.add(targetClass);
            }
        }
        return bean;
    }

    private void processScheduled(SearchDataTask searchDataTask, Method method, Object bean, String beanName) {
        Env env = Env.current();
        Env taskEnv = searchDataTask.env();
        if (env != taskEnv && !Env.match(taskEnv)) {
            return;
        }

        AutomaticTaskInfo task = new AutomaticTaskInfo(bean, method);
        task.setTask(searchDataTask);
        String key = beanName;
        if (CollectionUtils.isEmpty(blockUpdaters) || !blockUpdaters.contains(key)) {
            if (searchDataTask.afterBaseDataFinish()) {
                afterBaseDataList.add(task);
            } else {
                initList.add(task);
            }
            if (searchDataTask.hasSchedule()) {
                ScheduledFuture<?> future = schedule(task);
                task.setFuture(future);
            }
        }

        if (bean instanceof BaseUpdateTemplate) {
            BaseUpdateTemplate<?> updater = (BaseUpdateTemplate<?>) bean;

            if (updater.hasVersion()) {
                UpdaterInfo updaterInfo = bean.getClass().getAnnotation(UpdaterInfo.class);
                if (updaterInfo == null || StringUtils.isBlank(updaterInfo.tableName())) {
                    String errorMsg = beanName + "必须用@UpdaterInfo注解来指定表名和版本号字段名";
                    log.warn(errorMsg);
                }
                task.setUpdaterInfo(updaterInfo);
            }

            taskMap.put(key, task);
            if (updater.needMonitor()) {
                Metrics.gauge(CACHE_MONITOR_KEY).tag("updater", updater.getClass().getSimpleName()).call(updater::size);
            }
        }
    }

    private ScheduledFuture<?> schedule(AutomaticTaskInfo task) {
        String updaterName = getKey(task.getMethod());
        if (CollectionUtils.isNotEmpty(blockUpdaters) && blockUpdaters.contains(updaterName)) {
            log.error("{} is block!", updaterName);
            return null;
        }

        if (task.getBean() instanceof BaseUpdateTemplate) {
            ((BaseUpdateTemplate<?>) task.getBean()).setEnableSchedule(true);
        }

        return SCHEDULED_EXECUTOR_SERVICE.scheduleAtFixedRate(() -> {
            if (isInitFinish.get()) {
                task.run();
            }
        }, task.getTask().initialDelay(), task.getTask().fixedDelay(), task.getTask().timeUnit());
    }

    public boolean cancel(String taskKey) {
        if (!taskMap.containsKey(taskKey)) {
            return false;
        }

        AutomaticTaskInfo taskInfo = taskMap.get(taskKey);
        if (taskInfo.getFuture() == null) {
            return false;
        }

        if (taskInfo.getBean() instanceof BaseUpdateTemplate) {
            ((BaseUpdateTemplate<?>) taskInfo.getBean()).setEnableSchedule(false);
        }

        return taskInfo.getFuture().cancel(true);
    }

    public boolean reSchedule(String taskKey) {
        if (!taskMap.containsKey(taskKey)) {
            return false;
        }
        cancel(taskKey);

        AutomaticTaskInfo task = taskMap.get(taskKey);
        task.setFuture(schedule(task));
        return true;
    }

    private String getKey(Method method) {
        return StringUtils.join(method.getDeclaringClass().getSimpleName(), ".", method.getName());
    }

    public Set<String> getUpdater() {
        return taskMap.keySet();
    }

    public static Map<String, AutomaticTaskInfo> getTaskMap() {
        return taskMap;
    }

    public AutomaticTaskInfo getTaskInfo(String key) {
        return taskMap.get(key);
    }

    @Override
    public void destroy() {
        SCHEDULED_EXECUTOR_SERVICE.shutdown();
    }

    private Set<String> blockUpdaters() {
        String s = this.embeddedValueResolver.resolveStringValue("${block.updater:}");
        if (StringUtils.isBlank(s)) {
            return Collections.emptySet();
        }

        String[] updaters = StringUtils.split(s, ",");
        return ImmutableSet.copyOf(updaters);
    }

    @Override
    public void setEmbeddedValueResolver(StringValueResolver resolver) {
        this.embeddedValueResolver = resolver;
        this.blockUpdaters = blockUpdaters();
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE;
    }
}
