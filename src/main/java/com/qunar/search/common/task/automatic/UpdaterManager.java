package com.qunar.search.common.task.automatic;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.search.common.base.UpdaterInfo;
import com.qunar.search.common.bean.UpdaterDetail;
import com.qunar.search.common.dao.UpdaterCommonDao;
import com.qunar.search.common.update.BaseUpdateTemplate;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class UpdaterManager {
	private static final Logger log = LoggerFactory.getLogger(UpdaterManager.class);

	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	@Resource
	private UpdaterCommonDao updaterCommonDao;

	@Autowired
	AutomaticTaskScanner scanner;

	public void manualScheduling(String updaterName, String versionStr, boolean stop) {
		if (!scanner.getUpdater().contains(updaterName)) {
			log.error("无效任务名称{}", updaterName);
			return;
		}

		AutomaticTaskInfo taskInfo = scanner.getTaskInfo(updaterName);

		if (taskInfo.getTask().hasSchedule()) {
			scanner.cancel(updaterName);
		}

		BaseUpdateTemplate updater = null;
		if (taskInfo.getBean() instanceof BaseUpdateTemplate) {
			updater = (BaseUpdateTemplate) taskInfo.getBean();
		}

		Object version;
		Class keyClass = updater.getKeyClass();
		if (keyClass == Integer.class) {
			version = NumberUtils.toInt(versionStr);
		} else if (keyClass == Long.class) {
			version = NumberUtils.toLong(versionStr);
		} else if (keyClass == Double.class) {
			version = NumberUtils.toDouble(versionStr);
		} else {
			version = versionStr;
		}

		updater.update(version, true);

		if (!stop && taskInfo.getTask().hasSchedule()) {
			scanner.reSchedule(updaterName);
		}
	}

	public void stopSchedule(String updaterName) {
		scanner.cancel(updaterName);
	}

	public void reSchedule(String updaterName) {
		scanner.reSchedule(updaterName);
	}

	public List updaterList() {
		Map<String, AutomaticTaskInfo> taskMap = AutomaticTaskScanner.getTaskMap();
		List<Map<String, Object>> list = Lists.newArrayListWithCapacity(taskMap.size());
		taskMap.forEach((k, v) -> {
			Map<String, Object> map = Maps.newHashMap();
			if (v.getBean() instanceof BaseUpdateTemplate) {

			    BaseUpdateTemplate updater = (BaseUpdateTemplate) v.getBean();
				map.put("name", k);
				map.put("size",  updater.size());
				map.put("version", updater.getVersion());
				map.put("enableSchedule", updater.isEnableSchedule());
                map.put("costTime", updater.getCostTime().get());
                map.put("desc", updater.getDesc());
                map.put("tag", updater.getClass().getSimpleName());
                map.put("hasVersion", updater.hasVersion());
                map.put("needMonitor", updater.needMonitor());
                map.put("fixedDelay", v.getTask().fixedDelay());
                map.put("timeUnit", v.getTask().timeUnit());
                map.put("enablePaging", updater.isEnablePaging());
                map.put("pageSize", updater.getPageSize());
                long time = updater.getLastExecuteTime().get();
                String timeStr = DATE_TIME_FORMATTER.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time),ZoneId.systemDefault()));
                map.put("lastExecuteTime", timeStr);

                if (updater.hasVersion()) {
                    if (v.getUpdaterInfo() != null) {
                        map.put("tableName", v.getUpdaterInfo().tableName());
                        map.put("versionField", v.getUpdaterInfo().versionField());
                    }
                }

				list.add(map);
			}
		});
		return list;
	}

	public UpdaterDetail getUpdaterDetail(String updaterName) {
        AutomaticTaskInfo taskInfo = scanner.getTaskInfo(updaterName);
        if (taskInfo == null) {
            return null;
        }

        BaseUpdateTemplate updater = (BaseUpdateTemplate) taskInfo.getBean();

        long time = updater.getLastExecuteTime().get();
        String timeStr = DATE_TIME_FORMATTER.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time),ZoneId.systemDefault()));

        List<Integer> historyVersion = Collections.emptyList();
        String tableName = null;
        String versionField = null;
		if (taskInfo.getUpdaterInfo() != null) {
			tableName = taskInfo.getUpdaterInfo().tableName();
			versionField = taskInfo.getUpdaterInfo().versionField();
		}

        return UpdaterDetail.builder()
                .name(updaterName)
                .tag(updater.getClass().getSimpleName())
                .fixedDelay(taskInfo.getTask().fixedDelay())
                .timeUnit(taskInfo.getTask().timeUnit())
                .size(updater.size())
                .version(updater.getVersion())
                .enableSchedule(updater.isEnableSchedule())
                .costTime(updater.getCostTime().get())
                .desc(updater.getDesc())
                .hasVersion(updater.hasVersion())
                .needMonitor(updater.needMonitor())
                .lastExecuteTime(timeStr)
                .tableName(tableName)
                .versionField(versionField)
                .historyVersion(historyVersion)
                .verifyPassed(updater.isVerifyPassed())
                .build();
    }

    /**
     * 查DB中的历史版本
     *
     * @param updaterName 定时任务名称
     * @return 历史版本列表
     */
	public List<Integer> findHistoryVersion(String updaterName) {
        AutomaticTaskInfo taskInfo = scanner.getTaskInfo(updaterName);
        UpdaterInfo updaterInfo = taskInfo.getUpdaterInfo();
        if (updaterInfo != null) {
            return updaterCommonDao.selectVersionList(updaterInfo.versionField(), updaterInfo.tableName());
        }
        return Collections.emptyList();
    }

	public void cleanData(String updaterName) {
		AutomaticTaskInfo taskInfo = scanner.getTaskInfo(updaterName);
		((BaseUpdateTemplate) taskInfo.getBean()).clean();
	}

	public Object check(String updaterName, String value) {
		AutomaticTaskInfo taskInfo = scanner.getTaskInfo(updaterName);
		return ((BaseUpdateTemplate) taskInfo.getBean()).check(value);
	}
}
