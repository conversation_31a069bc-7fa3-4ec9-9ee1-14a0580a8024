package com.qunar.search.common.profit.entity;

import com.qunar.search.common.util.Strings;

/**酒店质量指标
 * Created by fangxue.zhang on 2016/6/1.
 */
public class HotelQuality {


    /**
     * 酒店seq
     */
    private String hotelSeq;

    /**
     * 酒店图片质量
     */
    private  float hotelImageQuality = 0;

    /**
     * 酒店分数质量
     */
    private float hotelScoreQuality = 0;

    /**
     * 酒店房型质量
     */
    private float hotelRoomTypeQuality = 0;

    /**
     * 信息完整度质量
     */
    private float hotelInformationIntegrityQuality = 0;

    /**
     * 数据的日期
     */
    private String dt;

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public String getHotelSeq() {
        return hotelSeq;
    }

    public void setHotelSeq(String hotelSeq) {
        this.hotelSeq = hotelSeq;
    }

    public float getHotelImageQuality() {
        return hotelImageQuality;
    }

    public void setHotelImageQuality(float hotelImageQuality) {
        this.hotelImageQuality = hotelImageQuality;
    }

    public float getHotelScoreQuality() {
        return hotelScoreQuality;
    }

    public void setHotelScoreQuality(float hotelScoreQuality) {
        this.hotelScoreQuality = hotelScoreQuality;
    }

    public float getHotelRoomTypeQuality() {
        return hotelRoomTypeQuality;
    }

    public void setHotelRoomTypeQuality(float hotelRoomTypeQuality) {
        this.hotelRoomTypeQuality = hotelRoomTypeQuality;
    }

    public float getHotelInformationIntegrityQuality() {
        return hotelInformationIntegrityQuality;
    }

    public void setHotelInformationIntegrityQuality(float hotelInformationIntegrityQuality) {
        this.hotelInformationIntegrityQuality = hotelInformationIntegrityQuality;
    }

    public void internStringField() {
        this.hotelSeq = Strings.getInternString(this.hotelSeq);
        this.dt = Strings.getInternString(this.dt);
    }

    /**
     * 覆盖数据.
     * @param hotelQuality
     */
    public void copyFrom(HotelQuality hotelQuality){
        this.hotelImageQuality = hotelQuality.hotelImageQuality;
        this.hotelInformationIntegrityQuality = hotelQuality.hotelInformationIntegrityQuality;
        this.hotelRoomTypeQuality = hotelQuality.hotelRoomTypeQuality;
        this.hotelScoreQuality = hotelQuality.hotelScoreQuality;
    }
}
