package com.qunar.search.common.profit.entity;

import com.qunar.search.common.util.Strings;

/**酒店收益相关数据.
 * Created by fangxue.zhang on 2016/6/1.
 */

public class HotelProfitData {

    /**
     * 酒店seq
     */
    private String hotelSeq;


    /**
     * 历史收益, 默认是0
     */
    private float totalCommission = 0;

    /**
     * 预估的最近一周的收益
     */
    private float predictCommission = 0;

    /**
     * 平均没订单收益
     */
    private float avgCommissionByOrder = 0;


    /**
     * 平均每间夜收益.
     */
    private float avgCommissionByRoomNight = 0;

    /**
     * 数据生产的日期
     */
    private String dt;

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public float getTotalCommission() {
        return totalCommission;
    }

    public void setTotalCommission(float totalCommission) {
        this.totalCommission = totalCommission;
    }

    public float getPredictCommission() {
        return predictCommission;
    }

    public void setPredictCommission(float predictCommission) {
        this.predictCommission = predictCommission;
    }

    public float getAvgCommissionByOrder() {
        return avgCommissionByOrder;
    }

    public void setAvgCommissionByOrder(float avgCommissionByOrder) {
        this.avgCommissionByOrder = avgCommissionByOrder;
    }

    public float getAvgCommissionByRoomNight() {
        return avgCommissionByRoomNight;
    }

    public void setAvgCommissionByRoomNight(float avgCommissionByRoomNight) {
        this.avgCommissionByRoomNight = avgCommissionByRoomNight;
    }

    public String getHotelSeq() {
        return hotelSeq;
    }

    public void setHotelSeq(String hotelSeq) {
        this.hotelSeq = hotelSeq;
    }

    public void internStringField() {
        this.hotelSeq = Strings.getInternString(this.hotelSeq);
        this.dt = Strings.getInternString(this.dt);
    }

    public void copyFrom(HotelProfitData hotelProfitData){

        this.totalCommission = hotelProfitData.totalCommission;
        this.avgCommissionByOrder = hotelProfitData.avgCommissionByOrder;
        this.avgCommissionByRoomNight = hotelProfitData.avgCommissionByRoomNight;
        this.predictCommission = hotelProfitData.predictCommission;
    }
}
