package com.qunar.search.common.profit.activity;

import com.qunar.search.common.util.DateUtil;
import com.qunar.hotel.qmonitor.QMonitor;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.TypedConfig;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 *
 */
public class CityActivityConfigParser implements TypedConfig.Parser<CityActivityConfig> {

    private static final Logger logger = LoggerFactory.getLogger(CityActivityConfigParser.class);

    @Override
    public CityActivityConfig parse(String data) throws IOException {
        Document document = null;
        CityActivityConfig cityActivityConfig = new CityActivityConfig();
        try {
            document = DocumentHelper.parseText(data);
            if (document == null) {
                return null;
            }
            Element node = document.getRootElement();
            List<Element> activityList = node.elements("activity");

            cityActivityConfig.updateMap(createMap(activityList));

            return cityActivityConfig;

        } catch (Exception e) {
            QMonitor.recordOne("CITYACTIVITY_CONFIG_ERROR");
            logger.error("parse city_profit_activity.xml error ", e);
            return null;
        }
    }

    private Map<String, List<CityProfitActivity>> createMap(List<Element> elements) {

        Map<String, List<CityProfitActivity>> map = new HashMap<>();

        for (Element e : elements) {
            CityProfitActivity cityProfitActivity = new CityProfitActivity();
            cityProfitActivity.setCityUrl(e.element("cityUrl").attributeValue("value"));
            cityProfitActivity.setStartDate(Integer.parseInt(e.element("startDate").attributeValue("value")));
            cityProfitActivity.setEndDate(Integer.parseInt(e.element("endDate").attributeValue("value")));
            if (cityProfitActivity.getEndDate() <= cityProfitActivity.getStartDate() || cityProfitActivity.getEndDate() < DateUtil.getTodayYyyyMMdd()) {
                logger.warn("city {}, startDate:{}, endDate:{}", cityProfitActivity.getCityUrl(), cityProfitActivity.getStartDate(), cityProfitActivity.getEndDate());
                QMonitor.recordOne("CITYACTIVITY_CONFIG_INVALID");
                continue;
            }
            if (!map.containsKey(cityProfitActivity.getCityUrl())) {
                List<CityProfitActivity> cityActivityConfigs = new LinkedList<>();
                map.put(cityProfitActivity.getCityUrl(), cityActivityConfigs);
            }
            map.get(cityProfitActivity.getCityUrl()).add(cityProfitActivity);
        }
        return map;
    }

}
