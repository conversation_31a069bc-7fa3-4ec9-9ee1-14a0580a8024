package com.qunar.search.common.profit.entity;

import com.qunar.search.common.util.Strings;

/**酒店服务质量数据描述
 * Created by fangxue.zhang on 2016/6/1.
 */
public class HotelServiceQuality {


    /**
     * 酒店seq
     */
    private String hotelSeq;

    /**
     * 酒店确认率
     */
    private float confirmRate = 0;

    /**
     * 自助使用率
     */
    private float ebSelfServiceRate = 0;

    /**
     * 到店无房率
     */
    private float arriveNoRoomRate = 0;

    /**
     * 预留房得房率
     */
    private float reservedGetRate = 0;

    /**
     * 酒店平均订单处理时间.
     */
    private float orderAvgProcessTime = 0;

    /**
     * 订单反悔率
     */
    private float orderRegretRate = 0;

    /**
     * 入住率
     */
    private float checkInRate = 0;

    /**
     * 数据更新的日期
     */
    private String dt;

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public String getHotelSeq() {
        return hotelSeq;
    }

    public void setHotelSeq(String hotelSeq) {
        this.hotelSeq = hotelSeq;
    }

    public float getConfirmRate() {
        return confirmRate;
    }

    public void setConfirmRate(float confirmRate) {
        this.confirmRate = confirmRate;
    }

    public float getReservedGetRate() {
        return reservedGetRate;
    }

    public void setReservedGetRate(float reservedGetRate) {
        this.reservedGetRate = reservedGetRate;
    }

    public float getOrderAvgProcessTime() {
        return orderAvgProcessTime;
    }

    public void setOrderAvgProcessTime(float orderAvgProcessTime) {
        this.orderAvgProcessTime = orderAvgProcessTime;
    }

    public float getOrderRegretRate() {
        return orderRegretRate;
    }

    public void setOrderRegretRate(float orderRegretRate) {
        this.orderRegretRate = orderRegretRate;
    }

    public float getCheckInRate() {
        return checkInRate;
    }

    public void setCheckInRate(float checkInRate) {
        this.checkInRate = checkInRate;
    }

    public void internStringField() {
        this.hotelSeq = Strings.getInternString(this.hotelSeq);
        this.dt = Strings.getInternString(this.dt);
    }

    public void copyFrom(HotelServiceQuality hotelServiceQuality) {
        this.checkInRate = hotelServiceQuality.checkInRate;
        this.confirmRate = hotelServiceQuality.confirmRate;
        this.orderAvgProcessTime = hotelServiceQuality.orderAvgProcessTime;
        this.orderRegretRate = hotelServiceQuality.orderRegretRate;
        this.arriveNoRoomRate = hotelServiceQuality.arriveNoRoomRate;
        this.reservedGetRate = hotelServiceQuality.reservedGetRate;
        //note eb使用率是通过http接口直接读过来的, 不从这里复制.
    }

    public float getEbSelfServiceRate() {
        return ebSelfServiceRate;
    }

    public void setEbSelfServiceRate(float ebSelfServiceRate) {
        this.ebSelfServiceRate = ebSelfServiceRate;
    }

    public float getArriveNoRoomRate() {
        return arriveNoRoomRate;
    }

    public void setArriveNoRoomRate(float arriveNoRoomRate) {
        this.arriveNoRoomRate = arriveNoRoomRate;
    }
}
