package com.qunar.search.common.profit.entity;

/**
 * 收益里面要配的系数
 * Created by fangxue.zhang on 2016/6/1.
 */
public class ProfitAdjustableParam {
    /**
     *  收益分组，每组酒店的个数
     */
    private int hotelsPerSectionProfitParam;

    /**
     * rank 的打分在收益中的系数
     */
    private double rankProfitParam;

    /**
     * 这个参数对应的桶的名字
     */
    private String bucketName;

    ////////////////////////////////////////////////////////////////////
    /**
     * 历史收益系数
     */

    private double historyProfitParam;

    /**
     * 7天内预估历史收益参数
     */
    private double predictedProfitParam;

    /**
     * 实时收益系数
     */
    private double realTimeProfitParam;

    /**
     * 酒店服务质量系数
     */
    private double hotelServiceQualityParam;

    /**
     * 酒店质量参数
     */
    private double hotelQualityParam;

    /**
     * 入住率相关系数
     */
    private double checkInRateParam;

    //以上是主要系数
    /////////////////////////////////////////////////////////////////////////


    //////////////////////////////////////////////////////////////////////

    /**
     * 酒店图片质量系数
     */
    private double hotelImageQualityParam;

    /**
     * 酒店房型质量系数
     */
    private double hotelRoomTypeQualityParam;

    /**
     * 酒店信息完整度系数
     */
    private double hotelInformationIntegrityQualityParam;

    /**
     * 酒店分数质量系数
     */
    private double hotelScoreQualityParam;

    //以上是酒店质量相关系数
    /////////////////////////////////////////////////////////////////////




    ////////////////////////////////////////////////////////////////////

    /**
     * 订单平均处理时间
     */
    private double orderAvgProcessTimeParam;

    /**
     * 自助使用率
     */
    private double ebSelfServiceRateParam;

    /**
     * 订单确认率
     */
    private double orderConfirmRateParam;

    /**
     * 入住可靠性
     */
    private double checkinReliabilityRateParam;



    /**
     * 预留得房率系数
     */
    private double reservedGetRateParam;

    ////////////////////////////////////////////////////////////////////


    /**
     * 收益桶使用的旧桶的逻辑
     */
    private String oldBucketName;


    /**
     * 给处理平均每订单收益的扭曲公式使用, beta
     */
    private double beta;

    /**
     * 给处理平均没订单收益的扭曲公式使用,alpha
     */
    private double alpha;

    /**
     * 前几页进行重排序
     */
    private int reRankPageCount;

    public int getReRankPageCount() {
        return reRankPageCount;
    }

    public void setReRankPageCount(int reRankPageCount) {
        this.reRankPageCount = reRankPageCount;
    }

    public double getCheckinReliabilityRateParam() {
        return checkinReliabilityRateParam;
    }

    public void setCheckinReliabilityRateParam(double checkinReliabilityRateParam) {
        this.checkinReliabilityRateParam = checkinReliabilityRateParam;
    }

    public double getBeta() {
        return beta;
    }

    public void setBeta(double beta) {
        this.beta = beta;
    }

    public double getAlpha() {
        return alpha;
    }

    public void setAlpha(double alpha) {
        this.alpha = alpha;
    }

    public String getOldBucketName() {
        return oldBucketName;
    }

    public void setOldBucketName(String oldBucketName) {
        this.oldBucketName = oldBucketName;
    }

    public double getHistoryProfitParam() {
        return historyProfitParam;
    }

    public void setHistoryProfitParam(double historyProfitParam) {
        this.historyProfitParam = historyProfitParam;
    }

    public double getPredictedProfitParam() {
        return predictedProfitParam;
    }

    public void setPredictedProfitParam(double predictedProfitParam) {
        this.predictedProfitParam = predictedProfitParam;
    }

    public double getRealTimeProfitParam() {
        return realTimeProfitParam;
    }

    public void setRealTimeProfitParam(double realTimeProfitParam) {
        this.realTimeProfitParam = realTimeProfitParam;
    }

    public double getHotelServiceQualityParam() {
        return hotelServiceQualityParam;
    }

    public void setHotelServiceQualityParam(double hotelServiceQualityParam) {
        this.hotelServiceQualityParam = hotelServiceQualityParam;
    }

    public double getHotelQualityParam() {
        return hotelQualityParam;
    }

    public void setHotelQualityParam(double hotelQualityParam) {
        this.hotelQualityParam = hotelQualityParam;
    }

    public double getHotelImageQualityParam() {
        return hotelImageQualityParam;
    }

    public void setHotelImageQualityParam(double hotelImageQualityParam) {
        this.hotelImageQualityParam = hotelImageQualityParam;
    }

    public double getHotelRoomTypeQualityParam() {
        return hotelRoomTypeQualityParam;
    }

    public void setHotelRoomTypeQualityParam(double hotelRoomTypeQualityParam) {
        this.hotelRoomTypeQualityParam = hotelRoomTypeQualityParam;
    }

    public double getHotelInformationIntegrityQualityParam() {
        return hotelInformationIntegrityQualityParam;
    }

    public void setHotelInformationIntegrityQualityParam(double hotelInformationIntegrityQualityParam) {
        this.hotelInformationIntegrityQualityParam = hotelInformationIntegrityQualityParam;
    }

    public double getHotelScoreQualityParam() {
        return hotelScoreQualityParam;
    }

    public void setHotelScoreQualityParam(double hotelScoreQualityParam) {
        this.hotelScoreQualityParam = hotelScoreQualityParam;
    }

    public double getOrderAvgProcessTimeParam() {
        return orderAvgProcessTimeParam;
    }

    public void setOrderAvgProcessTimeParam(double orderAvgProcessTimeParam) {
        this.orderAvgProcessTimeParam = orderAvgProcessTimeParam;
    }

    public double getOrderConfirmRateParam() {
        return orderConfirmRateParam;
    }

    public void setOrderConfirmRateParam(double orderConfirmRateParam) {
        this.orderConfirmRateParam = orderConfirmRateParam;
    }

    public double getReservedGetRateParam() {
        return reservedGetRateParam;
    }

    public void setReservedGetRateParam(double reservedGetRateParam) {
        this.reservedGetRateParam = reservedGetRateParam;
    }

    public double getCheckInRateParam() {
        return checkInRateParam;
    }

    public void setCheckInRateParam(double checkInRateParam) {
        this.checkInRateParam = checkInRateParam;
    }

    public double getEbSelfServiceRateParam() {
        return ebSelfServiceRateParam;
    }

    public void setEbSelfServiceRateParam(double ebSelfServiceRateParam) {
        this.ebSelfServiceRateParam = ebSelfServiceRateParam;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public double getRankProfitParam() {
        return rankProfitParam;
    }

    public void setRankProfitParam(double rankProfitParam) {
        this.rankProfitParam = rankProfitParam;
    }

    public int getHotelsPerSectionProfitParam() {
        return hotelsPerSectionProfitParam;
    }

    public void setHotelsPerSectionProfitParam(int hotelsPerSectionProfitParam) {
        this.hotelsPerSectionProfitParam = hotelsPerSectionProfitParam;
    }
}
