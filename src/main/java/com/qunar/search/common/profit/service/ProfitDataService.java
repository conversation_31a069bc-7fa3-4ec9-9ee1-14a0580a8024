package com.qunar.search.common.profit.service;

import com.qunar.search.common.profit.entity.*;
import com.qunar.search.common.util.CommonTools;
import com.qunar.search.common.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 存储从内存中加载过来的收益相关的数据.
 * Created by fangxue.zhang on 2016/6/1.
 */

public class ProfitDataService {


    private static ConcurrentHashMap<String, HotelProfitDataCluster> hotelProfitDataClusterConcurrentHashMap = new ConcurrentHashMap<>();


    private static ConcurrentHashMap<String, HotelServiceQualityDefault> hotelServiceQualityDefaultConcurrentHashMap = new ConcurrentHashMap<>();


    public static void putHotelServiceQualityDefault(HotelServiceQualityDefault hotelServiceQualityDefault){

        hotelServiceQualityDefault.internStringField();

        hotelServiceQualityDefaultConcurrentHashMap.put(hotelServiceQualityDefault.getCityCode(), hotelServiceQualityDefault);
    }

    public static void putHotelQuality(HotelQuality hotelQuality) {

        hotelQuality.internStringField();

        HotelProfitDataCluster cluster = hotelProfitDataClusterConcurrentHashMap.get(hotelQuality.getHotelSeq());

        if (cluster != null) {
            cluster.setHotelQuality(hotelQuality);
        } else {
            cluster = new HotelProfitDataCluster();
            cluster.setHotelQuality(hotelQuality);
            hotelProfitDataClusterConcurrentHashMap.put(hotelQuality.getHotelSeq(), cluster);
        }
    }


    public static void putHotelServiceQuality(HotelServiceQuality hotelServiceQuality) {

        hotelServiceQuality.internStringField();

        HotelProfitDataCluster cluster = hotelProfitDataClusterConcurrentHashMap.get(hotelServiceQuality.getHotelSeq());

        if (cluster != null) {
            if(cluster.getHotelServiceQuality() != null){
                cluster.getHotelServiceQuality().copyFrom(hotelServiceQuality);
            }else{
                cluster.setHotelServiceQuality(hotelServiceQuality);
            }
        }else{
            cluster = new HotelProfitDataCluster();
            cluster.setHotelServiceQuality(hotelServiceQuality);
            hotelProfitDataClusterConcurrentHashMap.put(hotelServiceQuality.getHotelSeq(), cluster);
        }
    }


    public static void putHotelProfitData(HotelProfitData hotelProfitData) {

        hotelProfitData.internStringField();

        HotelProfitDataCluster cluster = hotelProfitDataClusterConcurrentHashMap.get(hotelProfitData.getHotelSeq());

        if (cluster != null) {
            cluster.setHotelProfitData(hotelProfitData);
        } else {
            cluster = new HotelProfitDataCluster();
            cluster.setHotelProfitData(hotelProfitData);
            hotelProfitDataClusterConcurrentHashMap.put(hotelProfitData.getHotelSeq(), cluster);
        }
    }

    public static HotelProfitDataCluster getHotelProfitDataCluster(String hotelSeq) {
        HotelProfitDataCluster hotelProfitDataCluster = hotelProfitDataClusterConcurrentHashMap.get(hotelSeq.intern());
        if (null == hotelProfitDataCluster) {
            hotelProfitDataCluster = new HotelProfitDataCluster();
            hotelProfitDataCluster.setHotelProfitData(new HotelProfitData());
            hotelProfitDataCluster.setHotelQuality(new HotelQuality());
            hotelProfitDataCluster.setHotelServiceQuality(new HotelServiceQuality());

            String cityCode = CommonTools.getCityFromHotel(hotelSeq);
            HotelServiceQualityDefault hotelServiceQualityDefault = hotelServiceQualityDefaultConcurrentHashMap.get(cityCode);
            if(hotelServiceQualityDefault != null){
                overwriteHotelServiceQualityWithDefault(hotelProfitDataCluster.getHotelServiceQuality(), hotelServiceQualityDefault);
            }
        }
        if(hotelProfitDataCluster.getHotelServiceQuality() == null){
            hotelProfitDataCluster.setHotelServiceQuality(new HotelServiceQuality());
            String cityCode = CommonTools.getCityFromHotel(hotelSeq);
            HotelServiceQualityDefault hotelServiceQualityDefault = hotelServiceQualityDefaultConcurrentHashMap.get(cityCode);
            if(hotelServiceQualityDefault != null){
                overwriteHotelServiceQualityWithDefault(hotelProfitDataCluster.getHotelServiceQuality(), hotelServiceQualityDefault);
            }
        }
        if(hotelProfitDataCluster.getHotelProfitData() == null){
            hotelProfitDataCluster.setHotelProfitData(new HotelProfitData());
        }
        if(hotelProfitDataCluster.getHotelQuality() == null){
            hotelProfitDataCluster.setHotelQuality(new HotelQuality());
        }
        return hotelProfitDataCluster;
    }

    private static void overwriteHotelServiceQualityWithDefault(HotelServiceQuality hotelServiceQuality, HotelServiceQualityDefault hotelServiceQualityDefault){
        hotelServiceQuality.setCheckInRate(hotelServiceQualityDefault.getCheckinRatePercentile());
        hotelServiceQuality.setConfirmRate(hotelServiceQualityDefault.getConfirmRatePercentile());
        hotelServiceQuality.setArriveNoRoomRate(hotelServiceQualityDefault.getNoRoomRatePercentile());
        hotelServiceQuality.setOrderAvgProcessTime(hotelServiceQualityDefault.getOrderAvgProcessTimePercentile());
        hotelServiceQuality.setOrderRegretRate(hotelServiceQualityDefault.getRegretRatePercentile());
        hotelServiceQuality.setReservedGetRate(hotelServiceQualityDefault.getReservedGetRatePercentile());
    }

    public static void putEbSelfServiceRate(String seq, float rate){

        seq = Strings.getInternString(seq);

        hotelProfitDataClusterConcurrentHashMap.putIfAbsent(seq, new HotelProfitDataCluster());

        HotelProfitDataCluster cluster = hotelProfitDataClusterConcurrentHashMap.get(seq);
        HotelServiceQuality hotelServiceQuality = cluster.getHotelServiceQuality();
        if(hotelServiceQuality == null){
            hotelServiceQuality = new HotelServiceQuality();
            cluster.setHotelServiceQuality(hotelServiceQuality);
        }
        hotelServiceQuality.setEbSelfServiceRate(rate);
    }

}
