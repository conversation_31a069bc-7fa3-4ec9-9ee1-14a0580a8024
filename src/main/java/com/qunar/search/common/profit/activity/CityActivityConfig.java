package com.qunar.search.common.profit.activity;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CityActivityConfig {
    public static final String ADR = "60";
    public static final String IOS = "80";
    /**
     * 城市相关的配置
     */
    private Map<String, List<CityProfitActivity>> cityActivityMap = new HashMap<>();
    private static final Logger LOG = LoggerFactory.getLogger(CityActivityConfigParser.class);

    public boolean isCityActive(String cityUrl, String fromDate) {
        int startDate = Integer.valueOf(fromDate.replace("-", ""));
        List<CityProfitActivity> activities = cityActivityMap.get(cityUrl);
        if (null == activities) return false;
        for (CityProfitActivity activity : activities) {
            if (activity.getStartDate() <= startDate && activity.getEndDate() >= startDate) {
                return true;
            }
        }
        return false;
    }

    public void updateMap(Map<String, List<CityProfitActivity>> activityMap) {
        this.cityActivityMap = activityMap;
        int size = 0;
        for (List<CityProfitActivity> list : activityMap.values()) {
            size += list.size();
        }
        LOG.info("city activity size: {}", size);
    }

    public int getHotelsPerSection(String platform) {
        int hotelsPerSection = 3; //安卓默认3家一段
        if (platform.equals(IOS)) {
            hotelsPerSection = 5;
        }
        return hotelsPerSection;
    }

}
