package com.qunar.search.common.profit.entity;

/**
 * 这个类用来聚合存储酒店跟收益算分相关的数据
 * Created by fangxue.zhang on 2016/6/2.
 */
public class HotelProfitDataCluster {


    /**
     * 酒店收益数据
     */
    private HotelProfitData hotelProfitData;

    /**
     * 酒店服务质量
     */
    private HotelServiceQuality hotelServiceQuality;

    /**
     * 酒店质量
     */
    private HotelQuality hotelQuality;


    public HotelProfitData getHotelProfitData() {
        return hotelProfitData;
    }

    public void setHotelProfitData(HotelProfitData hotelProfitData) {
        this.hotelProfitData = hotelProfitData;
    }

    public HotelServiceQuality getHotelServiceQuality() {
        return hotelServiceQuality;
    }

    public void setHotelServiceQuality(HotelServiceQuality hotelServiceQuality) {
        this.hotelServiceQuality = hotelServiceQuality;
    }

    public HotelQuality getHotelQuality() {
        return hotelQuality;
    }

    public void setHotelQuality(HotelQuality hotelQuality) {
        this.hotelQuality = hotelQuality;
    }
}
