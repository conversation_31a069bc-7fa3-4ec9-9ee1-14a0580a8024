package com.qunar.search.common.profit.activity;

import com.qunar.hotel.qmonitor.QMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.TypedConfig;

/**
 * 收益城市配置.
 * Created by andy on 2016/6/1.
 */
public class CityActivityConfigHolder {
    private static final Logger LOG = LoggerFactory.getLogger(CityActivityConfigParser.class);
    /**
     * 初始化配置文件并加监听器
     */
    private static TypedConfig<CityActivityConfig> cityActivityConfigTypedConfig = TypedConfig.get("city_profit_activity.xml",
            new CityActivityConfigParser());

    public static CityActivityConfig getCityActivityConfig() {
        CityActivityConfig result = cityActivityConfigTypedConfig.current();
        if (null == result) {
            QMonitor.recordOne("CITYACTIVITY_CONFIG_ERROR");
            LOG.error("cityActivityConfigTypedConfig is null");
            result = new CityActivityConfig();
        }
        return result;
    }
}
