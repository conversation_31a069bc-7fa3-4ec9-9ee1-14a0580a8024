package com.qunar.search.common.profit.entity;

/**存放归一化的数据
 * Created by fangxue.zhang on 2016/6/15.
 */
public class HotelProfitDataClusterNormalized {


    /**
     * 历史总收益归一化
     */
    private float normalizedTotalCommission;

    /**
     * 归一化的预估收益
     */
    private float normalizedPredictCommission;


    /**
     * 实时收益的归一化
     */
    private float normalizedRealTimeCommission;


    /**
     * 内存中的持久数据.
     */
    private HotelProfitDataCluster cluster;

    public HotelProfitDataClusterNormalized(HotelProfitDataCluster cluster) {
        this.cluster = cluster;
    }

    public HotelProfitDataCluster getCluster() {
        return cluster;
    }

    public void setCluster(HotelProfitDataCluster cluster) {
        this.cluster = cluster;
    }

    public float getNormalizedTotalCommission() {
        return normalizedTotalCommission;
    }

    public void setNormalizedTotalCommission(float normalizedTotalCommission) {
        this.normalizedTotalCommission = normalizedTotalCommission;
    }

    public float getNormalizedPredictCommission() {
        return normalizedPredictCommission;
    }

    public void setNormalizedPredictCommission(float normalizedPredictCommission) {
        this.normalizedPredictCommission = normalizedPredictCommission;
    }

    public float getNormalizedRealTimeCommission() {
        return normalizedRealTimeCommission;
    }

    public void setNormalizedRealTimeCommission(float normalizedRealTimeCommission) {
        this.normalizedRealTimeCommission = normalizedRealTimeCommission;
    }


}
