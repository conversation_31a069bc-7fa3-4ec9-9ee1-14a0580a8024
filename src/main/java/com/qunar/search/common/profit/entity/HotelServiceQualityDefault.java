package com.qunar.search.common.profit.entity;

import com.qunar.search.common.util.Strings;

/**酒店服务质量在各个城市的默认值
 * Created by fangxue.zhang on 2016/6/21.
 */
public class HotelServiceQualityDefault {


    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 到点无房率的默认值
     */
    private float noRoomRatePercentile;

    /**
     * 反悔率
     */
    private float regretRatePercentile;


    /**
     * 确认率默认值
     *
     */
    private float confirmRatePercentile;


    /**
     * 预留房得房率
     */
    private float reservedGetRatePercentile;


    /**
     * 订单平均处理时间
     */
    private float orderAvgProcessTimePercentile;

    /**
     * 入住率
     */
    private float checkinRatePercentile;


    /**
     * 数据的日期.
     */
    private String dt;


    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public float getNoRoomRatePercentile() {
        return noRoomRatePercentile;
    }

    public void setNoRoomRatePercentile(float noRoomRatePercentile) {
        this.noRoomRatePercentile = noRoomRatePercentile;
    }

    public float getRegretRatePercentile() {
        return regretRatePercentile;
    }

    public void setRegretRatePercentile(float regretRatePercentile) {
        this.regretRatePercentile = regretRatePercentile;
    }

    public float getConfirmRatePercentile() {
        return confirmRatePercentile;
    }

    public void setConfirmRatePercentile(float confirmRatePercentile) {
        this.confirmRatePercentile = confirmRatePercentile;
    }

    public float getReservedGetRatePercentile() {
        return reservedGetRatePercentile;
    }

    public void setReservedGetRatePercentile(float reservedGetRatePercentile) {
        this.reservedGetRatePercentile = reservedGetRatePercentile;
    }

    public float getOrderAvgProcessTimePercentile() {
        return orderAvgProcessTimePercentile;
    }

    public void setOrderAvgProcessTimePercentile(float orderAvgProcessTimePercentile) {
        this.orderAvgProcessTimePercentile = orderAvgProcessTimePercentile;
    }

    public float getCheckinRatePercentile() {
        return checkinRatePercentile;
    }

    public void setCheckinRatePercentile(float checkinRatePercentile) {
        this.checkinRatePercentile = checkinRatePercentile;
    }

    public void internStringField() {
        this.cityCode = Strings.getInternString(this.cityCode);
        this.dt = Strings.getInternString(this.dt);
    }
}
