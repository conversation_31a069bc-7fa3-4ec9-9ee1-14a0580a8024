package com.qunar.search.common.conf;

import com.qunar.search.common.util.GlobalProperties;
import com.qunar.search.common.util.PropertiesUtil;

import java.util.Properties;

/**
 * http url的相关配置.
 */
public class HttpUrlConf {
    private static Properties p = PropertiesUtil.asPropIgnoreException("http_url.properties");

    /**
     * 根据业务返回对应接口地址
     *
     * @param key
     * @return
     */
    public static String getUrlByKey(String key) {
        if (GlobalProperties.has(key)) {
            return GlobalProperties.get(key);
        }

        if (p == null) {
            throw new RuntimeException("HttpUrlConf获取url配置失败");
        }
        return p.getProperty(key);
    }
}
