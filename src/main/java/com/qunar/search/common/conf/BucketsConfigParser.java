package com.qunar.search.common.conf;

import com.google.common.base.Predicate;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.BucketEntry;
import com.qunar.search.common.enums.StrategyType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.TypedConfig;

import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 通用各个频道桶流量配置文件解析器
 *
 * <AUTHOR>
 *
 */
    public class BucketsConfigParser implements TypedConfig.Parser<HotelBucketsConfig> {
    private final static Logger log = LoggerFactory.getLogger(BucketsConfigParser.class);
    /**
     * 配置检查器
     */
    private Predicate<String> configChecker;

    public BucketsConfigParser() {}

    public BucketsConfigParser(Predicate<String> configChecker) {
        this.configChecker = configChecker;
    }

    @SuppressWarnings("unchecked")
    @Override
    public HotelBucketsConfig parse(String data) throws IOException {
        Document document = null;
        HotelBucketsConfig hotelBucketsConfig = new HotelBucketsConfig();
        try {
            document = DocumentHelper.parseText(data);
            if (document == null) {
                return null;
            }
            Element node = document.getRootElement();
            List<Element> bucketsList = node.elements("Buckets");
            List<Element> envList = node.elements("EnvDistribute");
            hotelBucketsConfig.setBuckets(initBuckets(bucketsList, envList));
            hotelBucketsConfig.setEnvModelMap(initEnvDistribute(envList));
            Set<BucketEntry> realBuckets = initRealBuckets(bucketsList);
            hotelBucketsConfig.setBucketMap(initBucketMappings(envList, realBuckets));
            List<Element> uidWhiteList = node.elements("WhiteUid");
            hotelBucketsConfig.setUidWhiteListMap(initUidWhiteList(uidWhiteList));
            List<Element> blackUidList = node.elements("BlackUid");
            initUidBlackList(blackUidList, hotelBucketsConfig);
            Map<String, Set<String>> disableStrategyMap = initEnvBucketStrategy(envList);
            hotelBucketsConfig.setDisableStrategyMap(disableStrategyMap);
            hotelBucketsConfig.setHashArray(parseHash(node.elementText("RandomSeed")));
        } catch (Exception e) {
            log.error("parse bucketsConfig.xml error ", e);
            QMonitor.recordOne("BUCKET_PARSE_ERROR");
            return null;
        }
        return hotelBucketsConfig;
    }

    /**
     * 生成用于分桶的数组，这里的代码不要乱动，否则每台机器上生成的数组不一样，切分流量就不准了。
     * @param data
     * @return
     */
    private int[] parseHash(String data){
        log.info("seed:{}", data);
        int hash = data.hashCode();
        Random random = new Random(hash);
        List<Integer> array = new ArrayList<>(100);
        for (int i = 1; i <= 100; i++) { //[1,100]
            array.add(i);
        }

        Collections.shuffle(array, random);
        int[] res = new int[100];
        for(int i = 0; i < 100;i++){
            res[i] = array.get(i);
        }
        log.info(Arrays.toString(res));
        return res;
    }

    /**
     * 初始化白名单
     *
     * @param uidWhiteList
     */
    @SuppressWarnings("unchecked")
    private Map<String, Set<String>> initUidWhiteList(List<Element> uidWhiteList) {
        Map<String, Set<String>> uidMap = new HashMap<String, Set<String>>();
        if (null == uidWhiteList || uidWhiteList.size() == 0) {
            return uidMap;
        }
        Element whiteUid = (Element) uidWhiteList.get(0);
        if (null == whiteUid) {
            return uidMap;
        }
        List<Element> childList = whiteUid.elements("uid");
        if (childList == null || childList.size() <= 0) {
            return uidMap;
        }
        for (int i = 0; i < childList.size(); i++) {
            Element uidElement = childList.get(i);
            if (uidElement == null) {
                continue;
            }
            String uid = uidElement.attributeValue("value");
            String belong = uidElement.attributeValue("belong");
            if (StringUtils.isBlank(belong)) {
                continue;
            }
            belong = belong.toUpperCase();
            if (uidMap.containsKey(belong)) {
                Set<String> list = uidMap.get(belong);
                list.add(uid);
            } else {
                Set<String> list = new HashSet<String>();
                list.add(uid);
                uidMap.put(belong, list);
            }
        }
        return uidMap;
    }

    /**
     * 初始化黑名单
     *
     * @param uidBlackList
     */
    @SuppressWarnings("unchecked")
    private void initUidBlackList(List<Element> uidBlackList, HotelBucketsConfig hotelBucketsConfig) {
        if (null == uidBlackList || uidBlackList.size() == 0) {
            return;
        }
        Element blackUid = uidBlackList.get(0);
        if (null == blackUid) {
            return;
        }
        Set<String> uidSet = new HashSet<String>();
        List<Element> childList = blackUid.elements("uid");
        if (childList != null && childList.size() > 0) {
            for (int i = 0; i < childList.size(); i++) {
                Element uidElement = childList.get(i);
                String uid = uidElement.attributeValue("value");
                if (StringUtils.isNotEmpty(uid)) {
                    uidSet.add(uid);
                }
            }
            hotelBucketsConfig.setUidBlackSet(uidSet);
        }
        Set<Pattern> regexUidSet = new HashSet<Pattern>();
        List<Element> regexList = blackUid.elements("regexUid");
        if (regexList != null && regexList.size() > 0) {
            for (int i = 0; i < regexList.size(); i++) {
                Element uidElement = regexList.get(i);
                String regex = uidElement.attributeValue("value");
                if (StringUtils.isNotEmpty(regex)) {
                    regexUidSet.add(Pattern.compile(regex));
                }
            }
            hotelBucketsConfig.setUidBlackRegexSet(regexUidSet);
        }
        return ;
    }

    /**
     * 初始化多个环境桶流量数据
     *
     * @param envList
     */
    @SuppressWarnings("unchecked")
    private Map<String, List<ClientEnvModel>> initEnvDistribute(List<Element> envList) {
        Map<String, List<ClientEnvModel>> envMap = new HashMap<String, List<ClientEnvModel>>();
        if (null == envList || envList.size() <= 0) {
            return envMap;
        }

        for (int i = 0; i < envList.size(); i++) {
            Element envElement = envList.get(i);
            if (envElement == null) {
                continue;
            }
            String envType = envElement.attributeValue("logicBucket");
            List<ClientEnvModel> models = new ArrayList<ClientEnvModel>();
            List<Element> childList = envElement.elements("client");
            if (childList == null || childList.size() <= 0) {
                continue;
            }
            for (int j = 0; j < childList.size(); j++) {
                Element clienteElement = childList.get(j);
                if (clienteElement == null) {
                    continue;
                }
                ClientEnvModel model = new ClientEnvModel();
                model.setPlat(clienteElement.attributeValue("plat"));
                model.setStartPercent(NumberUtils.toInt(clienteElement.attributeValue("startPercent"), -1));
                model.setEndPercent(NumberUtils.toInt(clienteElement.attributeValue("endPercent"), 100));
                models.add(model);
            }
            if (StringUtils.isNotBlank(envType)) {
                envType = envType.toUpperCase();
                envMap.put(envType, models);
            }
        }

        return envMap;
    }


    /**
     * 初始化多个环境桶配置策略数据
     *
     * @param envList
     */
    private Map<String, Set<String>> initEnvBucketStrategy(List<Element> envList) {
        Map<String, Set<String>> envMap = Maps.newConcurrentMap();
        if (null == envList || envList.size() <= 0) {
            return envMap;
        }

        for (int i = 0; i < envList.size(); i++) {
            Element envElement = envList.get(i);
            if (envElement == null) {
                continue;
            }
            String envType = envElement.attributeValue("logicBucket");
            Set<String> strategySet = Sets.newHashSet();
            List<Element> childList = envElement.elements("strategy");
            if (childList == null || childList.size() <= 0) {
                continue;
            }
            for (int j = 0; j < childList.size(); j++) {
                Element clienteElement = childList.get(j);
                if (clienteElement == null) {
                    continue;
                }
                String name = clienteElement.attributeValue("name");
                String status = clienteElement.attributeValue("status");
                if (!StrategyType.isValidStrategy(name)){
                    if (Objects.isNull(configChecker) || !configChecker.apply(name)) {
						log.warn("invalid strategy:" + name);
                    }
                }
                if (!"true".equals(status)){
                    strategySet.add(name);
                }
            }

            if (StringUtils.isNotBlank(envType)) {
                envType = envType.toUpperCase();
                if (!CollectionUtils.isEmpty(strategySet)){
                    envMap.put(envType, strategySet);
                }
            }


        }

        return envMap;
    }


    /**
     * 初始化桶列表
     *
     * @param envList
     */
    @SuppressWarnings("unchecked")
	private Set<BucketEntry> initBuckets(List<Element> bucketsList, List<Element> envList) {
		Set<BucketEntry> buckets = new HashSet<BucketEntry>();
		Set<BucketEntry> realBukets = initRealBuckets(bucketsList);
        Set<BucketEntry> logicBuckets = initLogicBuckets(envList, realBukets);
        if(realBukets.isEmpty() && (null == logicBuckets || logicBuckets.isEmpty())) {
            return buckets;
        }
        if(realBukets.isEmpty()) {
            return logicBuckets;
        }
        if(null == logicBuckets || logicBuckets.isEmpty()) {
            return realBukets;
        }
        buckets.addAll(realBukets);
        for(BucketEntry bucketEntry : logicBuckets) {
            if(buckets.contains(bucketEntry)) {
                continue;
            }
            buckets.add(bucketEntry);
        }
		return buckets;
	}

    /**
     * 初始化物理桶列表
     *
     * @param bucketsList
     */
    @SuppressWarnings("unchecked")
    private Set<BucketEntry> initRealBuckets(List<Element> bucketsList) {
        Set<BucketEntry> buckets = new HashSet<BucketEntry>();
        if (null == bucketsList || bucketsList.size() <= 0) {
            return buckets;
        }
        for (int i = 0; i < bucketsList.size(); i++) {
            Element envElement = bucketsList.get(i);
            if (envElement == null) {
                continue;
            }
            List<Element> childList = envElement.elements("bucket");
            if (childList == null || childList.size() <= 0) {
                continue;
            }
            for (int j = 0; j < childList.size(); j++) {
                Element clienteElement = childList.get(j);
                if (clienteElement == null) {
                    continue;
                }
                String bucket = clienteElement.attributeValue("isLr");
                if (StringUtils.isNotEmpty(bucket)) {
                    bucket = bucket.toUpperCase();
                }
                BucketEntry entry = new BucketEntry();
                entry.setName(bucket);
                entry.setPromotion(clienteElement.attributeValue("promotion") == null || "true".equalsIgnoreCase(clienteElement.attributeValue("promotion")));
                /**
                 * 物理桶对应的桶模型实例，只改了主搜配置的逻辑
                 */
                String modelInstance = clienteElement.attributeValue("modelInstance");
                if (StringUtils.isNotEmpty(modelInstance)){
                    entry.setModelInstance(modelInstance);
                }

                /**
                 * 算法精排策略
                 */

                String algoInstance = clienteElement.attributeValue("algoInstance");
                if (StringUtils.isNotEmpty(algoInstance)){
                    entry.setAlgoInstance(algoInstance);
                }


                /**
                 * 指定模型名，优先级高于lrBucket.thisBucketName()
                 */
                String modelName = clienteElement.attributeValue("modelName");
                if (StringUtils.isNotEmpty(modelName)){
                    entry.setModelName(modelName);
                }

                buckets.add(entry);
            }
        }
        return buckets;
    }

    /**
     * 初始化物理桶列表
     *
     * @param envList
     */
    @SuppressWarnings("unchecked")
    private Set<BucketEntry> initLogicBuckets(List<Element> envList, Set<BucketEntry> realBuckets) {
        Set<BucketEntry> buckets = new HashSet<BucketEntry>();
        if (null == envList || envList.size() <= 0) {
            return buckets;
        }
        for (int i = 0; i < envList.size(); i++) {
            Element envElement = envList.get(i);
            if (envElement == null) {
                continue;
            }
            String logicBucket = envElement.attributeValue("logicBucket");
            String realBucket = envElement.attributeValue("realBucket");
            if(Strings.isNullOrEmpty(logicBucket)) {
                continue;
            }
            if(Strings.isNullOrEmpty(realBucket)) {
                if(contain(realBuckets, logicBucket)) {
                    realBucket = logicBucket;
                } else {
                    realBucket = "B";
                }
            }
            if(!contain(realBuckets, logicBucket) && !contain(realBuckets, realBucket)) {
                realBucket = "B";
            }
            logicBucket = logicBucket.toUpperCase();
            realBucket = realBucket.toUpperCase();

            BucketEntry realEntry = find(realBuckets, realBucket);
            BucketEntry entry = new BucketEntry();
            entry.setName(logicBucket);
            if(null == realEntry) {
                entry.setPromotion(true);
            } else {
                entry.setPromotion(realEntry.isPromotion());
            }
            buckets.add(entry);
        }
        return buckets;
    }

    /**
     * 初始化物理桶与逻辑桶的映射关系
     * @param envList
     * @return
     */
    private Map<String, String> initBucketMappings(List<Element> envList, Set<BucketEntry> realBuckets) {
        Map<String, String> bucketMappings = Maps.newHashMap();
        if (null == envList || envList.isEmpty()) {
            return bucketMappings;
        }
        for (int i = 0; i < envList.size(); i++) {
            Element envElement = envList.get(i);
            if (envElement == null) {
                continue;
            }
            String logicBucket = envElement.attributeValue("logicBucket");
            String realBucket = envElement.attributeValue("realBucket");
            if(Strings.isNullOrEmpty(logicBucket)) {
                continue;
            }
            if(Strings.isNullOrEmpty(realBucket)) {
                if(contain(realBuckets, logicBucket)) {
                    realBucket = logicBucket;
                } else {
                    realBucket = "B";
                }
            }
            if(!contain(realBuckets, logicBucket) && !contain(realBuckets, realBucket)) {
                realBucket = "B";
            }
            logicBucket = logicBucket.toUpperCase();
            realBucket = realBucket.toUpperCase();
            bucketMappings.put(logicBucket, realBucket);
        }
        return bucketMappings;
    }

    /**
     * 通过桶名查找桶
     * @param realBuckets
     * @param realBucket
     * @return
     */
    private BucketEntry find(Set<BucketEntry> realBuckets, String realBucket) {
        if((null == realBuckets || realBuckets.isEmpty()) || Strings.isNullOrEmpty(realBucket)) {
            return null;
        }
        for(BucketEntry bucketEntry : realBuckets) {
            if(StringUtils.equalsIgnoreCase(realBucket, bucketEntry.getName())) {
                return bucketEntry;
            }
        }
        return null;
    }

    private boolean contain(Set<BucketEntry> realBuckets, String realBucket) {
        if((null == realBuckets || realBuckets.isEmpty()) || Strings.isNullOrEmpty(realBucket)) {
            return false;
        }
        for(BucketEntry bucketEntry : realBuckets) {
            if(StringUtils.equalsIgnoreCase(realBucket, bucketEntry.getName())) {
                return true;
            }
        }
        return false;
    }

}
