package com.qunar.search.common.conf;

import com.qunar.search.common.conf.annotation.QTomlConfig;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.tomlj.Toml;
import org.tomlj.TomlArray;
import org.tomlj.TomlParseResult;
import org.tomlj.TomlTable;
import qunar.tc.qconfig.client.Configuration;
import qunar.tc.qconfig.client.impl.Translators;

import java.lang.reflect.*;
import java.util.*;

@Slf4j
public class TomlConfigInject implements Configuration.ConfigListener<TomlParseResult> {
	private final Object bean;
	private final QTomlConfig tomlConfig;
	private final AccessibleObject accessibleObject;
	private final InjectType injectType;

	public TomlConfigInject(Object bean, QTomlConfig tomlConfig, AccessibleObject accessibleObject,
			InjectType injectType) {
		this.bean = bean;
		this.tomlConfig = tomlConfig;
		this.accessibleObject = accessibleObject;
		this.accessibleObject.setAccessible(true);
		this.injectType = injectType;
	}

	@Override
	public void onLoad(TomlParseResult conf) {
		try {
			injectType.inject(accessibleObject, bean,
					getTomlValue(tomlConfig.value(), tomlConfig.key(), injectType.getType(accessibleObject), tomlConfig.defaultValue(), conf));
		} catch (Exception e) {
			log.error("注入{}的{}到{}失败", tomlConfig.value(), tomlConfig.key(), accessibleObject, e);
		}
	}

	enum InjectType {
		FIELD {
			@Override
			void inject(AccessibleObject accessibleObject, Object obj, Object value) throws IllegalAccessException {
				((Field) accessibleObject).set(obj, value);
			}

			@Override
			Type getType(AccessibleObject accessibleObject) {
				return ((Field) accessibleObject).getGenericType();
			}
		}, METHOD {
			@Override
			void inject(AccessibleObject accessibleObject, Object obj, Object value)
					throws InvocationTargetException, IllegalAccessException {
				((Method) accessibleObject).invoke(obj, value);
			}

			@Override
			Type getType(AccessibleObject accessibleObject) {
				Type[] parameterTypes = ((Method) accessibleObject).getGenericParameterTypes();
				if (parameterTypes.length != 1) {
					throw new RuntimeException("接收变更通知的方法只能有一个参数, method: " + accessibleObject);
				}

				return parameterTypes[0];
			}
		};

		abstract void inject(AccessibleObject accessibleObject, Object obj, Object value)
				throws IllegalAccessException, InvocationTargetException;

		abstract Type getType(AccessibleObject accessibleObject);
	}

	private Object getTomlValue(String file, String key, Type type, String defaultValue, TomlParseResult result) {
		Object value = null;
		try {
			if (StringUtils.isNotBlank(key) && !result.contains(key) && StringUtils.isNotBlank(defaultValue)) {
				return Translators.getInternalTranslator(type).translate(defaultValue);
			}

			if (type.equals(TomlParseResult.class)) {
				return result;
			} else if (type.equals(TomlArray.class)) {
				value = result.getArray(key);
			} else if (type.equals(TomlTable.class)) {
				value = result.getTable(key);
			} else if (type.equals(Integer.class) || type.equals(int.class)) {
				Long obj = result.getLong(key);
				value = (obj != null ? obj.intValue() : null);
			} else if (type instanceof ParameterizedType) {
				ParameterizedType parameterizedType = (ParameterizedType) type;
				Type rawType = parameterizedType.getRawType();
				if (rawType.equals(List.class)) {
					value = getCollection(result, key, List.class, (Class<?>) parameterizedType.getActualTypeArguments()[0]);
				} else if (rawType.equals(Map.class)) {
					value = getMap(result, key, (Class<?>) parameterizedType.getActualTypeArguments()[0], (Class<?>) parameterizedType.getActualTypeArguments()[1]);
				} else if (rawType.equals(Set.class)) {
					value = getCollection(result, key, Set.class, (Class<?>) parameterizedType.getActualTypeArguments()[0]);
				}
			} else {
				value = result.get(key);
			}
		} catch (Exception e) {
			log.error("解析Toml失败: file {}, key {}", file, key, e);
		}
		return value;
	}

	/**
	 * 反序列化List类型
	 * @param result
	 * @param key
	 * @param clazz List的范型
	 * @return
	 */
	private Object getCollection(TomlParseResult result, String key, Class<? extends Collection> collectionClass, Class<?> clazz) {
		String json;
		if (StringUtils.isBlank(key)) {
			json = result.toJson();
		} else {
			json = Optional.ofNullable(result.getArray(key)).map(TomlArray::toJson).orElse("");
		}

		return JsonUtils.fromJson(json, JsonUtils.buildCollectionType(collectionClass, clazz));
	}

	private Object getMap(TomlParseResult result, String key, Class<?> keyClass, Class<?> valueClass) {
		String json;
		if (StringUtils.isBlank(key)) {
			json = result.toJson();
		} else {
			json = Optional.ofNullable(result.getTable(key)).map(TomlTable::toJson).orElse("");
		}

		return JsonUtils.fromJson(json, JsonUtils.buildMapType(Map.class, keyClass, valueClass));
	}

	@Override
	public String toString() {
		return "TomlConfigInject{tomlConfig=" + tomlConfig + ", accessibleObject=" + accessibleObject + ", injectType="
				+ injectType + '}';
	}
}
