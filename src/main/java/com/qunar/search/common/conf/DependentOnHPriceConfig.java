package com.qunar.search.common.conf;

import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

/**
 * rank-search系统中依赖无线报价的各个业务开关配置
 *
 *
 * @author: luming.lv
 * @date: 2016-06-16
 */
public class DependentOnHPriceConfig {

    private static final Logger logger = LoggerFactory.getLogger(DependentOnHPriceConfig.class);

    // 开关关闭
    public static final String CLOSE = "0";

    private Map<String, String> dependentSwitch = Maps.newHashMap();

    @QConfig("hprice-dependency-switch.properties")
    public void onChangeDenpendent(Map<String, String> conf) {
        if (conf == null) {
            logger.error("dependentOnHprice switch reload conf info is null");
            return;
        }
        dependentSwitch = conf;
    }

    /**
     * 判断某项配置开关是否为打开
     * 默认开关打开, 当配置不为空且明确指定关闭某个配置项时开关关闭
     *
     * @param confKey
     * @return
     */
    public boolean isSwitchOn(String confKey) {
        boolean isOn = true;
        if (dependentSwitch != null
                && dependentSwitch.containsKey(confKey)
                && CLOSE.equals(dependentSwitch.get(confKey)))
            isOn = false;
        return isOn;
    }

}
