package com.qunar.search.common.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * 读取hotel开头的相关配置数据, 读取本地配置文件.
 */
public class Config {

    private static Logger logger = LoggerFactory.getLogger(Config.class.getName());

    private static ResourceBundle rb = null;

    static {
        try {
            rb = ResourceBundle.getBundle("hotel");
        } catch (Exception mre) {
            mre.printStackTrace();
        }
    }

    public static String getItem(String item, String defaultValue) {
        String value;
        if (rb != null) {
            try {
                value = rb.getString(item.trim());
            } catch (MissingResourceException e) {
                value = defaultValue;
            }
        } else {
            value = defaultValue;
        }
        return value;
    }

    public static int getIntItem(String item, String defaultValue) {
        int i = 0;
        String value = getItem(item, defaultValue);
        try {
            i = Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn(e.getMessage());
        }
        return i;
    }
}
