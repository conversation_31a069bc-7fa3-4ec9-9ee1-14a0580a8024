package com.qunar.search.common.conf;


import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.qunar.search.common.base.MockDataInter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;

/**
 * 市辖区酒店和特殊citytag酒店白名单
 * Created by den<PERSON><PERSON><PERSON> on 16-8-30.
 */
public class HotelWhiteListConfig implements MockDataInter {

    private static final Logger LOGGER = LoggerFactory.getLogger(HotelWhiteListConfig.class);

    /**
     * qconfig数据格式：beijing_city=beijing_city_1,beijing_city_2,beijing_city_3
     */
    private Map<String, List<String>> whiteListMap;

    @QConfig("district_hotel_white_list.properties")
    public void onChange(Map<String, String> conf) {
        Map<String, List<String>> newMap;
        try {
            newMap = this.parse(conf);
        } catch (Exception e) {
            LOGGER.error("parse district hotel white list error!", e);
            newMap = Maps.newHashMap();
        }
        this.whiteListMap = newMap;
    }

    /**
     * 取某城市下的白名单酒店
     * @param cityCode
     * @return
     */
    public List<String> getWhiteList(String cityCode) {
        if (Strings.isNullOrEmpty(cityCode)) {
            return Collections.EMPTY_LIST;
        }
        if (null == this.whiteListMap) {
            return Collections.EMPTY_LIST;
        }
        return this.whiteListMap.get(cityCode);
    }

    /**
     * 解析城市下的白名单酒店
     * @param conf
     * @return
     */
    private Map<String, List<String>> parse(Map<String, String> conf) {
        Map<String, List<String>> newMap = Maps.newHashMap();
        if (null == conf || conf.isEmpty()) {
            return newMap;
        }
        for (Map.Entry<String, String> whiteData : conf.entrySet()) {
            String data = whiteData.getValue();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            List<String> whiteList = COMMA_SPLITTER.splitToList(data);
            newMap.put(whiteData.getKey(), whiteList);
        }
        return newMap;
    }

    @Override
    public void loadFileData(String fileName, Map<String, String> config) {
        if ("district_hotel_white_list.properties".equals(fileName)) {
            onChange(config);
        }
    }
}
