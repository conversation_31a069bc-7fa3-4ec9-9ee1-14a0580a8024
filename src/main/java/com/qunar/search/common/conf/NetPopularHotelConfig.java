package com.qunar.search.common.conf;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/7
 */
public class NetPopularHotelConfig {
	private static final Logger log = LoggerFactory.getLogger(NetPopularHotelConfig.class);

	private static Map<String, String> popMap = Maps.newConcurrentMap();

	@QConfig("net_pop_hotel.properties")
	public void onInfoConfig(Map<String, String> conf) {
		Map<String, String> newMap;
		try {
			newMap = parseConfig(conf);
		} catch (Exception e) {
			log.error("parse HotelListEfficiencyPoiConfig error!", e);
			return;
		}
		setPopMap(newMap);
	}

	public static String getTag(String hotelSeq) {
		if (popMap.containsKey(hotelSeq)) {
			return popMap.get(hotelSeq);
		}
		return "";
	}

	private static void setPopMap(Map<String, String> popMap) {
		NetPopularHotelConfig.popMap = popMap;
	}

	/**
	 * @param conf
	 * @return
	 */
	private Map<String, String> parseConfig(Map<String, String> conf) {
		Map<String, String> map = Maps.newConcurrentMap();
		if (MapUtils.isEmpty(conf)) {
			return map;
		}
		for (Map.Entry<String, String> entry : conf.entrySet()) {
			if (!Strings.isNullOrEmpty(entry.getKey())) {
				map.put(entry.getKey(), entry.getValue());
			}
		}
		return map;
	}
}
