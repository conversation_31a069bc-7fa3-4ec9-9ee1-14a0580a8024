package com.qunar.search.common.conf;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * todo cityClient的配置信息, 不需要存在这个类, 因为参数仅仅是一个map.
 */
public class CityClientConfig {
	
	private Map<String, String> propsMap = new LinkedHashMap<String, String>();
	
	public void setPropsMap (Map<String, String> map) {
		propsMap.putAll(map);
	}
	
	public Map<String, String> getProps () {
		return propsMap;
	}
}
