package com.qunar.search.common.conf;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;

import com.qunar.search.common.bean.AlgoPunishParam;
import com.qunar.search.common.bean.AlgoStrategyConfigInfo;
import com.qunar.search.common.bean.AlgoXgbModelParam;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import qunar.tc.qconfig.client.TypedConfig;

import static java.util.Arrays.asList;

/**
 * @Author: ruheng.liu
 * @Date: 19-5-6 下午2:39
 * @desc:
 */
public class AlgoLmBucketConfigParser implements TypedConfig.Parser<AlgoStrategyConfigInfo> {

    private final static Logger log = LoggerFactory.getLogger(AlgoLmBucketConfigParser.class);

    private static final String REG = "|";
    private static final String COMMA = ",";
    private static final String EQUAL = "=";
    private static final String WRAP = "\n";

    @Override
    public AlgoStrategyConfigInfo parse(String data) throws IOException {
        Map<String, String> conf = getDataToMap(data);

        AlgoStrategyConfigInfo configInfo = new AlgoStrategyConfigInfo();

        if (MapUtils.isEmpty(conf)) {
            log.error("conf doesn't exist or conf is empty.");
        }

        AlgoPunishParam punishStrategy = AlgoPunishParam.getPunishStrategy(conf);
        configInfo.setAlgoPunishParam(punishStrategy);

        Map<String, AlgoXgbModelParam> algoXgbModelParamMap = AlgoXgbModelParam.getAlgoXgbModelParam(conf);
        configInfo.setAlgoXgbModelParamMap(algoXgbModelParamMap);

        try {
            configInfo.setAlgoPriceWeightSwitch(Boolean.valueOf(conf.get("algo_price_weight_switch")));
            configInfo.setHolidayWeightFromToDate(getWeightDate(conf.get("price_weight_from_to_date")));
        } catch (Exception e) {
            configInfo.setAlgoPriceWeightSwitch(false);
            log.error("配置文件节假日加权参数解析出错。", e);
        }

        return configInfo;
    }

    /**
     * 将配置文件解析成map
     * @param data
     * @return
     */
    private Map<String, String> getDataToMap(String data) {
        Map<String, String> map = Maps.newHashMap();
        try {
            List<String> lines = asList(StringUtils.split(data, WRAP));
            for (int i = 0; i < lines.size(); i++) {
                String[] split = lines.get(i).split(EQUAL);
                if (split.length == 2) {
                    map.put(split[0], split[1]);
                }
            }
        } catch (Exception e) {
            return null;
        }

        return map;
    }

    /**
     * 解析并校验配置文件日期是否正确
     */
    public List<Range> getWeightDate(String priceWeightFromToDateStr) {

        ArrayList<Range> fromToDateList = Lists.newArrayList();
        if (StringUtils.isEmpty(priceWeightFromToDateStr)) {
            return fromToDateList;
        }

        String[] split = StringUtils.split(priceWeightFromToDateStr, REG);

        org.joda.time.format.DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        for (String formToDateStr : split) {
            String[] formToDate = StringUtils.split(formToDateStr, COMMA);
            if (formToDate.length != 2) {
                continue;
            }

            DateTime fromDate = DateTime.parse(formToDate[0], formatter);
            DateTime toDate = DateTime.parse(formToDate[1], formatter);
            fromToDateList.add(Range.closed(fromDate, toDate));
        }
        return fromToDateList;
    }
}
