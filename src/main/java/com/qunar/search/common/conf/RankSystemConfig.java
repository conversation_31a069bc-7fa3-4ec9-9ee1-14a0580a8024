package com.qunar.search.common.conf;

import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import com.qunar.search.common.base.MockDataInter;
import com.qunar.search.common.conf.annotation.QTomlConfig;
import com.qunar.search.common.enums.MobileRankSearchBusinessSwitchConfig;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.*;

/**
 * search的相关配置.
 */
public class RankSystemConfig implements MockDataInter {

	private static final Logger logger = LoggerFactory.getLogger(RankSystemConfig.class);

	private Map<String, String> config;
	
    private Map<String, String> commonConfig;

    @QConfig("lm_time.properties")
    private Map<String, String> lmTimeConfig;

	private Set<String> confCities = Sets.newHashSet();
    
    public Map<String, String> getCommonConfig() {
        return commonConfig;
    }

	public List<Range<Integer>> getScSplitDistanceConfig() {
		return scSplitDistanceConfig;
	}

	private List<Range<Integer>> scSplitDistanceConfig;

	private int[] serviceSplitConfig;

	@QMapConfig(value = "mobile_rank_system_config.properties",key = "updateChangeRate")
	private static double updateChangeRate = 0.5;

	private static volatile Map<String, Integer> topBoard = Collections.emptyMap();

	@QMapConfig(value = "search_common.properties", key = "enableComputeRelativityOnlyOneEntity", defaultValue = "true")
	@Getter
	private volatile static boolean enableComputeRelativityOnlyOneEntity;

	/**
	 * 预计的dnn特征个数
	 */
	@QMapConfig(value = "search_common.properties", key = "dnn.feature.size", defaultValue = "120")
	@Getter
	private volatile static int dnnFeatureSize = 120;

	/**
	 * 预计的xgb特征个数
	 */
	@QMapConfig(value = "search_common.properties", key = "xgb.feature.size", defaultValue = "200")
	@Getter
	private volatile static int xgbFeatureSize = 200;

	/**
	 * 预计的粗排特征个数
	 */
	@QMapConfig(value = "search_common.properties", key = "lr.feature.size", defaultValue = "100")
	@Getter
	private volatile static int lrFeatureSize = 100;

	@QMapConfig(value = "search_common.properties", key = "enableL1LrFeatureConvertOptimize", defaultValue = "false")
	@Getter
	private volatile static boolean enableL1LrFeatureConvertOptimize;

	@QMapConfig(value = "search_common.properties", key = "enable292FeatureError", defaultValue = "true")
	@Getter
	private volatile static boolean enable292FeatureError;


	/**
     * 获取酒店真实曝光数时，最多查询的历史天数(不包含当天)。数据组那边是存了历史7天数据(不包含当天)
     */
    @QMapConfig(value = "search_common.properties", key = "hotelExposureDaysThreshold", defaultValue = "7")
    @Getter
    private volatile static int hotelExposureDaysThreshold;

    /**
     * 计算酒店兜底曝光值时用到的系数
     */
    @QMapConfig(value = "search_common.properties", key = "defaultExposureCoefficient", defaultValue = "0.8")
    @Getter
    private volatile static double defaultExposureCoefficient;

    /**
     * 是否用真实曝光来判断广告酒店是否达到曝光上限。
     */
    @QMapConfig(value = "search_common.properties", key = "adUseRealExposure", defaultValue = "false")
    @Getter
    private volatile static boolean adUseRealExposure;

    /**
     * 是否用真实曝光来判断重点酒店是否达到曝光上限。
     */
    @QMapConfig(value = "search_common.properties", key = "keyHotelUseRealExposure", defaultValue = "false")
    @Getter
    private volatile static boolean keyHotelUseRealExposure;


	/**
	 * 流式updater加载数据时的默认休眠时间，用于初始化。单位是ms
	 */
	@QMapConfig(value = "search_common.properties", key = "streamUpdaterDefaultInitSleepTime", defaultValue = "100")
	@Getter
	private volatile static int streamUpdaterDefaultInitSleepTime = 100;

	/**
	 * 流式updater加载数据时的默认批大小
	 */
	@QMapConfig(value = "search_common.properties", key = "streamUpdaterDefaultBatchSize", defaultValue = "1000")
	@Getter
	private volatile static int streamUpdaterDefaultBatchSize = 1000;

	/**
	 * 流式updater的休眠时间，用于初始化。单位是ms
	 */
	@QMapConfig(value = "search_common.properties", key = "streamUpdaterInitSleepTimeMap", defaultValue = "HotelLibSvmFeatureUpdater:1000")
	@Getter
	private static Map<String, Integer> streamUpdaterInitSleepTimeMap;

	/**
	 * 流式updater的批大小，用于控制一次返回的数据量
	 */
	@QMapConfig(value = "search_common.properties", key = "streamUpdaterBatchSizeMap", defaultValue = "HotelLibSvmFeatureUpdater:2000")
	@Getter
	private static Map<String, Integer> streamUpdaterBatchSizeMap;

	@QTomlConfig(value = "rank_updater.toml", key = "defaultPageSize", defaultValue = "500")
	@Getter
	private static int defaultPageSize;

	@QTomlConfig(value = "rank_updater.toml", key = "pageSizeMap")
	@Getter
	private static Map<String, Integer> pageSizeMap;

	public static int getUpdaterPageSize(String updaterName) {
		if (pageSizeMap == null) {
			return defaultPageSize;
		}
		return pageSizeMap.getOrDefault(updaterName, defaultPageSize);
	}

	@QConfig("servicescore.properties")
	public void initServiceSpiltRange(Map<String, String> config) {
		String split = config.get("service");
		if (!StringUtils.isEmpty(split)) {
			String sp[] = org.apache.commons.lang3.StringUtils.split(split,",");
			int tp[] = new int[sp.length];

			int start = 0;
			for (String each : sp) {
				tp[start++] = Integer.parseInt(each);
			}
			serviceSplitConfig = tp;
		}

	}


	@QConfig("sc_distance_split.properties")
	public void initScDistanceSpiltRange(Map<String,String> config){
		Map<Integer,Integer> tmp = new TreeMap<>();
		for(Map.Entry<String,String> entry : config.entrySet()) {
			tmp.put(Integer.parseInt(entry.getKey()), Integer.parseInt(entry.getValue()));
		}

		List<Range<Integer>> ranges = new ArrayList<>();
		for(Map.Entry<Integer,Integer> itmp : tmp.entrySet()){
			ranges.add(Range.openClosed(itmp.getKey(),itmp.getValue()));
		}
		scSplitDistanceConfig = ranges;
	}



	@QConfig("mobile_rank_system_config.properties")
	public void reloadConfig(Map<String, String> conf) {
		if (conf != null) {
			this.config = conf;
			MobileRankSearchBusinessSwitchConfig.reloadBusinessSwitchConfig(conf);
			logger.info(MobileRankSearchBusinessSwitchConfig.configToString());
		}
	}

    @QConfig("search_common.properties")
	public void reloadCommonConfig(Map<String, String> conf) {
		if (conf != null) {
			this.commonConfig = conf;
			MobileRankSearchBusinessSwitchConfig.reloadBusinessSwitchConfig(conf);
			logger.info(MobileRankSearchBusinessSwitchConfig.configToString());
		}
	}

    public String getCommonConfigValue(String configKey) {
        return commonConfig != null ? commonConfig.get(configKey) : null;
    }

	/**
	 * by fangxue.zhang , 在这里支持一个默认值.
	 * @param configKey
	 * @param defaultVal
	 * @return
     */
    public String getCommonConfigValueOrDefault(String configKey, String defaultVal){

    	if(commonConfig == null){
    		return defaultVal;
		}
		String ret = commonConfig.get(configKey);
		if(ret == null){
			return defaultVal;
		}

		return ret;
	}

    public Map<String, String> getLmTimeConfig() {
		return lmTimeConfig;
	}

	public int getLmTimeConfigValue(String configKey) {
		int time = 0;
		try {
			if (lmTimeConfig != null) {
				if (lmTimeConfig.get(configKey) != null) {
					time = Integer.parseInt(lmTimeConfig.get(configKey));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return time;
	}

	@QConfig("cross-city-querysuggest-cities.properties")
	public void onConfCityChange(Map<String, String> conf) {
		if (conf == null || conf.isEmpty()) return;
		this.confCities = conf.keySet();
		logger.info("cross city query suggest conf city update, size:{}", this.confCities.size());
	}

	public Set<String> getConfCities() {
		return confCities;
	}

	public void setConfCities(Set<String> confCities) {
		this.confCities = confCities;
	}

	public Map<String, String> getConfig() {
		return config;
	}

	public int[] getServiceSplitConfig() {
		return serviceSplitConfig;
	}
	public String getConfigValue(String configKey) {
		return config != null ? config.get(configKey) : null;
	}
	
    public int getConfigValue(String configKey, int defaultValue) {
        String val = getConfigValue(configKey);
        if (org.apache.commons.lang3.StringUtils.isBlank(val)) {
            return defaultValue;
        }
        return Integer.parseInt(val);
    }

	@Override 
	public void loadFileData(String fileName, Map<String, String> config) {
		if (fileName != null) {
            if (fileName.equals("mobile_rank_system_config.properties")) {
                this.config = config;
				MobileRankSearchBusinessSwitchConfig.reloadBusinessSwitchConfig(config);
				logger.info(MobileRankSearchBusinessSwitchConfig.configToString());
            } else if (fileName.equals("search_common.properties")) {
                this.commonConfig = config;
				MobileRankSearchBusinessSwitchConfig.reloadBusinessSwitchConfig(config);
				logger.info(MobileRankSearchBusinessSwitchConfig.configToString());
            } else if (fileName.equals("lm_time.properties")) {
            	this.lmTimeConfig = config;
            }
		}
	}

	public static double getUpdateChangeRate() {
		return updateChangeRate;
	}

	@QMapConfig(value = "mobile_rank_system_config.properties", key = "board.top", defaultValue = "本地精选")
	public void injectTopBoard(List<String> boardNames){
		if (CollectionUtils.isEmpty(boardNames)) {
			return;
		}

		Map<String, Integer> map = Maps.newHashMapWithExpectedSize(boardNames.size());
		for (int i = 0; i < boardNames.size(); i++) {
			map.put(boardNames.get(i), i);
		}

		setTopBoard(map);
	}

	public static Map<String, Integer> getTopBoard() {
		return topBoard;
	}

	public static void setTopBoard(Map<String, Integer> topBoard) {
		RankSystemConfig.topBoard = topBoard;
	}
}
