package com.qunar.search.common.conf;

import com.qunar.search.common.conf.inter.QconfigBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.Configuration;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 对配置对象的包装，以满足：
 * 
 * <ol>
 * <li>无锁的原子级的get和set方法</li>
 * <li>immutable</li>
 * </ol>
 * 
 */
public class QconfigWrapper<T extends QconfigBase> {
    private static final Logger log = LoggerFactory.getLogger(QconfigWrapper.class);

    /**
     * 配置名称，应保持唯一
     */
    protected final String confName;

    /**
     * 包装器
     */
    private final AtomicReference<T> wrapper = new AtomicReference<T>();

    /**
     * 构造，设置第一个被包装的配置对象实例（可以未初始化）
     * 
     * @param confName 配置名称，必须唯一,请使用小写字母和[-_.]
     * @param config 配置对象实例
     */
    public QconfigWrapper(String confName, T config) {
        this.confName = confName;
        wrapper.set(config);
        try {
            MapConfig mapconfig = MapConfig.get(confName);
            mapconfig.addListener(new Configuration.ConfigListener<Map<String, String>>() {
                @Override
                public void onLoad(Map<String, String> conf) {
                    // 配置发生变更的时候会触发
                    get().setProperties(conf);
                    log.info("Qconfig加载 配置文件\"" + getConfName() + "\"成功");
                }
            });
        } catch (Exception e) {
            log.error("Qconfig加载 配置文件\"" + getConfName() + "\"失败", e);
        }
    }

    /**
     * 获取配置名称
     * 
     * @return 配置名称
     */
    public String getConfName() {
        return confName;
    }

    /**
     * 原子级地获取包装的配置对象
     * 
     * @return 配置对象。如果尚未设置配置对象或配置对象尚未初始化，扔出 @see RuntimeException
     */
    public T get() {
        T config = wrapper.get();
        if (config == null) {
            log.error("Qconfig加载 get {} == null", getConfName());
            throw new RuntimeException("尚未初始化");
        }
        return config;
    }

    /**
     * 原子级地设置包装的配置对象
     * 
     * @param config 配置对象。如果尚未设置配置对象或配置对象尚未初始化，扔出 @see RuntimeException
     */
    public void set(T config) {
        if (config == null) {
            log.error("Qconfig加载 set {} == null", getConfName());
            throw new RuntimeException("尚未初始化");
        }
        wrapper.set(config);
    }
}