package com.qunar.search.common.conf;

import com.google.common.collect.Maps;
import com.qunar.search.common.base.MockDataInter;
import com.qunar.search.common.enums.MobileRankSearchBusinessSwitchConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 * 依赖的接口开关
 */
public class DependCloseConfig implements MockDataInter {

    private static final Logger logger = LoggerFactory.getLogger(DependCloseConfig.class);
    
    /**
     * 格式: Map<接口, 开关>
     */
    private Map<String, Integer> dependSwitchConfig;

    public Map<String, Integer> getDependSwitchConfig() {
		return dependSwitchConfig;
	}

	public void setDependSwitchConfig(Map<String, Integer> dependSwitchConfig) {
		this.dependSwitchConfig = dependSwitchConfig;
	}

	@QConfig("depend-inter-switch.properties")
    public void onChange(Map<String, String> conf) {
        Map<String, Integer> newMap;
        try {
             newMap = this.parseOriginConfig(conf);
        } catch (Exception e) {
            logger.error("parse dependence switch error.", e);
            return;
        }
        this.dependSwitchConfig = newMap;
        MobileRankSearchBusinessSwitchConfig.reloadBusinessSwitchConfig(conf);
        logger.info(MobileRankSearchBusinessSwitchConfig.configToString());
    }

    /**
     * 解析原始配置文件
     * @param conf conf
     * @return map
     */
    @Deprecated
    private Map<String, Integer> parseOriginConfig(Map<String, String> conf) {
        Map<String, Integer> newMap = Maps.newHashMap();
        if (conf == null) return newMap;
        for(Map.Entry<String, String> entry : conf.entrySet()){
            int switchValue = Integer.parseInt(entry.getValue());
            newMap.put(entry.getKey(), switchValue);
        }
        return newMap;
    }

    @Deprecated
    public Boolean getDependIfOpen (String key) {
    	Boolean ifOpen = true;
    	if (this.dependSwitchConfig != null && this.dependSwitchConfig.get(key) != null) {
    		ifOpen = this.dependSwitchConfig.get(key) == 1;
    	}
    	return ifOpen;
    }

    public Boolean isMock(String key) {
        Boolean isMock = false;
        if(this.dependSwitchConfig != null && this.dependSwitchConfig.get(key) != null) {
            isMock = this.dependSwitchConfig.get(key) == 1;
        }
        return isMock;
    }

    public Boolean isDiff(String key) {
        Boolean isDiff = false;
        if(null != this.dependSwitchConfig && null != this.dependSwitchConfig.get(key)) {
            isDiff = this.dependSwitchConfig.get(key) == 1;
        }
        return isDiff;
    }

	@Override
	public void loadFileData(String fileName, Map<String, String> config) {
		// TODO Auto-generated method stub
		if (fileName != null) {
			if (fileName.equals("depend-inter-switch.properties")) {
				this.onChange(config);
			}
		}
	}
}
