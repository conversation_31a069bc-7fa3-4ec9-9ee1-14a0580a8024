package com.qunar.search.common.conf;

import com.qunar.search.common.bean.AlgoStrategyConfigInfo;

import qunar.tc.qconfig.client.TypedConfig;

/**
 * @Author: ruheng.liu
 * @Date: 19-3-27 下午6:06
 * @desc:
 */
public class AlgoLmBucketConfigHolder {

    /**
     * 初始化配置文件并加监听器
     */
    private static TypedConfig<AlgoStrategyConfigInfo> configInfo = TypedConfig.get("algo-lm-bucket.properties", new AlgoLmBucketConfigParser());

    public static AlgoStrategyConfigInfo getConfigInfo(){
        return configInfo.current();
    }
}