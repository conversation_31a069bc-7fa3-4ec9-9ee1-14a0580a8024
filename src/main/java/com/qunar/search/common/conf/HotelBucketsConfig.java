package com.qunar.search.common.conf;

import com.google.common.base.Strings;
import com.qunar.search.common.bean.BucketEntry;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 酒店频道桶流量配置
 * todo 这个配置的类, 持有了数据和对象描述, 拆分成两个类会更好一些, 一个持有数据, 一个描述桶的配置.
 * <AUTHOR>
 *
 */
public class HotelBucketsConfig {



    /**
     * 桶列表可按需要扩展
     */
    private Set<BucketEntry> buckets;
    /**
     * 各个桶处理流量
     */
    private Map<String, List<ClientEnvModel>> envModelMap;
    /**
     * 各个逻辑桶与物理桶对应关系
     */
    private Map<String, String> bucketMap;
    /**
     * uid各个桶的白名单
     */
    private Map<String, Set<String>> uidWhiteListMap;
    /**
     * uid的黑名单
     */
    private Set<String> uidBlackSet;
    /**
     * uid的黑名单正则
     */
    private Set<Pattern> uidBlackRegexSet;

    /**
     * 桶策略配置
     * 只关心配置不生效的情况:不配置或者配置为true策略才生效
     * logicBucket --> strategy
     */
    private Map<String, Set<String>> disableStrategyMap;


    private int[] hashArray; //size=100,value在[1,100], 用于切分流量。



    public Set<String> getUidBlackSet() {
        return uidBlackSet;
    }

    public void setUidBlackSet(Set<String> uidBlackSet) {
        this.uidBlackSet = uidBlackSet;
    }

    public Set<Pattern> getUidBlackRegexSet() {
        return uidBlackRegexSet;
    }

    public void setUidBlackRegexSet(Set<Pattern> uidBlackRegexSet) {
        this.uidBlackRegexSet = uidBlackRegexSet;
    }

    public Set<BucketEntry> getBuckets() {
        return buckets;
    }

    public void setBuckets(Set<BucketEntry> buckets) {
        this.buckets = buckets;
    }

    public Map<String, List<ClientEnvModel>> getEnvModelMap() {
        return envModelMap;
    }

    public void setEnvModelMap(Map<String, List<ClientEnvModel>> envModelMap) {
        this.envModelMap = envModelMap;
    }

    public Map<String, String> getBucketMap() {
        return bucketMap;
    }

    public void setBucketMap(Map<String, String> bucketMap) {
        this.bucketMap = bucketMap;
    }

    public Map<String, Set<String>> getUidWhiteListMap() {
        return uidWhiteListMap;
    }

    public void setUidWhiteListMap(Map<String, Set<String>> uidWhiteListMap) {
        this.uidWhiteListMap = uidWhiteListMap;
    }

    public boolean isPromotion(String bucketName) {
        if (buckets == null) {
            return true;
        }
        for (BucketEntry entry : buckets) {
            if (entry.getName().equalsIgnoreCase(bucketName)) {
                return entry.promotion;
            }
        }
        return true;
    }

    public String getModelInstanceName(String bucketName) {
        if (buckets == null) {
            return "";
        }
        for (BucketEntry entry : buckets) {
            if (entry.getName().equalsIgnoreCase(bucketName)) {
                return entry.getModelInstance();
            }
        }
        return "";
    }

    public String getAlgoInstanceName(String bucketName) {
        if (buckets == null) {
            return "";
        }
        for (BucketEntry entry : buckets) {
            if (entry.getName().equalsIgnoreCase(bucketName)) {
                return entry.getAlgoInstance();
            }
        }
        return "";
    }

    public String getModelName(String bucketName) {
        if (buckets == null) {
            return "";
        }
        for (BucketEntry entry : buckets) {
            if (entry.getName().equalsIgnoreCase(bucketName)) {
                return entry.getModelName();
            }
        }
        return "";
    }

    public boolean isContainsBucket(String bucketName) {
        if (buckets == null) {
            return false;
        }
        for (BucketEntry entry : buckets) {
            if (entry.getName().equalsIgnoreCase(bucketName)) {
                return true;
            }
        }
        return false;
    }

    public String getRealBucket(String logicBucket) {
        if(Strings.isNullOrEmpty(logicBucket)) {
            return StringUtils.EMPTY;
        }
        if(null == bucketMap || bucketMap.isEmpty()) {
            return StringUtils.EMPTY;
        }
        String realBucket = bucketMap.get(logicBucket);
        if(Strings.isNullOrEmpty(realBucket)) {
            return StringUtils.EMPTY;
        }
        return realBucket;
    }


    public Map<String, Set<String>> getDisableStrategyMap() {
        return disableStrategyMap;
    }

    public void setDisableStrategyMap(Map<String, Set<String>> disableStrategyMap) {
        this.disableStrategyMap = disableStrategyMap;
    }

    public int[] getHashArray() {
        return hashArray;
    }

    public void setHashArray(int[] hashArray) {
        this.hashArray = hashArray;
    }
}
