package com.qunar.search.common.conf.annotation;

import qunar.tc.qconfig.client.impl.Translators;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Target({ElementType.TYPE, ElementType.FIELD, ElementType.METHOD})
public @interface QTomlConfig {
	String value();

	String key() default "";

	/**
	 * 使用QConfig默认值规则, 不支持Toml数据类型
	 * <br>
	 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=63243290#id-1.%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8-%E4%BE%8B%E5%AD%90.1
	 * @see Translators#parseTranslator
	 * @return
	 */
	String defaultValue() default "";
}
