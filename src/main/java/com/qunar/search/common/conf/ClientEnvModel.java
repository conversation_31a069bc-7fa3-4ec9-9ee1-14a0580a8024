package com.qunar.search.common.conf;

/**
 * 分桶逻辑中, 对于平台的流量配置的描述
 */
public class ClientEnvModel {
    /**
     * 平台
     */
    private String plat;
    /**
     * 起始模数
     */
    private int startPercent;
    /**
     * 终止模数
     */
    private int endPercent;
    public String getPlat() {
        return plat;
    }
    public void setPlat(String plat) {
        this.plat = plat;
    }
    public int getStartPercent() {
        return startPercent;
    }
    public void setStartPercent(int startPercent) {
        this.startPercent = startPercent;
    }
    public int getEndPercent() {
        return endPercent;
    }
    public void setEndPercent(int endPercent) {
        this.endPercent = endPercent;
    }
}
