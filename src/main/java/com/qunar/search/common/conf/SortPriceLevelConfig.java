package com.qunar.search.common.conf;

import com.google.common.collect.Maps;
import com.qunar.search.common.conf.inter.QconfigBase;
import com.qunar.search.common.constants.CommonConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * sort 报价定级配置
 * <AUTHOR>
 * @date 2017/12/18
 */
public class SortPriceLevelConfig implements QconfigBase{

    private static final Logger logger = LoggerFactory.getLogger(SortPriceLevelConfig.class);

    private static Map<String, Integer> props = Maps.newHashMap();

    public static final int DEFAULT_LEVEL = 0;

    static {
        new QconfigWrapper<SortPriceLevelConfig>(CommonConstants.SORT_PRICE_LEVEL_FILENAME, new SortPriceLevelConfig());
    }

    @Override
    public void setProperties(Map<String, String> properties) {
        if (properties == null) {
            // 为 null 说明非正常流程入参或者获取配置文件失败，直接返回
            return;
        }
        Map<String, Integer> temp = Maps.newHashMap();
        int integetValue;
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            try {
                integetValue = Integer.parseInt(value);
            } catch (NumberFormatException e) {
                logger.error("parse sort_price_level.properties error, invalid config. key={}, value={}", key, value, e);
                return;
            }
            temp.put(key, integetValue);
        }
        setProps(temp);
    }

    private static void setProps(Map<String, Integer> propsTmp) {
        props = propsTmp;
    }

    public static int getLevel(String business){
        Integer level = props.get(business);
        if (level == null) {
            return DEFAULT_LEVEL;
        }
        return level;
    }

}
