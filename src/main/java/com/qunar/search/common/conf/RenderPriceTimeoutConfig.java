package com.qunar.search.common.conf;

import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.conf.annotation.QTomlConfig;
import com.qunar.search.common.util.JsonUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.tomlj.TomlTable;

import java.util.Collections;
import java.util.Map;

/**
 * Render报价超时配置管理器,支持不同场景的超时配置
 */
@Slf4j
public class RenderPriceTimeoutConfig {

    /**
     * <场景code, 场景对应的超时配置>
     */
    @Getter
    @Setter
    private static Map<String, TimeoutConfig> configMap = Collections.emptyMap();

    /**
     * 注入配置
     */
    @QTomlConfig(value = "render_price_timeout.toml", key = "config")
    public static void injectConfig(TomlTable tomlTable) {
        try {
            String json = tomlTable.toJson();
            Map<String, TimeoutConfig> map = JsonUtils.fromJson(json,
                JsonUtils.buildMapType(Map.class, String.class, TimeoutConfig.class));
            if (map == null) {
                log.error("Render报价超时配置为空。json:{}", json);
                return;
            }
            setConfigMap(map);
        } catch (Exception e) {
            log.error("Render报价超时配置解析失败。", e);
            QMonitor.recordOne("renderPriceTimeoutConfigParseFailed");
        }
    }

    /**
     * 超时配置
     */
    @Data
    public static class TimeoutConfig {
        /**
         * 外层获取render报价的超时时间，单位ms
         */
        private long fetchRenderPriceTimeout;

        /**
         * 内部最小超时时间，单位ms
         */
        private int innerMinTimeout;

        /**
         * 内部最大超时时间，单位ms
         */
        private int innerMaxTimeout;

        /**
         * 完成率阈值
         */
        private float innerFinishRatio;
    }

    public static TimeoutConfig getConfig(String sceneCode) {
        if (MapUtils.isEmpty(configMap)) {
            return null;
        }
        return configMap.get(sceneCode);
    }
}