package com.qunar.search.common.conf;

import com.google.common.collect.Maps;
import com.qunar.search.common.conf.annotation.QTomlConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.tomlj.Toml;
import org.tomlj.TomlParseResult;
import qunar.tc.qconfig.client.Configuration;
import qunar.tc.qconfig.client.TypedConfig;

import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.function.BiConsumer;

@Slf4j
public class QTomlAnnotationProcessor implements BeanPostProcessor {
	private static final TypedConfig.Parser<TomlParseResult> TOML_PARSER = Toml::parse;

	private static final Map<TypedConfig<TomlParseResult>, ConfigListenerChain> LISTENER_MANAGER_MAP = Maps
			.newConcurrentMap();

	@Override
	public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
		Class<?> clazz = AopUtils.getTargetClass(bean);
		//		parseBean(bean, clazz);
		parseFields(bean, clazz.getDeclaredFields());
		parseMethods(bean, clazz.getDeclaredMethods());

		return bean;
	}

	private void parseMethods(Object bean, Method[] declaredMethods) {
		if (declaredMethods.length == 0) {
			return;
		}

		Arrays.stream(declaredMethods).forEach(method -> parseConfig(method,
				(c, t) -> register(t, new TomlConfigInject(bean, c, method, TomlConfigInject.InjectType.METHOD))));
	}

	private void parseConfig(AnnotatedElement annotatedElement, BiConsumer<QTomlConfig,TypedConfig<TomlParseResult>> consumer) {
		QTomlConfig tomlConfig = AnnotationUtils.findAnnotation(annotatedElement, QTomlConfig.class);
		if (tomlConfig == null) {
			return;
		}
		log.info("注册Toml配置：file {}, key {}", tomlConfig.value(), tomlConfig.key());
		TypedConfig<TomlParseResult> config = TypedConfig.get(tomlConfig.value(), TOML_PARSER);
		if (config == null) {
			log.error("加载Qconfig文件{}失败", tomlConfig.value());
			return;
		}

		consumer.accept(tomlConfig, config);
	}

	private void parseFields(final Object bean, final Field[] fields) {
		Arrays.stream(fields).forEach(field -> parseConfig(field,
				(c, t) -> register(t, new TomlConfigInject(bean, c, field, TomlConfigInject.InjectType.FIELD))));
	}

	private void register(TypedConfig<TomlParseResult> config, Configuration.ConfigListener<TomlParseResult> listener) {
		ConfigListenerChain listenerChain;
		if (LISTENER_MANAGER_MAP.containsKey(config)) {
			listenerChain = LISTENER_MANAGER_MAP.get(config);
		} else {
			listenerChain = new ConfigListenerChain();
			config.current();
			config.addListener(listenerChain);
			LISTENER_MANAGER_MAP.put(config, listenerChain);
		}

		listenerChain.add(listener);
	}

	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		return bean;
	}
}