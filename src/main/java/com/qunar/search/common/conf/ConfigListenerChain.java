package com.qunar.search.common.conf;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.tomlj.TomlParseResult;
import qunar.tc.qconfig.client.Configuration;
import qunar.tc.qconfig.client.Configuration.ConfigListener;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ConfigListenerChain implements ConfigListener<TomlParseResult> {
	private final List<ConfigListener<TomlParseResult>> listeners = new ArrayList<>();
	private TomlParseResult config = null;

	public void add(ConfigListener<TomlParseResult> listener) {
		Preconditions.checkNotNull(listener);
		if (config != null) {
			trigger(listener, config);
		}
		listeners.add(listener);
	}

	@Override
	public void onLoad(TomlParseResult conf) {
		for (ConfigListener<TomlParseResult> listener : listeners) {
			trigger(listener, conf);
		}
		config = conf;
	}

	private void trigger(Configuration.ConfigListener<TomlParseResult> listener, TomlParseResult conf) {
		try {
			listener.onLoad(conf);
		} catch (Throwable e) {
			log.error("配置文件变更, 事件触发异常. data: {}", conf, e);
		}
	}
}