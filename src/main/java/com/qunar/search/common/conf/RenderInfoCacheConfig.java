package com.qunar.search.common.conf;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qunar.hotel.qmonitor.QMonitor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.spring.QMapConfig;
import qunar.tc.qconfig.client.spring.QTableConfig;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.CommonConstants.POUND_JOINER;

/**
 * render 信息缓存使用的配置
 */
public class RenderInfoCacheConfig {

    /**
     * rank 缓存 render 信息，解析的 商家券后卖价
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.key.bizCouponPrice", defaultValue = "priceAfterMerchantForRender")
    public static volatile String bizCouponPriceKey;

    /**
     * rank 缓存 render 信息，解析的 佣金
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.key.commission", defaultValue = "commissionForRender")
    public static volatile String commissionKey;

    /**
     * rank 缓存 render 信息，解析的 划线价
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.key.linePrice", defaultValue = "disp_orgPrice")
    public static volatile String linePriceKey;

    /**
     * rank 缓存 render 信息，解析的 最终 beat 值
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.key.beat", defaultValue = "fbr")
    public static volatile String beatKey;

    /**
     * rank 缓存 render 信息，解析的 优惠明细对应的key
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.discountsDetail.key", defaultValue = "disp_detail")
    public static volatile String discountsDetailKey;

    /**
     * 写券相关报价缓存开关
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.couponCache.writeEnable", defaultValue = "true")
    public static volatile boolean writeCouponCacheEnable;

    /**
     * 读券相关报价缓存开关
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.couponCache.readEnable", defaultValue = "false")
    public static volatile boolean readCouponCacheEnable;

    /**
     * qmq中增加缓存报价与实时报价diff信息开关
     */
    @QMapConfig(value = "render_price_common.properties", key = "add.render.diff.info.switch", defaultValue = "true")
    public static volatile boolean addRenderDiffInfo;

    /**
     * rank侧根据用户身份加券开关
     */
    @QMapConfig(value = "render_price_common.properties", key = "render.price.couponCache.addCoupon.enable", defaultValue = "false")
    private static volatile boolean addCouponEnable;

    /**
     * 增加券 675795239766 - “2023mzdq_ZK_13d85c_ee848b”需要排除的hades身份
     */
    private static final Set<String> excludedCodes1 = Sets.newHashSet("marketNewUser", "marketOldUserAll", "ctripOneYearOrder", "promoter", "diversionCashback", "wechatLaHuo");

    /**
     * 增加券 676813352080 - “25cjthq_ZK_7c1e61_0d70cd”需要排除的hades身份
     */
    private static final Set<String> excludedCodes2 = Sets.newHashSet("newUser", "molingCardRiskUser", "marketOldUserAll", "ctripOneYearOrder", "promoter", "diversionCashback", "wechatLaHuo");

    // Map<券id, 券相关配置>
    private static final Map<String, CouponConfig> couponConfigMap = new ConcurrentHashMap<>();
    // Map<券id, 券id> 泛化券id对应的配置的券id
    private static final Map<String, String> similarCouponMap = new ConcurrentHashMap<>();

    @QTableConfig("render_price_cache_coupon_config.t")
    public void onCouponConfigChange(List<CouponConfig> configList) {
        couponConfigMap.clear();
        similarCouponMap.clear();

        if (CollectionUtils.isEmpty(configList)) {
            return;
        }

        for (CouponConfig config : configList) {
            couponConfigMap.put(config.getCouponId(), config);
            String similarCouponIds = config.getSimilarCouponIds();
            if (StringUtils.isBlank(similarCouponIds)) {
                continue;
            }
            Arrays.stream(StringUtils.split(similarCouponIds, ","))
                    .forEach(similarCouponId -> similarCouponMap.put(similarCouponId, config.getCouponId()));
        }
    }

    /**
     * 获取每种类型优先级最高的券，考虑相似券
     * @param couponIdList
     * @return Map<券类型, 券id>
     */
    private static Map<String, String> getTopPriorityCoupon(List<String> couponIdList) {
        if (CollectionUtils.isEmpty(couponIdList)) {
            return Collections.emptyMap();
        }

        // treeMap 固定按照“券类型”排序
        Map<String, String> topPriorityCoupon = new TreeMap<>();
        couponIdList.stream()
                .map(id -> StringUtils.substringBeforeLast(id, "_"))  // 提取券ID，通过最后一个下划线进行分隔，前面的部分为券ID，后面的为策略ID
                .map(id -> similarCouponMap.getOrDefault(id, id))
                .map(couponConfigMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        CouponConfig::getType,
                        Collectors.minBy(Comparator.comparingInt(CouponConfig::getPriority))))
                .forEach((type, optionalConfig) -> optionalConfig.ifPresent(config -> topPriorityCoupon.put(type, config.getCouponId())));

        return topPriorityCoupon;
    }

    /**
     * 获取用户拥有券的key
     * @param couponIdList
     * @return
     */
    public static String getCouponKey(List<String> couponIdList) {
        if (CollectionUtils.isEmpty(couponIdList)) {
            return StringUtils.EMPTY;
        }
        // 获取每种类型优先级最高的券
        Map<String, String> couponMap = getTopPriorityCoupon(couponIdList);
        if (MapUtils.isEmpty(couponMap)) {
            return StringUtils.EMPTY;
        }
        return POUND_JOINER.join(couponMap.values());
    }

    /**
     * 获取用户拥有券的key
     * @param couponIdList 用户拥有的券列表
     * @param identityCodes 用户全身份 json
     * @return
     */
    public static String getCouponKey(List<String> couponIdList, Set<String> identityCodes) {
        List<String> finalCouponIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(couponIdList)) {
            finalCouponIdList.addAll(couponIdList);
        }
        if (addCouponEnable && CollectionUtils.isNotEmpty(identityCodes)) {
            if (Collections.disjoint(identityCodes, excludedCodes1)) {
                finalCouponIdList.add("2023mzdq_ZK_13d85c_ee848b");
                QMonitor.recordOne("render_price_coupon_add_2023mzdq");
            }
            if (identityCodes.contains("sdyearuserv2") && Collections.disjoint(identityCodes, excludedCodes2)) {
                finalCouponIdList.add("25cjthq_ZK_7c1e61_0d70cd");
                QMonitor.recordOne("render_price_coupon_add_25cjthq");
            }
        }
        if (CollectionUtils.isEmpty(finalCouponIdList)) {
            return StringUtils.EMPTY;
        }
        // 获取每种类型优先级最高的券
        Map<String, String> couponMap = getTopPriorityCoupon(finalCouponIdList);
        if (MapUtils.isEmpty(couponMap)) {
            return StringUtils.EMPTY;
        }
        return POUND_JOINER.join(couponMap.values());
    }

    @Data
    @NoArgsConstructor
    public static class CouponConfig {
        /**
         * 券类型
         */
        private String type;
        /**
         * 券id
         */
        private String couponId;
        /**
         * 券名称
         */
        private String couponName;
        /**
         * 券优先级
         */
        private int priority;
        /**
         * 相似券id
         */
        private String similarCouponIds;
    }
}
