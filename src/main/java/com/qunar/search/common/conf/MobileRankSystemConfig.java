package com.qunar.search.common.conf;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/8
 */
@Slf4j
public class MobileRankSystemConfig {

    /**
     * 酒店头图域名
     */
    @QMapConfig(value = "mobile_rank_system_config.properties", key = "hotelHeadImageDomainName", defaultValue = "https://userimg.qunarzz.com/imgs/")
    @Getter
    private volatile static String hotelHeadImageDomainName = "https://userimg.qunarzz.com/imgs/";

    /**
     * 酒店头图url后缀
     */
    @QMapConfig(value = "mobile_rank_system_config.properties", key = "hotelHeadImageUrlSuffix", defaultValue = "76.jpg")
    @Getter
    private volatile static String hotelHeadImageUrlSuffix = "76.jpg";


    @QConfig("mobile_rank_system_config.properties")
    private volatile static Map<String, String> config;


    public static String getConfigValue(String configKey) {
        Preconditions.checkArgument(MapUtils.isNotEmpty(config), "配置不能为空");
        return config.get(configKey);
    }

    public static boolean getBoolean(String key, boolean defaultValue){
        Preconditions.checkArgument(MapUtils.isNotEmpty(config), "配置不能为空");
        String value = config.get(key);
        try {
            if(StringUtils.isNotBlank(value)){
                return Boolean.parseBoolean(value);
            }
        } catch (Exception e) {
            log.error("配置文件类型转化错误,key:{},value:{}",key, value, e);
        }
        return defaultValue;
    }

}
