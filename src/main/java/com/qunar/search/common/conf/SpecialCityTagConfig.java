package com.qunar.search.common.conf;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;

/**
 * 特殊citytag配置信息
 * Created by den<PERSON><PERSON><PERSON> on 16-8-30.
 */
public class SpecialCityTagConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpecialCityTagConfig.class);

    /**
     * qconfig数据格式：jiashan=xitang,wuzhen
     */
    private Map<String, List<String>> cityTagMap;

    @QConfig("special_citytag_list.properties")
    public void onChange(Map<String, String> conf) {
        Map<String, List<String>> newMap;
        try {
            newMap = this.parse(conf);
        } catch (Exception e) {
            LOGGER.error("parse special citytag list error!", e);
            newMap = Maps.newHashMap();
        }
        this.cityTagMap = newMap;
    }

    /**
     * 取某城市下的特殊citytag
     * @param cityCode
     * @return
     */
    public List<String> getSpecialList(String cityCode) {
        if (Strings.isNullOrEmpty(cityCode)) {
            return Collections.EMPTY_LIST;
        }
        if (null == this.cityTagMap) {
            return Collections.EMPTY_LIST;
        }
        return this.cityTagMap.get(cityCode);
    }

    /**
     * 解析城市下的特殊citytag信息
     * @param conf
     * @return
     */
    private Map<String, List<String>> parse(Map<String, String> conf) {
        Map<String, List<String>> newMap = Maps.newHashMap();
        if (null == conf || conf.isEmpty()) {
            return newMap;
        }
        for (Map.Entry<String, String> specialData : conf.entrySet()) {
            String data = specialData.getValue();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            List<String> specialList = COMMA_SPLITTER.splitToList(data);
            newMap.put(specialData.getKey(), specialList);
        }
        return newMap;
    }

}
