package com.qunar.search.common.conf;

import com.qunar.search.common.conf.inter.QconfigBase;
import com.qunar.search.common.constants.CommonConstants;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 计算最低报价以及抓取报价使用的配置信息
 *
 * <AUTHOR>
 */
public class HpriceParamConfig implements QconfigBase {
    private static final Logger log = LoggerFactory.getLogger(HpriceParamConfig.class);
    private static Map<String, Integer> props = new HashMap<String, Integer>();

    static {
        props.put(CommonConstants.SIGNINGIFTLOWMONEY, 40);
        props.put(CommonConstants.SIGNINGIFTMIDDLEMONEY, 50);
        props.put(CommonConstants.SIGNINGIFTHIGHMONEY, 100);
        props.put(CommonConstants.A2BMAXSEQNUM, 200);
        props.put(CommonConstants.A2BMINDISTANCE, 3000);
        new QconfigWrapper<HpriceParamConfig>(CommonConstants.HPRICPARAMINFOFILENAME, new HpriceParamConfig());
    }

    @Override
    public void setProperties(Map<String, String> map) {
        if (map == null || map.size() <= 0) {
            return;
        }
        Map<String, Integer> propsTmp = new HashMap<String, Integer>();
        try {
            for (Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StringUtils.isEmpty(key) || !NumberUtils.isNumber(value)) {
                    return;
                }
                Integer val = Integer.parseInt(value);
                propsTmp.put(key, val);
            }
            setProps(propsTmp);
        } catch (Exception e) {
            log.error("HpriceParamConfig parse props info error. {}", e);
        }
    }

    public static void setProps(Map<String, Integer> propsTmp) {
        props = propsTmp;
    }

    /**
     * 获取所有配置信息
     *
     * @return props
     */
    public static Map<String, Integer> getAllProps() {
        return props;
    }
}
