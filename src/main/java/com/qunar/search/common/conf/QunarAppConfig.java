package com.qunar.search.common.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.agile.FileProperties;

import java.io.File;
import java.util.Properties;

/**
 * QunarAppConfig
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public class QunarAppConfig {

    /**
     * 获取工程的qunar-app.properties文件信息，从而获得工程信息加载不同数据
     */

    private final static Logger LOG = LoggerFactory.getLogger(QunarAppConfig.class);
    private static Properties prop = null;

    static {
        try {
            LOG.info("qunar-app.properties path = " + QunarAppConfig.class.getResource("/").getPath());
            prop = new FileProperties(
                    new File(QunarAppConfig.class.getResource("/").getPath(), "qunar-app.properties"), "UTF-8");
        } catch (Exception e) {
            LOG.error("解析 qunar-app.properties 文件失败 {}", e);
        }
    }

    public static String getConditionByKey(String key) {
        if (null == prop) {
            LOG.error("获取配置QunarAppConfig环境信息错误");
            return null;
        }
        return prop.getProperty(key);
    }




}
