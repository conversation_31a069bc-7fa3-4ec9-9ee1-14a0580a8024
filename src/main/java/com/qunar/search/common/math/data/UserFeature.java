package com.qunar.search.common.math.data;

/**
 * 全量用户特征
 * <AUTHOR>
 *
 */
public class UserFeature {
	public static final int PARTIAL_FEATURE_SIZE=2048;

	public String uid;
	public double currentCityOrder400DAvg;//用户城市下400天内订单金额
	//历史数据
	public UserPartialFeature userHistoryFeature=null;//new UserPartialFeature();
	//当天
	public UserPartialFeature userIntradayFeature=null;//new UserPartialFeature();

    private PlatformUserProfile userProfile;         // 平台用户画像
	
    public UserFeature() {
    }

	public double getCurrentCityOrder400DAvg() {
		return currentCityOrder400DAvg;
	}

	public void setCurrentCityOrder400DAvg(double currentCityOrder400DAvg) {
		this.currentCityOrder400DAvg = currentCityOrder400DAvg;
	}

	public UserFeature(UserPartialFeature userHistoryFeature, UserPartialFeature userIntradayFeature) {
		this.userHistoryFeature = userHistoryFeature;
		this.userIntradayFeature = userIntradayFeature;
	}

    public PlatformUserProfile getUserProfile() {
        return userProfile;
    }

}
