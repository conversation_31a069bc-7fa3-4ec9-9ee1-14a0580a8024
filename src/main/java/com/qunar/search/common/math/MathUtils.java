package com.qunar.search.common.math;


/**
 * 数学计算相关的辅助类
 *
 * <AUTHOR> wang
 * @datetime 2016-06-16/6/15 15:47:00
 */

public class MathUtils {


    private static final double EPS = 0.000001;

    /**
     * tanh函数
     * 将value拉伸到(-1,1)
     *
     * @param value
     * @return
     */
    public static Double tanh(Double value) {
        double eValue = Math.exp(value);
        double neValue = Math.exp(-value);
        return (eValue - neValue) / (eValue + neValue);
    }

    /**
     * features*model
     *
     * @param features
     * @param model
     * @return
     */
    public static Double innerProduct(double features[], double[] model) {
        double score = 0;
        for (int i = 0; i < features.length; i++) {
            score += features[i] * model[i];
        }
        return score;
    }

    /**
     * logistic(features*model)
     *
     * @param features
     * @param model
     * @return
     */
    public static Double logisticScore(double features[], double[] model) {
        double score = innerProduct(features, model);
        return 1 / (1 + Math.exp(-score));
    }


    /**
     * mix-max-Normaliztion
     *
     * @param max
     * @param min
     * @param value
     * @return
     */

    public static float minMaxNormalization(float max, float min, float value) {
        if(Float.compare(max, min) == 0){
            return 0.0f;
        }
        return (value - min) / (max - min);
    }


    /**
     * 判断两个浮点数是否相等.
     * @param a
     * @param b
     * @return
     */
    public static boolean isEqual(double a, double b){
        return Math.abs(a - b) < EPS;
    }
}
