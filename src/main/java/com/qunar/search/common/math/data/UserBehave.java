package com.qunar.search.common.math.data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-06-16 下午2:07
 * @DESCRIPTION 用户不同的行为中统计相同的指标，行为可以是历史点击，历史180订单，历史800天订单
 * 实时点击，收藏酒店等等数据
 **/
@Data
public class UserBehave {

    /**
     * 行为次数
     */
    int behaveCount;

    /**
     *  指标中最大值
     */
    int behaveMaxGrade;

    /**
     * 行为中不同档次比值的map
     */
    Map<Integer, Double> gradeRateMap = Collections.emptyMap();

    /**
     * 不同星级比值的map
     */
    Map<Integer, Double> starRateMap = Collections.emptyMap();

    /**
     * 历史点击不同品牌比值的map
     */
    Map<String, Double> brandRateMap = Collections.emptyMap();

    /**
     * 不同商圈比值的map
     */
    Map<String, Double> tradingAreaRateMap = Collections.emptyMap();

    /**
     * 不同类型比值的map
     */
    Map<String, Double> typeRateMap = Collections.emptyMap();

    /**
     * 不同酒店比值的map
     */
    Map<String, Double> hotelSeqRateMap = Collections.emptyMap();

    /**
     * 不同城市比值的map
     */
    Map<String, Double> hotelCityRateMap = Collections.emptyMap();

    /**
     * 不同价格区间比值的map，主要针对低星
     */
    Map<Integer, Double> gradePriceRateMap = Collections.emptyMap();

    /**
     * 同一城市下酒店比值的map
     */
    Map<String, Map<String, Double>> cityHotelRateMap = Collections.emptyMap();
}
