package com.qunar.search.common.math.data;

import java.io.Serializable;

/**
 * 平台用户画像
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-12-14.
 */
public class PlatformUserProfile implements Serializable {

    private static final long serialVersionUID = 6026168661467700984L;

    private byte gender;                      //性别
    private short age;                        //年龄
    private byte hasHistoryFlightOrder;       //是否有历史机票订单
    private byte hasLastFlightOrder;          //近一个月是否有机票订单

    public PlatformUserProfile() {

    }

    public PlatformUserProfile(String gender, String age, String hasHistoryFlightOrder, String hasLastFlightOrder) {
        this.gender = Byte.parseByte(gender);
        this.age = Short.parseShort(age);
        this.hasHistoryFlightOrder = Byte.parseByte(hasHistoryFlightOrder);
        this.hasLastFlightOrder = Byte.parseByte(hasLastFlightOrder);
    }

    public byte getGender() {
        return gender;
    }

    public void setGender(byte gender) {
        this.gender = gender;
    }

    public short getAge() {
        return age;
    }

    public void setAge(short age) {
        this.age = age;
    }

    public byte getHasHistoryFlightOrder() {
        return hasHistoryFlightOrder;
    }

    public void setHasHistoryFlightOrder(byte hasHistoryFlightOrder) {
        this.hasHistoryFlightOrder = hasHistoryFlightOrder;
    }

    public byte getHasLastFlightOrder() {
        return hasLastFlightOrder;
    }

    public void setHasLastFlightOrder(byte hasLastFlightOrder) {
        this.hasLastFlightOrder = hasLastFlightOrder;
    }
}
