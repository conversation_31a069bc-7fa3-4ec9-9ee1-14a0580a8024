package com.qunar.search.common.math;

/**
 * 坐标距离
 */
public class CoordDistance {
    private static final double R = 6370996.81;

    private static double normalizeLongitude(double l) {
        while (l <= -180) l += 360;
        while (l >= 180)  l -= 360;
        return l * Math.PI / 180;
    }

    private static double normalizeLatitude(double l) {
        return (l <= -74? -74: (l >= 74? 74: l)) * Math.PI / 180;
    }

    public static double distance(double lat1, double lng1, double lat2, double lng2) {
        double theta = Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(lng2 - lng1);
        if (theta == 1.0) return 0;
        return R * Math.acos(theta);
    }

    public static double distanceWithNormalized(double lat1, double lng1, double lat2, double lng2) {
        return distance(normalizeLatitude(lat1), normalizeLongitude(lng1), normalizeLatitude(lat2), normalizeLongitude(lng2));
    }
}
