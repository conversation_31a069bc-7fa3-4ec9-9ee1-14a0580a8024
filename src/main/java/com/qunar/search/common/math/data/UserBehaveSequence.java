package com.qunar.search.common.math.data;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-10-24
 * @DESCRIPTION 用户不同的行为序列，提交单400天 最大200条，点击2天，最多100条
 * 实时点击，收藏酒店等等数据
 **/
@Data
public class UserBehaveSequence {

    /**
     * 行为的时间
     */
    List<String> timeList = Collections.emptyList();

    /**
     * 行为的酒店seq
     */
    List<String> hotelSeqList = Collections.emptyList();

    /**
     * 价格行为序列
     */
    List<Float> priceList = Collections.emptyList();

    /**
     * 不同价格区间比值的map，主要针对低星
     */
    List<Float> distanceList = Collections.emptyList();

    /**
     * 品牌行为序列
     */
    List<String> brandList = Collections.emptyList();

    /**
     * 类型行为序列
     */
    List<List<String>> typeList = Collections.emptyList();

    /**
     * 城市行为序列
     */
    List<String> hotelCityList = Collections.emptyList();

    /**
     * 档次行为序列
     */
    List<String> gradeList = Collections.emptyList();
}
