package com.qunar.search.common.math.data;


import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户历史订单的属性分布
 * <AUTHOR>
 *
 */
public class HotelSimilarity implements Serializable{
    private static final long serialVersionUID = 1L;
    //<指标， 用户历史定的次数>  eg: dangci = 5 : count = 10
	public Map<Integer, Double> historyLevelMap = new HashMap<Integer, Double>();
	protected Map<Integer, Double> historyStarMap = new HashMap<Integer, Double>();
	public Map<String, Double> historyBrandMap = new HashMap<String, Double>();
	public Map<String, Double> historyTypeMap = new HashMap<String, Double>();
	//商圈
	protected Map<String, Double> historyBusiMap = new HashMap<String, Double>();
}
