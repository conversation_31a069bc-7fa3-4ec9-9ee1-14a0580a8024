package com.qunar.search.common.math.data;

import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.qunar.search.common.enums.FeatureIndexOffsetMapping;
import com.qunar.search.common.feature.UserHotel.RealtimeClickHotel;
import com.qunar.search.common.math.data.UserShowOrderData.OrderInfo;
import com.qunar.search.common.math.data.UserShowOrderData.PvInfo;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 部分公共用户特征
 *
 * <AUTHOR>
 *
 */
@Setter
public class UserPartialFeature {
	private static final Logger LOGGER = LoggerFactory.getLogger(UserPartialFeature.class);
	public static final int FILTER_FEATURE_MAX = 5;
	private static final Set<String> badNums = Sets.newHashSet("0", "-1");

	public static final String DATE_TIME_UNDERLINE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String REAL_TIME_FILTER_PV_SPLITTER = "^^^";
	public static final Splitter FEATURE_SPLITTER = Splitter.on(REAL_TIME_FILTER_PV_SPLITTER).trimResults().omitEmptyStrings();
	public static final String REAL_TIME_FILTER_PV_SEGMENT_SPLITTER = "#";
	public static final String REAL_TIME_FILTER_DEFAULT_STR = "$$";
	public static final String REAL_TIME_FILTER_DEFAULT_NUM = "0";
	public static final String COMMA = ",";
	protected static final double FEATURE_POSITIVE = 1.0;

	public int today = 0;
	public UserShowOrderData showOrderData = new UserShowOrderData();

	//<hotelSeq, count>  当前用户历史半年预定该酒店的次数
	protected Map<String, Integer> orderCounts = new HashMap<String, Integer>();
	//user平均订单价格
	public double orderAvgPrice;
	// 用户实时点击酒店均价
	public double clickAvgPrice;

	//实时点击酒店list
	public List<RealtimeClickHotel> realtimeClickHotels = new LinkedList<RealtimeClickHotel>();

    // 用户历史订单平均分位值
    public Double avgQuantile;

	/* 实时筛选档次时间排序, 排在越靠前发生筛选行为的时间越新 */
	public List<String> realTimeFilterDangciTimeSort = Lists.newArrayList();

	/* 实时筛选品牌时间排序 */
	public List<String> realTimeFilterBrandTimeSort = Lists.newArrayList();

	public List<String> realTimeFilterTradingTimeSort = Lists.newArrayList();

	public List<Range<Double>> realTimeFilterPriceTimeSort = Lists.newArrayList();

	public List<String> realTimeQueryTimeSort = Lists.newArrayList();

	/**
	 * 实时关键词对应的候选酒店
	 * <q, hotelseq1,hotelseq2,hotelseq3>
	 */
	public Map<String, Set<String>> realTimeQueryCandidates = Maps.newHashMap();

	//用户每个城市最近一笔订单
	public Map<String, OrderInfo> cityLastOrder = new HashMap<String, OrderInfo>();
	//<seq, 展现>
	public Map<String, PvInfo> hisShowSt = new HashMap<String, PvInfo>();
	//用户各指标订单次数
	public HotelSimilarity hotelSimilarity=new HotelSimilarity();
	// 用户历史点击档次排序
	public Map<Integer, Integer> levelSort = new HashMap<Integer, Integer>(8);

	//收藏数据，4天之前收藏酒店
	protected Set<String> favSt = new HashSet<String>();
	//收藏数据，4天内收藏酒店
	protected Set<String> favSt_curr = new HashSet<String>();

	public UserPartialFeature() {

	}

}
