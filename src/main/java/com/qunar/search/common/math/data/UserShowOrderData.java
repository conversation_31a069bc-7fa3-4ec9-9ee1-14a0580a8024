package com.qunar.search.common.math.data;

import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * 酒店用户历史查询订单记录
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserShowOrderData implements Serializable{
    private static final long serialVersionUID = 1L;

    private static Logger log = LoggerFactory.getLogger(UserShowOrderData.class);
	
	public String uid="";
	//用户浏览数据
	public List<PvInfo> pvInfoList=new ArrayList<PvInfo>();
	//用户订单数据
	public List<OrderInfo> orderInfoList = new ArrayList<OrderInfo>();
	//用户收藏数据
	public List<CollectionInfo> collectionInfoList=new ArrayList<CollectionInfo>();
	
	/**
	 * 
	 * <AUTHOR>
	 *酒店的展现，click，book,order数据，历史查询信息
	 */
	@Data
	public static class PvInfo implements Serializable{
        private static final long serialVersionUID = 1L;
        public String hotelSeq = "";
		public int pv = 0;
		public int cl = 0;
		public boolean isBookingPageOpened = false;
		public boolean isOrdered = false;
		public PvInfo() {}
		public PvInfo(String pvInfoStr) {
			String[] items = org.apache.commons.lang3.StringUtils.split(pvInfoStr, ",");
			hotelSeq = items[0];
			pv = Integer.parseInt(items[1]);
			cl = Integer.parseInt(items[2]);
			if (items[3].equals("1")) {
				isBookingPageOpened = true;
			} else {
				isBookingPageOpened = false;
			}
			if (items[4].equals("1")) {
				isOrdered = true;
			} else {
				isOrdered = false;
			}
		}
	}
	
	/**
	 * 
	 * <AUTHOR>
	 * 酒店历史订单信息（价格，房间量，预定日期）
	 */
	@Data
	public static class OrderInfo implements Serializable{
        private static final long serialVersionUID = 1L;
        public String hotelSeq = "";	
		public double price = 0;
		public int rooms = 0;
		public String orderDate = "00000000";
		public OrderInfo() {}
		public OrderInfo(String orderInfoStr) {
			String[] items = org.apache.commons.lang3.StringUtils.split(orderInfoStr,",");
			hotelSeq = items[0];
			price = Double.valueOf(items[1]);
			rooms = Integer.parseInt(items[2]);
			orderDate = items[3].replace("-", "");
		}

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof OrderInfo)) return false;
            OrderInfo orderInfo = (OrderInfo) o;
            return rooms == orderInfo.rooms &&
                    Objects.equals(hotelSeq, orderInfo.hotelSeq) &&
                    Objects.equals(orderDate, orderInfo.orderDate);
        }

        @Override
        public int hashCode() {
            return Objects.hash(hotelSeq, rooms, orderDate);
        }
    }
	
	/**
	 * 收藏数据（收藏酒店，收藏日期）
	 * <AUTHOR>
	 *
	 */
	@Data
	public static class CollectionInfo implements Serializable{
        private static final long serialVersionUID = 1L;
        public String hotelSeq="";	
		public int logDay=0;
		public CollectionInfo() {}
		public CollectionInfo(String CollectionInfoStr) {
			String[] items=org.apache.commons.lang3.StringUtils.split(CollectionInfoStr,",");
			hotelSeq=items[0];
			logDay=Integer.parseInt(items[1]);
		}
	}

	public UserShowOrderData() {}
	
	public UserShowOrderData(String pvStr, String orderStr, String collectionStr) {
		parseOneUserHistoryPv(pvStr);
		parseOneUserHisotryOrder(orderStr);
		parseOneUserHistoryCollection(collectionStr);
	}
	
	public UserShowOrderData(String line) {
		line+="\tend";
		String[] items=org.apache.commons.lang3.StringUtils.split(line,"\t");
		if (items.length>=8) {
			uid=items[2].startsWith("un.") ? items[2].toLowerCase(Locale.ENGLISH) : items[2].toUpperCase(Locale.ENGLISH);
			parseOneUserHistoryPv(items[3]);
			parseOneUserHisotryOrder(items[4]);
			parseOneUserHistoryCollection(items[6]);
		}
		else if (items.length>=4 && items.length<=5) {
			parseOneUserHistoryPv(items[0]);
			parseOneUserHisotryOrder(items[1]);
			parseOneUserHistoryCollection(items[2]);
		}
	}
	public UserShowOrderData(String line, String userNameCollections) {
		line+="\tend";
		String[] items=org.apache.commons.lang3.StringUtils.split(line,"\t");
		if (items.length>=8) {
			uid=items[2].startsWith("un.") ? items[2].toLowerCase(Locale.ENGLISH) : items[2].toUpperCase(Locale.ENGLISH);
			parseOneUserHistoryPv(items[3]);
			parseOneUserHisotryOrder(items[4]);
			parseOneUserHistoryCollection(items[6]+"#"+userNameCollections);
		}
		else if (items.length>=4 && items.length<=5) {
			parseOneUserHistoryPv(items[0]);
			parseOneUserHisotryOrder(items[1]);
			parseOneUserHistoryCollection(items[2]+"#"+userNameCollections);
		}
	}
	public UserShowOrderData(String[] items) {
		if (items.length>=7) {
			uid=items[2].startsWith("un.") ? items[2].toLowerCase(Locale.ENGLISH) : items[2].toUpperCase(Locale.ENGLISH);
			parseOneUserHistoryPv(items[3]);
			parseOneUserHisotryOrder(items[4]);
			parseOneUserHistoryCollection(items[6]);
		}
		else if (items.length>=3 && items.length<=5) {
			parseOneUserHistoryPv(items[0]);
			parseOneUserHisotryOrder(items[1]);
			parseOneUserHistoryCollection(items[2]);
		}
	}
	public UserShowOrderData(String[] items, String userNameCollections) {
		if (items.length>=7) {
			uid=items[2].toUpperCase();
			parseOneUserHistoryPv(items[3]);
			parseOneUserHisotryOrder(items[4]);
			parseOneUserHistoryCollection(items[6]+"#"+userNameCollections);
		}
		else if (items.length>=3 && items.length<=5) {
			parseOneUserHistoryPv(items[0]);
			parseOneUserHisotryOrder(items[1]);
			parseOneUserHistoryCollection(items[2]+"#"+userNameCollections);
		}
	}
	public UserShowOrderData(List<PvInfo> pvInfoList, List<OrderInfo> orderInfoList, List<CollectionInfo> collectionInfoList) {
		if (pvInfoList!=null) {
			this.pvInfoList=pvInfoList;
		}
		if (orderInfoList!=null) {
			this.orderInfoList=orderInfoList;
		}
		if (collectionInfoList!=null) {
			this.collectionInfoList=collectionInfoList;
		}
	}
	public UserShowOrderData(String uid, List<PvInfo> pvInfoList, List<OrderInfo> orderInfoList, List<CollectionInfo> collectionInfoList) {
		this.uid=uid;
		if (pvInfoList!=null) {
			this.pvInfoList=pvInfoList;
		}
		if (orderInfoList!=null) {
			this.orderInfoList=orderInfoList;
		}
		if (collectionInfoList!=null) {
			this.collectionInfoList=collectionInfoList;
		}
	}
	
	protected boolean parseOneUserHistoryPv(String historyPvStr) {
		if (historyPvStr == null) {
			return false;
		}
		if (!historyPvStr.toLowerCase(Locale.ENGLISH).equals("null") && !historyPvStr.equals("")) {
			String[] historyPvs = org.apache.commons.lang3.StringUtils.split(historyPvStr,"#");
			for (String oneHistoryPv : historyPvs) {
				try {
					if ("jj".equals(oneHistoryPv) || oneHistoryPv == null || "".equals(oneHistoryPv)) {
						continue;
					}
					PvInfo onePvInfo = new PvInfo(oneHistoryPv);
					pvInfoList.add(onePvInfo);
				} catch (Exception e) {
					log.error("show data parse error", e);
					continue;
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	protected boolean parseOneUserHisotryOrder(String historyOrderStr) {
		if (historyOrderStr == null) {
			return false;
		}
		if (!historyOrderStr.toLowerCase(Locale.ENGLISH).equals("null") && !historyOrderStr.equals("")) {
			String[] historyOrders = org.apache.commons.lang3.StringUtils.split(historyOrderStr,"#");
			for (String oneHistoryOrder : historyOrders) {
				try {
					if (oneHistoryOrder == null || "".equals(oneHistoryOrder)) {
						continue;
					}
					OrderInfo oneOrderInfo = new OrderInfo(oneHistoryOrder);
					orderInfoList.add(oneOrderInfo);
				} catch (Exception e) {
					log.error("order data parse error", e);
					continue;
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	protected boolean parseOneUserHistoryCollection(String historyCollectionStr) {
		if (historyCollectionStr == null) {
			return false;
		}
		if (!historyCollectionStr.isEmpty() && !historyCollectionStr.toLowerCase(Locale.ENGLISH).equals("null")) {
			String[] historyCollection=org.apache.commons.lang3.StringUtils.split(historyCollectionStr,"#");
			for (String oneHistoryCollection : historyCollection) {
				try {
					if (oneHistoryCollection == null || "".equals(oneHistoryCollection)) {
						continue;
					}
					CollectionInfo oneCollectionInfo = new CollectionInfo(oneHistoryCollection);
					collectionInfoList.add(oneCollectionInfo);
				} catch (Exception e) {
					log.error("collection data parse error", e);
					continue;
				}
			}
			return true;
		} else {
			return false;
		}
	}
}