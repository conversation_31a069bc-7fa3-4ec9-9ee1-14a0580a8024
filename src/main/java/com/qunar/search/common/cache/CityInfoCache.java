package com.qunar.search.common.cache;

import com.google.common.base.Strings;
import com.qunar.hotel.city.CityClient;
import com.qunar.hotel.city.CityClientFactory;
import com.qunar.hotel.city.CityItem;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.conf.CityClientConfig;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

/**
 * 在内存中存储城市相关数据.
 */
public class CityInfoCache {
	private static final Logger log = LoggerFactory.getLogger(CityInfoCache.class);
 	private static CityClient cityClient;

    private static final String HONGKONG_CODE = "hongkong";
    private static final String MACAO_CODE = "macao";

    public static void init(CityClientConfig config) {
		cityClient = createCityClient(config);
	}

	private static CityClient createCityClient(CityClientConfig config) {
		if (null == config) {
			log.error("CityClientConfig config is null");
			return null;
		}
		Map<String, String> props = config.getProps();
		try {
			return CityClientFactory.createCityClient(props);
		} catch (Exception e) {
			log.error("初始化cityClient失败", e);
		}
		return null;
	}

	public static String getCityUrl(String zhName) {
		String cityUrl = null;
		try {
			cityUrl = cityClient.getCityCodeByCityName(zhName);
		} catch (Exception e) {
			log.warn("获取城市代码错误,CITY_CLIENT服务异常", e);
		}
		return cityUrl;
	}

	public static String getCityName(String cityUrl) {
		try {
			return StringUtils.trimToEmpty(cityClient.getCityNameByCityCode(cityUrl));
		} catch (Exception e) {
			log.warn("无法获取城市信息", new Exception("无法获取城市信息，可能因为没有中文名:" + cityUrl));
		}
		return StringUtils.EMPTY;
	}

	/**
	 * 是否大陆 不包含港澳台
	 * @param cityurl
	 * @return
	 */
	public static boolean isMainLand(String cityurl) {
		String country = StringUtils.trimToEmpty(cityClient.getCountryFromCity(cityurl, "zh"));
		if ("中国".equals(country)) {
			String province = cityClient.getProvinceByCityName(
					cityClient.transformCityURL(cityurl, "zh"), "zh");
			return !("台湾".equals(province) || "香港".equals(province) || "澳门".equals(province));
		}
		return false;
	}

	/**
	 * 是否中国 不包含台湾
	 * @param cityurl
	 * @return
	 */
    public static boolean isChina(String cityurl) {

        String country = StringUtils.trimToEmpty(cityClient.getCountryFromCity(cityurl, "zh"));
        if ("中国".equals(country)) {
            String province = cityClient.getProvinceByCityName(
                    cityClient.transformCityURL(cityurl, "zh"), "zh");
            return !("台湾".equals(province));
        }
        return false;
    }

    /**
     *  是否国际城市，包含港澳台
     * @param cityUrl
     * @return
     */
    public static boolean isInternational(final String cityUrl) {
        if (cityUrl.startsWith(HONGKONG_CODE)) {
            return true;
        }

        if (cityUrl.startsWith(MACAO_CODE)) {
            return true;
        }

        String province = cityClient.getProvinceByCityName(
                cityClient.transformCityURL(cityUrl, "zh"), "zh");
        if ("台湾".equals(province)) {
            return true;
        }

        String country = StringUtils.trimToEmpty(cityClient.getCountryFromCity(cityUrl, "zh"));
        return !"中国".equals(country);
    }

    public static void destroy() {
		if (cityClient != null) {
			cityClient.destroy();
		}
	}

	/**
	 * 取当前目的地时间
	 * @param cityCode 目的地城市code
	 *
	 * @return
	 */
	public static Date getCurDestTime(String cityCode) {
		try {
			if (StringUtils.isBlank(cityCode)) {
				return new Date();
			}
			return cityClient.getCurDestTime(cityCode);
		} catch (Exception e) {
			log.warn("get destination local time error, cityCode:{}", cityCode);
			QMonitor.recordOne("GetCurDestTimeError");
			return new Date();
		}
	}


	public static String getProvinceByCityURL(String cityurl) {
		return cityClient.getProvinceByCityName(cityClient.transformCityURL(cityurl, "zh"), "zh");
	}

	public static boolean isInternationalIncludeTaiwan(String cityurl) {
		String country = org.apache.commons.lang3.StringUtils.trimToEmpty(cityClient.getCountryFromCity(
				cityurl, "zh"));
		if ("中国".equals(country)) {
			String province = cityClient.getProvinceByCityName(
					cityClient.transformCityURL(cityurl, "zh"), "zh");
			return "台湾".equals(province);
		}
		return true;
	}

	public static String getCountryNameZH(String cityurl) {
		try {
			return StringUtils.trimToEmpty(cityClient.getCountryFromCity(
					cityurl, "zh"));
		} catch (Exception e) {
			log.warn("获取国家名字失败,CITY_CLIENT服务异常", e);
		}
		return StringUtils.EMPTY;
	}

	/**
	 * 获取国家英文名称
	 * @param cityUrl
	 * @return
	 */
	public static String getCountryNameEn(String cityUrl){
		try {
			return StringUtils.trimToEmpty(cityClient.getCountryFromCity(
					cityUrl, "en"));
		} catch (Exception e) {
			log.warn("获取国家名字失败,CITY_CLIENT服务异常", e);
		}
		return StringUtils.EMPTY;
	}

	public static String getProvinceByCityName(String cityurl) {
		String province = cityClient.getProvinceByCityName(
				cityClient.transformCityURL(cityurl, "zh"), "zh");
		return province;
	}


    /**
     * 判断城市是否地级市
     * @param cityCode
     * @return
     */
    public static boolean isRootCity(String cityCode) {
        if (Strings.isNullOrEmpty(cityCode)) {
            return false;
        }
        try {
            CityItem cityItem = cityClient.getCityItemByCityCode(cityCode);
            if (null == cityItem) {
                return false;
            }
            return cityItem.isRootCity();
        } catch (Exception e) {
            log.warn("获取城市是否地级市失败, CITY_CLIENT服务异常", e);
            QMonitor.recordOne("CityClientServiceError");
            return false;
        }
    }

    /**
     * 判断城市是否市辖区
     * @param cityCode
     * @return
     */
    public static boolean isDistrict(String cityCode) {
        if (Strings.isNullOrEmpty(cityCode)) {
            return false;
        }
        try {
            CityItem cityItem = cityClient.getCityItemByCityCode(cityCode);
            if (null == cityItem) {
                return false;
            }
            return Boolean.parseBoolean(cityItem.getProperty("ia"));
        } catch (Exception e) {
            log.warn("获取城市是否市辖区失败, CITY_CLIENT服务异常", e);
            QMonitor.recordOne("CityClientServiceError");
            return false;
        }
    }

    /**
     * 判断城市是否景区
     * @param cityCode
     * @return
     */
    public static boolean isSight(String cityCode) {
        if (Strings.isNullOrEmpty(cityCode)) {
            return false;
        }
        try {
            CityItem cityItem = cityClient.getCityItemByCityCode(cityCode);
            if (null == cityItem) {
                return false;
            }
            return StringUtils.equalsIgnoreCase("sight", cityItem.getProperty("st"));
        } catch (Exception e) {
            log.warn("获取城市是否景区失败, CITY_CLIENT服务异常", e);
            QMonitor.recordOne("CityClientServiceError");
            return false;
        }
    }

    /**
     * 获得地级市
     * @param cityCode
     * @return
     */
    public static String getRootCityCode(String cityCode) {
        String rootCityCode = "";
        if (Strings.isNullOrEmpty(cityCode)) {
            return rootCityCode;
        }
        try {
            CityItem cityItem = cityClient.getCityItemByCityCode(cityCode);
            if (null == cityItem) {
                return rootCityCode;
            }
            rootCityCode = cityItem.getRootCityCode();
        } catch (Exception e) {
            log.warn("获取Root城市失败, CITY_CLIENT服务异常", e);
            QMonitor.recordOne("GetRootCityCodeError");
            rootCityCode = "";
        }
        return rootCityCode;
    }


	/**
	 * 把cityClient暴露出来, 让需要的地方直接用.
	 * @return
     */
	public static CityClient getCityClient() {
		return cityClient;
	}
}
