package com.qunar.search.common.cache;

import com.qunar.hotel.city.CityClient;
import com.qunar.hotel.city.CityItem;
import com.qunar.search.common.util.HttpClientUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.AppServer;
import qunar.ServiceFinder;
import qunar.hc.QunarClient;
import qunar.management.ServerManagement;

import java.util.Map;

public class HlistCityClient extends CityClient {
	private static final Logger log = LoggerFactory.getLogger(HlistCityClient.class);
	private static final QunarClient httpClient = HttpClientUtils.getHlistCityClient();

	@Override
	public String getHttpContent(String url) {
		String content = null;
		ServerManagement serviceInstance = ServiceFinder.getService(ServerManagement.class);
		AppServer server = serviceInstance.getAppConfig().getServer();
		String token = server.getToken();
		try {
			HttpGet get = new HttpGet(url);
			get.addHeader("Q-Server-Token", token);
			HttpResponse response = httpClient.execute(get);
			HttpEntity entity = null;
			try {
				StatusLine status = response.getStatusLine();
				entity = response.getEntity();
				if (status != null && status.getStatusCode() == 200) {
					content = EntityUtils.toString(entity, "UTF-8");
					entity = null;
				}
			} catch (Exception e) {
				log.warn("读取http接口数据失败，url:" + url, e);
			} finally {
				EntityUtils.consume(entity);
			}
		} catch (Exception e) {
			log.warn("获取http接口数据失败，url:" + url, e);
		}
		return StringUtils.trimToEmpty(content);
	}

	@Override
	public void createCustomDataOnRefresh(Map<String, CityItem> map) {

	}

}
