package com.qunar.search.common.bean;

import java.io.Serializable;

/**
 * 酒店标签信息
 *
 * @author: luming.lv
 * @date: 2015-07-29
 */
public class HotelLabel implements Serializable {

    private static final long serialVersionUID = -103937434709346540L;
    private int labelType;
    private String labelContent;
    private String labelDetailDesc;
    private double weight;

    public HotelLabel(int labelType, String labelContent, String labelDetailDesc) {
        this.labelType = labelType;
        this.labelContent = labelContent;
        this.labelDetailDesc = labelDetailDesc;
    }

    public int getLabelType() {
        return labelType;
    }

    public void setLabelType(int labelType) {
        this.labelType = labelType;
    }

    public String getLabelContent() {
        return labelContent;
    }

    public void setLabelContent(String labelContent) {
        this.labelContent = labelContent;
    }

    public String getLabelDetailDesc() {
        return labelDetailDesc;
    }

    public void setLabelDetailDesc(String labelDetailDesc) {
        this.labelDetailDesc = labelDetailDesc;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }
}
