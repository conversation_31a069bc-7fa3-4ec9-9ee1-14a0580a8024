package com.qunar.search.common.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-08-03 下午7:12
 * @DESCRIPTION 用户画像数据
 **/
@Data
public class UserProfile {
    /**
     * 性别
     */
    int gender = -1;

    /**
     * 信赖用户标识，未知：0，新用户：1，老用户：2
     */
    int newOldUserFlag = 0;

    /**
     * 年龄
     */
    int age = -1;

    /**
     * 价格敏感度
     */
    int sensitiveScore = -1;

    /**
     * 偏好同城订单
     */
    double preferLocalCity;

    /**
     * 偏好异地订单
     */
    double preferDiffCity;

    /**
     * 是否安装美团
     */
    int hasMeituan = -1;

    /**
     * 是否安装携程
     */
    int hasCtrip = -1;

    /**
     * 是否安装艺龙
     */
    int hasElong = -1;

    /**
     * 是否安装精品
     */
    int hasCompet = -1;

    /**
     * 节假日偏好
     */
    double preferHoliday;

    /**
     * 周末偏好
     */
    double preferWeekend;

    /**
     * 一年内机票酒店交叉标签
     */
    String flightUser;

    /**
     * 一年内火车票酒店交叉标签
     */
    String trainUser;

    /**
     * 一年内门票酒店交叉标签
     */
    String ticketUser;

    /**
     * 一年内度假酒店交叉标签
     */
    String vacationUser;

    /**
     * 常住地
     */
    String oftenCity;
}
