package com.qunar.search.common.bean;

import com.google.common.collect.Lists;
import com.google.common.collect.Range;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: ruheng.liu
 * @Date: 19-3-27 下午7:33
 * @desc:
 */
@Getter
@Setter
public class AlgoStrategyConfigInfo {
    /**
     * 各个策略模型app及版本配置
     */
    Map<String, AlgoXgbModelParam> algoXgbModelParamMap = Collections.emptyMap();

    /**
     * 算法惩罚策略参数
     */
    AlgoPunishParam algoPunishParam = AlgoPunishParam.getDefaultInstance();

    /**
     * 模型节假日价格加权开关及日期配置
     */
    private boolean algoPriceWeightSwitch;
    private List<Range> holidayWeightFromToDate = Lists.newArrayList();

    public AlgoXgbModelParam getAlgoXgbModelParam(String key) {

        AlgoXgbModelParam xgbModelParam = algoXgbModelParamMap.get(key);

        if (Objects.isNull(xgbModelParam)) {
            return AlgoXgbModelParam.getEmptyInstance();
        }

        return xgbModelParam;
    }
}