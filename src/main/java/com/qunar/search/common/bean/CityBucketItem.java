package com.qunar.search.common.bean;

import com.qunar.search.common.parser.impl.HotelTypeFilterParser;
import com.qunar.search.common.util.HotelTypeUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;

public class CityBucketItem implements Iterable<HotelInfoWrapper> {

    private final AtomicLong lastModified = new AtomicLong(System.currentTimeMillis());
    /**
     * 这个城市下有那些酒店,CopyOnWriteArrayList保证并发同步
     */
    private final List<HotelInfoWrapper> hotelList = new CopyOnWriteArrayList<HotelInfoWrapper>();
    //Map<hotelTypeCode, (hotelTypeCode,hotelTypeVlue,num)>
    private final Map<String, HotelTypeWrapper> hotelTypeWrapperMap = new ConcurrentHashMap<String, HotelTypeWrapper>();

    @Override
    public Iterator<HotelInfoWrapper> iterator() {
        return hotelList.iterator();
    }

    public void addHotel(HotelInfoWrapper wrapper) {
        hotelList.add(wrapper);
        addHotelTypeWrapper(wrapper);//酒店类型
        lastModified.set(System.currentTimeMillis());
    }

    public void removeHotel(HotelInfoWrapper wrapper) {
        hotelList.remove(wrapper);
        lastModified.set(System.currentTimeMillis());
    }

    public boolean containsHotel(HotelInfoWrapper wrapper) {
        return hotelList.contains(wrapper);
    }

    public int hotelCount() {
        return hotelList.size();
    }

    public boolean hasHotel() {
        return !hotelList.isEmpty();
    }

    public long getLastModified() {
        return lastModified.get();
    }

    public void setLastModified(long lastModified) {
        this.lastModified.set(lastModified);
    }

    private void addHotelTypeWrapper(HotelInfoWrapper wrapper) {
        String[] codeList = wrapper.info.getHotelType();
        if(codeList == null || codeList.length == 0) return;
        for (String code : codeList) {
            HotelTypeWrapper typeWrapper = hotelTypeWrapperMap.get(code);
            if (typeWrapper == null) {
                String value = HotelTypeUtils.getHotelTypeDefinition().get(code);
                if (value == null) continue;
                typeWrapper = new HotelTypeWrapper(code.trim(), value.trim());
            }
            typeWrapper.addCount();
            hotelTypeWrapperMap.put(code, typeWrapper);
        }
    }

    public List<HotelTypeWrapper> getHotelTypes() {
        List<HotelTypeWrapper> list = new ArrayList<HotelTypeWrapper>();
        boolean containsEcoChainHotel = false;
        boolean containsComforLuxHotel = false;

        for (Map.Entry<String, HotelTypeWrapper> entry : hotelTypeWrapperMap.entrySet()) {
            if (entry.getKey().equals(HotelTypeFilterParser.RANK_ECOMONIC_CHAIN_HOTEL)) {
                containsEcoChainHotel = true;
            } else if (entry.getKey().equals(HotelTypeFilterParser.RANK_COMFORT_LUXURY_HOTEL)) {
                containsComforLuxHotel = true;
            }
            list.add(entry.getValue());
        }
        if (!containsEcoChainHotel) {
            list.add(new HotelTypeWrapper(HotelTypeFilterParser.RANK_ECOMONIC_CHAIN_HOTEL,
                    HotelTypeFilterParser.RANK_ECOMONIC_CHAIN_HOTEL_V));
        }
        if (!containsComforLuxHotel) {
            list.add(new HotelTypeWrapper(HotelTypeFilterParser.RANK_COMFORT_LUXURY_HOTEL,
                    HotelTypeFilterParser.RANK_COMFORT_LUXURY_HOTEL_V));
        }
        return list;
    }

    public List<HotelTypeWrapper> getPcHotelTypes() {
        List<HotelTypeWrapper> list = new ArrayList<HotelTypeWrapper>();
        for (Map.Entry<String, HotelTypeWrapper> entry : hotelTypeWrapperMap.entrySet()) {
            list.add(entry.getValue());
        }
        return list;
    }

}
