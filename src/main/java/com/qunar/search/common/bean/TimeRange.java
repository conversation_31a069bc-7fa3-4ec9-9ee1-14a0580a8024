package com.qunar.search.common.bean;

import com.qunar.search.common.util.JsonUtils;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 时间范围
 */
@Getter
@ToString
public class TimeRange {


    /**
     * 开始
     */
    private LocalTime start;
    /**
     * 结束
     */
    private LocalTime end;

    public TimeRange(){}

    public TimeRange(String startStr, String endStr) {
        this.start = LocalTime.parse(startStr);
        this.end = LocalTime.parse(endStr);
    }

    private void setStart(String startStr){
        this.start = LocalTime.parse(startStr);
    }

    private void setEnd(String endStr){
        this.end = LocalTime.parse(endStr);
    }

    /**
     * 判断当前时间是否在限流时间范围内，前闭后开
     * 支持（"12:00", "02:00"）跨天配置
     * @param time 时间
     */
    public boolean contains(LocalTime time) {
        if (end.isAfter(start)) {
            return !time.isBefore(start) && time.isBefore(end);
        } else {
            // 处理跨天时间
            return !time.isBefore(start) || time.isBefore(end);
        }
    }

    public static void main(String[] args) {
        String s = "[{\n" +
                "  \"end\" : \"08:00\",\n" +
                "  \"start\" : \"05:00\"\n" +
                "},{\n" +
                "  \"end\" : \"10:00\",\n" +
                "  \"start\" : \"08:00\"\n" +
                "}]";

        List<TimeRange> configs = JsonUtils.fromJson(s,
                JsonUtils.buildCollectionType(List.class, TimeRange.class));
        System.out.println(configs);
    }


}
