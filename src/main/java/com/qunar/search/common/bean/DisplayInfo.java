package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: yunpeng.wu
 * @Date: 2018/8/28 12:29 AM
 */
public class DisplayInfo {

    @JsonProperty("Order")
    private int order;
    @JsonProperty("IsShow")
    private String isShow;
    @JsonProperty("IsSupportFilter")
    private String isSupportFilter;
    @JsonProperty("DateRangeStr")
    private DateRangeStr dateRangeStr;

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public String getIsSupportFilter() {
        return isSupportFilter;
    }

    public void setIsSupportFilter(String isSupportFilter) {
        this.isSupportFilter = isSupportFilter;
    }

}
