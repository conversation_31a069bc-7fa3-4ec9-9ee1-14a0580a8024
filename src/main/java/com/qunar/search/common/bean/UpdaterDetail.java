package com.qunar.search.common.bean;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * updater的详细信息
 *
 * <AUTHOR>
 * @date 2020/6/19
 */
@Data
@Builder
public class UpdaterDetail {

    /**
     * updater名称，这里会拼接上加了@SearchDataTask注解的方法名
     */
    private String name;

    /**
     * 监控tag值
     */
    private String tag;

    /**
     * 定时任务执行间隔
     */
    private long fixedDelay;

    /**
     * 定时任务执行间隔的单位
     */
    private TimeUnit timeUnit;

    /**
     * 当前版本
     */
    private Object version;

    /**
     * 当前数据大小
     */
    private int size;

    /**
     * 最后一次执行任务的时间
     */
    private String lastExecuteTime;

    /**
     * 最后一次执行任务所消耗的时间，单位是ms
     */
    private long costTime;

    /**
     * 该定时任务对应的DB表是否有版本号
     */
    private boolean hasVersion;

    /**
     * 是否需要监控
     */
    private boolean needMonitor;

    /**
     * 调度是否开启。true:开启，false:关闭
     */
    private boolean enableSchedule;

    /**
     * 定时任务的中文描述
     */
    private String desc;

    /**
     * 关联的DB的表名
     */
    private String tableName;

    /**
     * 版本号字段名
     */
    private String versionField;

    /**
     * DB里存在的历史版本
     */
    private List<Integer> historyVersion;

    /**
     * 该定时任务最后一次执行时，数据校验是否通过。true:通过，false:不通过
     */
    private boolean verifyPassed;

}
