package com.qunar.search.common.bean;

public class Promotion {

    private String wi = "";
    private String pt = "";
    private String pd = "";
    private int pr = 0;
    private String bu = "";
    private String cu = "";
    private String wiN = "";

    /*
     * attr fm: 1 from hotelVip 2 from ppc 4 direct
     */
    private int fm = 0;
    private int od = 0;

    public int getOd() {
        return od;
    }

    public void setOd(int od) {
        this.od = od;
    }

    public String getWi() {
        return wi;
    }

    public void setWi(String wi) {
        this.wi = wi;
    }

    public int getFM() {
        return fm;
    }

    public void setFM(int fm) {
        this.fm = fm;
    }

    public String getWiN() {
        return wiN;
    }

    public String getPt() {
        return pt;
    }

    public void setPt(String pt) {
        this.pt = pt;
    }

    public String getPd() {
        return pd;
    }

    public void setPd(String pd) {
        this.pd = pd;
    }

    public int getPr() {
        return pr;
    }

    public void setWiN(String wiN) {
        this.wiN = wiN;
    }

    public void setPr(int pr) {
        this.pr = pr;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getCu() {
        return cu;
    }

    public void setCu(String cu) {
        this.cu = cu;
    }

    @Override
    public String toString() {
        return String.format("wi:%s,pt:%s,pd:%s,pr:%d,bu:%s,cu:%s,wiN:%s", wi, pt, pd, pr, bu, cu, wiN);
    }
}
