package com.qunar.search.common.bean;

import com.qunar.search.common.enums.HotelAdvertiseEnum;

/**
 * HotelAdvertiseAtrrs
 *
 * 直通车广告酒店信息
 * <AUTHOR>
 * @date 16-10-14.
 */
public class HotelAdvertiseAtrrs {

    /**
     * 酒店替换信息
     * 0，正常酒店
     * 1，替换的广告酒店
     * 2，被替换的相似酒店
     */
    private int changeInfo = HotelAdvertiseEnum.ChangeInfo.NORMAL.getType();
    /**
     * 权重
     * 来源qconfig配置，未配置走默认权重
     */
    private int weight = 1;
    /**
     * 替换前索引
     */
    private int fromIndex = -1;
    /**
     * 替换后的索引
     */
    private int toIndex = -1;

    public int getChangeInfo() {
        return changeInfo;
    }

    public void setChangeInfo(int changeInfo) {
        this.changeInfo = changeInfo;
    }

    public int getFromIndex() {
        return fromIndex;
    }

    public void setFromIndex(int fromIndex) {
        this.fromIndex = fromIndex;
    }

    public int getToIndex() {
        return toIndex;
    }

    public void setToIndex(int toIndex) {
        this.toIndex = toIndex;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }
}
