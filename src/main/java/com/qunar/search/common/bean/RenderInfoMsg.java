package com.qunar.search.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 获取Render报价后，构造Render报价QMQ消息
 * 最后用于缓存报价
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RenderInfoMsg {

    /**
     * @see com.qunar.search.common.enums.RenderInfoSourceEnum
     */
    private int sourceCode;
    /**
     * 入店时间
     */
    private String fromDate;
    /**
     * 离店时间
     */
    private String toDate;
    /**
     * 身份
     */
    private String identity;
    /**
     * 用户拥有券的key
     */
    private String couponKey;

    /**
     * 需要缓存的 Render 报价信息
     */
    private List<RenderCoreInfo> renderCoreInfos;

    /**
     * 新老客身份分类
     */
    private Integer newOrOldFlag;

    /**
     * 积分换算出的价格
     */
    private Integer multiPointOfPrice;
    /**
     * 该条消息的时间戳，精确到秒
     */
    private long timestamp;

    /**
     * 用户拥有的积分
     */
    private Integer userMultiPoint;
}
