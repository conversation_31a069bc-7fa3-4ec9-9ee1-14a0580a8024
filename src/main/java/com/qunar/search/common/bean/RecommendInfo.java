package com.qunar.search.common.bean;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 推荐信息
 */

public class RecommendInfo implements Serializable {
	/**
	 * 序列号
	 */
	private static final long serialVersionUID = -1406234203941796332L;
	private List<RecommendItem> recommendItems= new LinkedList<RecommendItem>();
	public List<RecommendItem> getRecommendItems() {
		return recommendItems;
	}
	public void setRecommendItems(List<RecommendItem> recommendItems) {
		this.recommendItems = recommendItems;
	}
	/**
	 * 推荐词属性
	 */
	public static class RecommendItem implements Comparable<Object>, Serializable {
		/**
		 * 序列号
		 */
		private static final long serialVersionUID = 3390498566590493590L;
		private String suggestion; //推荐词
	    private int type; //推荐词类别 : 1:价格区间;2:档次;3:品牌;4:商圈
	    private int rank; //推荐词在该类别排名
	    private String display; //推荐词展示文案
	    private int priority; //该类别推荐词在suggestion页显示优先级
	    public int getPriority() {
			return priority;
		}
		public void setPriority(int priority) {
			this.priority = priority;
		}
		public String getDisplay() {
			return display;
		}
		public void setDisplay(String display) {
			this.display = display;
		}
		public String getSuggestion() {
			return suggestion;
		}
		public void setSuggestion(String suggestion) {
			this.suggestion = suggestion;
		}
		public int getType() {
			return type;
		}
		public void setType(int type) {
			this.type = type;
		}
		public int getRank() {
			return rank;
		}
		public void setRank(int rank) {
			this.rank = rank;
		}
		public boolean equals(Object obj) {
			if (obj == null) {
				return false;
			}
			if (obj instanceof RecommendItem) {
				RecommendItem ri = (RecommendItem) obj;
				if (StringUtils.isEmpty(ri.getSuggestion())) {
					return false;
				} else if (ri.getSuggestion().equals(this.getSuggestion())) {
					return true;
				}
			}
			return false;
		}

		public int hashCode() {
			return this.getSuggestion().hashCode();
		}
		
		@Override
		public int compareTo(Object obj) {
			if (obj == null) {
				return 0;
			}
			if (obj instanceof RecommendItem) {
				RecommendItem recItem = (RecommendItem) obj;
				if (this.getPriority() < recItem.getPriority()) {
					return -1;
				} else if (this.getPriority() > recItem.getPriority()) {
					return 1;
				}
			}
			return 0;
		}
	}
}
