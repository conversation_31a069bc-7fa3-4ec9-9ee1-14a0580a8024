package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

/**
 * 实时数据--用户进入到详情页查看的酒店数据
 */
@Slf4j
public class DetailShow {

    private static final DateTimeFormatter YMDHMS = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 酒店 seq
     */
    @Getter @Setter
    private String seq;

    /**
     * 点击时间
     */
    @JsonIgnore
    @Getter
    private DateTime time;

    /**
     * 1、原始数据是actionTime, String 不方便后续使用
     * 2、重写set get 方法，解析时间到DateTime对象
     */
    public String getActionTime() {
        if (null == YMDHMS) {
            return StringUtils.EMPTY;
        }
        return time.toString(YMDHMS);
    }

    public void setActionTime(String actionTime) {
        if (StringUtils.isEmpty(actionTime)) {
            return;
        }
        try {
            this.time = DateTime.parse(actionTime, YMDHMS);
        } catch (Exception e) {
            log.error("时间转换错误", e);
        }
    }

    /**
     * 用户从列表也点击进入到详情页的请求列表
     */
    public static class ListClick extends DetailShow {

        /**
         * 列表展示价格
         */
        @Getter @Setter
        private int price;
    }
}
