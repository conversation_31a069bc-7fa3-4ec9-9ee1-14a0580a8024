package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qunar.search.common.gis.GLatLng;

import java.io.Serializable;

/**
 * 地标信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-8-2.
 */
public class PoiInfo implements Serializable{

    private static final long serialVersionUID = 1950307996255625835L;

    private String name;      //地标名称
	private GLatLng gLatLng;
	/**
	 * poi类型，http://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=167039269
	 */
	private String type;
	private Integer defaultDistance;

    public PoiInfo() {

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

	public String getLatlng() {
		return gLatLng == null ? "" : gLatLng.toString();
	}

    @JsonIgnore
	public GLatLng getgLatLng() {
		return gLatLng;
	}

	public void setgLatLng(GLatLng gLatLng) {
		this.gLatLng = gLatLng;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getDefaultDistance() {
		return defaultDistance;
	}

	public void setDefaultDistance(Integer defaultDistance) {
		this.defaultDistance = defaultDistance;
	}
}
