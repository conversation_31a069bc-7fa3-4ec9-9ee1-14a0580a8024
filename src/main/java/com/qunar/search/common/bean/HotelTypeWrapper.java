package com.qunar.search.common.bean;

import java.util.concurrent.atomic.AtomicLong;

public class HotelTypeWrapper {
    private String value;
    private String key;
    private AtomicLong count = new AtomicLong();

    public HotelTypeWrapper(String key, String value) {
        this.value = value;
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public AtomicLong getCount() {
        return count;
    }

    public void addCount() {
        this.count.addAndGet(1L);
    }
}
