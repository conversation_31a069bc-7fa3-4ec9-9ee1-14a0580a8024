package com.qunar.search.common.bean;

import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.enums.HotelVariableFeatureType;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;

/** 把一些酒店的统计相关的数据放在这个字段里面.
 * Created by fangxue.zhang on 2016/9/1.
 */
@Data
public class HotelStat {

    /**
     * one hour in millisecond
     */
    private static final long ONE_HOUR = 3600000L;

    /**
     * 酒店seq
     */
    private String hotelSeq;

    /**
     * 酒店质量分v2
     */
    private double qualityScoreV2;

    /**
     * 收益
     */
    private double profit;

	/**
     * 对象创建时间, 用来控制删除空对象的逻辑, 两小时之前添加的才能删除.
     */
    private int serviceScore;

	/**
	 * 酒店诚信分，正常值大于0
	 */
	private double honestyScore = Double.NaN;

    /**
     * 酒店离线统计数据map
     */
    private EnumMap<FeatureType, Double> offlineFeature;

    /**
     * 酒店可变长离线统计数据map
     */
    private EnumMap<HotelVariableFeatureType, Object> offlineVariableFeature;

    /**
     * 算法 embedding v1 数据
     */
    private List<Double> algoEmbeddingFeature = Collections.emptyList();

    /**
     * 算法 embedding v2 数据
     */
    private List<Double> algoEmbeddingFeatureV2 = Collections.emptyList();

    /**
     * 粗排模型生成的hotel embedding
     */
    private float[] preRankHotelEmbedding ;

    /**
     * hotel_id (向量粗排使用)
     */
    private int hotelId;

    /**
     * 酒店最近一个月总单量
     */
    private int orderNums;

    /**
     * 物理房型对应的单量(最近一个月)
     */
    private HashMap<Long,Integer> physicalHouseOrders;

    /**
     * 酒店的点击下单关联性
     * 详细解释见：https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=558568724
     */
    private String hotelClickOrderRates;


    /**
     * 创建时间
     */
    private long createTime;

    public HotelStat(String hotelSeq) {
        this.hotelSeq = hotelSeq;
        createTime = System.currentTimeMillis();
    }

	/**
     * 检查这个对象是不是所有字段都是0, 如果是, 就判断为一个无效对象.
     * @return
     */
    public boolean isInvalid(){

        // by fangxue.zhang 如果距离创建这个对象还没有超过一小时, 那么就不要删除这个对象.
        //为了解决并发delete 和insert的问题.
        if(System.currentTimeMillis() < createTime + ONE_HOUR){
            return false;
        }

        return qualityScoreV2 == 0.0d &&
                profit == 0.0d &&
                serviceScore == 0.0d &&
                MapUtils.isEmpty(offlineFeature) &&
                MapUtils.isEmpty(offlineVariableFeature) &&
                CollectionUtils.isEmpty(algoEmbeddingFeature) &&
                CollectionUtils.isEmpty(algoEmbeddingFeatureV2) &&
                ArrayUtils.isEmpty(preRankHotelEmbedding);
    }
}
