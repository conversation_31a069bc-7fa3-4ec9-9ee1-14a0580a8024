package com.qunar.search.common.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 广告信息
 *
 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=384600342
 *
 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=433464752
 **/
@Data
public class AdMsg {

    /**
     * 当前酒店广告出价
     */
    private BigDecimal bidPrice;

    /**
     * 下一位酒店出价
     */
    private BigDecimal secondBidPrice;

    /**
     * 该酒店模型计算的点击率
     */
    private double ctr;

    /**
     * 下一家酒店的点击率
     */
    private double nextCtr;

    /**
     * 当前预估CTCVR
     */
    private double ctcvr;

    /**
     * 下一位预估CTCVR
     */
    private double nextCtcvr;

    /**
     * 当前广告类型。CPC、CPS等等
     */
    private String adType;

    /**
     * 如有替换记录原始广告方式，否则为空
     */
    private String originalPromotionType;

    /**
     * 下一位广告类型
     */
    private String nextAdType;

    private String nextPromotionType;

    /**
     * 当前ecpm
     */
    private double ecpm;

    /**
     * 下一位ecpm
     */
    private double nextEcpm;

    /**
     * 是否有下一位。true：有，false：没有
     */
    private boolean hasNext;

    /**
     * 搜索天数。离店日期 - 入住日期
     */
    private int searchDays;

    /**
     * 搜索类型 com.qunar.search.enums.QuerySearchType
     */
    private int searchType;

    /**
     * 竞争圈广告酒店购买的定向酒店seq。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=629966392
     */
    private String targetHotelSeq;

    /**
     * 广告场景分类。1：唯一搜索推荐，2：酒店详情页附近酒店推荐。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=629966392
     */
    private int adSceneType;

}
