package com.qunar.search.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 获取Render报价后，构造Render报价QMQ消息
 * 最后用于缓存报价
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RenderCoreInfo {

    /** 酒店 seq */
    private String seq;
    /** 佣金 */
    private String commission;
    /** 商家券后卖价 */
    private String priceAfterMerchant;
    /** 划线价 */
    private String dispOrgPrice;
    /** 实时最低价 **/
    private int renderMinPrice;
    /** 最终 beat 值 */
    private String beat;
    /** 新版 beat 值 */
    private String beatV2;
    /** 是否有效beat */
    private Boolean isValidBeat;
    /** 原始底价 */
    private int basePrice;
    /** 房型 */
    private String roomId;
    /** 优惠明细 */
    private List<DiscountsDetail> discountsDetail;
    /** 缓存报价price的traceId */
    private String cachePriceTraceId;

    /** 缓存报价佣金的traceId */
    private String cacheCommissionTraceId;
    /** 缓存的最低价 */
    private int cacheMinPrice;
    /** 缓存的佣金 */
    private Integer cacheCommission;

    /**
     * 是否是多倍积分酒店
     */
    private Boolean multiPointHotel;

    /*
     * 缓存报价命中的缓存级别
     */
    private String renderCacheLevel;


    /**
     * 命中render报价缓存真实key
     */
    private String redisHitKey;

    /**
     * render报价缓存第一次尝试的key
     */
    private String redisFirstTryKey;

    /**
     * render报价缓存第一次尝试的key的级别
     */
    private String redisFirstTryLevel;

}
