package com.qunar.search.common.bean;

import java.io.Serializable;

public class FastFilterItem implements Serializable {

	// 模型分
	public double modelScore = 0;

    public final String filterName;

    public FastFilterItem() {
        this.filterName = "";
    }

    public FastFilterItem(String filterName) {
        this.filterName = filterName;
    }

    public String getFilterName() {
        return this.filterName;
    }

    public void setModelScore(double modelScore) {
        this.modelScore = modelScore;
    }

    public double getModelScore() {
        return this.modelScore;
    }
}