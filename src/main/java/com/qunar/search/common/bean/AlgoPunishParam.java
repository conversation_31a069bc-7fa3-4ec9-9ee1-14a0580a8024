package com.qunar.search.common.bean;

import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlgoPunishParam {

    private final static Logger log = LoggerFactory.getLogger(AlgoPunishParam.class);

    private static AlgoPunishParam defaultAlgoPunishParam;

    /**
     * 惩罚数量
     */
    private int punishNum;

    /**
     * 头图权重
     */
    private double imageIDW = 0;

    /**
     * 评分为0权重
     */
    private double commentScore00W = 0;

    /**
     * 评分大于0小于2.5权重
     */
    private double commentScore25W = 0;

    /**
     * 评分大于2.5小于3.0权重
     */
    private double commentScore30W = 0;

    /**
     * 投诉率
     */
    private double complaintProp = 0;

    /**
     * 投诉率权重
     */
    private double complaintW = 0;

    /**
     * 价格差
     */
    private double priceDiff = 0;

    /**
     * 价格差权重
     */
    private double priceDiffW = 0;

    /**
     * 逐渐废弃上面评分
     */
    List<Triple<Double, Double, Double>> commentScoreRuleList;

    public boolean isContainZero() {
        if (imageIDW == 0 || commentScore00W == 0 || commentScore25W == 0 || commentScore30W == 0
                || complaintProp == 0 || complaintW == 0 || priceDiff == 0 || priceDiffW == 0) {
            return true;
        }

        return false;
    }

    public static AlgoPunishParam getPunishStrategy(Map<String, String> conf) {

        try {
            int changeScoreNum = Integer.valueOf(conf.get("change_score_num"));
            double imageIDW = Double.valueOf(conf.get("image_id_w")).doubleValue();
			double commentScore00W = Double.valueOf(conf.get("comment_score_00w")).doubleValue();
			double commentScore25W = Double.valueOf(conf.get("comment_score_25w")).doubleValue();
			double commentScore30W = Double.valueOf(conf.get("comment_score_30w")).doubleValue();
			double complaintProp = Double.valueOf(conf.get("complaint_prop")).doubleValue();
			double complaintW = Double.valueOf(conf.get("complaint_w")).doubleValue();
			double priceDiff = Double.valueOf(conf.get("price_diff")).doubleValue();
			double priceDiffW = Double.valueOf(conf.get("price_diff_w")).doubleValue();

            return new AlgoPunishParam()
                    .builder()
                    .punishNum(changeScoreNum)
                    .imageIDW(imageIDW)
                    .commentScore00W(commentScore00W)
                    .commentScore25W(commentScore25W)
                    .commentScore30W(commentScore30W)
                    .complaintProp(complaintProp)
                    .complaintW(complaintW)
                    .priceDiff(priceDiff)
                    .priceDiffW(priceDiffW)
                    .build();
        } catch (Exception e) {
            log.error("模型配置文件惩罚参数解析出emptyAlgoPunishParam错。", e);
            return getDefaultInstance();
        }
    }

    /**
     * 返回一个缺省的参数值
     */
    public static AlgoPunishParam getDefaultInstance() {
        if (defaultAlgoPunishParam == null) {
            defaultAlgoPunishParam = new AlgoPunishParam()
                    .builder()
                    .punishNum(1500)
                    .imageIDW(0.1)
                    .commentScore00W(0.01)
                    .commentScore25W(0.15)
                    .commentScore30W(0.25)
                    .complaintProp(0.1)
                    .complaintW(0.9)
                    .priceDiff(2000)
                    .priceDiffW(0.75)
                    .build();
        }
        return defaultAlgoPunishParam;
    }

}
