package com.qunar.search.common.bean;

/**
 * 桶实体类
 *
 */
public class BucketEntry {

	//桶名称
	String name;
	//是否2，4，6推荐
	//fixme no public
	public boolean promotion;

	private String modelInstance;

	private String modelName;
	//算法精排策略
	private String algoInstance;

	public BucketEntry() {
	}

	public BucketEntry(String name, boolean promotion) {
		super();
		this.name = name;
		this.promotion = promotion;
	}

	public String getAlgoInstance() {
		return algoInstance;
	}

	public void setAlgoInstance(String algoInstance) {
		this.algoInstance = algoInstance;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isPromotion() {
		return promotion;
	}

	public void setPromotion(boolean promotion) {
		this.promotion = promotion;
	}

	public String getModelInstance() {
		return modelInstance;
	}

	public void setModelInstance(String modelInstance) {
		this.modelInstance = modelInstance;
	}


	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	@Override
	public int hashCode() {
		return name.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BucketEntry other = (BucketEntry) obj;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equals(other.name))
			return false;
		return true;
	}
}