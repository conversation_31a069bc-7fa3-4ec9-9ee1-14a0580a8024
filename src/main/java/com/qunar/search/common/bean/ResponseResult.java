package com.qunar.search.common.bean;

import com.qunar.search.common.enums.ErrorEnum;

/**
 * http请求返回解析后结果
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-3-17.
 */
public class ResponseResult<T> {
    private int cancelNum;       // 取消次数
    private T data;              // 解析后结果
    private boolean ret;
    private ErrorEnum errorEnum;

    public ResponseResult() {

    }

    public int getCancelNum() {
        return cancelNum;
    }

    public void setCancelNum(int cancelNum) {
        this.cancelNum = cancelNum;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isRet() {
        return ret;
    }

    public void setRet(boolean ret) {
        this.ret = ret;
    }

    public ErrorEnum getErrorEnum() {
        return errorEnum;
    }

    public void setErrorEnum(ErrorEnum errorEnum) {
        this.errorEnum = errorEnum;
    }
}
