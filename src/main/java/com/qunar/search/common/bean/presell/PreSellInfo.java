package com.qunar.search.common.bean.presell;

import lombok.*;

import java.util.Set;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/12/6
 特价预售产品
 */
@Data
public class PreSellInfo {

	private String cityCode;

	private int productId;

	private String thirdProductId;

	private String productName;

	private String name;

	private String productType;

	private String supplierName;

	private String supplierCode;

	private double lon;

	private double lat;

	//状态 1在线，9下线
	private int status;

	/**
	 * 商品销量
	 */
	private int saleAmount;

	/**
	 * 券最大使用数量
	 */
	private int ticketMaxUseNum;

	/**
	 * 该商品相似产品集合，一般是同一商品的不同规格
	 */
	private Set<Integer> similarityProductIdSet;

	/**
	 * 酒店产品关联的seq
	 */
	private String hotelSeq;

	/**
	 * 促销产品二级分类
	 */
	private Set<String> secondPredictType;

	@Override
	public boolean equals(Object obj) {
		if (obj == null) {
			return false;
		}
		//如果是同一个对象返回true，反之返回false
		if (this == obj) {
			return true;
		}

		//判断是否类型相同
		if (this.getClass() != obj.getClass()) {
			return false;
		}
		PreSellInfo info = (PreSellInfo) obj;
		return productId == info.productId;
	}
	@Override
	public int hashCode(){
		return productId;
	}
}
