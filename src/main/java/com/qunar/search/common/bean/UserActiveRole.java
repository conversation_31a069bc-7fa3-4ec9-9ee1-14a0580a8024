package com.qunar.search.common.bean;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;

@Data
public class UserActiveRole {
    /**
     * 限时特惠
     */
    public boolean limitTime = false;
    /**
     * 学生房
     */
    public boolean student = false;
    /**
     * 超返超减
     */
    public boolean includeExcess = false;

    /**
     * 语音报价
     */
    public boolean voiceList = false;
    /**
     * 距离范围内，酒店隐藏报价黑名单List<String> kylin已经删除
     */
//    public List<String> attSeqs = new LinkedList<String>();

    /**
     * terminal Touch
     */
    public boolean touch = false;
    /**
     * 新用户标识
     */
    public boolean newUser = false;
    /**
     * 星券低价
     */
    public boolean needConpon = false;
    /**
     * 是否要车券报价
     */
    public boolean needTaxiCoupon = false;
    /**
     * 五折大促
     */
    public boolean isFivePercent = false;
    /**
     * 五折大促 授信非授信
     */
    public boolean fivePercentIsCredit = false;
    /**
     * 五折大促 新老用户
     */
    public boolean fivePercentNewUser = false;
    /**
     * 五折大促 机火用户
     */
    public boolean planeTrainUser = false;
    /**
     * 有机火红包的用户
     */
    public boolean planeTrainRedBagUser = false;
    /**
     * 签到红包首单
     */
    public boolean isRedBagFirstOrder = false;
    /**
     * 签到红包金额
     */
    public int signInGiftMoney = 0;
    /**
     * 星券黑名单
     */
    public Set<String> starblacklist = new TreeSet<String>();
    /**
     * 酒店特惠（夜宵频道）
     */
    public boolean isLM = false;
    /**
     * 透传用户身份给hprice
     */
    public long userIdentityBit = 0l;
    /**
     * 用户可用绑卡+答题红包最大额度
     */
    public int maxLimitProBalance = -1;
    /**
     * "优惠码分享"url参数activityIds=n1,n2,...解析成的集合
     */
    public Set<String> activityIdsSet = new HashSet<String>();
    /**
     * A2B酒店黑名单（用户已经下过单的酒店seq集合）
     */
    public Set<String> a2bSeqs = new HashSet<String>();
    /**
     * 客户端老版本所有A2B报价
     */
    public boolean a2bShieldAll = false;
    /**
     * 最大距离
     */
    public int maxDistance = -1;
    /**
     * 最小距离
     */
    public int minDistance = -1;
    /**
     * 是否是模拟坐标
     */
    public boolean isLt = false;
    /**
     * 是否是disCover的请求
     */
    public boolean isDiscover = false;
    /**
     * 用户 坐标(google坐标)
     */
    public String latlng = "";
    /**
     * 请求来源
     */
    public String source = "";

    /**
     * 终端类型
     */
    public String querySource = "mob_hotel";

    /**
     * 不同步标识位的user角色
     */
    public long userBit = 0l;

    /**
     * 是否2，4，6推荐
     */
    public boolean promotion = true;

    /**
     * 用户属于哪个桶--逻辑通
     */
    public String isLr = "B";

    /**
     * 用户属于哪一个桶--物理通
     */
    public String realBucket = "B";

    /**
     * 价格abtest的策略
     */
    public String abtest;
    /**
     * 抓取标识：
     * 0. 正常
     * 1. 抓取 （kylin中断正常不会传过来）
     * 2. 疑似抓取
     * 3. 特殊渠道正常
     * 4. 特殊渠道的疑似抓取
     * 5. 未知
     */
    public int crawl;
    /**
     * 标识是否需要获取报价：true需要，false不需要
     */
    public boolean needPrice = true;

}
