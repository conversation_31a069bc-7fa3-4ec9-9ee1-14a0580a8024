package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @author: shuailei.lei
 * @create: 2024-08-20
 * @description: Cps广告酒店收益重排日志
 **/
@Getter
@Setter
@Builder
public class CpsProfitReRankInfo {

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 用户realUid
     */
    private String realUid;

    /**
     * 酒店seq
     */
    private String hotelSeq;

    /**
     * 精排位序
     */
    private int modelSort;

    /**
     * 广告位序
     */
    private int reRankSort;

    /**
     * 原始底价
     */
    private int basePrice;

    /**
     * 普通收益
     */
    private double normalProfit;

    /**
     * 广告收益
     */
    private double adProfit;

    /**
     * 普通佣金
     */
    private double normalCommission;

    /**
     * 广告佣金
     */
    private double adCommission;

    /**
     * 广告费率
     */
    private double adCommissionRate;

    /**
     * cr的值
     */
    private double cr;

    /**
     * ctcvr的值
     */
    private double ctcvr;

    /**
     * 计算酒店普通收益，cr的调节系数
     */
    private double crPower;

    /**
     * 计算酒店的广告收益，ctcvr的调节系数
     */
    private double ctcvrPower;

    /**
     * 计算酒店普通收益，普通佣金的调节系数
     */
    private double normalCommissionPower;

    /**
     * 计算酒店的广告收益，广告佣金的调节系数
     */
    private double adCommissionPower;

    /**
     * 计算酒店总收益，广告收益的系数
     */
    private double adProfitCoefficient;

    /**
     * 开始生效时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionStartTime;

    /**
     * 失效时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionEndTime;
}
