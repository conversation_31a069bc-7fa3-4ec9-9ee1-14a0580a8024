package com.qunar.search.common.bean;

import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-08-03 下午7:13
 * @DESCRIPTION 酒店订单用户统计数据
 **/
@Data
public class HotelUserProfile {

    /**
     * 一年内下单用户性别占比--全量用户矫正
     */
    Map<Integer, Double> genderMap;

    /**
     * 一年内下单用户年龄段占比--全量用户矫正
     */
    Map<Integer, Double> ageMap;

    /**
     * 一年内下单用户商务标签占比--全量用户矫正
     */
    Map<Integer, Double> businessFlagMap;

    /**
     * 一年内下单用户价格敏感度分组占比--全量用户矫正
     */
    Map<Integer, Double> sensitiveScoreMap;

    /**
     * 一年内下单用户是否安装美团占比--全量用户矫正
     */
    Map<Integer, Double> hasMeituanMap;

    /**
     * 一年内下单用户是否安装携程占比--全量用户矫正
     */
    Map<Integer, Double> hasCtripMap;

    /**
     * 一年内下单用户是否安装艺龙占比--全量用户矫正
     */
    Map<Integer, Double> hasElongMap;

    /**
     * 一年内下单用户是否安装竞品占比--全量用户矫正
     */
    Map<Integer, Double> hasCompetMap;

    /**
     * 一年内下单用户机票酒店交叉标签占比--全量用户矫正
     */
    Map<String, Double> flightUserMap;

    /**
     * 一年内下单用户会火车票酒店交叉标签占比--全量用户矫正
     */
    Map<String, Double> trainUserMap;

    /**
     * 一年内下单用户门票酒店交叉标签占比--全量用户矫正
     */
    Map<String, Double> ticketUserMap;

    /**
     * 一年内下单用户度假酒店交叉标签占比--全量用户矫正
     */
    Map<String, Double> vacationUserMap;

    /**
     * 一年内下订单场景下单比例
     */
    Map<String, Double> sceneMap;

    /**
     * 一年内下订单周末下单比例
     */
    Map<String, Double> weekendMap;

    /**
     * 一年内下订单场景节假日比例
     */
    Map<String, Double> holidayMap;
}
