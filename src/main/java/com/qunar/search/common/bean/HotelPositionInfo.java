package com.qunar.search.common.bean;

import com.qunar.search.common.enums.SceneSearch;
import com.qunar.search.common.enums.SortType;

/**
 * HotelPositionInfo
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public class HotelPositionInfo {

    private Integer pos;
    private SceneSearch scene;
    private SortType sortType;
    private Integer pos0To10;
    private Integer pos10To20;
    private Integer pos20To30;
    private Integer pos30To40;
    private Integer pos40To50;
    private Integer pos50To60;
    private Integer pos60To70;
    private Integer pos70To80;
    private Integer pos80To90;
    private Integer pos90To100;

    public Integer getPos0To10() {
        return pos0To10;
    }

    public void setPos0To10(Integer pos0To10) {
        this.pos0To10 = pos0To10;
    }

    public Integer getPos10To20() {
        return pos10To20;
    }

    public void setPos10To20(Integer pos10To20) {
        this.pos10To20 = pos10To20;
    }

    public Integer getPos20To30() {
        return pos20To30;
    }

    public void setPos20To30(Integer pos20To30) {
        this.pos20To30 = pos20To30;
    }

    public Integer getPos30To40() {
        return pos30To40;
    }

    public void setPos30To40(Integer pos30To40) {
        this.pos30To40 = pos30To40;
    }

    public Integer getPos40To50() {
        return pos40To50;
    }

    public void setPos40To50(Integer pos40To50) {
        this.pos40To50 = pos40To50;
    }

    public Integer getPos50To60() {
        return pos50To60;
    }

    public void setPos50To60(Integer pos50To60) {
        this.pos50To60 = pos50To60;
    }

    public Integer getPos60To70() {
        return pos60To70;
    }

    public void setPos60To70(Integer pos60To70) {
        this.pos60To70 = pos60To70;
    }

    public Integer getPos70To80() {
        return pos70To80;
    }

    public void setPos70To80(Integer pos70To80) {
        this.pos70To80 = pos70To80;
    }

    public Integer getPos80To90() {
        return pos80To90;
    }

    public void setPos80To90(Integer pos80To90) {
        this.pos80To90 = pos80To90;
    }

    public Integer getPos90To100() {
        return pos90To100;
    }

    public void setPos90To100(Integer pos90To100) {
        this.pos90To100 = pos90To100;
    }

    public Integer getPos() {
        return pos;
    }

    public void setPos(Integer pos) {
        this.pos = pos;
    }

    public SceneSearch getScene() {
        return scene;
    }

    public void setScene(SceneSearch scene) {
        this.scene = scene;
    }

    public SortType getSortType() {
        return sortType;
    }

    public void setSortType(SortType sortType) {
        this.sortType = sortType;
    }

    @Override
    public String toString() {
        return "HotelPositionInfo{" +

                ", pos=" + pos +
                ", scene=" + scene.getName() +
                ", sortType=" + sortType.getName() +
                ", pos0To10=" + pos0To10 +
                ", pos10To20=" + pos10To20 +
                ", pos20To30=" + pos20To30 +
                ", pos30To40=" + pos30To40 +
                ", pos40To50=" + pos40To50 +
                ", pos50To60=" + pos50To60 +
                ", pos60To70=" + pos60To70 +
                ", pos70To80=" + pos70To80 +
                ", pos80To90=" + pos80To90 +
                ", pos90To100=" + pos90To100 +
                '}';
    }
}
