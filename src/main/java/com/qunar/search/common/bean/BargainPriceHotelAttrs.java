package com.qunar.search.common.bean;

import java.io.Serializable;

/**特价酒店频道需要附加在HotelItem里面的属性.
 * 这里的这些报价, 1是原来twell接口给的报价, 2,是特惠相关的报价.
 * Created by fangxue.zhang on 2016/7/25.
 */
public class BargainPriceHotelAttrs implements Serializable{

    /**
     * 酒店特惠报价
     */
    private int teHuiMarketPrice = Integer.MAX_VALUE;

    /**
     * 特惠价格
     */
    private int teHuiMinPrice = Integer.MAX_VALUE;


    /**
     * 秒杀市场价
     */
    private int seckillMarketPrice = Integer.MAX_VALUE;

    /**
     * 秒杀价格
     */
    private int seckillMinPrice = Integer.MAX_VALUE;

    /**
     * 试睡报价, 有就是0
     */
    private int sleepPrice = Integer.MAX_VALUE;

    public int getTeHuiMarketPrice() {
        return teHuiMarketPrice;
    }

    public void setTeHuiMarketPrice(int teHuiMarketPrice) {
        this.teHuiMarketPrice = teHuiMarketPrice;
    }

    public int getTeHuiMinPrice() {
        return teHuiMinPrice;
    }

    public void setTeHuiMinPrice(int teHuiMinPrice) {
        this.teHuiMinPrice = teHuiMinPrice;
    }

    public int getSeckillMarketPrice() {
        return seckillMarketPrice;
    }

    public void setSeckillMarketPrice(int seckillMarketPrice) {
        this.seckillMarketPrice = seckillMarketPrice;
    }

    public int getSeckillMinPrice() {
        return seckillMinPrice;
    }

    public void setSeckillMinPrice(int seckillMinPrice) {
        this.seckillMinPrice = seckillMinPrice;
    }

    public int getSleepPrice() {
        return sleepPrice;
    }

    public void setSleepPrice(int sleepPrice) {
        this.sleepPrice = sleepPrice;
    }
}
