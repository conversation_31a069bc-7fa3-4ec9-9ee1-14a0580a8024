package com.qunar.search.common.bean;

import com.qunar.search.common.enums.ReRankTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/12/6
 */
@Getter
@Setter
@ToString
public class HotelReRankInfo {

	/**
	 * 重排的类型
	 */
	private ReRankTypeEnum type;

	/**
	 * 重排前的位置
	 */
	private int fromPosition;

	/**
	 * 重排后的位置
	 */
	private int toPosition;

	/**
	 * 直通车酒店的让利率
	 */
	private BigDecimal rate;

	/**
	 * 直通车酒店的加权配置值
 	 */
	private double weight;

}
