package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.qunar.search.common.conf.MobileRankSystemConfig;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.enums.HotelOperatingStatus;
import com.qunar.search.common.enums.HotelStarsType;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.update.UgcInfoUpdater;
import com.qunar.search.common.util.CommonTools;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.Map.Entry;

/**
 * HotelInfo描述酒店的基础数据.
 */

public class HotelInfo {
    private Map<String, String> props = new HashMap<String, String>();

    private int stars = -1;

    private GLatLng ll = null;

    private String imageId = null;

    private int miniRetailPrice;

    public String hotelSEQ;

    private String hotelName;

    private String hotelAddress;

    private boolean hasDangci;

    private boolean isBNB;
    //酒店的营业状态
    private HotelOperatingStatus hotelStatus;
    //酒店档次
    private HotelDangciType hotelDangciType;
    //酒店星级
    private HotelStarsType hotelStarsType;
    //酒店cityTagArr
    private String[] cityTagArr;
    //酒店类型TagArr
    private String[] hotelTypeArr;

    private String[] onlineChannel;

    private String hotelStar = "0";

	//评论数commentIds，主题themeList 为了减少减少内存分配，先解析好
	private Set<String> commentIds;
	/**
	 * 主题，是基础数据里多个属性的聚合, {@link com.qunar.search.common.update.HotelPropertyDict.HotelProperty#THEME}
	 */
	private Set<String> themes;
    // 酒店类型
    private List<String> screenHotelType;

	//酒店商圈 为了减少内存分配，先解析好
	private Set<String> tradingAreaSet;

    /**
     * 酒店tag集合，为了减少内存分配提前解析好
     */
    private Set<String> hotelTagSet;

	private boolean hasInnOnly = false;

    private Map<String, Set<String>> setProps = Maps.newHashMap();

    /**
     * 酒店装修时间距现在的年数
     */
    private Double fitmentYearDiff = 0D;

    /**
     * 基于inlandCommonTag是否包含"24"来判断的是否是新店
     */
    private boolean newHotel;

    /**
     * 同步到的qhotel侧的酒店信息更新时间。是精确到微妙的时间戳。
     */
    private long qhotelUpdateTime;

	public HotelInfo() {
	}

	public String getHotelStar() {
        return hotelStar;
    }

    public void setHotelStar(String hotelStar) {
        this.hotelStar = hotelStar;
    }

    /**
     * used for first time reload
     *
     * @param hotelSEQ
     */
    public HotelInfo(String hotelSEQ) {
        this.hotelSEQ = hotelSEQ;
    }

    public String[] getOnlineChannel() {
        return onlineChannel;
    }

    public void setOnlineChannel(String[] onlineChannel) {
        this.onlineChannel = onlineChannel;

        // 数量少，直接查找
        for (String channel : onlineChannel) {
            // TODO: 常量
            if ("only_inn".equals(channel)) {
                hasInnOnly = true;
                break;
            }
        }
    }

    public boolean hasInnOnly() {
        return hasInnOnly;
    }

    private String convertToString(String[] arr) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (String str : arr) {
            if (i > 0) {
                sb.append("|");
            }
            sb.append(str);
            i++;
        }
        return sb.toString();
    }

    public int getMINIRetailPrice() {
        return this.miniRetailPrice;
    }

    /**
     * 不会有新的重复的
     * reload从hotel_props中得到props(imageId也从hotel_props加载)
     *
     * @param props
     */
    public void reloadHotelNewProps(Map<String, String> props) {
        this.props.putAll(props);
        Iterator<Entry<String, String>> iterator = this.props.entrySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next().getKey();
            if (!props.containsKey(key)) {
                iterator.remove();
            }
        }
    }

    /**
     * 优化后
     *
     * @param name
     * @return
     */
    public String getProperty(String name) {
        if (com.google.common.base.Strings.isNullOrEmpty(name)) {
            return null;
        }
        switch (name) {
            case "hotelName":
                return this.hotelName;
            case "hotelname":
                return this.hotelName;
            case "hoteladdress":
                return this.hotelAddress;
            case "hotelAddress":
                return this.hotelAddress;
            case "gpoint":
                return this.ll == null ? "" : this.ll.getLat() + "," + this.ll.getLng();
            case "imageID":
                return this.imageId;
            case "dangci":
                return hasDangci ? String.valueOf(hotelDangciType.getDangci()) : String.valueOf(HotelDangciType.OTHER.getDangci());
            case "gradeName":
                return hasDangci ? hotelDangciType.getDangciName() : "";
            case "hotelStars":
                return String.valueOf(hotelStarsType.getStar());
            case "OperatingStatus":
                return hotelStatus.getValue();
            case "hotelType":
                return hotelTypeArr == null ? "" : convertToString(hotelTypeArr);
            case "citytag":
                return cityTagArr == null ? "" : convertToString(getCityTag());
            case "CommentScore":
                return String.valueOf(UgcInfoUpdater.getCommentScore(this.hotelSEQ));
            case "COMM_SCORE":
                return String.valueOf(UgcInfoUpdater.getCommentScore(this.hotelSEQ));
            case "CommentCount":
                return String.valueOf(UgcInfoUpdater.getCommentCount(this.hotelSEQ));
            case "COMM_CNT":
                return String.valueOf(UgcInfoUpdater.getCommentCount(this.hotelSEQ));
            case "MiniRetailPrice":
                return String.valueOf(this.miniRetailPrice);
            default:
                return StringUtils.trimToNull(props.get(name));
        }
    }


    public String getProperty(String name, String def) {
        String v = getProperty(name);
        return v == null || "".equals(v) ? def : v;
    }

    public int getStars() {
        return stars;
    }


    public String[] getHotelType() {
        return this.hotelTypeArr;
    }

    @JsonIgnore
    public String[] getCityTag() {
        if (this.cityTagArr == null) {
            String cityTag = CommonTools.getCityFromHotel(hotelSEQ);
            return new String[]{cityTag};
        }
        return this.cityTagArr;
    }

    public GLatLng getLatLng() {
        if (ll == null) {
            return ll;
        } else if (ll.getLat() == 0) {
            return null;
        } else {
            return ll;
        }
    }

    @JsonIgnore
    public String getHotelImage() {
        String domainName = MobileRankSystemConfig.getHotelHeadImageDomainName();
        String suffix = MobileRankSystemConfig.getHotelHeadImageUrlSuffix();
        Preconditions.checkArgument(StringUtils.isNotBlank(domainName), "酒店头图域名不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(suffix), "酒店头图url的后缀不能为空");
        String imgPath = null;
        if (this.imageId != null && !this.imageId.isEmpty()) {
            if (this.imageId.indexOf("/") != -1) {
                imgPath = domainName + this.imageId + suffix;
            }
        }
        return imgPath;
    }

    @JsonIgnore
    public Map<String, String> getAtrributes() {
        return Collections.unmodifiableMap(props);
    }

    public boolean getIsBNB() {
        return isBNB;
    }

    public int getDangci() {
        return hasDangci ? hotelDangciType.getDangci() : 5;
    }

    public GLatLng getLl() {
        return ll;
    }

    public void setLl(GLatLng ll) {
        this.ll = ll;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public int getMiniRetailPrice() {
        return miniRetailPrice;
    }

    public void setMiniRetailPrice(int miniRetailPrice) {
        this.miniRetailPrice = miniRetailPrice;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getHotelAddress() {
        return hotelAddress;
    }

    public void setHotelAddress(String hotelAddress) {
        this.hotelAddress = hotelAddress;
    }

    public boolean isHasDangci() {
        return hasDangci;
    }

    public void setHasDangci(boolean hasDangci) {
        this.hasDangci = hasDangci;
    }


    public HotelOperatingStatus getHotelStatus() {
        return hotelStatus;
    }

    public void setHotelStatus(HotelOperatingStatus hotelStatus) {
        this.hotelStatus = hotelStatus;
    }

    public HotelDangciType getHotelDangciType() {
        return hotelDangciType;
    }

    public void setHotelDangciType(HotelDangciType hotelDangciType) {
        this.hotelDangciType = hotelDangciType;
    }

    public HotelStarsType getHotelStarsType() {
        return hotelStarsType;
    }

    public void setHotelStarsType(HotelStarsType hotelStarsType) {
        this.hotelStarsType = hotelStarsType;
    }

    public String[] getCityTagArr() {
        return cityTagArr;
    }

    public void setCityTagArr(String[] cityTagArr) {
        this.cityTagArr = cityTagArr;
    }

    public String[] getHotelTypeArr() {
        return hotelTypeArr;
    }

    public void setHotelTypeArr(String[] hotelTypeArr) {
        this.hotelTypeArr = hotelTypeArr;
    }

    public Map<String, String> getProps() {
        return props;
    }

    public String getHotelSEQ() {
        return hotelSEQ;
    }

    public void setStars(int stars) {
        this.stars = stars;
    }

    public void setBNB(boolean isBNB) {
        this.isBNB = isBNB;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }


	public void setCommentIds(Set<String> commentIds) {
		this.commentIds = commentIds;
	}

	public Set<String> getCommentIds() {
		return commentIds;
	}

	public Set<String> getThemes() {
		return themes;
	}

	public void setThemes(Set<String> themes) {
		this.themes = themes;
	}

	public Set<String> getTradingAreaSet() {
		return tradingAreaSet;
	}

	public void setTradingAreaSet(Set<String> tradingAreaSet) {
		this.tradingAreaSet = tradingAreaSet;
	}

    public Set<String> getHotelTagSet() {
        return hotelTagSet;
    }

    public void setHotelTagSet(Set<String> hotelTagSet) {
        this.hotelTagSet = hotelTagSet;
    }

    public Set<String> getSetProp(String key) {
        return setProps.get(key);
    }

    public void putSetProp(String key, Set<String> value) {
        this.setProps.put(key, value);
    }

    public Map<String, Set<String>> getSetProps() {
        return setProps;
    }

    public void setSetProps(Map<String, Set<String>> setProps) {
        this.setProps = setProps;
    }

    public Double getFitmentYearDiff() {
        return fitmentYearDiff;
    }

    public void setFitmentYearDiff(Double fitmentYearDiff) {
        this.fitmentYearDiff = fitmentYearDiff;
    }

    public boolean isNewHotel() {
        return newHotel;
    }

    public void setNewHotel(boolean newHotel) {
        this.newHotel = newHotel;
    }

    public List<String> getScreenHotelType() {
        return screenHotelType;
    }

    public void setScreenHotelType(List<String> screenHotelType) {
        this.screenHotelType = screenHotelType;
    }

    public long getQhotelUpdateTime() {
        return qhotelUpdateTime;
    }

    public void setQhotelUpdateTime(long qhotelUpdateTime) {
        this.qhotelUpdateTime = qhotelUpdateTime;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj == null) {
            return false;
        }

        if (obj instanceof HotelInfo) {
            return StringUtils.equals(this.hotelSEQ, ((HotelInfo) obj).hotelSEQ);
        }

        return false;
    }

    @Override
    public int hashCode() {
        return this.hotelSEQ.hashCode();
    }

}

