package com.qunar.search.common.bean;

import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.dc.HotelInfoHolder;
import com.qunar.search.common.enums.FilterType;
import com.qunar.search.common.enums.HotelNameMatchType;
import com.qunar.search.common.enums.MobileRoomStatus;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.constants.CommonConstants.FULL_MATCH_RELATIVITY;
import static com.qunar.search.common.constants.CommonConstants.RELATIVITY;

@Setter
@Getter
public class HotelItem implements Serializable {

    private static final long serialVersionUID = 7120364302616031993L;

    public transient HotelInfo info;
    /**
     * 满房的时候，kylin用的是这个价格字段。
     */
    public volatile int price = Integer.MAX_VALUE;

    //针对钟点房可办理入住时间
    public String transactCheckinTime = null;

    //个性化分值
    public double cityIndivScore = 0;
    //分组算分值
    public double groupScore = 0;
	//个性化分值惩罚系数
	public double punishScore = 1;
	// 模型分
	public double modelScore = 0;
    // 精排模型校准分（用来收益*佣金倒排）
    public Map<String, Double> calibrationScoreMap = Collections.emptyMap();
    // 多目标模型的原始得分（基于abtest配置获取的融合分和校准分的key）
    Map<String, Double> modelOriginScoreMap = Collections.emptyMap();

    /**
     * cr分，基于模型校准分来计算
     */
    private double crScore;

    /**
     * 收益分
     */
    private double profitScore;

	// 欢迎度命中分组索引,3是特金牌+收藏预定+模型topN酒店，2是银牌，1是其他酒店
	public int popularityHitIndex;

    // 广告模型分值
	private double adModelScore;
	// 向量粗排分值
    private double vectorPreRankScore;

    /**
     * 预估的CTCVR(点击率和转化率)
     */
	private double ctcvr;

    // 广告埋点信息--吐传给前端，点击埋点统计价格时使用
    private AdMsg adMsg;

    //距离关键词地标的距离
    public int distance = 0;
    public int normalDistance = 0;

    //无线没有用到
    public double corrDistance = 0; //距离无线传入坐标点的距离
    public double normalCorrDistance = 0; //距离无线传入坐标点的归一化距离

    public boolean nearAll = true;
    /*
     * 酒店距用户当前所在坐标的距离，
     * 而不是用户长点击地图时的坐标点的距离。
     */
    public double currCoordDistance = 0;

	/**
	 * 酒店标签(包装)
	 */
	public List<HotelLabel> hotelLabels;

	public int originalPriceForMin = 0;

    public double discount = 10;

    //酒店的搜索匹配信息
    private Map<String, Object> filterInfo;
    public final String hotelSEQ;

    //用于无线，酒店是否有团购报价
    public boolean hasGroupBuy = false;

    // Mobile
    public int mobileMinAvailablePrice = Integer.MAX_VALUE;

    /**
     * 带身份的最低价(变身份的sort报价)
     */
    private int minPriceWithIdentity = Integer.MAX_VALUE;

    /**
     * 收益预估报价
     */
    private double profitPrice = Double.MAX_VALUE;

    /**
     * 带身份的最低价（不变身份的sort报价）。
     */
    private int minPriceWithoutUpdateIdentity = Integer.MAX_VALUE;

    public MobileRoomStatus mobileRoomStatus = MobileRoomStatus.UNKNOWN;

    //用于配置无线端icon
    public int wapinfoConfig = 0;

    //rankInfo
    public long rankInfo = 0L;
    /**
     * 钟点房标签位
     */
    public int hourResultInfo = 0;
    /**
     * 钟点房时长
     */
    public double hourRoomLength = 0;

    /**
     * 钟点房入住时长
     */
    public int stayTimeLength = 0;

    public float mobilePriceRatio = 0f;

    public long lastCallRTTime;

    //无线专享
    public Map<String, Integer> promotionFlags;

    /**
     * 被选中的推广酒店
     * 只要被作为推广酒店选出来该字段就会置为true
     */
    public boolean isServiceInsert = false;

    /**
     * 推广类型细分
     *
     * 对于推广酒店123/246,推广标识细分
     * 已排好序的全量酒店集记A,推广后台录入酒店推广123/246记为B,A交B产生20家,会有如下结果
     * 1-10位表示123,11-20位表示246
     * 0, 初始化排好序的全量酒店集记A
     * 1, A交B中未被选中11~13家推广酒店
     * 2, 被选中5~6家推广酒店集合, 并生效
     * 3, 被选中2~3家推广酒店集合, 但由于自然排序高导致推广不生效
     */
    public int flagServiceInsert = 0;

    /**
     * 强插策略标识。具体见search系统里com.qunar.search.enums.ServiceSpecial这个枚举类里的定义。
     */
    public int isServiceSpecial = 0;

    public boolean isHidePrice = false;

    public long activity = 0L;
    // 金币
    public long logicBit = 0L;

    public long specialPriceBit = 0L;
    /**
     * 用户浏览时间，距当前系统时间多少秒
     */
    public double browsingTime = 0;
    /**
     * 用户浏览月份 bit1为1代表当月, bit2为1代表上一个月
     */
    public int browsingMonth = 0;

    private transient int originalPrice = 0;

    /**
     * "优惠码分享"各优惠码集合
     */
    public transient Set<String> activityIdsSet = Collections.emptySet();

    /**
     * twell扩展字段,筛选
     */
    private long listTagExt = 0;

    /**
     * 报价系统传递过来的trace字符串
     * 具体见wiki: http://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=173675030
     */
    private String priceTraceStr;

    /**
     * 酒店代金卷
     *discountType : {"cashback":10} //返现10元
     *discountType : {"reduce":10} //立减10元
     *discountType : {"reduce":10,"cashback":10} //优惠20元
     *
     */
    private Map<String,Integer> discountType;

    /**
     * 最低价对应的wrapper信息
     */
    private String codebase;
    private String codename;

    /**
     * 位聚合多渠道数据源的字段筛选
     */
    private long filterRankInfo;


    /**
     * 撒币最高返现金额
     */
    private int sabiMaxAmount;


	/**
	 * 场景位置推荐酒店标记 0:不推荐 1：推荐 2:高铁推荐
	 */
	private int scenePromotion = 0;

	/**
	 * 4/9位推广的类型 CPC,直通车,直升机
	 */
	private String promotionType;
    /**
     * 4/9位推广的子业务类型
     */
	private String realBusiness;

    /**
     * ccpsType
     */
    private Byte ccpsType;

	/**
	 * 推广id。只有ccpc金字塔的类型的才有值
	 */
	private String campaignId;

	/**
     * 列表页活动折扣数
     */
    private Map<String, Integer> discountArr;

    private Integer makeUp;

    private Integer minPriceTax;

    /**
     * 唯一ｉｄ
     */
    private String tw;
    /**
     * twell traceId
     */
    private String trd;

	/**
	 * 最低价支付类型
	 */
	private int ppbPayment;

    private List<String> breakfaskTypes;

    private List<String> cancelPolicys;

    private List<String> bedTypes;

    private List<String> payTypes;

    /**
     * 卧室数集合
     */
    private List<String> bedroomCounts;

    /**
     * 特色房型
     */
    private int specialRoomBit;

    private Map<String, Double> saleDiscount;

	private Map<String,String>  extendsMap;

    /**
     * 用于存储优惠加回后的价格字段存储
     */
    private Map<String, String> showPriceInfo;

	/**
	 * 酒店重排信息
 	 */
	private HotelReRankInfo reRankInfo;

    /**
     * cps酒店收益重排信息
     */
    private CpsProfitReRankInfo cpsProfitReRankInfo;

    /**
	 * 最低折扣
	 */
	private Integer lowestCommission;

    /**
     * 钟点房价格
     */
    private int hourlyRoomPrice = Integer.MAX_VALUE;

    /**
     * 酒店标记位。将多个boolean类型的标识压缩到这个字段上, 节省内存开销
     */
    private int hotelMarkBit = 0;

	/**
	 * 酒店标签，for 标签系统
	 * 所有打标字段全放这里
	 * 加transient是避免召回缓存
	 */
	private transient Map<String,Object> conditions;

    /**
     * 用于标识当前报价是哪种房型的。0：全日房，1：钟点房
     *
     * PMO:FD-7245
     */
	private transient int roomType;

    /**
     * 用于标识酒店热门物理房型是否已售罄。true：已售罄，false：未售罄。
     * PMO:FD-15660
     */
	private transient boolean hotRoomSoldOut;

    /**
     * 模型预估的render报价
     */
    private transient int estimatedRenderPrice;

    /**
     * XGB模型预估的每个场景下的s2o，依次是same_city, nearby, poi_key, not_same_city
     */
    private transient List<Double> estimatedCitySceneS2o;

    /**
     * mixedSort之后收益排序之前的排名
     */
    private int beforeProfitRankSort;

    /**
     * 精排之后的排名
     */
    private int modelSort;

    /**
     * earningsReRank之后的排名
     */
    private int profitSort;

    /**
     * algoReRank之后的排名
     */
    private int originSort;

    /**
     * 商业化强插后的排名
     */
    private int reRankSort;

    /**
     * 扶持计划id。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=491979721
     */
    private String planId;

    /**
     * 父计划id。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=507652379
     */
    private String parentPlanId;

    /**
     * 扶持比例
     */
    private Double supportRatio;

    /**
     * 酒店特征map https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=539818686
     */
    private Map<Integer, Object> featureMap;

    /**
     * 酒店套餐等的标识位
     */
    private int listDetailFilterBit;

    /**
     * 酒店最近三个月的单量
     */
    private int orderCnt3Month;

    /**
     * 酒店近三个月单量排名, 从0开始
     */
    private int orderVolumeNumber;

    /**
     * 酒店在请求中的距离分桶排序
     */
    private int distanceBucketSort;

    /**
     * 酒店在请求中的距离前的酒店数cdf
     * */
    private double distanceBucketCdf;

    /**
     * 酒店在请求中的距离桶的酒店数
     * */
    private int distanceBucketNum;

    /**
     * 赢客宝-交互推荐广告：目标酒店Seq https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=748901141
     */
    private String targetHotelSeq;

    /**
     * 赢客宝-交互推荐广告：未推荐广告酒店原因
     */
    private int notRecommendReason;

    /**
     * 赢客宝-交互推荐广告：未推荐广告酒店hotelSeq
     */
    private String notRecommendHotelSeq;

    /**
     * sort报价佣金
     */
    private int sortPriceCommission;

    /**
     * sort报价佣金率
     */
    private int sortPriceCommissionRate;


    /**
     * 缓存的的佣金
     */
    private int cachedCommission;

    /**
     * 缓存的的佣金V2
     */
    private Integer cachedCommissionV2;

    /**
     * 缓存的新版beat率
     */
    private Double cachedBeatV2;

    /**
     * 是否有效beat
     */
    private Boolean cachedValidBeat;

    /**
     * 缓存的的商家券后卖价
     */
    private int cachedPriceAfterMerchant;

    /**
     * 缓存的划线价
     */
    private int cachedDispOrgPrice;

    /**
     * 缓存的最低价
     */
    private int cachedRenderMinPrice;

    /**
     * 缓存的原始底价
     */
    private int cachedBasePrice;

    /**
     * 缓存的beat值
     */
    private float cachedBeat;

    /**
     * 报价缓存更新的时间戳
     */
    private long cachedTimestamp;

    /**
     * 最低价格报价缓存更新的时间戳
     */
    private long cachedTimestampOfMinPrice;

    /*
     * 独立佣金缓存更新的时间戳
     */
    private long cachedTimestampOfCommission;

    /**
     * render报价产品房型id
     */
    private String roomId;


    /**
     * 最大房间面积
     */
    private int maxRoomArea;


    /**
     * 命中render报价缓存的级别
     */
    private String renderCacheLevel;

    /**
     * 命中render报价缓存真实key
     */
    private String redisHitKey;

    /**
     * render报价缓存第一次尝试的key
     */
    private String redisFirstTryKey;

    /**
     * render报价缓存第一次尝试的key的级别
     */
    private String redisFirstTryLevel;

    /**
     * sort报价酒店支持入住的成人数
     */
    private int totalPeopleCapacity;

    /**
     * 需要查询库存的权益类型列表
     */
    private List<String> pendingEntitlementTypes;

    /**
     * 组合报价相关信息。http://pmo.corp.qunar.com/browse/FD-337688
     */
    private Map<String, Object> combine;
    /**
     * 步驾距离
     */
    private PoiHotelDistance  poiHotelDistance;


    public HotelItem() {
        this.hotelSEQ = "";
    }

    public HotelItem(HotelInfo info) {
        this.hotelSEQ = info.hotelSEQ;
        this.info = info;
    }


    public void setOrderVolumeNumber(int orderVolumeNumber){
        this.orderVolumeNumber = orderVolumeNumber;
    }

    public int getOrderVolumeNumber(){
        return this.orderVolumeNumber;
    }


    public void setOrderCnt3Month(int orderCnt3Month){
        this.orderCnt3Month = orderCnt3Month;
    }

    public int getOrderCnt3Month(){
        return this.orderCnt3Month;
    }

    public void setVectorPreRankScore(double vectorPreRankScore){
        this.vectorPreRankScore = vectorPreRankScore;
    }

    public double getVectorPreRankScore(){
        return this.vectorPreRankScore;
    }

    public Map<String, Object> getFilterInfo() {
        return this.filterInfo;
    }

	/**
     * 返回酒店坐标信息
     *
     * @return
     */
    public Object getPoiInfo() {
		if(MapUtils.isEmpty(getFilterInfo())){
			return null;
		}
        return filterInfo.get(FilterType.poi.name());
    }

    public void setLogicBit(long logicBit) {
        this.logicBit = logicBit;
    }


    public int getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(int originalPrice) {
        this.originalPrice = originalPrice;
    }

    public void setCityIndivScore(double cityIndivScore) {
        this.cityIndivScore = cityIndivScore;
    }

    public double getCityIndivScore() {
        return cityIndivScore;
    }

	public double getPunishScore() {
		return punishScore;
	}

	public void setPunishScore(double punishScore) {
		this.punishScore = punishScore;
	}

	public double getModelScore() {
		return modelScore;
	}

	public void setModelScore(double modelScore) {
		this.modelScore = modelScore;
	}

	public int getPopularityHitIndex() {
		return popularityHitIndex;
	}

	public void setPopularityHitIndex(int popularityHitIndex) {
		this.popularityHitIndex = popularityHitIndex;
	}

    public double getGroupScore() {
        return groupScore;
    }

    public void setGroupScore(double groupScore) {
        this.groupScore = groupScore;
    }

    public double getAdModelScore() {
        return adModelScore;
    }

    public void setAdModelScore(double adModelScore) {
        this.adModelScore = adModelScore;
    }

    public AdMsg getAdMsg() {
        return adMsg;
    }

    public void setAdMsg(AdMsg adMsg) {
        this.adMsg = adMsg;
    }

    public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getPriceTraceStr() {
		return priceTraceStr;
	}

	public void setPriceTraceStr(String priceTraceStr) {
		this.priceTraceStr = priceTraceStr;
	}


	/**
	 * 用户浏览月份 bit1为1代表当月, bit2为1代表上一个月
	 */
	public int getBrowsingMonth() {
		return browsingMonth;
	}

	/**
	 * 用户浏览月份 bit1为1代表当月, bit2为1代表上一个月
	 */
	public void setBrowsingMonth(int browsingMonth) {
		this.browsingMonth = browsingMonth;
	}

	public void setBrowsingTime(double browsingTime) {
		this.browsingTime = browsingTime;
	}

	public boolean getIsHidePrice() {
		return isHidePrice;
	}

	public void setIsHidePrice(boolean isHidePrice) {
		this.isHidePrice = isHidePrice;
	}

    /**
     * @return 酒店是否是精确命中, 精确命中是指search返回的结果中班好
     */
    public boolean isFullMatch() {
        if (MapUtils.isEmpty(getFilterInfo())) {
            return false;
        }

        if (StringUtils.isBlank(info.getImageId())) {
            return false;
        }

        if (getFilterInfo().containsKey(HotelNameMatchType.FULL_HOTEL_NAME.getValue())) {
            return true;
        }

        try {
            String o = (String) filterInfo.get(RELATIVITY);
            double relativity = Double.parseDouble(o);
            if (relativity > FULL_MATCH_RELATIVITY) {
                return true;
            }
        } catch (Exception ignore) {
        }

        return false;
    }

    /**
     * @return 酒店是否是判定精确命中
     */
    public Boolean isHotellNameMatch() {
		if(MapUtils.isEmpty(getFilterInfo())){
			return false;
		}
        return this.filterInfo.containsKey(HotelNameMatchType.HOTEL_NAME.getValue());
    }

    /**
     * @return  酒店是否是部分命中
     */
    public boolean isPartHotelNameMatch() {
		if(MapUtils.isEmpty(getFilterInfo())){
			return false;
		}
        return (this.filterInfo.size() == 1 && this.filterInfo.containsKey(HotelNameMatchType.PART_HOTEL_NAME.getValue()))
                || (this.filterInfo.size() == 2 && this.filterInfo.containsKey(HotelNameMatchType.PART_HOTEL_NAME.getValue()) && this.filterInfo.containsKey("fts2"));
    }

    /**
     * 返回酒店判定精确命中的相关性,如果不存在，则返回0.0
     * @param
     * @return
     */
    public double getHotelNameRelativity() {
        double result = 0.0d;
		if (MapUtils.isEmpty(getFilterInfo())) {
			return result;
		}

        // 当命中的实体个数是1的时候才去计算相似度
        if (RankSystemConfig.isEnableComputeRelativityOnlyOneEntity() && (filterInfo.size() != 1)) {
            return result;
        }

		Map<String, String> hotelName = (Map<String, String>) this.getFilterInfo().get(HotelNameMatchType.HOTEL_NAME.getValue());
        if (null != hotelName) {
            return NumberUtils.toDouble(hotelName.get("relativity"));
        }

        Map<String, String> partHotelName = (Map<String, String>) this.getFilterInfo().get(HotelNameMatchType.PART_HOTEL_NAME.getValue());
        if (null != partHotelName) {
            return NumberUtils.toDouble(partHotelName.get("relativity"));
        }

        return result;
    }

    public long getLogicBit() {
        return logicBit;
    }

    public void setFilterInfo(Map<String, Object> filterInfo) {
        this.filterInfo = filterInfo;
    }

    public String getHotelSEQ() {
        return hotelSEQ;
    }

    public long getListTagExt() {
        return listTagExt;
    }

    public void setListTagExt(long listTagExt) {
        this.listTagExt = listTagExt;
    }

    public Map<String, Integer> getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Map<String, Integer> discountType) {
        this.discountType = discountType;
    }

    public long getFilterRankInfo() {
        return filterRankInfo;
    }

    public void setFilterRankInfo(long filterRankInfo) {
        this.filterRankInfo = filterRankInfo;
    }


    public String getCodebase() {
        return codebase;
    }

    public void setCodebase(String codebase) {
        this.codebase = codebase;
    }

    public String getCodename() {
        return codename;
    }

    public void setCodename(String codename) {
        this.codename = codename;
    }

    public int getSabiMaxAmount() {
        return sabiMaxAmount;
    }

    public void setSabiMaxAmount(int sabiMaxAmount) {
        this.sabiMaxAmount = sabiMaxAmount;
    }

	public int getScenePromotion() {
		return scenePromotion;
	}

	public void setScenePromotion(int scenePromotion) {
		this.scenePromotion = scenePromotion;
	}

	public Integer getMakeUp() {
        return makeUp;
    }

    public void setMakeUp(Integer makeUp) {
        this.makeUp = makeUp;
    }

    public Integer getMinPriceTax() {
        return minPriceTax;
    }

    public void setMinPriceTax(Integer minPriceTax) {
        this.minPriceTax = minPriceTax;
    }

    public Map<String, Integer> getDiscountArr() {
        return discountArr;
    }

    public void setDiscountArr(Map<String, Integer> discountArr) {
        this.discountArr = discountArr;
    }

    public Map<String, Double> getSaleDiscount() {
        return saleDiscount;
    }

    public void setSaleDiscount(Map<String, Double> saleDiscount) {
        this.saleDiscount = saleDiscount;
    }

	public Map<String, String> getExtendsMap() {
		return extendsMap;
	}

	public void setExtendsMap(Map<String, String> extendsMap) {
		this.extendsMap = extendsMap;
	}

	public int getPpbPayment() {
		return ppbPayment;
	}

	public void setPpbPayment(int ppbPayment) {
		this.ppbPayment = ppbPayment;
	}

	public String getTw() {
		return tw;
	}

	public void setTw(String tw) {
		this.tw = tw;
	}

	public String getTrd() {
		return trd;
	}

	public void setTrd(String trd) {
		this.trd = trd;
	}

	public List<String> getBreakfaskTypes() {
		return breakfaskTypes;
	}

	public void setBreakfaskTypes(List<String> breakfaskTypes) {
		this.breakfaskTypes = breakfaskTypes;
	}

	public List<String> getCancelPolicys() {
		return cancelPolicys;
	}

	public void setCancelPolicys(List<String> cancelPolicys) {
		this.cancelPolicys = cancelPolicys;
	}

	public List<String> getBedTypes() {
		return bedTypes;
	}

	public void setBedTypes(List<String> bedTypes) {
		this.bedTypes = bedTypes;
	}

    public int getSpecialRoomBit() {
        return specialRoomBit;
    }

    public void setSpecialRoomBit(int specialRoomBit) {
        this.specialRoomBit = specialRoomBit;
    }

    public List<String> getPayTypes() {
		return payTypes;
	}

	public void setPayTypes(List<String> payTypes) {
		this.payTypes = payTypes;
	}

    public List<String> getBedroomCounts() {
        return bedroomCounts;
    }

    public void setBedroomCounts(List<String> bedroomCounts) {
        this.bedroomCounts = bedroomCounts;
    }

    public HotelReRankInfo getReRankInfo() {
		return reRankInfo;
	}

	public void setReRankInfo(HotelReRankInfo reRankInfo) {
		this.reRankInfo = reRankInfo;
	}

	private void readObject(ObjectInputStream stream) throws IOException, ClassNotFoundException {
        stream.defaultReadObject();
        this.info = HotelInfoHolder.getHotel(hotelSEQ);
    }

	public String getPromotionType() {
		return promotionType;
	}

	public void setPromotionType(String promotionType) {
		this.promotionType = promotionType;
	}
	public Integer getLowestCommission() {
		return lowestCommission;
	}

	public void setLowestCommission(Integer lowestCommission) {
		this.lowestCommission = lowestCommission;
	}

    public int getHourlyRoomPrice() {
        return hourlyRoomPrice;
    }

    public void setHourlyRoomPrice(int hourlyRoomPrice) {
        this.hourlyRoomPrice = hourlyRoomPrice;
    }

    public String getRealBusiness() {
        return realBusiness;
    }

    public void setRealBusiness(String realBusiness) {
        this.realBusiness = realBusiness;
    }

	public Map<String, Object> getConditions() {
		return conditions;
	}

	public void setConditions(Map<String, Object> conditions) {
		this.conditions = conditions;
	}

    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}

    public boolean isHotRoomSoldOut() {
        return hotRoomSoldOut;
    }

    public void setHotRoomSoldOut(boolean hotRoomSoldOut) {
        this.hotRoomSoldOut = hotRoomSoldOut;
    }

    public int getHotelMarkBit() {
        return hotelMarkBit;
    }

    public void setHotelMarkBit(int hotelMarkBit) {
        this.hotelMarkBit = hotelMarkBit;
    }

    public double getCtcvr() {
        return ctcvr;
    }

    public void setCtcvr(double ctcvr) {
        this.ctcvr = ctcvr;
    }

    public int getMinPriceWithIdentity() {
        return minPriceWithIdentity;
    }

    public void setMinPriceWithIdentity(int minPriceWithIdentity) {
        this.minPriceWithIdentity = minPriceWithIdentity;
    }

    public int getEstimatedRenderPrice() {
        return estimatedRenderPrice;
    }

    public void setEstimatedRenderPrice(int estimatedRenderPrice) {
        this.estimatedRenderPrice = estimatedRenderPrice;
    }

    public List<Double> getEstimatedCitySceneS2o() {
        return estimatedCitySceneS2o;
    }

    public void setEstimatedCitySceneS2o(List<Double> estimatedCitySceneS2o) {
        this.estimatedCitySceneS2o = estimatedCitySceneS2o;
    }

    public int getMinPriceWithoutUpdateIdentity() {
        return minPriceWithoutUpdateIdentity;
    }

    public void setMinPriceWithoutUpdateIdentity(int minPriceWithoutUpdateIdentity) {
        this.minPriceWithoutUpdateIdentity = minPriceWithoutUpdateIdentity;
    }

    public int getProfitSort() {
        return profitSort;
    }

    public void setProfitSort(int profitSort) {
        this.profitSort = profitSort;
    }

    public int getOriginSort() {
        return originSort;
    }

    public void setOriginSort(int originSort) {
        this.originSort = originSort;
    }

    public int getModelSort() {
        return modelSort;
    }

    public void setModelSort(int modelSort) {
        this.modelSort = modelSort;
    }

    public int getReRankSort() {
        return reRankSort;
    }

    public void setReRankSort(int reRankSort) {
        this.reRankSort = reRankSort;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getParentPlanId() {
        return parentPlanId;
    }

    public void setParentPlanId(String parentPlanId) {
        this.parentPlanId = parentPlanId;
    }

    public Double getSupportRatio() {
        return supportRatio;
    }

    public void setSupportRatio(Double supportRatio) {
        this.supportRatio = supportRatio;
    }

    public Map<Integer, Object> getFeatureMap() {
        return featureMap;
    }

    public void setFeatureMap(Map<Integer, Object> featureMap) {
        this.featureMap = featureMap;
    }

    public double getProfitPrice() {
        return profitPrice;
    }

    public void setProfitPrice(double profitPrice) {
        this.profitPrice = profitPrice;
    }

    public int getListDetailFilterBit() {
        return listDetailFilterBit;
    }

    public void setListDetailFilterBit(int listDetailFilterBit) {
        this.listDetailFilterBit = listDetailFilterBit;
    }

    public int getMaxRoomArea() {
        return maxRoomArea;
    }

    public void setMaxRoomArea(int maxRoomArea) {
        this.maxRoomArea = maxRoomArea;
    }

    public Double getCachedBeatV2() {
        return cachedBeatV2;
    }

    public void setCachedBeatV2(Double cachedBeatV2) {
        this.cachedBeatV2 = cachedBeatV2;
    }

    public Boolean getCachedValidBeat() {
        return cachedValidBeat;
    }

    public void setCachedValidBeat(Boolean cachedValidBeat) {
        this.cachedValidBeat = cachedValidBeat;
    }

    public PoiHotelDistance getPoiHotelDistance() {
        return poiHotelDistance;
    }

    public void setPoiHotelDistance(PoiHotelDistance poiHotelDistance) {
        this.poiHotelDistance = poiHotelDistance;
    }
}