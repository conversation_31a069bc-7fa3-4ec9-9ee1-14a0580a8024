package com.qunar.search.common.bean;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求参数封装类
 * 
 * <AUTHOR>
 *
 */
public class RequestParam {
	// 搜索城市
	private String cityUrl;

	// 入店日期
	private String fromDate;

	// 离店日期
	private String toDate;

	// 请求标示
	private String requestor;

	// 是否同城搜索
	private boolean channelCity;

	// 平台
	private String platform;

	// 是否是关键词搜索
	private boolean keySearch;

	// 搜索关键词
	private String q;

	// 用户经纬度坐标
	private String currCoord;

	// 红包显示距离最大值
	private int maxDistance;

	// 红包显示距离最小值
	private int minDistance;

	//
	private boolean showPromotion;

	private boolean quick;

	private String vipUser;

	private boolean showAllCondition;

	private String uid;

	private String gid;

	private String uname;

	private boolean cooKeyLocation;

	private boolean addService;

	private boolean containsCoordinateFilter;

	// 是否是推荐排序
	private boolean promoteSort;

	// 是否是榜单请求
	private boolean bangdanReq;

	private PageInfo pageInfo;

	private String lmPriceType;

	// 是否是钟点房
	private boolean hourRoom;

	private HttpServletRequest request;

	// 手机型号
	private String model;

	private boolean isMainLand;

    // mock的用户特征
    private String mockFeature;

	public boolean isMainLand() {
		return isMainLand;
	}

	public void setMainLand(boolean isMainLand) {
		this.isMainLand = isMainLand;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public boolean isHourRoom() {
		return hourRoom;
	}

	public void setHourRoom(boolean hourRoom) {
		this.hourRoom = hourRoom;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public String getLmPriceType() {
		return lmPriceType;
	}

	public void setLmPriceType(String lmPriceType) {
		this.lmPriceType = lmPriceType;
	}

	public PageInfo getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
	}

	public boolean isBangdanReq() {
		return bangdanReq;
	}

	public void setBangdanReq(boolean bangdanReq) {
		this.bangdanReq = bangdanReq;
	}

	public boolean isPromoteSort() {
		return promoteSort;
	}

	public void setPromoteSort(boolean promoteSort) {
		this.promoteSort = promoteSort;
	}

	public boolean isContainsCoordinateFilter() {
		return containsCoordinateFilter;
	}

	public void setContainsCoordinateFilter(boolean containsCoordinateFilter) {
		this.containsCoordinateFilter = containsCoordinateFilter;
	}

	public boolean isAddService() {
		return addService;
	}

	public void setAddService(boolean addService) {
		this.addService = addService;
	}

	public boolean isCooKeyLocation() {
		return cooKeyLocation;
	}

	public void setCooKeyLocation(boolean cooKeyLocation) {
		this.cooKeyLocation = cooKeyLocation;
	}

	public String getUname() {
		return uname;
	}

	public void setUname(String uname) {
		this.uname = uname;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getGid() {
		return gid;
	}

	public void setGid(String gid) {
		this.gid = gid;
	}

	public boolean isShowAllCondition() {
		return showAllCondition;
	}

	public void setShowAllCondition(boolean showAllCondition) {
		this.showAllCondition = showAllCondition;
	}

	public String getVipUser() {
		return vipUser;
	}

	public void setVipUser(String vipUser) {
		this.vipUser = vipUser;
	}

	public boolean isQuick() {
		return quick;
	}

	public void setQuick(boolean quick) {
		this.quick = quick;
	}

	public boolean isShowPromotion() {
		return showPromotion;
	}

	public void setShowPromotion(boolean showPromotion) {
		this.showPromotion = showPromotion;
	}

	public String getCurrCoord() {
		return currCoord;
	}

	public void setCurrCoord(String currCoord) {
		this.currCoord = currCoord;
	}

	public int getMaxDistance() {
		return maxDistance;
	}

	public void setMaxDistance(int maxDistance) {
		this.maxDistance = maxDistance;
	}

	public int getMinDistance() {
		return minDistance;
	}

	public void setMinDistance(int minDistance) {
		this.minDistance = minDistance;
	}

	public String getQ() {
		return q;
	}

	public void setQ(String q) {
		this.q = q;
	}

	public boolean isKeySearch() {
		return keySearch;
	}

	public void setKeySearch(boolean keySearch) {
		this.keySearch = keySearch;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public boolean isChannelCity() {
		return channelCity;
	}

	public void setChannelCity(boolean channelCity) {
		this.channelCity = channelCity;
	}

	public String getCityUrl() {
		return cityUrl;
	}

	public void setCityUrl(String cityUrl) {
		this.cityUrl = cityUrl;
	}

	public String getFromDate() {
		return fromDate;
	}

	public void setFromDate(String fromDate) {
		this.fromDate = fromDate;
	}

	public String getToDate() {
		return toDate;
	}

	public void setToDate(String toDate) {
		this.toDate = toDate;
	}

	public String getRequestor() {
		return requestor;
	}

	public void setRequestor(String requestor) {
		this.requestor = requestor;
	}

    public String getMockFeature() {
        return mockFeature;
    }

    public void setMockFeature(String mockFeature) {
        this.mockFeature = mockFeature;
    }
}
