package com.qunar.search.common.bean;

import com.qunar.search.common.enums.CarnivalType;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 给web搜索添加的酒店属性
 * 这个类对象嵌入到HotelItem中.
 * 长期设计可能是, 这个类持有PC专用的字段, 而
 * Created by fangxue.zhang on 2016/4/13.
 */
public class HotelAttrsForWeb implements Serializable {


    private static final long serialVersionUID = 4043485129251608597L;

    private static final int INVALID_CAROUSEL_ORDER_UPPER_LIMIT = -1;
    private String specialCommend;
    private int[] walkDistanceAndTime = new int[]{0, 0}; //步行实际距离和时间
    private int[] driveDistanceAndTime = new int[]{0, 0}; //驾车实际距离和时间
    //酒店客栈嘉年华标识，包括四种优惠数据
    private int carnivalConfig = 0;
    // 酒店影响静态得分的等级，因直销酒店提权项目而加入；初步有效值为[1-4]
    private byte rankLevel = 0;
    @Deprecated
    private int cashback = 0;// 返现值
    @Deprecated
    private int reduce = 0;// 直减值
    @Deprecated
    private int taxes = 0;// 税费值
    //用于主站www国际酒店搜索，标识报价是否从low price获取
    private boolean isLowPrice = false;
    // 用于控制list页的报价icon展示
    private byte displayMobilePriceType = 0; // 0 无活动 1：无线新用户专享 2：无线专享
    private int mobileSave = 0; //物理房型内，AB类无线最低价相对WWW最低价要节省多少，默认节省0元
    // query的命中个数(范围：poi、hotelName、tradingArea、brand)
    private int hitNum = 0;
    //新酒店轮播
    private int carouselOrder = INVALID_CAROUSEL_ORDER_UPPER_LIMIT;
    private int beforeCarouselIndex = -1;
    private boolean isCarouseled = false;
    // 酒店rank
    //private double rank = 0;
    private double staticRank = 0;
    private double queryScore = 0; // pc代码里面都是用的默认0
    // 酒店层级展示权重，默认为E级
    private int levelWeight = 0;
    private transient int dangci = -1;
    private int vipSaving = 0;
    private int wholesalePriceRatio = 0;//裸卖比例

    /**
     * 带Query搜索时,酒店名命中的相似度值
     */
    private double relativity = -1.0D;
    private boolean isSimilarRujia = false;
    //酒店客栈嘉年华 对应的活动数据

    //Map 没有继承Serializable, 所以findbugs会报warning, 不过问题不大, HashMap是继承了的
    private Map<CarnivalType, CarnivalBean> carnivalPromotions = new HashMap<CarnivalType, CarnivalBean>();
    //以下两个个字段只有在连住优惠的时候才会赋值
    private int lianzhuNight = 0;//连住天数
    private String lianzhuUniqueKey = "";//扩容之后连住对应的房型roomId

    // for 大众点评
    private int origPrice; // 立减或返现前的原价
    private int reward; // 立减或返现价格
    private int rewardType; // NONE(0, "无"), REDUCE(1,"直减"), CASHBACK(2, "返现"), TAXES(3, "税费"), GIFTOFPHONE(4, "赠话费");
    private boolean isGbPrice; // 是否是团购报价
    private int vipPriceInfo;//vip报价信息
    private volatile int partPrice = Integer.MAX_VALUE;
    // rank值默认为－100
    public static final float DEFAULT_MOBILE_RANK = -100;

    public HotelAttrsForWeb() {

    }

    public int getBeforeCarouselIndex() {
        return beforeCarouselIndex;
    }

    public void setBeforeCarouselIndex(int beforeCarouselIndex) {
        this.beforeCarouselIndex = beforeCarouselIndex;
    }

    public int getCarnivalConfig() {
        return carnivalConfig;
    }

    public void setCarnivalConfig(int carnivalConfig) {
        this.carnivalConfig = carnivalConfig;
    }

    public Map<CarnivalType, CarnivalBean> getCarnivalPromotions() {
        return carnivalPromotions;
    }

    public void setCarnivalPromotions(Map<CarnivalType, CarnivalBean> carnivalPromotions) {
        this.carnivalPromotions = carnivalPromotions;
    }

    public int getCarouselOrder() {
        return carouselOrder;
    }

    public void setCarouselOrder(int carouselOrder) {
        this.carouselOrder = carouselOrder;
    }

    public int getCashback() {
        return cashback;
    }

    public void setCashback(int cashback) {
        this.cashback = cashback;
    }

    public int getDangci() {
        return dangci;
    }

    public void setDangci(int dangci) {
        this.dangci = dangci;
    }

    public byte getDisplayMobilePriceType() {
        return displayMobilePriceType;
    }

    public void setDisplayMobilePriceType(byte displayMobilePriceType) {
        this.displayMobilePriceType = displayMobilePriceType;
    }

    public int[] getDriveDistanceAndTime() {
        return driveDistanceAndTime;
    }

    public void setDriveDistanceAndTime(int[] driveDistanceAndTime) {
        this.driveDistanceAndTime = driveDistanceAndTime;
    }

    public int getHitNum() {
        return hitNum;
    }

    public void setHitNum(int hitNum) {
        this.hitNum = hitNum;
    }

    public static int getInvalidCarouselOrderUpperLimit() {
        return INVALID_CAROUSEL_ORDER_UPPER_LIMIT;
    }

    public boolean isCarouseled() {
        return isCarouseled;
    }

    public void setCarouseled(boolean carouseled) {
        isCarouseled = carouseled;
    }

    public boolean isGbPrice() {
        return isGbPrice;
    }

    public void setGbPrice(boolean gbPrice) {
        isGbPrice = gbPrice;
    }

    public boolean isLowPrice() {
        return isLowPrice;
    }

    public void setLowPrice(boolean lowPrice) {
        isLowPrice = lowPrice;
    }

    public boolean isSimilarRujia() {
        return isSimilarRujia;
    }

    public void setSimilarRujia(boolean similarRujia) {
        isSimilarRujia = similarRujia;
    }

    public int getLevelWeight() {
        return levelWeight;
    }

    public void setLevelWeight(int levelWeight) {
        this.levelWeight = levelWeight;
    }

    public int getLianzhuNight() {
        return lianzhuNight;
    }

    public void setLianzhuNight(int lianzhuNight) {
        this.lianzhuNight = lianzhuNight;
    }

    public String getLianzhuUniqueKey() {
        return lianzhuUniqueKey;
    }

    public void setLianzhuUniqueKey(String lianzhuUniqueKey) {
        this.lianzhuUniqueKey = lianzhuUniqueKey;
    }

    public int getMobileSave() {
        return mobileSave;
    }

    public void setMobileSave(int mobileSave) {
        this.mobileSave = mobileSave;
    }

    public int getOrigPrice() {
        return origPrice;
    }

    public void setOrigPrice(int origPrice) {
        this.origPrice = origPrice;
    }

    public int getPartPrice() {
        return partPrice;
    }

    public void setPartPrice(int partPrice) {
        this.partPrice = partPrice;
    }

    public double getQueryScore() {
        return queryScore;
    }

    public void setQueryScore(double queryScore) {
        this.queryScore = queryScore;
    }
/*
    public double getRank() {
        return rank;
    }

    public void setRank(double rank) {
        this.rank = rank;
    }
    */

    public byte getRankLevel() {
        return rankLevel;
    }

    public void setRankLevel(byte rankLevel) {
        this.rankLevel = rankLevel;
    }

    public int getReduce() {
        return reduce;
    }

    public void setReduce(int reduce) {
        this.reduce = reduce;
    }

    public double getRelativity() {
        return relativity;
    }

    public void setRelativity(double relativity) {
        this.relativity = relativity;
    }

    public int getReward() {
        return reward;
    }

    public void setReward(int reward) {
        this.reward = reward;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public String getSpecialCommend() {
        return specialCommend;
    }

    public void setSpecialCommend(String specialCommend) {
        this.specialCommend = specialCommend;
    }

    public double getStaticRank() {
        return staticRank;
    }

    public void setStaticRank(double staticRank) {
        this.staticRank = staticRank;
    }

    public int getTaxes() {
        return taxes;
    }

    public void setTaxes(int taxes) {
        this.taxes = taxes;
    }

    public int getVipPriceInfo() {
        return vipPriceInfo;
    }

    public void setVipPriceInfo(int vipPriceInfo) {
        this.vipPriceInfo = vipPriceInfo;
    }

    public int getVipSaving() {
        return vipSaving;
    }

    public void setVipSaving(int vipSaving) {
        this.vipSaving = vipSaving;
    }

    public int[] getWalkDistanceAndTime() {
        return walkDistanceAndTime;
    }

    public void setWalkDistanceAndTime(int[] walkDistanceAndTime) {
        this.walkDistanceAndTime = walkDistanceAndTime;
    }

    public int getWholesalePriceRatio() {
        return wholesalePriceRatio;
    }

    public void setWholesalePriceRatio(int wholesalePriceRatio) {
        this.wholesalePriceRatio = wholesalePriceRatio;
    }

    public int getRealDistance() {
        return this.walkDistanceAndTime[0] == 0 ? this.driveDistanceAndTime[0] : this.walkDistanceAndTime[0];
    }

}
