package com.qunar.search.common.bean;

import com.qunar.search.common.enums.ErrorEnum;

/**
 * http请求返回结果
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-3-17.
 */
public class HttpRequestResult {
    private boolean ret;
    private ErrorEnum errorEnum;
    private String result;

    public HttpRequestResult() {

    }

    public HttpRequestResult(boolean ret, ErrorEnum errorEnum) {
        this.ret = ret;
        this.errorEnum = errorEnum;
    }

    public boolean isRet() {
        return ret;
    }

    public void setRet(boolean ret) {
        this.ret = ret;
    }

    public ErrorEnum getErrorEnum() {
        return errorEnum;
    }

    public void setErrorEnum(ErrorEnum errorEnum) {
        this.errorEnum = errorEnum;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
