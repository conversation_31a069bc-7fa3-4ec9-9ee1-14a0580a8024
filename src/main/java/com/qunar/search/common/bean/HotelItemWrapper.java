package com.qunar.search.common.bean;

import com.qunar.search.common.enums.HotelNameMatchType;
import com.qunar.search.common.enums.HourRoomDistance;
import com.qunar.search.common.util.Numbers;
import org.apache.commons.collections.MapUtils;

import java.util.Optional;

/**用来处理一些HotelItem字段的转化逻辑
 * Created by fangxue.zhang on 2016/10/9.
 */
public final class HotelItemWrapper {

    private static String[] keyWordTypes = new String[]{"poi", "tradingArea", "brand", HotelNameMatchType.HOTEL_NAME.getValue()};

    /**
     * 把档次按照我们实际要用的值进行映射
     * @param hi
     * @return
     */
    public static int dangciAdjust(HotelItem hi) {
        int adjust = 0;
        try {
            adjust = Numbers.toInt(hi.info.getProperty("dangci"), 0);
        } catch (Exception ex) {
            adjust = 0;
        }
        switch (adjust) {
            case 5:
                adjust = 2;
                break;
            case 4:
                adjust = 5;
                break;
            case 3:
                adjust = 4;
                break;
            case 2:
                adjust = 3;
                break;
            default:
        }
        return adjust;
    }

    /**
     * 获取hotelItem关键词命中的个数,目前值关注keyWordTypes中的关键词
     *
     * @param hotelItem
     * @return 关键词命中的个数
     */
    public static int getKeywordTypeCount(HotelItem hotelItem) {
        int result = 0;
        for (String keyWord : keyWordTypes) {
			if (Optional.ofNullable(hotelItem.getFilterInfo()).map(t-> t.containsKey(keyWord)).orElse(false)) {
                result++;
            }
        }
        return result;
    }

    /**
     *
     * @return 关键词类型数目的长度
     */
    public static int getTypeCount(){
        return keyWordTypes.length;
    }

    /**
     * 计算小时房酒店的距离分级.
     * @param hotelItem
     * @return
     */
    public static HourRoomDistance getHourRoomDistance(HotelItem hotelItem){
        for (HourRoomDistance hourRoomDistance : HourRoomDistance.values()) {
            if (hourRoomDistance.match(hotelItem.corrDistance)) {
                return  hourRoomDistance;
            }
        }
        return HourRoomDistance.THREE_MAX_KILO;
    }


}
