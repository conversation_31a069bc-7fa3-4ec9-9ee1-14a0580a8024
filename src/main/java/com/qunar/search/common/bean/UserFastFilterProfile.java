package com.qunar.search.common.bean;

import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-09-15
 * @DESCRIPTION 用户快筛画像数据
 **/
@Data
public class UserFastFilterProfile {
    /**
     * 新用户标识0，1
     */
    int newUserFlag = 0;

    /**
     * 用户等级1，2，3，4，5
     */
    int userLevel = 0;

    /**
     * 学生表示0，1
     */
    int studentFlag = 0;

    /**
     * 性别0（未知），1，2
     */
    int gender = 0;

    /**
     * 年龄
     */
    int age;

    /**
     * 1年订单的平均价格
     */
    double orderAvgPrice1y;

    /**
     * 1年订单的最大价格
     */
    double orderMaxPrice1y = 0.0;

    /**
     * 1年订单的最小价格
     */
    double orderMinPrice1y = 0.0;

    /**
     * 1年订单数
     */
    double orderNum1y = -1.0;

    /**
     * 用户的酒店星级偏好
     */
    double hotelGradePrefer = -1.0;

    /**
     * 用户一年内的商务订单占比
     */
    double orderBusRate1y;

    /**
     * 用户一年内的节假日订单占比
     */
    double orderHolidaysRate1y;

    /**
     * 用户一年内的周末订单占比
     */
    double orderWdayRate1y;

    /**
     * 用户最近点击时间的排序map
     */
    Map<String, Double> clickTimeRank = Collections.emptyMap();

    /**
     * 用户点击筛选项的次数排序map
     */
    Map<String, Double>  clickCntRank = Collections.emptyMap();

    /**
     * 用户7天点击筛选项的次数map
     */
    Map<String, Double>  clickCnt7d = Collections.emptyMap();

    /**
     * 用户1年点击筛选项的次数map
     */
    Map<String, Double>  clickCnt1y = Collections.emptyMap();

    /**
     * 用户7天浏览筛选项的次数map
     */
    Map<String, Double>  showCnt7d = Collections.emptyMap();

    /**
     * 用户1年浏览筛选项的次数map
     */
    Map<String, Double>  showCnt1y = Collections.emptyMap();

    /**
     * 用户7天对筛选项的ctrmap
     */
    Map<String, Double>  ctr7d = Collections.emptyMap();

    /**
     * 用户1年对筛选项的ctrmap
     */
    Map<String, Double>  ctr1y = Collections.emptyMap();

}
