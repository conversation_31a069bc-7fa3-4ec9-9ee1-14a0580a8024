package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @create 20210314
 * @DESCRIPTION 用户历史机票和火车票订单信息
 **/
@Data
public class UserTravelInfo {

    private static final DateTimeFormatter FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_YMD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final Set NAN_SET = Sets.newHashSet("NAN", "nan", "NaN", "NULL", "null");

    /**
     * 用户90天内得机票订单列表
     */
    List<UserTravelOrder> flightOrders90Day = Collections.emptyList();

    /**
     * 用户90天内得火车票订单列表
     */
    List<UserTravelOrder> trainOrders90Day = Collections.emptyList();

    /**
     * 常住地
     */
    String oftenCity;


    @JsonIgnore
    boolean flightArrHit;

    @JsonIgnore
    boolean trainArrHit;

    @JsonIgnore
    boolean flightDepHit;

    @JsonIgnore
    boolean trainDepHit;

    @JsonIgnore
    boolean hasFlightOrd;

    @JsonIgnore
    boolean hasTrainOrd;

    public void transform(String requestCity, String checkInDate, int requestDate) {

        long requestDay = LocalDate.parse(String.valueOf(requestDate), DATE_YMD).toEpochDay();
        if (StringUtils.isEmpty(requestCity) || StringUtils.isEmpty(checkInDate) || NAN_SET.contains(checkInDate)) {
            return;
        }

        long checkInDay = LocalDate.parse(checkInDate, FORMAT).toEpochDay();
        flightArrHit = hit(flightOrders90Day, requestCity, checkInDay, 7, UserTravelOrder::getArrCity, UserTravelOrder::getArrDate);

        trainArrHit = hit(trainOrders90Day, requestCity, checkInDay, 7, UserTravelOrder::getArrCity, UserTravelOrder::getArrDate);

        flightDepHit = hit(flightOrders90Day, requestCity, checkInDay, 0, UserTravelOrder::getDepCity, UserTravelOrder::getDepDate);

        trainDepHit = hit(trainOrders90Day, requestCity, checkInDay, 0, UserTravelOrder::getDepCity, UserTravelOrder::getDepDate);

        hasFlightOrd = has(flightOrders90Day, requestDay, 30);

        hasTrainOrd = has(trainOrders90Day, requestDay, 30);
    }

    private boolean hit(List<UserTravelOrder> orderList, String requestCity, long checkInDay, int diffDay,
                        Function<UserTravelOrder, String> cityFu, Function<UserTravelOrder, String> dateFn) {
        return orderList.stream().filter(s -> StringUtils.equals(requestCity, cityFu.apply(s)))
                .filter(s -> !StringUtils.isEmpty(dateFn.apply(s)) && !NAN_SET.contains(dateFn.apply(s)))
                .anyMatch(s -> LocalDate.parse(dateFn.apply(s), FORMAT).toEpochDay() + diffDay > checkInDay);
    }

    private boolean has(List<UserTravelOrder> orderList, long requestDay, int diffDay) {
        return orderList.stream().filter(s -> !StringUtils.isEmpty(s.ordDate) && !NAN_SET.contains(s.ordDate))
                .anyMatch(s -> LocalDate.parse(s.ordDate, FORMAT).toEpochDay() + diffDay > requestDay);
    }
}
