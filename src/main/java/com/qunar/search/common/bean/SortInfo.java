package com.qunar.search.common.bean;

import com.qunar.search.common.enums.SortType;

/**
 * SortInfo
 *
 * <AUTHOR>
 * @date 17-1-17.
 */
public class SortInfo {

    private SortType sortType;
    private double liftRate;

    public SortInfo(SortType sortType, double liftRate) {
        this.sortType = sortType;
        this.liftRate = liftRate;
    }

    public double getLiftRate() {
        return liftRate;
    }

    public void setLiftRate(double liftRate) {
        this.liftRate = liftRate;
    }

    public SortType getSortType() {
        return sortType;
    }

    public void setSortType(SortType sortType) {
        this.sortType = sortType;
    }

    @Override
    public String toString() {
        return "SortInfo{" +
                "sortType=" + sortType.getName() +
                ",liftRate=" + liftRate +
                '}';
    }
}
