package com.qunar.search.common.bean;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;

import com.qunar.search.common.enums.HotelChannel;
import com.qunar.search.common.enums.SceneSearch;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.parser.impl.DistanceWithGivenCoordinateFilterParser;
import com.qunar.search.common.util.Strings;


/**
 * 场景相关数据, 主要是用于在算法中区分场景.
 */

public class SceneData {
	private boolean isCooKeyLocation;
	private String platform;
	private boolean channelCity;
	private boolean containsCoordinateFilter;
	private String model;
	private double studentCityPrice;
	private boolean isKeySearch;
	private boolean isClockRoom;
	private String checkInDate;
	private String cityUrl;
	private boolean isMainLand;
	private HotelChannel hotelChannel;
	private GLatLng coord; //用户的坐标,如果是针扎坐标,则是针扎坐标
    private String mockFeature;
    private SceneSearch sceneSearch;
    private int highQualityHotelItemCnt;//场景下优质酒店个数

	private String cabin;


	public SceneData(){

	}

	public SceneData(HttpServletRequest request, boolean isCooKeyLocation, boolean channelCity, boolean isKeySearch) {
		this.containsCoordinateFilter = DistanceWithGivenCoordinateFilterParser.accept(request);
		this.model = StringUtils.trimToEmpty(request.getParameter("model")).toLowerCase();
		this.isCooKeyLocation = isCooKeyLocation;
		this.platform = StringUtils.trimToEmpty(request.getParameter("platform"));
		this.studentCityPrice = (Double) request.getAttribute("studentCityPrice");
		this.checkInDate = StringUtils.trimToEmpty(request.getParameter("fromDate"));
		this.channelCity = channelCity;
		this.isKeySearch = isKeySearch;
		this.isClockRoom = "1".equals(StringUtils.trimToEmpty(request.getParameter("isHourlyRoom")));
		this.cityUrl = Strings.getString(request.getAttribute("cityurl"));
		this.isMainLand = "1".equals(Strings.getString(request.getAttribute("isMainLand")));
	}

	public SceneData(RequestParam requestParam) {
		this.containsCoordinateFilter = requestParam.isContainsCoordinateFilter();
		this.isCooKeyLocation = requestParam.isCooKeyLocation();
		this.platform = requestParam.getPlatform();
		this.checkInDate = requestParam.getFromDate();
		this.channelCity = requestParam.isChannelCity();
		this.isKeySearch = requestParam.isKeySearch();
		this.isClockRoom = requestParam.isHourRoom();
		this.cityUrl = requestParam.getCityUrl();
		this.isMainLand = requestParam.isMainLand();
		this.model = requestParam.getModel();
        this.mockFeature = requestParam.getMockFeature();
	}

	public int getHighQualityHotelItemCnt() {
		return highQualityHotelItemCnt;
	}

	public void setHighQualityHotelItemCnt(int highQualityHotelItemCnt) {
		this.highQualityHotelItemCnt = highQualityHotelItemCnt;
	}


	public String getCheckInDate() {
		return checkInDate;
	}

	public void setCheckInDate(String checkInDate) {
		this.checkInDate = checkInDate;
	}

	public boolean isCooKeyLocation() {
		return isCooKeyLocation;
	}

	public void setCooKeyLocation(boolean isCooKeyLocation) {
		this.isCooKeyLocation = isCooKeyLocation;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public boolean isChannelCity() {
		return channelCity;
	}

	public void setChannelCity(boolean channelCity) {
		this.channelCity = channelCity;
	}

	public boolean isContainsCoordinateFilter() {
		return containsCoordinateFilter;
	}

	public void setContainsCoordinateFilter(boolean containsCoordinateFilter) {
		this.containsCoordinateFilter = containsCoordinateFilter;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public double getStudentCityPrice() {
		return studentCityPrice;
	}

	public void setStudentCityPrice(double studentCityPrice) {
		this.studentCityPrice = studentCityPrice;
	}

	public boolean isKeySearch() {
		return isKeySearch;
	}

	public void setKeySearch(boolean isKeySearch) {
		this.isKeySearch = isKeySearch;
	}

	public boolean isClockRoom() {
		return isClockRoom;
	}

	public void setClockRoom(boolean isClockRoom) {
		this.isClockRoom = isClockRoom;
	}

	public String getCityUrl() {
		return cityUrl;
	}

	public void setCityUrl(String cityUrl) {
		this.cityUrl = cityUrl;
	}

	public boolean isMainLand() {
		return isMainLand;
	}

	public void setMainLand(boolean mainLand) {
		isMainLand = mainLand;
	}
	public String getCabin() {
		return cabin;
	}

	public void setCabin(String cabin) {
		this.cabin = cabin;
	}
	public void setHotelChannel(HotelChannel hotelChannel) {
		this.hotelChannel = hotelChannel;
	}

	public HotelChannel getHotelChannel() {
		return hotelChannel;
	}

	public GLatLng getCoord() {
		return coord;
	}

	public void setCoord(GLatLng coord) {
		this.coord = coord;
	}

    public String getMockFeature() {
        return mockFeature;
    }

    public void setMockFeature(String mockFeature) {
        this.mockFeature = mockFeature;
    }

    public SceneSearch getSceneSearch() {
        return sceneSearch;
    }

    public void setSceneSearch(SceneSearch sceneSearch) {
        this.sceneSearch = sceneSearch;
    }


}
