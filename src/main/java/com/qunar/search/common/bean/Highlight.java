package com.qunar.search.common.bean;

public class Highlight {
	private String seq;
	/**
	 * 星级档次
	 */
	private int grade;
	/**
	 * 评分占比
	 */
	private float scoreScale;

	/**
	 * 亮点信息
	 */
	private String tag;

	/**
	 * 满分占比
	 */
	private float fullScale;
	/**
	 * 情感词
	 */
	private String baseWord;

	/**
	 * 出行类型
	 */
	private String tripType;

	public String getSeq() {
		return seq;
	}

	public void setSeq(String seq) {
		this.seq = seq;
	}

	public int getGrade() {
		return grade;
	}

	public void setGrade(int grade) {
		this.grade = grade;
	}

	public float getScoreScale() {
		return scoreScale;
	}

	public void setScoreScale(float scoreScale) {
		this.scoreScale = scoreScale;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public float getFullScale() {
		return fullScale;
	}

	public void setFullScale(float fullScale) {
		this.fullScale = fullScale;
	}

	public String getBaseWord() {
		return baseWord;
	}

	public void setBaseWord(String baseWord) {
		this.baseWord = baseWord;
	}

	public String getTripType() {
		return tripType;
	}

	public void setTripType(String tripType) {
		this.tripType = tripType;
	}
}
