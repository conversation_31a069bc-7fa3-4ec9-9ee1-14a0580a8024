package com.qunar.search.common.bean;

import com.qunar.search.common.enums.GeneralPlatformType;
import com.qunar.search.common.enums.SceneSearch;
import com.qunar.search.common.enums.SortType;

/**
 * HotelFlexibleSortInfo
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public class HotelFlexibleSortInfo {

    private String hotelSeq;
    private SortType sortType;
    private GeneralPlatformType platform;
    private SceneSearch scene;
    private double liftRate;

    public String getHotelSeq() {
        return hotelSeq;
    }

    public void setHotelSeq(String hotelSeq) {
        this.hotelSeq = hotelSeq;
    }

    public double getLiftRate() {
        return liftRate;
    }

    public void setLiftRate(double liftRate) {
        this.liftRate = liftRate;
    }

    public GeneralPlatformType getPlatform() {
        return platform;
    }

    public void setPlatform(GeneralPlatformType platform) {
        this.platform = platform;
    }

    public SceneSearch getScene() {
        return scene;
    }

    public void setScene(SceneSearch scene) {
        this.scene = scene;
    }

    public SortType getSortType() {
        return sortType;
    }

    public void setSortType(SortType sortType) {
        this.sortType = sortType;
    }

    @Override
    public String toString() {
        return "HotelFlexibleSortInfo{" +
                "hotelSeq='" + hotelSeq + '\'' +
                ", sortType=" + sortType.getName() +
                ", platform=" + platform.getName() +
                ", scene=" + scene.getName() +
                ", liftRate=" + liftRate +
                '}';
    }
}
