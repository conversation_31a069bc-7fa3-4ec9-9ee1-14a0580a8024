package com.qunar.search.common.bean;

/**
 * HotelItemListSplitPoint
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public class HotelItemListSplitPoint {

    /**
     * 抽象出来排序列表分割点信息
     */
    /* 开始索引 */
    private int start = 0;
    /* 关键字完全命中集合最后索引的next */
    private int matchFullLastNext = 0;
    /* 非酒店名字非品牌命中集合最后索引的next */
    private int noHotelNameNoBrandLastNext = 0;
    /* 可订集合后首个非可订索引　*/
    private int bookableLastNext = 0;
    /* 最后索引 */
    private int end = 0;

    public HotelItemListSplitPoint(int start, int bookableLastNext, int end) {
        this.start = start;
        this.bookableLastNext = bookableLastNext;
        this.end = end;
    }

    public HotelItemListSplitPoint(int start, int matchFullLastNext, int noHotelNameNoBrandLastNext, int bookableLastNext, int end) {
        this.start = start;
        this.matchFullLastNext = matchFullLastNext;
        this.noHotelNameNoBrandLastNext = noHotelNameNoBrandLastNext;
        this.bookableLastNext = bookableLastNext;
        this.end = end;
    }

    public int getBookableLastNext() {
        return bookableLastNext;
    }

    public void setBookableLastNext(int bookableLastNext) {
        this.bookableLastNext = bookableLastNext;
    }

    public int getEnd() {
        return end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public int getMatchFullLastNext() {
        return matchFullLastNext;
    }

    public void setMatchFullLastNext(int matchFullLastNext) {
        this.matchFullLastNext = matchFullLastNext;
    }

    public int getNoHotelNameNoBrandLastNext() {
        return noHotelNameNoBrandLastNext;
    }

    public void setNoHotelNameNoBrandLastNext(int noHotelNameNoBrandLastNext) {
        this.noHotelNameNoBrandLastNext = noHotelNameNoBrandLastNext;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    @Override
    public String toString() {
        return "HotelItemListSplitPoint{" +
                "start=" + start +
                ", matchFullLastNext=" + matchFullLastNext +
                ", noHotelNameNoBrandLastNext=" + noHotelNameNoBrandLastNext +
                ", bookableLastNext=" + bookableLastNext +
                ", end=" + end +
                "}";
    }
}
