package com.qunar.search.common.bean;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgoXgbModelParam {

    private final static Logger log = LoggerFactory.getLogger(AlgoXgbModelParam.class);

    private static AlgoXgbModelParam emptyAlgoXgbModelParam = null;

    /**
     * 配置中策略前缀
     */
    public static final String STRATEGY_NAME_PREFIX = "strategy_";

    /**
     * 开关
     */
    private boolean xgbModelSwith;

    /**
     * 预测个数
     */
    private int predNum;

    /**
     * 算法
     */
    private String app;

    /**
     * 版本
     */
    private String verison;

    public boolean isAccept() {
        if (xgbModelSwith && predNum > 0 && !Strings.isNullOrEmpty(app) && !Strings.isNullOrEmpty(verison)) {
            return true;
        }
        return false;
    }

    /**
     * 解析所有的策略参数
     */
    public static Map<String, AlgoXgbModelParam> getAlgoXgbModelParam(Map<String, String> conf) {
        Map<String, AlgoXgbModelParam> map = Maps.newHashMap();

        Iterator<String> iterator = conf.keySet().iterator();
        while (iterator.hasNext()) {
            String keyName = iterator.next();
            if (!keyName.startsWith(STRATEGY_NAME_PREFIX)) {
                continue;
            }

            AlgoXgbModelParam xgbModelStrategy = new AlgoXgbModelParam();
            try {
                xgbModelStrategy = AlgoXgbModelParam.getXgbModelStrategy(conf, keyName);
            } catch (Exception e) {
                log.error("模型配置文件模型参数解析出错。", e);
                xgbModelStrategy = AlgoXgbModelParam.getEmptyInstance();
            } finally {
                map.put(keyName, xgbModelStrategy);
            }
        }

        return map;
    }

    private static final String REG = "|";
    private static final String ALGO_SWITCH_KEY = "algo_switch";
    private static final String RERANK_NUM_KEY = "rerank_num";

    /**
     * 解析一个的策略参数
     */
    private static AlgoXgbModelParam getXgbModelStrategy(Map<String, String> conf, String strategyName) {

        boolean algoSwitch = Boolean.valueOf(conf.get(ALGO_SWITCH_KEY));
        int predNum = Integer.valueOf(conf.get(RERANK_NUM_KEY));
        String strategy = conf.get(strategyName);

        String[] appVersion = StringUtils.split(strategy, REG);

        String app = null;
        String version = null;
        if (appVersion.length == 2) {
            app = appVersion[0];
            version = appVersion[1];
        }

        return new AlgoXgbModelParam(algoSwitch, predNum, app, version);
    }

    /**
     * 返回一个关闭的空策略，解析失败时关闭策略
     */
    public static AlgoXgbModelParam getEmptyInstance() {
        if (emptyAlgoXgbModelParam == null) {
            emptyAlgoXgbModelParam = new AlgoXgbModelParam(false, 0, null, null);
        }
        return emptyAlgoXgbModelParam;
    }
}
