package com.qunar.search.common.bean;


/**
 * todo 添加注释.
 */
public class CarnivalBean {
    private final String roomName;// 房型name
    private final int ppbpayment;
    private final String codebase;
    private final String promotionKey;// 优惠信息 只有一价格全包、超值买赠有该字段
    private final int price;// 只有震撼低价有最低价格
    public CarnivalBean(String roomName, int ppbpayment, String codebase,
                        String promotionKey, int price) {
        this.roomName = roomName;
        this.ppbpayment = ppbpayment;
        this.codebase = codebase;
        this.promotionKey = promotionKey;
        this.price = price;
    }
    public String getRoomName() {
        return roomName;
    }
    public int getPpbpayment() {
        return ppbpayment;
    }
    public String getCodebase() {
        return codebase;
    }
    public String getPromotionKey() {
        return promotionKey;
    }
    public int getPrice() {
        return price;
    }
    public String getUniqueKey(){
        return this.codebase + "|" + (this.roomName + this.ppbpayment).hashCode();
    }
}
