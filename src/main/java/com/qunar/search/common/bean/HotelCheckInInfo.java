package com.qunar.search.common.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * 字段含义：https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=750302666
 */
@Data
public class HotelCheckInInfo implements Serializable {

    private String hotelSeq;
    /**
     * 入住时间
     */
    private String needDate;
    /**
     * 入住日期单量
     */
    private int orderCnt;
    /**
     * 前一小时单量
     */
    private int beforeHourNum;
    /**
     * 前一小时单量占比
     */
    private double hourRate;
    /**
     * 酒店入住日期当天间夜价格
     */
    private double dayAvgNightFee;
    /**
     * 酒店一小时前单量间夜价格
     */
    private double hourAvgNightFee;
    /**
     * 小时内间夜价/入住日期当天间夜价
     */
    private double hourDayNightFee;

    /**
     * 城市入住日期当天单量
     */
    private int cityDayOrderNum;
    /**
     * 酒店单量占比
     */
    private double hotelCityOrderDayRate;
    /**
     * 城市一小时单量
     */
    private int cityBeforeHourOrderNum;
    /**
     * 酒店一小时内单量占比
     */
    private double hotelCityOrderHourRate;
    /**
     * 城市平均间夜价格
     */
    private double cityDayAvgNightFee;
    /**
     * 酒店间夜价格 / 城市间夜价格
     */
    private double hotelCityNightFeeDayRate;
    /**
     * 城市一小时内平均间夜价格
     */
    private double cityHourAvgNightFee;
    /**
     * 酒店小时间夜价格 / 城市小时间夜价格
     */
    private double hotelCityNightFeeHourRate;


    /**
     * geo 订单数量
     */
    private int geoidDayOrderNum;
    /**
     * 酒店在geo 订单占比
     */
    private double hotelGeoidOrderDayRate;
    /**
     * geo 在city 订单占比
     */
    private double geoidCityOrdeRdaRate;
    /**
     * geo 一小时内单量
     */
    private int geoidBeforeHourOrderNum;
    /**
     * 酒店geo 小时单量占比
     */
    private double hotelGeoidOrderHourRate;
    /**
     * geo 在城市小时内单量占比
     */
    private double geoidCityOrderHourRate;
    /**
     * geo 平均间夜价格
     */
    private double geoidDayAvgNightFee;
    /**
     * 酒店间夜均价 / geo 间夜均价
     */
    private double hotelGeoidNightFeeDayRate;
    /**
     * geo 间夜均价 / 城市间夜均价
     */
    private double geoidCityNightFeeDayRate;




}
