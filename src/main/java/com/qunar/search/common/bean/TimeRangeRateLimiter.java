package com.qunar.search.common.bean;

import com.google.common.util.concurrent.RateLimiter;
import lombok.*;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 时间范围限流器
 */
@Getter
@ToString
public class TimeRangeRateLimiter {

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRangeRateLimiterConfig {
        private String start;
        private String end;
        private  int rps;
    }

    private final TimeRange timeRange;

    /**
     * 限流器
     */
    private final RateLimiter limiter;

    public TimeRangeRateLimiter(TimeRangeRateLimiterConfig config) {
        this.timeRange = new TimeRange(config.getStart(), config.getEnd());
        this.limiter = RateLimiter.create(config.getRps());
    }


    public static void main(String[] args) {
        // 初始化时间范围配置
        List<TimeRangeRateLimiter> timeRanges = new ArrayList<>();
        timeRanges.add(new TimeRangeRateLimiter(new TimeRangeRateLimiterConfig("00:00","08:00", 50)));
        timeRanges.add(new TimeRangeRateLimiter(new TimeRangeRateLimiterConfig("12:00", "02:00", 80)));

        // 获取当前时间
        LocalTime currentTime = LocalTime.now();

        System.out.println(timeRanges.get(0));
        // 查找匹配的时间范围
        for (TimeRangeRateLimiter range : timeRanges) {
            if (range.getTimeRange().contains(currentTime)) {
                System.out.println("当前RPS: " + range.limiter.toString());
                return;
            }
        }

        System.out.println("未找到对应时间范围");
    }
}
