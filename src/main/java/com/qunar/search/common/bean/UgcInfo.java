package com.qunar.search.common.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class UgcInfo {

    private String hotelSeq;

    /**
     * 酒店评分
     */
    private float commentScore;

    /**
     * 调整后的酒店评分
     */
    private float adjustCommentScore;

    /**
     * 酒店评论数
     */
    private int commentCount;

    /**
     * 调整后的酒店评论数
     */
    private int adjustCommentCount;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public UgcInfo(String hotelSeq) {
        this.hotelSeq = hotelSeq;
    }
}
