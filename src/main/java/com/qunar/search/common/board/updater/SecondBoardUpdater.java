package com.qunar.search.common.board.updater;

import com.google.common.collect.Maps;

import com.qunar.search.common.board.bean.SecondBoard;
import com.qunar.search.common.board.dao.SecondBoardDao;
import com.qunar.search.common.update.AbstractUpdateTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;
import static com.qunar.search.common.util.CommonTools.generateKey;


/**
 * <AUTHOR>
 * @date 2019/5/13
 */
@Component
@Slf4j
public class SecondBoardUpdater extends AbstractUpdateTemplate<Long, SecondBoard> {

    private static final String WHOLE_CITY = "全城";

    private static volatile Map<String, SecondBoard> secondBoardMap = Maps.newConcurrentMap();

    @Autowired
    private SecondBoardDao secondBoardDao;

    public static Map<String, SecondBoard> getSecondBoardMap() {
        return secondBoardMap;
    }

    public static SecondBoard getSecondBoard(String cityCode, String poiName, String name) {
        if (StringUtils.isEmpty(cityCode) || StringUtils.isEmpty(poiName) || StringUtils.isEmpty(name)) {
            return null;
        }

        SecondBoard secondBoard = secondBoardMap.get(generateKey(cityCode, poiName, name));
        if (secondBoard == null) {
            // 通过商圈找不到二级榜单时，用全城下的二级榜单配置来兜底
            secondBoard = secondBoardMap.get(generateKey(cityCode, WHOLE_CITY, name));
        }
        return secondBoard;
    }

    @Scheduled(initialDelay = 0, fixedDelay = 5 * 60 * 1000)
    public void run() {
        update();
    }

    @Override
    protected boolean verify(List<SecondBoard> dataList) {
        return true;
    }

    @Override
    protected Long maxVersion() {
        LocalDateTime maxUpdateTime = secondBoardDao.maxUpdateTime();
        if (maxUpdateTime == null) {
            log.error("二级榜单数据, maxUpdateTime is null");
            return null;
        }
        return Timestamp.valueOf(maxUpdateTime).getTime();
    }

    @Override
    protected List<SecondBoard> findData(Long maxVersion) {
        if (lastVersion.get() == null) {
            lastVersion.set(0L);
        }
        LocalDateTime lastSyncTime = new Timestamp(lastVersion.get()).toLocalDateTime();
        return secondBoardDao.findData(lastSyncTime);
    }

    @Override
    protected boolean needUpdate(Long maxVersion) {
        return lastVersion.get() == null || (maxVersion != null && maxVersion > lastVersion.get());
    }

    @Override
    protected void process(List<SecondBoard> changedList) {
        for (SecondBoard secondBoard : changedList) {
            // cityCode字段支持多个城市，多个值之间用逗号分隔
            List<String> cityCodeList = COMMA_SPLITTER.splitToList(secondBoard.getCityCode());
            if (secondBoard.getStatus() == 0) {
                cityCodeList.forEach(cityCode -> secondBoardMap.put(generateKey(cityCode, secondBoard.getPoiName(), secondBoard.getName()), secondBoard));
            } else {
                cityCodeList.forEach(cityCode -> secondBoardMap.remove(generateKey(cityCode, secondBoard.getPoiName(), secondBoard.getName())));
            }
        }
    }

    @Override
    public String getDesc() {
        return "二级榜单数据";
    }

    @Override
    public boolean hasVersion() {
        return false;
    }
}
