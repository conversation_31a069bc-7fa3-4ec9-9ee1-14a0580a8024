package com.qunar.search.common.board.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 榜单卡片信息, List页和Home页共用
 * 各字段的具体含义见：http://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=240784454
 *
 * <AUTHOR>
 * @date 2019/3/7
 */
@Data
public class BoardInfo {

    private Integer citySource;
    private String poiName;
    private Integer source;
    private List<Board> boards;

    @Data
    @NoArgsConstructor
    public static class Board {
        private int first;
        private String name;
        private String subImg;
        private int second;
        private String title;
        private String subTitle;
        private String img;
        private String listImg;
        private String listTitle;
        private int grassCount;
        private String url;

        public Board(BoardListResult.Second second) {
            this.first = second.getFirst().getId();
            this.name = second.getFirst().getName();
            this.subImg = second.getFirst().getSubImg();
            this.second = second.getId();
            this.title = second.getTitle();
            this.subTitle = second.getSubTitle();
            this.img = second.getImg();
            this.listImg = second.getListImg();
            this.listTitle = second.getListTitle();
            this.grassCount = second.getGrassCount();
            this.url = second.getUrl();
        }
    }

}
