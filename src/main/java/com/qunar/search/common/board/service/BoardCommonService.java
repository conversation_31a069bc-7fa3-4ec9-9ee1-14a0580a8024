package com.qunar.search.common.board.service;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.search.common.board.bean.*;
import com.qunar.search.common.board.config.BoardCommonConfig;
import com.qunar.search.common.board.config.BoardLocatorConfig;
import com.qunar.search.common.board.updater.SecondBoardUpdater;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.enums.BoardInvokeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.qunar.search.common.util.CommonTools.generateKey;

/**
 * <AUTHOR>
 * @date 2019/9/26
 */
@Slf4j
public class BoardCommonService {
    private static final Comparator<BoardListResult.Second> SECOND_BOARD_COMPARATOR = Comparator.comparing(BoardListResult.Second::getSort);

    /**
     * 生成榜单卡片信息
     *
     * @param cityUrl 城市code
     * @param poiName poi名称
     * @param userAction 用户行为
     * @param userIdentity 用户身份
     * @param countLimit 榜单卡片数量限制
     * @param invokeType 调用类型，用于区分list页和home页
     * @return 卡片信息
     */
    public static BoardInfo generateBoardInfo(String cityUrl, String poiName, BoardUserAction userAction, BoardUserIdentity userIdentity, int countLimit, BoardInvokeType invokeType) {
        BoardListResult boardListResult = BoardDataService.getBoardList(generateKey(cityUrl, poiName));
        if (boardListResult == null) {
            return null;
        }
        // 获取一级榜单列表
        List<BoardListResult.First> firstBoardList = boardListResult.getList();
        if (CollectionUtils.isEmpty(firstBoardList)) {
            return null;
        }

        BoardInfo boardInfo = new BoardInfo();
        boardInfo.setCitySource(boardListResult.getCitySource());
        boardInfo.setPoiName(poiName);
        boardInfo.setSource(boardListResult.getSource());

        List<BoardInfo.Board> boards = buildBoards(firstBoardList, userAction, userIdentity, cityUrl, invokeType, poiName);
        if (CollectionUtils.isNotEmpty(boards)) {
            boards = boards.stream().limit(countLimit).collect(Collectors.toList());
        }

        boardInfo.setBoards(boards);
        return boardInfo;
    }

    /**
     * 通过用户行为和用户身份来召回二级榜单，用户行为 > 用户身份
     */
    private static List<BoardInfo.Board> buildBoards(List<BoardListResult.First> firstBoards,
            BoardUserAction userAction, BoardUserIdentity userIdentity, String cityUrl, BoardInvokeType invokeType, String poiName) {
        List<BoardListResult.Second> secondBoards = Lists.newArrayList();
        for (BoardListResult.First firstBoard : firstBoards) {
            firstBoard.getSubList().forEach(secondBoard -> secondBoard.setFirst(firstBoard));
            secondBoards.addAll(firstBoard.getSubList());
        }

        List<BoardListResult.Second> matchUserActionList = matchUserAction(secondBoards, userAction, cityUrl, poiName);
        List<BoardListResult.Second> matchUserIdentityList = matchUserIdentity(secondBoards, userIdentity, cityUrl, poiName);
        List<BoardListResult.Second> matchList = merge(matchUserActionList, matchUserIdentityList);
        matchList = matchList.stream().distinct().collect(Collectors.toList());

        // 如果通过用户行为和用户身份都匹配不到榜单且调用方是列表页的时候，从每个一级榜单中挑一个二级榜单
        if (CollectionUtils.isEmpty(matchList) && invokeType == BoardInvokeType.LIST) {
            matchList =  matchByDefault(firstBoards, cityUrl, poiName);
        }

        secondBoards.removeAll(matchList);
        // 剩下的这些按照sort排序
        secondBoards.sort(Comparator.comparing(BoardListResult.Second::getSort));

        List<BoardListResult.Second> resultList = merge(matchList, secondBoards);

        List<BoardInfo.Board> boards = Lists.newArrayList();

        Map<String, Integer> topBoardConfig = RankSystemConfig.getTopBoard();


        Map<Integer, BoardInfo.Board> topBoard = Maps.newHashMap();
        for (BoardListResult.Second secondBoard : resultList) {
            BoardInfo.Board e = new BoardInfo.Board(secondBoard);
            String title = secondBoard.getTitle();
            if (topBoardConfig.containsKey(title)) {
                topBoard.put(topBoardConfig.get(title), e);
                continue;
            }
            boards.add(e);
        }

        topBoard.forEach(boards::add);

        Map<Integer, BoardInfo.Board> locator = BoardLocatorConfig.getLocator(cityUrl, poiName);
        if (MapUtils.isNotEmpty(locator)) {
            for (Map.Entry<Integer, BoardInfo.Board> l : locator.entrySet()) {
                int index = l.getKey();
                if (index > boards.size()) {
                    boards.add(l.getValue());
                } else {
                    boards.add(index, l.getValue());
                }
            }
        }
        return boards;
    }

    /**
     * 每个一级榜单中选一个二级榜单，档次高的二级榜单优先召回
     *
     * @param firstBoards 一级榜单列表
     * @param cityUrl 城市url
     * @return 榜单卡片信息列表
     */
    private static List<BoardListResult.Second> matchByDefault(List<BoardListResult.First> firstBoards, String cityUrl, String poiName) {
        List<BoardListResult.Second> boards = Lists.newArrayList();
        for (BoardListResult.First firstBoard : firstBoards) {
            List<BoardListResult.Second> secondBoards = firstBoard.getSubList();
            if (CollectionUtils.isEmpty(secondBoards)) {
                continue;
            }
            // 默认按照榜单的分档来召回，档次高的优先召回
            BoardListResult.Second secondBoard = recallByDefault(secondBoards, cityUrl, poiName);
            boards.add(secondBoard);
        }
        return boards;
    }

    private static BoardListResult.Second recallByDefault(List<BoardListResult.Second> secondBoards, String cityUrl, String poiName) {
        ListMultimap<Integer, BoardListResult.Second> multimap = ArrayListMultimap.create();
        for (BoardListResult.Second secondBoard : secondBoards) {
            String boardName = secondBoard.getTitle();
            SecondBoard secondBoardInfo = SecondBoardUpdater.getSecondBoard(cityUrl, poiName, boardName);
            if (secondBoardInfo == null) {
                continue;
            }
            BoardCommonConfig.ConfigInfo configInfo = new BoardCommonConfig.ConfigInfo(secondBoardInfo);

            // 按照榜单的分档进行聚合
            multimap.put(configInfo.getGrade(), secondBoard);
        }

        if (multimap.isEmpty()) {
            return secondBoards.get(0);
        }

        // 按榜单的分档进行排序，grade的数值小代表档次高。若档次最高的榜单存在多个，则随机返回一个。
        List<Integer> grades = multimap.keySet().stream().sorted().collect(Collectors.toList());
        List<BoardListResult.Second> matchList = multimap.get(grades.get(0));
        matchList.sort(SECOND_BOARD_COMPARATOR);
        return matchList.get(0);
    }

    private static List<BoardListResult.Second> matchUserAction(List<BoardListResult.Second> secondBoards, BoardUserAction userAction, String cityUrl, String poiName) {
        if (userAction == null || StringUtils.isEmpty(cityUrl)) {
            return Collections.emptyList();
        }
        List<BoardListResult.Second> matchThemeList = Lists.newArrayList();
        List<BoardListResult.Second> matchStarList = Lists.newArrayList();
        List<BoardListResult.Second> matchPriceList = Lists.newArrayList();
        List<BoardListResult.Second> matchSortList = Lists.newArrayList();
        for (BoardListResult.Second secondBoard : secondBoards) {
            String boardName = secondBoard.getTitle();
            SecondBoard secondBoardInfo = SecondBoardUpdater.getSecondBoard(cityUrl, poiName, boardName);
            if (secondBoardInfo == null) {
                continue;
            }
            BoardCommonConfig.ConfigInfo configInfo = new BoardCommonConfig.ConfigInfo(secondBoardInfo);

            if (matchTheme(configInfo, userAction.getThemes())) {
                matchThemeList.add(secondBoard);
            }

            if (matchStar4UserAction(configInfo, userAction.getStarList())) {
                matchStarList.add(secondBoard);
            }

            if (matchPrice(configInfo, userAction.getMinPrice(), userAction.getMaxPrice())) {
                matchPriceList.add(secondBoard);
            }

            if (matchSort(configInfo, userAction.getSort())) {
                matchSortList.add(secondBoard);
            }
        }

        List<BoardListResult.Second> resultList = merge(matchThemeList, matchStarList, matchPriceList, matchSortList);

        return resultList.stream().distinct().collect(Collectors.toList());
    }

    private static List<BoardListResult.Second> matchUserIdentity(List<BoardListResult.Second> secondBoards, BoardUserIdentity userIdentity, String cityUrl, String poiName) {
        if (userIdentity == null || StringUtils.isEmpty(cityUrl)) {
            return Collections.emptyList();
        }
        List<BoardListResult.Second> matchThemeList = Lists.newArrayList();
        List<BoardListResult.Second> matchStarList = Lists.newArrayList();
        for (BoardListResult.Second secondBoard : secondBoards) {
            String boardName = secondBoard.getTitle();
            SecondBoard secondBoardInfo = SecondBoardUpdater.getSecondBoard(cityUrl, poiName, boardName);
            if (secondBoardInfo == null) {
                continue;
            }
            BoardCommonConfig.ConfigInfo configInfo = new BoardCommonConfig.ConfigInfo(secondBoardInfo);

            if (matchTheme(configInfo, userIdentity.getThemes())) {
                matchThemeList.add(secondBoard);
            }

            if (matchStar4UserIdentity(configInfo, userIdentity.getStarLabel())) {
                matchStarList.add(secondBoard);
            }
        }

        List<BoardListResult.Second> resultList = merge(matchThemeList, matchStarList);
        return resultList.stream().distinct().collect(Collectors.toList());
    }

    @SafeVarargs
    private static List<BoardListResult.Second> merge(List<BoardListResult.Second> ... secondBoardsList) {
        List<BoardListResult.Second> resultList = Lists.newArrayList();
        for (List<BoardListResult.Second> secondBoards : secondBoardsList) {
            resultList.addAll(secondBoards);
        }
        return resultList;
    }

    private static boolean matchTheme(BoardCommonConfig.ConfigInfo configInfo, List<String> themes) {
        if (CollectionUtils.isEmpty(configInfo.getThemes()) || CollectionUtils.isEmpty(themes)) {
            return false;
        }
        return !Collections.disjoint(configInfo.getThemes(), themes);
    }

    private static boolean matchStar4UserIdentity(BoardCommonConfig.ConfigInfo configInfo, String starLabel) {
        if (CollectionUtils.isEmpty(configInfo.getStars()) || StringUtils.isEmpty(starLabel)) {
            return false;
        }
        return configInfo.getStars().contains(starLabel);
    }

    private static boolean matchStar4UserAction(BoardCommonConfig.ConfigInfo configInfo, List<String> stars) {
        if (CollectionUtils.isEmpty(configInfo.getStars()) || CollectionUtils.isEmpty(stars)) {
            return false;
        }
        return !Collections.disjoint(configInfo.getStars(), stars);
    }

    private static boolean matchPrice(BoardCommonConfig.ConfigInfo configInfo, int minPrice, int maxPrice) {
        if (minPrice == 0 && maxPrice == Integer.MAX_VALUE) {
            return false;
        }
        int boardMinPrice = configInfo.getMinPrice();
        int boardMaxPrice = configInfo.getMaxPrice();

        // 如果都等于0，说明没有配置价格。返回false
        if (boardMinPrice == 0 && boardMaxPrice == 0) {
            return false;
        }

        // 用户选的价格区间与榜单的价格区间存在交集时，则算匹配上了
        if (boardMinPrice <= minPrice && minPrice <= boardMaxPrice) {
            return true;
        }
        return boardMinPrice <= maxPrice && maxPrice <= boardMaxPrice;
    }

    private static boolean matchSort(BoardCommonConfig.ConfigInfo configInfo, String sort) {
        if (StringUtils.isEmpty(sort)) {
            return false;
        }
        return StringUtils.equals(configInfo.getSort(), sort);
    }

    /**
     * 默认情况下，返回榜单标签优先级最高的排名信息
     */
    public static BoardHotelRankingResult computeByDefault(List<BoardHotelRankingResult> list) {
        BoardHotelRankingResult result = list.get(0);
        String boardName = result.getSecondName();
        String cityUrl = result.getCityCode();
        String poiName = result.getPoiName();
        int highestPriority = getBoardPriority(cityUrl, poiName, boardName);
        for (int i = 1; i < list.size(); ++i) {
            int priority = getBoardPriority(cityUrl, list.get(i).getPoiName(), list.get(i).getSecondName());
            // 数值小代表优先级高
            if (priority < highestPriority) {
                highestPriority = priority;
                result = list.get(i);
            }
        }
        return result;
    }

    /**
     * 从配置信息中获取榜单标签优先级
     */
    private static int getBoardPriority(String cityUrl, String poiName, String boardName) {
        SecondBoard secondBoard = SecondBoardUpdater.getSecondBoard(cityUrl, poiName, boardName);
        if (secondBoard != null) {
            return secondBoard.getPriority();
        }
        return Integer.MAX_VALUE;
    }
}