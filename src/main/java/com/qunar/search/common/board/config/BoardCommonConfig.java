package com.qunar.search.common.board.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.redis.storage.Sedis3;
import com.qunar.search.common.board.bean.BoardUserIdentity;
import com.qunar.search.common.board.bean.SecondBoard;
import com.qunar.search.common.board.updater.SecondBoardUpdater;
import com.qunar.search.common.util.SpringContextUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.TypedConfig;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;
import static com.qunar.search.common.constants.CommonConstants.PIPE_SPLITTER;
import static com.qunar.search.common.constants.CommonConstants.POUND_SPLITTER;

/**
 * <AUTHOR>
 * @date 2019/3/5
 */
@Slf4j
public class BoardCommonConfig {

    private static Sedis3 userInfoSedis;

    static {
        userInfoSedis = (Sedis3) SpringContextUtil.getBean("boardUserInfoSedis");
    }

    public static ConfigInfo getConfigInfo(String cityUrl, String poiName, String boardName) {
        SecondBoard secondBoard = SecondBoardUpdater.getSecondBoard(cityUrl, poiName, boardName);
        if (secondBoard != null) {
            return new ConfigInfo(secondBoard);
        }
        return null;
    }

    public static BoardUserIdentity getUserIdentity(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        try {
            String habit = userInfoSedis.get(StringUtils.join("user_", userId));
            if (StringUtils.isEmpty(habit)) {
                return null;
            }

            List<String> items = POUND_SPLITTER.splitToList(habit);
            if (items.size() < 2) {
                return null;
            }

            String starLabel = UserIdentityConfig.getStarMap().get(items.get(0));

            Map<String, String> themeMap = UserIdentityConfig.getThemeMap();
            List<String> themes = PIPE_SPLITTER.splitToList(items.get(1))
                    .stream()
                    .map(t -> themeMap.getOrDefault(t, StringUtils.EMPTY))
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

            return new BoardUserIdentity(starLabel, themes);

        } catch (Exception e) {
            QMonitor.recordOne("getBoardUserIdentityFailed");
            log.error("从redis获取榜单用户身份信息失败", e);
            return null;
        }
    }

    static class UserIdentityConfig implements TypedConfig.Parser<Map<String, Map<String, String>>> {

        private static UserIdentityConfig parser = new UserIdentityConfig();
        private static TypedConfig<Map<String, Map<String, String>>> config = TypedConfig.get("boardUserIdentity.json", parser);

        @Override
        public Map<String, Map<String, String>> parse(String data) throws IOException {
            return new ObjectMapper().readValue(data, new TypeReference<Map<String, Map<String, String>>>() {
            });
        }

        static Map<String, String> getStarMap() {
            return getLabelMap("starMap");
        }

        static Map<String, String> getThemeMap() {
            return getLabelMap("themeMap");
        }

        static Map<String, String> getLabelMap(String name) {
            if (config == null) {
                return Collections.emptyMap();
            }

            Map<String, Map<String, String>> map = config.current();
            if (MapUtils.isEmpty(map)) {
                return Collections.emptyMap();
            }

            Map<String, String> labelMap = map.get(name);
            if (MapUtils.isEmpty(labelMap)) {
                return Collections.emptyMap();
            }

            return labelMap;
        }
    }


    @Data
    @NoArgsConstructor
    public static class ConfigInfo {

        /**
         * 榜单标签优先级
         */
        private int priority;

        /**
         * 榜单分档
         */
        private Integer grade;

        /**
         * 榜单的排序规则
         */
        private String sort;

        /**
         * 最低价格
         */
        private int minPrice;

        /**
         * 最高价格
         */
        private int maxPrice;

        /**
         * 榜单的星级，三星/舒适，四星/高档 等等
         */
        private List<String> stars;

        /**
         * 榜单的主题标签
         */
        private List<String> themes;

        /**
         * 种草数
         */
        private int grass;

        public ConfigInfo(SecondBoard secondBoard) {
            this.priority = secondBoard.getPriority();
            this.grade = secondBoard.getGrade();
            this.sort = secondBoard.getSortRule();
            this.minPrice = secondBoard.getMinPrice();
            this.maxPrice = secondBoard.getMaxPrice();
            this.stars = COMMA_SPLITTER.splitToList(secondBoard.getStars());
            this.themes = COMMA_SPLITTER.splitToList(secondBoard.getThemes());
        }

    }

}
