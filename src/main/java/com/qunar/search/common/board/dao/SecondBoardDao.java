package com.qunar.search.common.board.dao;


import com.qunar.search.common.board.bean.SecondBoard;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/5/13
 */
public interface SecondBoardDao {

    /**
     * 查询最大更新时间
     *
     * @return 最大update_time
     */
    @Select("select max(update_time) from second_board")
    LocalDateTime maxUpdateTime();

    /**
     * 查询update_time大于最后一次同步时间的所有记录
     *
     * @param lastSyncTime 最后一次同步的时间
     * @return 满足 lastSyncTime < update_time 条件的所有记录
     */
    @Select("select city_code as cityCode, city_name as cityName, first_name as firstName, name, second_id as secondId,"
            + " sort, priority, grade, sort_rule as sortRule, min_price as minPrice, max_price as maxPrice, stars, grass, "
            + "themes, tags, description, list_img as listImg, touch_list_img as touchListImg,"
            + " touch_detail_img as touchDetailImg, status, poi_name as poiName from second_board where #{lastSyncTime} < update_time order by update_time")
    @ResultType(value = SecondBoard.class)
    List<SecondBoard> findData(@Param("lastSyncTime") LocalDateTime lastSyncTime);

}
