package com.qunar.search.common.board.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.search.common.board.bean.BoardInfo;
import com.qunar.search.common.board.bean.BoardListResult.First;
import com.qunar.search.common.board.bean.BoardListResult.Second;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import qunar.tc.qconfig.client.spring.QMapConfig;
import qunar.tc.qconfig.client.spring.QTableConfig;

import java.util.*;

/**
 * 榜单资源位 FD-27710
 */
public class BoardLocatorConfig {
    @QMapConfig(value = "mobile_rank_system_config.properties", key = "board.locator.enable", defaultValue = "true")
    private volatile static boolean enable;
    private volatile static Map<String, List<First>> CITY_FIRST_MAP = Collections.emptyMap();
    private volatile static Map<String, TreeMap<Integer, BoardInfo.Board>> CITY_SECOND_MAP = Collections.emptyMap();

    @QMapConfig(value = "mobile_rank_system_config.properties", key = "board.first.icon", defaultValue = "https://source.qunarzz.com/site/images/zhuanti/huodong/42x42teseL.png")
    private String firstIcon = "";

    @QTableConfig("board_locator.t")
    public void inject(List<Second> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<First>> cityFirstMap = Maps.newHashMap();
        Map<String, TreeMap<Integer, BoardInfo.Board>> citySecondMap = Maps.newHashMap();
        Map<String, First> firstMap = Maps.newHashMap();

        list.forEach(b -> {
            String key = b.getCityCode() + "_" + b.getPoiName();

            String firstName = b.getFirstName();
            First first = firstMap.computeIfAbsent(firstName, n -> {
                First f = new First();
                f.setName(n);
                f.setSubImg(firstIcon);
                return f;
            });
            b.setFirst(first);
            if (CollectionUtils.isEmpty(first.getSubList())) {
                first.setSubList(Lists.newArrayList());
            }
            first.getSubList().add(b);

            citySecondMap.computeIfAbsent(key, t -> Maps.newTreeMap()).put(b.getSort() - 1, new BoardInfo.Board(b));
            cityFirstMap.computeIfAbsent(key, t -> Lists.newArrayList()).add(first);
        });

        setCityFirstMap(cityFirstMap);
        setCitySecondMap(citySecondMap);
    }

    public static void setCityFirstMap(Map<String, List<First>> cityFirstMap) {
        CITY_FIRST_MAP = cityFirstMap;
    }

    public static void setCitySecondMap(Map<String, TreeMap<Integer, BoardInfo.Board>> citySecondMap) {
        CITY_SECOND_MAP = citySecondMap;
    }

    public static Map<Integer, BoardInfo.Board> getLocator(String city, String poiName) {
        if (!enable) {
            return Collections.emptyMap();
        }

        return CITY_SECOND_MAP.get(city + "_" + poiName);
    }

    public static Optional<List<First>> getFirst(String city, String poiName) {
        if (!enable) {
            return Optional.empty();
        }

        return Optional.ofNullable(CITY_FIRST_MAP.get(city + "_" + poiName));
    }
}