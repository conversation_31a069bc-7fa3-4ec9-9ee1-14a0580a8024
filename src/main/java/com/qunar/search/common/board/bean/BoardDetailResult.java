package com.qunar.search.common.board.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/5
 */
@Data
@NoArgsConstructor
public class BoardDetailResult {

    private int citySource;
    private int source;
    private int first;
    private String firstName;
    private String firstImg;
    private int second;
    private String img;
    private String subImg;
    private String title;
    private String subTitle;
    private List<HotelRanking> list;
    private Map<String, Object> credibilityInfo;

    @Data
    @NoArgsConstructor
    public static class HotelRanking {
        private String name;
        private String seq;
        private String title;
        private List<String> img;
        private String reason;
        private String distancePoi;
        private int sort;
		/**
		 * 头像
		 */
		private String head;
		/**
		 * 昵称
		 */
		private String nickname;
		/**
		 * 是否是试睡员
		 */
		private boolean sleep;
		/**
		 * 入住时间
		 */
		private String time;
		/**
		 * 试睡员评论
		 */
		private String ugcComment;
    }

}
