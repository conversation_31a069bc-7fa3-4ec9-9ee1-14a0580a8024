package com.qunar.search.common.board.service;

import com.google.common.collect.Maps;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.redis.storage.Sedis3;
import com.qunar.search.common.board.bean.BoardDetailResult;
import com.qunar.search.common.board.bean.BoardHotelRankingResult;
import com.qunar.search.common.board.bean.BoardListResult;
import com.qunar.search.common.board.bean.BoardPoiInfo;
import com.qunar.search.common.util.JsonUtils;
import com.qunar.search.common.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/9/26
 */
@Slf4j
public class BoardDataService {

    private static final String BOARD_LIST_RESULT_KEY = "boardListResult";
    private static final String BOARD_DETAIL_RESULT_KEY = "boardDetailResult";
    private static final String HOTEL_HIT_BOARDS_KEY = "hotelHitBoards";
    private static final String BOARD_CITY_POI_KEY = "boardCityPoi";
    private static final String SECOND_BOARD_COUNT_KEY = "secondBoardCount";
    private static final int DEFAULT_SECOND_BOARD_COUNT = 100;
    private static final BoardListResult EMPTY_BOARD_LIST_RESULT = new BoardListResult();
    private static final BoardDetailResult EMPTY_BOARD_DETAIL_RESULT = new BoardDetailResult();

    private static Sedis3 boardUserInfoSedis;

    static {
        boardUserInfoSedis = (Sedis3) SpringContextUtil.getBean("boardUserInfoSedis");
    }

    /**
     * 获取榜单列表
     *
     * @param condition cityUrl#poiName
     * @return 该条件下的榜单列表
     */
    public static BoardListResult getBoardList(String condition) {
        try {
            String resultStr = boardUserInfoSedis.hget(BOARD_LIST_RESULT_KEY, condition);
            return Optional.ofNullable(JsonUtils.fromJson(resultStr, BoardListResult.class)).orElse(EMPTY_BOARD_LIST_RESULT);
        } catch (Exception e) {
            log.error("从redis获取榜单列表数据失败。condition:{}", condition, e);
            QMonitor.recordOne("redisGetBoardListFailed");
        }
        return EMPTY_BOARD_LIST_RESULT;
    }

    /**
     * 获取榜单详情数据
     *
     * @param condition cityUrl#poiName#firstId#secondId
     * @return 该条件下的榜单详情数据
     */
    public static BoardDetailResult getBoardDetail(String condition) {
        try {
            String resultStr = boardUserInfoSedis.hget(BOARD_DETAIL_RESULT_KEY, condition);
            return Optional.ofNullable(JsonUtils.fromJson(resultStr, BoardDetailResult.class)).orElse(EMPTY_BOARD_DETAIL_RESULT);
        } catch (Exception e) {
            log.error("从redis获取榜单详情数据失败。condition:{}", condition, e);
            QMonitor.recordOne("redisGetBoardDetailFailed");
        }
        return EMPTY_BOARD_DETAIL_RESULT;
    }

    /**
     * 获取榜单城市poi筛选项数据
     */
    public static List<BoardPoiInfo> getBoardPoiInfoList() {
        try {
            String resultStr = boardUserInfoSedis.get(BOARD_CITY_POI_KEY);
            return JsonUtils.fromJson(resultStr, JsonUtils.buildCollectionType(List.class, BoardPoiInfo.class));
        } catch (Exception e) {
            log.error("从redis获取榜单城市poi筛选项失败", e);
            QMonitor.recordOne("redisBoardCityPoiFailed");
        }
        return Collections.emptyList();
    }

    /**
     * 获取单个酒店所命中的所有榜单
     *
     * @param hotelSeq 酒店编号
     * @return 满足条件的list，不存在则返回null
     */
    public static List<BoardHotelRankingResult> getHotelHitBoards(String hotelSeq) {
        try {
            String resultStr = boardUserInfoSedis.hget(HOTEL_HIT_BOARDS_KEY, hotelSeq);
            return JsonUtils.fromJson(resultStr, JsonUtils.buildCollectionType(List.class, BoardHotelRankingResult.class));
        } catch (Exception e) {
            log.error("从redis获取酒店命中的榜单失败。hotelSeq:{}", hotelSeq, e);
            QMonitor.recordOne("redisHotelHitBoardsFailed");
        }
        return Collections.emptyList();
    }

    /**
     * 批量获取多个酒店命中的榜单
     *
     * @param seqList 酒店seq列表
     * @return 这些酒店命中的榜单
     */
    public static Map<String, List<BoardHotelRankingResult>> batchFetchHotelHitBoards(List<String> seqList) {
        if (CollectionUtils.isEmpty(seqList)) {
            return Collections.emptyMap();
        }

        try {
            int size = seqList.size();
            List<String> dataList = boardUserInfoSedis.hmget(HOTEL_HIT_BOARDS_KEY, seqList.toArray(new String[size]));
            if (CollectionUtils.isEmpty(dataList)) {
                return Collections.emptyMap();
            }
            Map<String, List<BoardHotelRankingResult>> resultMap = Maps.newHashMap();
            for (int i = 0; i < size; i++) {
                String hotelSeq = seqList.get(i);
                String jsonStr = dataList.get(i);
                List<BoardHotelRankingResult> hitBoards = JsonUtils.fromJson(jsonStr, JsonUtils.buildCollectionType(List.class, BoardHotelRankingResult.class));
                if (CollectionUtils.isEmpty(hitBoards)) {
                    continue;
                }
                resultMap.put(hotelSeq, hitBoards);
            }
            return resultMap;
        } catch (Exception e) {
            log.error("从redis批量获取酒店命中的榜单失败。seqList:{}", seqList, e);
            QMonitor.recordOne("redisBatchHotelHitBoardsFailed");
        }

        return Collections.emptyMap();
    }

    /**
     * 获取二级榜单数量
     */
    public static int getSecondBoardCountFromRedis() {
        try {
            String resultStr = boardUserInfoSedis.get(SECOND_BOARD_COUNT_KEY);
            return NumberUtils.toInt(resultStr, DEFAULT_SECOND_BOARD_COUNT);
        } catch (Exception e) {
            log.error("从redis获取二级榜单数量失败", e);
            QMonitor.recordOne("redisSecondBoardCountFailed");
        }
        return DEFAULT_SECOND_BOARD_COUNT;
    }

}
