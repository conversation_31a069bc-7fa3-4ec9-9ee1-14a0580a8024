package com.qunar.search.common.board.updater;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.hotel.qhotel.qpoi.entity.QPoi;
import com.qunar.hotel.qhotel.qpoi.service.QPoiSearchService;

import com.qunar.search.common.board.bean.BoardKeywordPoi;
import com.qunar.search.common.board.dao.BoardKeywordPoiDao;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.update.UpdaterTemplate;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.collections.CollectionUtils.isEmpty;

/**
 * 榜单的关键词和poi的映射关系数据的更新器
 *
 * <AUTHOR>
 * @date 2019/5/13
 */
@Component
@Slf4j
public class BoardKeywordPoiUpdater extends UpdaterTemplate<BoardKeywordPoi> {

    private static volatile Map<String, Map<String, String>> cityKeywordPoiMap = Maps.newConcurrentMap();

    private static Map<String, List<Pair<String, GLatLng>>> cityPoi = Maps.newConcurrentMap();

    private static Cache<GLatLng, String> poiCache = CacheBuilder.newBuilder().maximumSize(10000)
            .expireAfterWrite(60, TimeUnit.MINUTES).build();

    @Autowired
    private BoardKeywordPoiDao boardKeywordPoiDao;

    @Resource
    private QPoiSearchService qPoiSearchService;

    @Scheduled(initialDelay = 5 * 60 * 1000, fixedDelay = 60 * 60 * 1000)
    public void run() {
        update();
    }

    @Override
    protected Integer maxVersion() {
        return boardKeywordPoiDao.maxVersion();
    }

    @Override
    protected List<BoardKeywordPoi> findData(Integer maxVersion) {
        return boardKeywordPoiDao.findData(maxVersion);
    }

    @Override
    protected void process(List<BoardKeywordPoi> list) {
        Map<String, Map<String, String>> tempMap = Maps.newConcurrentMap();
        for (BoardKeywordPoi keywordPoi : list) {
            Map<String, String> keywordPoiMap = tempMap.get(keywordPoi.getCityCode());
            if (keywordPoiMap == null) {
                keywordPoiMap = Maps.newHashMap();
            }
            keywordPoiMap.put(keywordPoi.getKeyword().intern(), keywordPoi.getPoiName().intern());
            tempMap.put(keywordPoi.getCityCode(), keywordPoiMap);
        }

        setCityKeywordPoiMap(tempMap);

        updateCityPoi();
    }

    private void updateCityPoi() {
        Map<String, List<Pair<String, GLatLng>>> temp = Maps.newConcurrentMap();
        for (Map.Entry<String, Map<String, String>> entry : cityKeywordPoiMap.entrySet()) {
            String cityUrl = entry.getKey();
            Map<String, String> keywordPoiMap = entry.getValue();
            List<String> poiNameList = keywordPoiMap.values().stream().distinct().collect(Collectors.toList());

            List<Pair<String, GLatLng>> list = Lists.newArrayList();
            for (String poiName : poiNameList) {
                QPoi qPoi = getQPoi(cityUrl, poiName);
                if (qPoi == null || qPoi.getGPoint() == null) {
                    log.warn("获取不到坐标。cityUrl:{}, poiName:{}", cityUrl, poiName);
                    continue;
                }
                GLatLng g = new GLatLng(qPoi.getGPoint().getLat(), qPoi.getGPoint().getLng());
                list.add(Pair.of(poiName, g));
            }

            temp.put(cityUrl, list);
        }

        setCityPoi(temp);
    }

    private QPoi getQPoi(String cityUrl, String poiName) {
        try {
            return qPoiSearchService.searchByName(cityUrl, poiName);
        } catch (Exception e) {
            log.error("获取poi坐标失败, cityUrl:{}, poiName:{}", cityUrl, poiName, e);
        }
        return null;
    }

    private static void setCityPoi(Map<String, List<Pair<String, GLatLng>>> cityPoi) {
        BoardKeywordPoiUpdater.cityPoi = cityPoi;
    }

    public static Map<String, String> getPoiMap(String cityUrl) {
        return cityKeywordPoiMap.get(cityUrl);
    }

    /**
     * 身边搜索时，召回距用户distanceLimit米内的最近的一个存在榜单的poi名称；若distanceLimit内没有存在榜单的poi，则返回空字符串
     *
     * @param userLocation 用户的坐标
     * @param cityUrl 城市url
     * @param distanceLimit 距离限制，单位是米
     * @return 返回满足条件的poi名称；若无满足条件的poi，则返回空字符串
     */
    public static String getPoiNameOfNearbySearch(GLatLng userLocation, String cityUrl, int distanceLimit) {
        try {
            return poiCache.get(userLocation, () -> {
                List<Pair<String, GLatLng>> poiList = cityPoi.get(cityUrl);
                if (isEmpty(poiList)) {
                    return StringUtils.EMPTY;
                }

                poiList = poiList.stream().sorted(Comparator.comparingDouble(o -> o.getValue().distance(userLocation))).collect(Collectors.toList());

                Pair<String, GLatLng> poi = poiList.get(0);
                double distance = poi.getValue().distance(userLocation) * 1000.0;
                if (distance < distanceLimit) {
                    return poi.getKey();
                }
                return StringUtils.EMPTY;
            });
        } catch (ExecutionException e) {
            log.error("", e);
        }
        return StringUtils.EMPTY;
    }

    private static void setCityKeywordPoiMap(Map<String, Map<String, String>> cityKeywordPoiMap) {
        BoardKeywordPoiUpdater.cityKeywordPoiMap = cityKeywordPoiMap;
    }

    @Override
    public String getDesc() {
        return "榜单的关键词和POI的映射关系";
    }
}
