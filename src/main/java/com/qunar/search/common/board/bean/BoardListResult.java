package com.qunar.search.common.board.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import lombok.*;
import org.apache.commons.beanutils.BeanUtils;
import qunar.tc.qconfig.client.spring.DisableQConfig;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/5
 */
@Data
@NoArgsConstructor
public class BoardListResult {

    private int citySource;
    private int source;
    private String subImg;
    private List<First> list;

    @Getter
    @Setter
    @NoArgsConstructor
    public static class First {
        private int id;
        private String name;
        private String img;
        private String subImg;
        private List<Second> subList;
    }

    @Data
    @NoArgsConstructor
    public static class Second {
        private int id;
        private String cityCode;
        private String title;
        private String subTitle;
        private List<String> tags;
        private String desc;
        private String img;
        private String listImg;
        private String listTitle;
        private String homeImg;
        private int grassCount;
        private String shortName;
        private String detailImg;
        private int sort;
        @DisableQConfig
        @JsonIgnore
        private First first;
        private String firstName;
        private String poiName;

		/**
		 * 皮肤 1：默认皮肤 2：新年皮肤
		 */
		private int skin;
		/**
		 * 推荐理由样式 1：默认 2：试睡员样式
		 */
        private int reasonStyle;

        private String url;

        @JsonIgnore
        public String getCityCode() {
            return cityCode;
        }

        @JsonIgnore
        public String getFirstName() {
            return firstName;
        }

        @JsonIgnore
        public String getPoiName() {
            return poiName;
        }
    }
}
