package com.qunar.search.common.board.dao;

import com.qunar.search.common.board.bean.BoardKeywordPoi;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 榜单关键词和poi映射关系dao
 *
 * <AUTHOR>
 * @date 2019/5/13
 */
@Repository
public interface BoardKeywordPoiDao {

    /**
     * 查询最大版本
     *
     * @return 返回最大版本号
     */
    @Select("select max(version) from board_keyword_poi")
    Integer maxVersion();

    /**
     * 查询榜单的关键词和poi映射关系
     *
     * @param version 版本号
     * @return 满足条件的数据
     */
    @Select("select city_code as cityCode, keyword, poi_name as poiName from board_keyword_poi where version=#{version}")
    @ResultType(value = BoardKeywordPoi.class)
    List<BoardKeywordPoi> findData(@Param("version") int version);

}
