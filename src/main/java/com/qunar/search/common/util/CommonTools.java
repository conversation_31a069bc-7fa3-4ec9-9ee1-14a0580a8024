package com.qunar.search.common.util;

import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import com.qunar.search.common.cache.CityInfoCache;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.update.HotelPropertyDict;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.agile.LocalHost;
import qunar.agile.Numbers;

import java.net.URLEncoder;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;
import static com.qunar.search.common.constants.CommonConstants.PIPE_SPLITTER;
import static com.qunar.search.common.update.HotelPropertyDict.HotelProperty.THEME;

public class CommonTools {
	private static final Logger LOGGER = LoggerFactory.getLogger(CommonTools.class);

	public static final long D1 = 1L * 24 * 3600 * 1000;

	private static final DateTimeFormatter ISO_DATE_FORMATER = DateTimeFormat.forPattern("yyyy-MM-dd");

    /**
     * 钟点房服务时间格式模板
     */
    private static final String SERVICE_TIME_TEMPLATE = "%02d:%02d-%02d:%02d";

	public static String encodeURI(String uri) {
		try {
			return URLEncoder.encode(uri, "UTF-8");
		} catch (Exception e) {
			return uri;
		}
	}

	private static String serverIP = null;

	public static String getServerIP() {
		if (serverIP != null) return serverIP;
		String id = LocalHost.getNoLoopBackV4().get(0).getHostAddress();
		return serverIP = id;
	}

    public static int getSeqNumber(String hotelSeq) {
        int idx = -1;
        if (hotelSeq == null || (idx = hotelSeq.lastIndexOf('_')) == -1 || hotelSeq.length() < idx + 1)
            return -1;

        return NumberUtils.toInt(hotelSeq.substring(idx + 1), -1);
    }

	public static String getCityFromHotel(String hotelSeq) {
		int i = hotelSeq.lastIndexOf('_');
		if (i == -1) return null;
		return hotelSeq.substring(0, i);
	}

	private static String[] dangciStr = { "", "经济型", "三星级/舒适", "四星级/高档", "五星级/豪华", "二星级/其他" };

	public static String dangciFormat(int dangci) {
		return (dangci >= 1 && dangci <= 5) ? dangciStr[dangci] : dangciStr[0];
	}

	public static GLatLng getLatLng(String s) {
		return GLatLng.getLatLng(s);
	}

	public static int[] parsePageInfo(String range) {
		int start = 0;
		int limit = -1;
		if (range != null) {
			int index = range.indexOf(',');
			if (index == -1) limit = Numbers.toInt(range, -1);
			else {
				start = Numbers.toInt(range.substring(0, index), 0);
				limit = Numbers.toInt(range.substring(index + 1), -1);
			}
		}
		if (limit < 1) limit = 15;
		else if (limit > 500) limit = 500;

		if (start < 0) start = 0;
		return new int[] { start, limit };
	}

	public static Pair<LocalDate, LocalDate> checkDate(String fromDateStr, String toDateStr, boolean isMainLand, String cityUrl,
			boolean isDestTimeOpen){
		DateTime localDateTime;
		if (StringUtils.isBlank(cityUrl) || isMainLand || !isDestTimeOpen) {
			localDateTime = DateTime.now();
		} else {
			localDateTime = new DateTime(CityInfoCache.getCurDestTime(cityUrl));
		}

		int localHour = localDateTime.getHourOfDay();
		LocalDate localDate = localDateTime.toLocalDate();

		LocalDate maxToDate;
		if (isMainLand) {
			maxToDate = localDate.plusDays(180);
		} else {
			maxToDate = localDate.plusDays(365);
		}

		Range<LocalDate> fromDateRange;
		//6点前昨天到最大时间、6点后今天到最大时间
		if (localHour > 6) {
			fromDateRange = Range.closedOpen(localDate, maxToDate);
		} else {
			fromDateRange = Range.closedOpen(localDate.minusDays(1), maxToDate);
		}

		LocalDate fromDate = null;
		if (StringUtils.isNotBlank(fromDateStr)) {
			try {
				fromDate = ISO_DATE_FORMATER.parseLocalDate(fromDateStr);
			} catch (IllegalArgumentException e) {
				LOGGER.error(String.format("fromDate格式错误: %s", fromDateStr), e);
			}
		}

		if (fromDate == null || !fromDateRange.contains(fromDate)) {
			fromDate = localDate.plusDays(2);
		}

		LocalDate toDate = null;
		if (StringUtils.isNotBlank(toDateStr)) {
			try {
				toDate = ISO_DATE_FORMATER.parseLocalDate(toDateStr);
			} catch (IllegalArgumentException e) {
				LOGGER.error(String.format("toDate格式错误: %s", toDateStr), e);
			}
		}

		if (toDate == null) {
			toDate = fromDate.plusDays(3);
		} else {
			if (localHour > 6) {
				if (toDate.isBefore(fromDate.plusDays(1)) || toDate.isAfter(maxToDate)) {
					toDate = fromDate.plusDays(3);
				}
			} else {
				int days = Days.daysBetween(fromDate, toDate).getDays();
				boolean period = isMainLand ? days >= 180 : days >= 365;

				if (toDate.isBefore(fromDate.plusDays(1)) || toDate.isAfter(maxToDate) || period) {
					toDate = fromDate.plusDays(3);
				}
			}
		}

		return Pair.of(fromDate, toDate);
	}

	/**
	 * 入离日期的时间跨度是否是一天
	 * @param fromDate
	 * @param toDate
	 * @return
	 */
	public static boolean isD1DateOfSpan(String fromDate, String toDate){
		if (StringUtils.isEmpty(fromDate) || StringUtils.isEmpty(toDate)) {
			return false;
		}
		long fDate = ISO_DATE_FORMATER.parseLocalDate(fromDate).toDate().getTime();
		long tDate = ISO_DATE_FORMATER.parseLocalDate(toDate).toDate().getTime();
		return tDate - fDate <= CommonTools.D1;
	}

	public static long parseDate(String date) {
		if (StringUtils.isBlank(date)) {
			return 0;
		}

		try {
			LocalDate localDate = ISO_DATE_FORMATER.parseLocalDate(date);
			return localDate.toDate().getTime();
		} catch (IllegalArgumentException e) {
			LOGGER.error(String.format("日期格式错误: %s", date), e);
		}
		return 0;
	}

    /**
     * 将报价传过来的钟点房服务时间转成"09:00-18:00"这种格式
     *
     * @param serviceTime 报价传过来的钟点房服务时间,示例"900,1800"
     * @return 转换后的时间段字符串
     */
    public static String convertHourlyRoomServiceTime(String serviceTime) {
        if (StringUtils.isNotEmpty(serviceTime)) {
            List<String> serviceTimeList = COMMA_SPLITTER.splitToList(serviceTime);
            int startValue = Numbers.toInt(serviceTimeList.get(0), -1);
            int endValue = Numbers.toInt(serviceTimeList.get(1), -1);
            if (startValue >= 0 && endValue >= 0) {
                if (startValue == 0 && endValue == 2400) {
                    return "24小时";
                }
                return String.format(SERVICE_TIME_TEMPLATE, startValue / 100, startValue % 100, endValue / 100, endValue % 100);
            }
        }
        return null;
    }

    public static Set<String> getCommentLabels(String commentId) {
        if (StringUtils.isBlank(commentId)) {
            return null;
        }
        return Sets.newHashSet(PIPE_SPLITTER.splitToList(commentId));
    }

	public static Set<String> getThemeList(Map<String, String> other) {
		Set<String> set = Sets.newHashSet();
		for (HotelPropertyDict.HotelAttrKey key : THEME.getHotelAttrKeys()) {
			String value = other.get(key.getName());
			if (StringUtils.isBlank(value)) {
				continue;
			}
			Iterable<String> ids = PIPE_SPLITTER.split(value);
			for (String id : ids) {
				set.add(key.getUniqId(id));
			}
		}
		return set;
	}

	public static Set<String> getTradingArea(String tradingArea) {
		if (StringUtils.isBlank(tradingArea)) {
			return null;
		}
		return new HashSet<>(PIPE_SPLITTER.splitToList(tradingArea));
	}

	public static Set<String> getHotelTagSet(String tag) {
        if (StringUtils.isBlank(tag)) {
            return null;
        }
        return new HashSet<>(PIPE_SPLITTER.splitToList(tag));
    }

    /**
     * 用#来拼接各个参数，一般用来作为缓存的key
     *
     * @param params 参数数组
     * @return 拼接后的字符串
     */
    public static String generateKey(Object ... params) {
        return StringUtils.join(params, "#");
    }

    /**
     * 判断城市是否有效
     *
     * @param city 城市code
     * @param cityWhiteSet 城市白名单集合
     * @return 如果城市在白名单里则返回true，否则返回false
     */
    public static boolean isValidCity(String city, Set<String> cityWhiteSet) {
        if (city == null || CollectionUtils.isEmpty(cityWhiteSet)) {
            return false;
        }

        // 如果包含"all"，代表开全量城市
        if (cityWhiteSet.contains("all")) {
            return true;
        }

        return cityWhiteSet.contains(city);
    }

	public static int getNormalDistance(int distance) {
		if (distance > 0 && distance <= 100) {
			return 100;
		} else if (distance > 100 && distance <= 300) {
			return 300;
		} else if (distance > 300 && distance <= 500) {
			return 500;
		} else if (distance > 500 && distance <= 1000) {
			return 1000;
		} else if (distance > 1000 && distance <= 1500) {
			return 1500;
		} else if (distance > 1500 && distance <= 2000) {
			return 2000;
		} else if (distance > 2000 && distance <= 2500) {
			return 2500;
		} else if (distance > 2500 && distance <= 3000) {
			return 3000;
		} else if (distance > 3000) {
			return 5000;
		} else {
			return 0;
		}
	}

	public static int getPreLmNormalDistance(int distance) {
		if (distance > 0 && distance <= 1000) {
			return 1000;
		} else if (distance > 1000 && distance <= 3000) {
			return 3000;
		} else if (distance > 3000 && distance <= 5000) {
			return 5000;
		} else if (distance > 5000 && distance <= 10000) {
			return 10000;
		} else if (distance > 10000) {
			return 50000;
		} else {
			return 0;
		}
	}

	/**
	 * 判断2个集合是否存在交集
	 *
	 * @param c1 集合1
	 * @param c2 集合2
	 * @return 如果存在交集则返回true，否则返回false
	 */
	public static boolean hasIntersection(Collection<?> c1, Collection<?> c2) {
		if (CollectionUtils.isEmpty(c1) || CollectionUtils.isEmpty(c2)) {
			return false;
		}
		return !Collections.disjoint(c1, c2);
	}
}