package com.qunar.search.common.util;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.agile.FileProperties;

import java.io.File;
import java.util.*;

/**
 * 处理酒店类型相关的工具类.
 */
public class HotelTypeUtils {

    private final static Logger log = LoggerFactory.getLogger(HotelTypeUtils.class);
    private final static Set<String> bnbTypeSet = new HashSet<String>();
    //酒店频道中的FAMILY_HOTEL，ECOMOMIC_HOTEL，GUZHEN_HOTEL属于客栈
    static {
        bnbTypeSet.add("FAMILY_HOTEL");
        bnbTypeSet.add("ECOMOMIC_HOTEL");
        bnbTypeSet.add("GUZHEN_HOTEL");
    }

    private static Properties p = new Properties();
    private static Map<String, String> typeDefinition = new HashMap<String, String>();
    private static Map<String,String> typeNameDefinition = Maps.newHashMap();

    static {
        try {
        	log.info("HotelTypeUtils = "+HotelTypeUtils.class.getResource("/").getPath());
            p = new FileProperties(new File(HotelTypeUtils.class.getResource("/").getPath(), "hoteltype.properties"), "UTF-8");
        } catch (Exception e) {
            log.warn("解析 hoteltype.properties 文件失败", e);
        } finally {
            for (String key : p.stringPropertyNames()) {
                typeDefinition.put(key, p.getProperty(key));
                typeNameDefinition.put(p.getProperty(key),key);
            }
            typeDefinition = Collections.unmodifiableMap(typeDefinition);
            typeNameDefinition = Collections.unmodifiableMap(typeNameDefinition);
        }
    }

    public static Map<String,String> getHotelTypeNameDefination(){
        return typeNameDefinition;
    }

    public static Map<String, String> getHotelTypeDefinition() {
        return typeDefinition;
    }

    public static String[] getHotelType(String typeCode) {
        ArrayList<String> r = new ArrayList<String>();
        if (StringUtils.isEmpty(typeCode))
            return new String[0];
        else {
            String[] codeList = StringUtils.split(typeCode, "|");
            for (String code : codeList) {
                code = code.trim();
                if (code.length() != 0 && p.containsKey(code))
                    r.add(p.getProperty(code));
            }
        }
        return r.toArray(new String[r.size()]);
    }

    
    public static String[] getHotelTypeCode(String typeCode) {
        if (StringUtils.isEmpty(typeCode)) {
            return new String[0];
        }
        return StringUtils.split(typeCode, "|");
    }

    public static boolean isBnb(String typeCode) {
        if (StringUtils.isEmpty(typeCode))
            return false;

        String[] ht = getHotelTypeCode(typeCode);
        boolean isBnb = false;
        for (String type : ht) {
            if (bnbTypeSet.contains(type)) {
                isBnb = true;
                break;
            }
        }
        return isBnb;

    }
   
}
