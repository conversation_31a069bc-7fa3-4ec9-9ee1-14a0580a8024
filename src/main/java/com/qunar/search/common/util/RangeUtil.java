package com.qunar.search.common.util;

import com.qunar.search.common.constants.FeatureConstants;

import java.util.Map;

/**
 * Created by liqing.zhan on 2017/3/15.
 */
public class RangeUtil {

    private static final Double FOUR = 4.0D;
    /**
     * 获取闭区间位置
     *
     * @param serviceScore
     * @param sections
     * @return
     */
    public static int getWhichSectionRightClosed(int serviceScore, int[] sections) {
        for (int i = 0; i < sections.length - 1; i++) {
            if (serviceScore > sections[i] && serviceScore <= sections[i + 1]) {
                return i;
            }
        }

        return Integer.MIN_VALUE;
    }

    /**
     * 获取闭区间位置
     *
     * @param serviceScore
     * @param sections
     * @return
     */
    public static int getWhichSectionLeftClosed(double serviceScore, int[] sections) {
        for (int i = 0; i < sections.length; i++) {
            if (serviceScore >= sections[i] && serviceScore < sections[i + 1]) {
                return i;
            }
        }

        return Integer.MIN_VALUE;
    }

    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    public static Double divisionValue(Object divisor, Object dividend, int len){
        if (divisor == null || dividend == null || (double)divisor ==0.0 || (double)dividend ==0.0){
            return FeatureConstants.ZERO;
        } else {
            return Numbers.roundStringDouble((double)dividend / (double)divisor,len);
        }

    }

    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     */
    public static Double divisionValue(Double divisor, Double dividend){
        if (divisor == 0.0 || dividend == 0.0){
            return FeatureConstants.ZERO;
        } else {
            Double v = Numbers.roundStringDouble(dividend / divisor, 6);

            if (v > 4.0D) {
                return FOUR;
            }
            return v;
        }
    }


    /**
     * 两个double 数值相除的计算
     * @param divisor 除数
     * @param dividend 被除数
     * @param len 保留几位小数
     */
    public static double divisionValue(Double divisor, Double dividend,int len, double maxValue){
        if (divisor == null || dividend == null || divisor ==0.0 || dividend ==0.0 || divisor-0.0 < 0.0000001){
            return 0.0;
        } else {
            double v = Numbers.roundStringDouble(dividend / divisor, len);

            if(v > maxValue){
                v = maxValue;
            }
            return v;

        }

    }

    public static double divisionValue(int divisor, int dividend, int len, double maxValue){
        return divisionValue(divisor*1.0, dividend*1.0, len,maxValue);
    }


    /**
     * Map<Integer, Object> resultMap 取数的工具方法
     * 只支持返回double 类型的数据
     */
    public static double getValueByResultMap(Map<Integer, Object> resultMap, int key){

        if (resultMap.containsKey(key) && resultMap.get(key) != null){
            return (double) resultMap.get(key);
        }

        return 0.0; // 返回默认值
    }

}
