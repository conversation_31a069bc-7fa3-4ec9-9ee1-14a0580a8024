package com.qunar.search.common.util;

import com.google.common.collect.Lists;
import com.qunar.search.common.bean.HotelInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

public class HotelInfoForRedisUtils {

    /**
     * 数据zstd压缩
     */
    public static byte[] dataCompress(List<String> hotelInfos) throws IOException {
        if (CollectionUtils.isEmpty(hotelInfos)) {
            return null;
        }
        return ZstdUtils.zstdCompress(JsonUtils.fromBean(hotelInfos));
    }

    /**
     * 数据zstd解压缩
     */
    public static List<HotelInfo> dataDecompress(byte[] compressData) {
        if (compressData == null || compressData.length == 0) {
            return Collections.emptyList();
        }
        String jsonStr = ZstdUtils.zstdDecompress(compressData);
        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyList();
        }
        List<String> hotelInfos = JsonUtils.fromJson(jsonStr, JsonUtils.buildCollectionType(List.class, String.class));
        if (CollectionUtils.isEmpty(hotelInfos)) {
            return Collections.emptyList();
        }
        List<HotelInfo> result = Lists.newArrayListWithCapacity(hotelInfos.size());
        for (String hotelInfo : hotelInfos) {
            result.add(JsonUtils.fromJson(hotelInfo, HotelInfo.class));
        }
        return result;
    }

    public static void main(String[] args) throws IOException, InvocationTargetException, IllegalAccessException {


        HotelInfo infoTest = JsonUtils.fromJson(hotelInfoString, HotelInfo.class);

        String json = JsonUtils.toJson(infoTest);
        List<String> infos = Lists.newArrayList(json);
        byte[] bytes1 = HotelInfoForRedisUtils.dataCompress(infos);

        List<HotelInfo> hotelInfos = HotelInfoForRedisUtils.dataDecompress(bytes1);
        String json1 = JsonUtils.toJson(hotelInfos.get(0));

        System.out.println(json1.equals(json));
    }

    private static String hotelInfoString = "{\n" +
            "    \"props\":\n" +
            "    {\n" +
            "        \"isWebFree\": \"Y\",\n" +
            "        \"hasPub\": \"Y\",\n" +
            "        \"hasHairDryer2\": \"0\",\n" +
            "        \"fitmentDate\": \"2021-02-01\",\n" +
            "        \"builtDate\": \"2021-02-01\",\n" +
            "        \"HasWakeUpService\": \"Y\",\n" +
            "        \"brandCode\": \"1\",\n" +
            "        \"hasInternationalPhoneCall2\": \"0\",\n" +
            "        \"hasReceiveVisitor2\": \"5\",\n" +
            "        \"HasParkingArea\": \"Y\",\n" +
            "        \"starHotel\": \"5\",\n" +
            "        \"hasVarietyPowerSocket\": \"0\",\n" +
            "        \"WifiAccess\": \"Y\",\n" +
            "        \"hasCafeBar\": \"0\",\n" +
            "        \"hotelSubCategory\": \"495\",\n" +
            "        \"inlandCommonTag\": \"1|4|16|7|8\",\n" +
            "        \"HasRestaurant\": \"N\",\n" +
            "        \"hasReceiveVisitor\": \"0\",\n" +
            "        \"HasAirportPickUp\": \"Y\",\n" +
            "        \"ctripid\": \"71661653\",\n" +
            "        \"HasStationPickUp\": \"Y\",\n" +
            "        \"WhenBuilt\": \"2021\",\n" +
            "        \"shortAddressDistance\": \"-1\",\n" +
            "        \"commentsTag\": \"1|20|12|17|5|7|13|14|9|3|16|4\",\n" +
            "        \"HasHairDryer\": \"Y\",\n" +
            "        \"imageHeight\": \"1349\",\n" +
            "        \"ctripRecommendLevel\": \"未知\",\n" +
            "        \"checkOutTime\": \"12:00\",\n" +
            "        \"hasFullHoursReception\": \"0\",\n" +
            "        \"hasWifiInRoom\": \"0\",\n" +
            "        \"HasAirConditioning\": \"Y\",\n" +
            "        \"PhoneNumber\": \"0592-6505999\",\n" +
            "        \"HasGuestLaundromat\": \"Y\",\n" +
            "        \"hasParkingArea2\": \"0\",\n" +
            "        \"ctripGroupId\": \"162\",\n" +
            "        \"starHotelType\": \"0\",\n" +
            "        \"hasFreeWifiInRoom2\": \"0\",\n" +
            "        \"hotelSubCategoryName\": \"酒店\",\n" +
            "        \"qunarGroupId\": \"124\",\n" +
            "        \"imageWidth\": \"2024\",\n" +
            "        \"hasLeftLuggage\": \"Y\",\n" +
            "        \"cityDistances\": \"xiamen:22697.0\",\n" +
            "        \"hotelFeatureTags\": \"酒吧|游戏室|SPA|免费停车|会议厅|冰箱|家庭房|环境优雅|商务中心|行李寄存|房间很大|酒店|电梯|商务出行2|儿童活动|景观很棒|24小时前台|床很舒适|可接待全球客人|房间视野开阔|接站服务|欢迎儿童入住|亲子主题房|茶室|管家服务|深受家庭用户喜爱|健身室|唤醒服务|洗衣服务|套房|亲子精选|空调|浴缸|送机服务|早餐很棒|活动丰富|停车场|服务超好|一句话推荐词|拍照出片|去哪儿都住最好的|提供儿童早餐|房间舒适|吹风机|多功能厅|低价房型面积大|棋牌室|接机服务|儿童免费入住|高端连锁\",\n" +
            "        \"NumberRooms\": \"245\",\n" +
            "        \"HasInternetAccess\": \"免费\",\n" +
            "        \"hasSPA\": \"Y\",\n" +
            "        \"hotelBrand\": \"huameida\",\n" +
            "        \"HasFitnessRoom\": \"Y\",\n" +
            "        \"has24hotwater\": \"Y\",\n" +
            "        \"hasMassageRoom\": \"0\",\n" +
            "        \"whenFitment\": \"2021\",\n" +
            "        \"hasStationPickUpQhotel\": \"1\",\n" +
            "        \"HasBusinessCenter\": \"Y\",\n" +
            "        \"allowChildrenToStay\": \"1\",\n" +
            "        \"hasAirportPickUpQhotel\": \"1\",\n" +
            "        \"hasFreeParkingArea\": \"0\",\n" +
            "        \"HasMeetingORBanquetSpace\": \"Y\",\n" +
            "        \"HotelArea\": \"同安区\",\n" +
            "        \"hasVideo\": \"1\",\n" +
            "        \"hasCurrencyExchange\": \"0\",\n" +
            "        \"checkInTime\": \"14:00\",\n" +
            "        \"hasTaxiService\": \"0\",\n" +
            "        \"qunarGroupName\": \"温德姆酒店集团\",\n" +
            "        \"hasElevator\": \"0\"\n" +
            "    },\n" +
            "    \"stars\": 0,\n" +
            "    \"ll\":\n" +
            "    {\n" +
            "        \"lat\": 24.68889045715332,\n" +
            "        \"lng\": 118.12187194824219\n" +
            "    },\n" +
            "    \"imageId\": \"202104/30/C.2hQ6543bPcRsDn6Nn\",\n" +
            "    \"miniRetailPrice\": -1,\n" +
            "    \"hotelSEQ\": \"xiamen_33831\",\n" +
            "    \"hotelName\": \"厦门汉景华美达广场酒店\",\n" +
            "    \"hotelAddress\": \"厦门同安区梧侣路241号\",\n" +
            "    \"hasDangci\": true,\n" +
            "    \"isBNB\": false,\n" +
            "    \"hotelStatus\": \"OPEN\",\n" +
            "    \"hotelDangciType\": \"LUXURY\",\n" +
            "    \"hotelStarsType\": \"ZERO\",\n" +
            "    \"cityTagArr\":\n" +
            "    [\n" +
            "        \"tongan\",\n" +
            "        \"xiamen\"\n" +
            "    ],\n" +
            "    \"hotelTypeArr\":\n" +
            "    [],\n" +
            "    \"onlineChannel\":\n" +
            "    [],\n" +
            "    \"hotelStar\": \"0\",\n" +
            "    \"commentIds\":\n" +
            "    [\n" +
            "        \"12\",\n" +
            "        \"1\",\n" +
            "        \"13\",\n" +
            "        \"14\",\n" +
            "        \"3\",\n" +
            "        \"4\",\n" +
            "        \"16\",\n" +
            "        \"5\",\n" +
            "        \"17\",\n" +
            "        \"7\",\n" +
            "        \"9\",\n" +
            "        \"20\"\n" +
            "    ],\n" +
            "    \"themes\":\n" +
            "    [\n" +
            "        \"T_1\",\n" +
            "        \"T_16\",\n" +
            "        \"T_4\",\n" +
            "        \"T_8\",\n" +
            "        \"T_7\"\n" +
            "    ],\n" +
            "    \"screenHotelType\":\n" +
            "    [],\n" +
            "    \"setProps\":\n" +
            "    {\n" +
            "        \"hotelFeatureTags\":\n" +
            "        [\n" +
            "            \"酒吧\",\n" +
            "            \"游戏室\",\n" +
            "            \"SPA\",\n" +
            "            \"免费停车\",\n" +
            "            \"会议厅\",\n" +
            "            \"冰箱\",\n" +
            "            \"家庭房\",\n" +
            "            \"环境优雅\",\n" +
            "            \"商务中心\",\n" +
            "            \"行李寄存\",\n" +
            "            \"房间很大\",\n" +
            "            \"酒店\",\n" +
            "            \"电梯\",\n" +
            "            \"商务出行2\",\n" +
            "            \"儿童活动\",\n" +
            "            \"景观很棒\",\n" +
            "            \"24小时前台\",\n" +
            "            \"床很舒适\",\n" +
            "            \"可接待全球客人\",\n" +
            "            \"房间视野开阔\",\n" +
            "            \"接站服务\",\n" +
            "            \"欢迎儿童入住\",\n" +
            "            \"亲子主题房\",\n" +
            "            \"茶室\",\n" +
            "            \"管家服务\",\n" +
            "            \"深受家庭用户喜爱\",\n" +
            "            \"健身室\",\n" +
            "            \"唤醒服务\",\n" +
            "            \"洗衣服务\",\n" +
            "            \"套房\",\n" +
            "            \"亲子精选\",\n" +
            "            \"空调\",\n" +
            "            \"浴缸\",\n" +
            "            \"送机服务\",\n" +
            "            \"早餐很棒\",\n" +
            "            \"活动丰富\",\n" +
            "            \"停车场\",\n" +
            "            \"服务超好\",\n" +
            "            \"一句话推荐词\",\n" +
            "            \"拍照出片\",\n" +
            "            \"去哪儿都住最好的\",\n" +
            "            \"提供儿童早餐\",\n" +
            "            \"房间舒适\",\n" +
            "            \"吹风机\",\n" +
            "            \"多功能厅\",\n" +
            "            \"低价房型面积大\",\n" +
            "            \"棋牌室\",\n" +
            "            \"接机服务\",\n" +
            "            \"儿童免费入住\",\n" +
            "            \"高端连锁\"\n" +
            "        ],\n" +
            "        \"ctripHotelFeatureTags\":\n" +
            "        [\n" +
            "            \"智能马桶\",\n" +
            "            \"游戏室\",\n" +
            "            \"基本信息类\",\n" +
            "            \"火灾报警器\",\n" +
            "            \"娱乐设施\",\n" +
            "            \"亚洲风味早餐\",\n" +
            "            \"门锁\",\n" +
            "            \"冰箱\",\n" +
            "            \"饮品小食\",\n" +
            "            \"便利设施\",\n" +
            "            \"KTV\",\n" +
            "            \"家庭房\",\n" +
            "            \"保险柜\",\n" +
            "            \"无障碍客房\",\n" +
            "            \"文化活动\",\n" +
            "            \"商务\",\n" +
            "            \"咖啡厅\",\n" +
            "            \"电梯\",\n" +
            "            \"前台服务语言\",\n" +
            "            \"收费接机\",\n" +
            "            \"香皂\",\n" +
            "            \"行李员\",\n" +
            "            \"寄存\",\n" +
            "            \"模拟高尔夫\",\n" +
            "            \"安全设施\",\n" +
            "            \"床具\",\n" +
            "            \"卫生间\",\n" +
            "            \"免费公用区WIFI\",\n" +
            "            \"叫醒服务\",\n" +
            "            \"欢迎礼品\",\n" +
            "            \"商务活动\",\n" +
            "            \"足浴\",\n" +
            "            \"运动设施\",\n" +
            "            \"牙具\",\n" +
            "            \"礼宾服务\",\n" +
            "            \"景观类\",\n" +
            "            \"洗涤用具\",\n" +
            "            \"演示系统\",\n" +
            "            \"深受家庭用户喜爱\",\n" +
            "            \"健身室\",\n" +
            "            \"观景视野好\",\n" +
            "            \"种类\",\n" +
            "            \"前台贵重物品保险柜\",\n" +
            "            \"交通服务\",\n" +
            "            \"西式早餐\",\n" +
            "            \"浴缸\",\n" +
            "            \"服务类\",\n" +
            "            \"早餐\",\n" +
            "            \"楼层\",\n" +
            "            \"干洗服务\",\n" +
            "            \"森林氧吧\",\n" +
            "            \"早餐很棒\",\n" +
            "            \"瓶装水\",\n" +
            "            \"专职行李员\",\n" +
            "            \"洗漱用品\",\n" +
            "            \"沙发\",\n" +
            "            \"牙膏\",\n" +
            "            \"亚洲风味\",\n" +
            "            \"酒店装修棒\",\n" +
            "            \"门禁系统\",\n" +
            "            \"森林\",\n" +
            "            \"餐别供应\",\n" +
            "            \"餐厅\",\n" +
            "            \"提供中文服务\",\n" +
            "            \"熨斗/挂烫机\",\n" +
            "            \"房间舒适\",\n" +
            "            \"多功能厅\",\n" +
            "            \"窗帘\",\n" +
            "            \"护发素\",\n" +
            "            \"康体设施\",\n" +
            "            \"清洁服务\",\n" +
            "            \"代客泊车\",\n" +
            "            \"自然景观\",\n" +
            "            \"烟雾报警器\",\n" +
            "            \"洗发水\",\n" +
            "            \"设施类\",\n" +
            "            \"打扫工具\",\n" +
            "            \"接/送机服务\",\n" +
            "            \"行政楼层\",\n" +
            "            \"中餐\",\n" +
            "            \"家具\",\n" +
            "            \"110V电压插座\",\n" +
            "            \"液晶电视机\",\n" +
            "            \"商务中心\",\n" +
            "            \"针线包\",\n" +
            "            \"大堂吧\",\n" +
            "            \"便民设施\",\n" +
            "            \"茶包\",\n" +
            "            \"中文指示\",\n" +
            "            \"厨房用具\",\n" +
            "            \"报警器\",\n" +
            "            \"按摩室\",\n" +
            "            \"书桌\",\n" +
            "            \"中式早餐\",\n" +
            "            \"行李服务\",\n" +
            "            \"餐饮厅\",\n" +
            "            \"早餐丰富\",\n" +
            "            \"环境\",\n" +
            "            \"浴室\",\n" +
            "            \"安静舒适\",\n" +
            "            \"空调\",\n" +
            "            \"停车场\",\n" +
            "            \"服务超好\",\n" +
            "            \"备用床具\",\n" +
            "            \"自助早餐\",\n" +
            "            \"迷你吧\",\n" +
            "            \"遮光窗帘\",\n" +
            "            \"房内保险箱\",\n" +
            "            \"康体美容服务\",\n" +
            "            \"热水\",\n" +
            "            \"活动类\",\n" +
            "            \"健身\",\n" +
            "            \"政策类\",\n" +
            "            \"梳子\",\n" +
            "            \"牙刷\",\n" +
            "            \"接机服务\",\n" +
            "            \"咖啡壶/茶壶\",\n" +
            "            \"多种规格电源插座\",\n" +
            "            \"饮品\",\n" +
            "            \"叫车服务\",\n" +
            "            \"酒吧\",\n" +
            "            \"淋浴\",\n" +
            "            \"私人浴室\",\n" +
            "            \"浴巾\",\n" +
            "            \"礼品\",\n" +
            "            \"会议厅\",\n" +
            "            \"公共区域监控\",\n" +
            "            \"日式料理\",\n" +
            "            \"插座\",\n" +
            "            \"24小时前台\",\n" +
            "            \"伞\",\n" +
            "            \"指示\",\n" +
            "            \"房型\",\n" +
            "            \"雨伞\",\n" +
            "            \"接站服务\",\n" +
            "            \"手动窗帘\",\n" +
            "            \"剃须刀\",\n" +
            "            \"地理类\",\n" +
            "            \"体育活动\",\n" +
            "            \"亲子主题房\",\n" +
            "            \"隔音好\",\n" +
            "            \"快递服务\",\n" +
            "            \"吸烟区\",\n" +
            "            \"灭火器\",\n" +
            "            \"婚宴服务\",\n" +
            "            \"WIFI\",\n" +
            "            \"宴会厅\",\n" +
            "            \"茶室\",\n" +
            "            \"浴帽\",\n" +
            "            \"运动场\",\n" +
            "            \"电话\",\n" +
            "            \"适合出差\",\n" +
            "            \"系统\",\n" +
            "            \"送机服务\",\n" +
            "            \"外送洗衣服务\",\n" +
            "            \"外币兑换服务\",\n" +
            "            \"快速入住退房\",\n" +
            "            \"衣柜/衣橱\",\n" +
            "            \"国际长途电话\",\n" +
            "            \"其他类\",\n" +
            "            \"浴衣\",\n" +
            "            \"送餐服务\",\n" +
            "            \"送站服务\",\n" +
            "            \"客房WIFI\",\n" +
            "            \"电热水壶\",\n" +
            "            \"鸭绒被\",\n" +
            "            \"其他服务\",\n" +
            "            \"可信用卡支付\",\n" +
            "            \"吹风机\",\n" +
            "            \"私人卫生间\",\n" +
            "            \"沐浴露\",\n" +
            "            \"浴缸或淋浴\",\n" +
            "            \"接/送站服务\",\n" +
            "            \"公共区\",\n" +
            "            \"便利场所\",\n" +
            "            \"棋牌室\",\n" +
            "            \"采光好\",\n" +
            "            \"儿童免费入住\",\n" +
            "            \"餐饮设施\",\n" +
            "            \"拖鞋\",\n" +
            "            \"茶几\",\n" +
            "            \"收费送机\",\n" +
            "            \"娱乐活动\",\n" +
            "            \"旅游票务服务\",\n" +
            "            \"Spa\",\n" +
            "            \"语言服务\",\n" +
            "            \"餐饮类\",\n" +
            "            \"环境优雅\",\n" +
            "            \"毛巾\",\n" +
            "            \"智能门锁\",\n" +
            "            \"丰富\",\n" +
            "            \"熨衣服务\",\n" +
            "            \"马桶\",\n" +
            "            \"菜系风味\",\n" +
            "            \"有行李寄存服务\",\n" +
            "            \"音响系统\",\n" +
            "            \"落地窗\",\n" +
            "            \"其他设施\",\n" +
            "            \"安全报警器\",\n" +
            "            \"衣架\",\n" +
            "            \"NULL\",\n" +
            "            \"24小时热水\",\n" +
            "            \"餐厅很棒\",\n" +
            "            \"盥洗设施\",\n" +
            "            \"茶水\",\n" +
            "            \"入离政策\",\n" +
            "            \"公用区wifi\",\n" +
            "            \"管家服务\",\n" +
            "            \"熨衣设备\",\n" +
            "            \"洗衣服务\",\n" +
            "            \"套房\",\n" +
            "            \"急救包\",\n" +
            "            \"软饮\",\n" +
            "            \"活动丰富\",\n" +
            "            \"餐饮用具\",\n" +
            "            \"220V电压插座\",\n" +
            "            \"洗浴用品\",\n" +
            "            \"拍照出片\",\n" +
            "            \"医疗设施\",\n" +
            "            \"餐食\",\n" +
            "            \"公共音响系统\",\n" +
            "            \"前台提供翻译工具\",\n" +
            "            \"前台便利服务\",\n" +
            "            \"提供儿童早餐\",\n" +
            "            \"电视机\",\n" +
            "            \"多媒体演示系统\",\n" +
            "            \"频道\",\n" +
            "            \"餐饮服务\",\n" +
            "            \"媒体科技\",\n" +
            "            \"清洁设施\",\n" +
            "            \"薰衣草花田\",\n" +
            "            \"收费接站\",\n" +
            "            \"免费停车场\",\n" +
            "            \"环境类\",\n" +
            "            \"有线频道\"\n" +
            "        ],\n" +
            "        \"inlandCommonTag\":\n" +
            "        [\n" +
            "            \"1\",\n" +
            "            \"4\",\n" +
            "            \"16\",\n" +
            "            \"7\",\n" +
            "            \"8\"\n" +
            "        ]\n" +
            "    },\n" +
            "    \"fitmentYearDiff\": 3.0,\n" +
            "    \"newHotel\": false,\n" +
            "    \"qhotelUpdateTime\": 1730099521111916,\n" +
            "    \"miniretailPrice\": -1,\n" +
            "    \"hotelType\":\n" +
            "    [],\n" +
            "    \"dangci\": 4,\n" +
            "    \"latLng\":\n" +
            "    {\n" +
            "        \"lat\": 24.68889045715332,\n" +
            "        \"lng\": 118.12187194824219\n" +
            "    }\n" +
            "}";
}
