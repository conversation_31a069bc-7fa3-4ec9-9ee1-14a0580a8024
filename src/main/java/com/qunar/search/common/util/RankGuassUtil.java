package com.qunar.search.common.util;

import com.qunar.search.common.constants.RankGuassConstants;


public class RankGuassUtil {

    /**
     * @param volume : 排第几位
     * @param totalNum : 共有多少
     * @return
     */
    public static double calRankGuassByIndex(int volume, int totalNum){

        double index = Numbers.roundStringDouble( ( (volume*1.0) / (totalNum*1.0) -0.5) * 2 , 3);

        if(RankGuassConstants.rankGuassMap.containsKey(index)){
            return RankGuassConstants.rankGuassMap.get(index);
        }

        return 0.0;
    }

}
