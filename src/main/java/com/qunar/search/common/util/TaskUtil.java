package com.qunar.search.common.util;

import com.qunar.search.common.base.NameableCallable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.Future;

/** 用例处理一些线程执行的公用逻辑.
 * Created by fangxue.zhang on 2016/11/22.
 */
public final class TaskUtil {

    private TaskUtil(){}

    private static Logger LOGGER = LoggerFactory.getLogger(TaskUtil.class);

    public static int afterExecution(List<Future<Boolean>> resultFutures, List<NameableCallable<Boolean>> futures,
                              String taskName) {
        if (resultFutures != null) {
            int cancelledTaskCnt = 0;
            for (Future<Boolean> f : resultFutures) {
                if (f.isDone()) {
                    if (f.isCancelled()) {
                        cancelledTaskCnt++;
                        LOGGER.warn(taskName + " future task was cancelled");
                    }
                } else {
                    LOGGER.warn(taskName + " callable not executed");
                }
            }
            if (cancelledTaskCnt > 0) {
                for (NameableCallable<Boolean> f : futures) {
                    f.recycle();
                }
            }
            return cancelledTaskCnt;
        }
        return -1;
    }
}
