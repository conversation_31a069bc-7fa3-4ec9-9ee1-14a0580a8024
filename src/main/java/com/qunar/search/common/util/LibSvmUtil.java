package com.qunar.search.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.enums.*;

import com.qunar.search.common.enums.cUserDatas.CUserClickBookCollectData;
import com.qunar.search.common.enums.cUserDatas.CUserOrderData;
import com.qunar.search.common.enums.cUserDatas.UserFlightHotelTrainBnbOrderData;
import com.qunar.search.common.enums.cUserDatas.UserPoiOrderDistancePriceData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.qunar.search.common.constants.CommonConstants.*;


/**
 * <AUTHOR>
 * @create 2020-08-17 下午4:42
 * @DESCRIPTION
 **/
@Slf4j
public class LibSvmUtil {

    public static final EnumMap<FeatureType, Double> EMPTY_MAP = Maps.newEnumMap(FeatureType.class);
    public static final EnumMap<HotelVariableFeatureType, Object> EMPTY_MAP_HOTEL_VARIABLE_FEATURE = Maps.newEnumMap(HotelVariableFeatureType.class);
    public static final EnumMap<CUserOrderData, Object> EMPTY_MAP_CUserOrderData = Maps.newEnumMap(CUserOrderData.class);
    public static final EnumMap<UserWeekDayOrderFeature, Object> EMPTY_MAP_USER_WEEK_DAY_ORDER_Data = Maps.newEnumMap(UserWeekDayOrderFeature.class);
    public static final EnumMap<UserQCOrderClickFeature, Object> USER_QC_ORDER_CLICK_FEATURE = Maps.newEnumMap(UserQCOrderClickFeature.class);
    public static final EnumMap<CUserClickBookCollectData, Object> EMPTY_MAP_CUSER_CLICK_BOOK_COLLECT_DATA = Maps.newEnumMap(CUserClickBookCollectData.class);
    public static final EnumMap<UserPoiOrderDistancePriceData, Object> EMPTY_MAP_USER_POI_ORDER_DISTANCE_PRICE_DATA = Maps.newEnumMap(UserPoiOrderDistancePriceData.class);
    public static final EnumMap<UserFlightHotelTrainBnbOrderData, Object> EMPTY_MAP_USER_FLIGHT_HOTEL_TRAIN_BNB_DATA = Maps.newEnumMap(UserFlightHotelTrainBnbOrderData.class);
    public static final EnumMap<FastFilterItemFeatureType, Double> FAST_FILTER_EMPTY_MAP = Maps.newEnumMap(FastFilterItemFeatureType.class);

    private static final String BLANK = " ";
    private static final String COLON = ":";
    private static final String COMA = ",";

    /**
     * libsvm格式字符串转换成 EnumMap
     */
    public static EnumMap<FeatureType, Double> stringToEnumMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<FeatureType, Double> infoMap = Maps.newEnumMap(FeatureType.class);
        try {
            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                FeatureType featureType = FeatureType.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == featureType) {
                    continue;
                }

                infoMap.put(featureType, Double.parseDouble(keyAndValue[1]));
            }
            return infoMap;
        } catch (Exception e) {
            log.error("libsvm格式字符串转换成EnumMap解析ERROR", e);
            QMonitor.recordOne("stringToEnumMapERROR");
        }
        return infoMap;
    }

    /**
     * 酒店可变libsvm格式字符串转换
     */
    public static EnumMap<HotelVariableFeatureType, Object> stringToVariableFeatureDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_HOTEL_VARIABLE_FEATURE;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<HotelVariableFeatureType, Object> infoMap = Maps.newEnumMap(HotelVariableFeatureType.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                HotelVariableFeatureType data = HotelVariableFeatureType.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == data) {
                    continue;
                }

                String value = keyAndValue[1];
                FeatureValueTypeEnum featureValueTypeEnum = data.getFeatureValueTypeEnum();

                if (featureValueTypeEnum == FeatureValueTypeEnum.STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.INTEGER_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(data, featureValueTypeEnum.toObject(value));
                } else if (featureValueTypeEnum == FeatureValueTypeEnum.LIST_STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.LIST_DOUBLE_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.MAP_STRING_STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE) {
                    infoMap.put(data, featureValueTypeEnum.toObject(value, data.getFeatureSize()));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从mysql获取用户的可变长的libsvm数据 解析 ERROR", e);
            QMonitor.recordOne("stringToVariableFeatureDataMapERROR");
        }

        return infoMap;
    }

    /**
     * 可变EnumMap转 string
     */
    public static String variableEnumMapToString(EnumMap<HotelVariableFeatureType, Object> map) {
        if (MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }

        StringBuilder builder = new StringBuilder();
        try {
            for (Map.Entry<HotelVariableFeatureType, Object> entry : map.entrySet()) {
                FeatureValueTypeEnum featureValueTypeEnum = entry.getKey().getFeatureValueTypeEnum();
                builder.append(entry.getKey().getIndex()).append(COLON);

                Object value = entry.getValue();
                if (value == null) {
                    continue;
                }

                if (featureValueTypeEnum == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    // 去除 1.0 中 .0 , 节省日志打印
                    double doubleV = (double) value;
                    if (doubleV  == (double) (int) doubleV) {
                        builder.append((int) doubleV).append(BLANK);
                    } else {
                        builder.append(doubleV).append(BLANK);
                    }
                } else {
                    builder.append(featureValueTypeEnum.toString(value)).append(BLANK);
                }
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("从可变长枚举的map转换成string解析 ERROR", e);
            QMonitor.recordOne("variableEnumMapToStringERROR");
        }
        return builder.toString();
    }

    /**
     * libsvm格式字符串转换成 EnumMap
     */
    public static EnumMap<FastFilterItemFeatureType, Double> stringToFastFilterEnumMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return FAST_FILTER_EMPTY_MAP;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);
        EnumMap<FastFilterItemFeatureType, Double> infoMap = Maps.newEnumMap(FastFilterItemFeatureType.class);
        try {
            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                FastFilterItemFeatureType featureType = FastFilterItemFeatureType.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == featureType) {
                    continue;
                }

                infoMap.put(featureType, Double.parseDouble(keyAndValue[1]));
            }
            return infoMap;
        } catch (Exception e) {
            log.error("快筛libsvm格式字符串转换成EnumMap解析ERROR", e);
            QMonitor.recordOne("stringToFastFilterEnumMapERROR");
        }
        return infoMap;
    }

    /**
     * EnumMap转 string
     */
    public static String enumMapToString(EnumMap<FeatureType, Double> map) {
        if (MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }

        StringBuilder builder = new StringBuilder();
        for (Map.Entry<FeatureType, Double> entry : map.entrySet()) {

            builder.append(entry.getKey().getIndex()).append(COLON);

            // 去除 1.0 中 .0 , 节省日志打印
            double doubleV = entry.getValue();
            if (doubleV  == (double) (int) doubleV) {
                builder.append((int) doubleV).append(BLANK);
            } else {
                builder.append(doubleV).append(BLANK);
            }
        }
        return builder.toString();
    }

    /**
     * libsvm格式字符串转换成 EnumMap
     */
    public static EnumMap<UserQCOrderClickFeature, Object> stringToQCUserClickOrderData(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return USER_QC_ORDER_CLICK_FEATURE;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<UserQCOrderClickFeature, Object> infoMap = Maps.newEnumMap(UserQCOrderClickFeature.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                UserQCOrderClickFeature qcUserOrderClickData = UserQCOrderClickFeature.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == qcUserOrderClickData) {
                    continue;
                }

                String value = keyAndValue[1];

                if (qcUserOrderClickData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.STRING_TYPE) {
                    infoMap.put(qcUserOrderClickData, value);
                } else if (qcUserOrderClickData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.INTEGER_TYPE) {
                    infoMap.put(qcUserOrderClickData, Integer.parseInt(value));
                } else if (qcUserOrderClickData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.LIST_STRING_TYPE) {
                    infoMap.put(qcUserOrderClickData, stringToStringList(value, qcUserOrderClickData.getFeatureSize()));
                } else if (qcUserOrderClickData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(qcUserOrderClickData, Double.parseDouble(value));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取qc交叉行为数据 解析 ERROR", e);
            QMonitor.recordOne("stringToQCUserClickOrderData");
        }

        return infoMap;
    }

    /**
     * 周末平日libsvm格式字符串转换成 EnumMap
     */
    public static EnumMap<UserWeekDayOrderFeature, Object> stringToUserWeekDayOrderDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_USER_WEEK_DAY_ORDER_Data;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<UserWeekDayOrderFeature, Object> infoMap = Maps.newEnumMap(UserWeekDayOrderFeature.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                UserWeekDayOrderFeature userOrderData = UserWeekDayOrderFeature.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == userOrderData) {
                    continue;
                }

                String value = keyAndValue[1];
                FeatureValueTypeEnum featureValueTypeEnum = userOrderData.getFeatureValueTypeEnum();

                if (featureValueTypeEnum == FeatureValueTypeEnum.STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.INTEGER_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(userOrderData, featureValueTypeEnum.toObject(value));
                } else if (featureValueTypeEnum == FeatureValueTypeEnum.LIST_STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.LIST_DOUBLE_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.MAP_STRING_STRING_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE
                        || featureValueTypeEnum == FeatureValueTypeEnum.MAP_INT_DOUBLE_TYPE) {
                    infoMap.put(userOrderData, featureValueTypeEnum.toObject(value, userOrderData.getFeatureSize()));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取用户周末平日order数据 解析 ERROR", e);
            QMonitor.recordOne("stringToUserWeekDayOrderDataMap");
        }

        return infoMap;
    }


    /**
     * libsvm格式字符串转换成 EnumMap
     */
    public static EnumMap<CUserOrderData, Object> stringToCUserOrderDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_CUserOrderData;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<CUserOrderData, Object> infoMap = Maps.newEnumMap(CUserOrderData.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                CUserOrderData cUserOrderData = CUserOrderData.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == cUserOrderData) {
                    continue;
                }

                String value = keyAndValue[1];

                if (cUserOrderData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.STRING_TYPE) {
                    infoMap.put(cUserOrderData, value);
                } else if (cUserOrderData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.INTEGER_TYPE) {
                    infoMap.put(cUserOrderData, Integer.parseInt(value));
                } else if (cUserOrderData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.LIST_STRING_TYPE) {
                    infoMap.put(cUserOrderData, stringToStringList(value, cUserOrderData.getFeatureSize()));
                } else if (cUserOrderData.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(cUserOrderData, Double.parseDouble(value));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取携程order数据 解析 ERROR", e);
            QMonitor.recordOne("stringToCUserOrderDataMapError");
        }

        return infoMap;
    }

    /**
     * libsvm格式字符串转换成 c的点击和book 收藏 EnumMap
     */
    public static EnumMap<CUserClickBookCollectData, Object> stringToCUserClickBookCollectDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_CUSER_CLICK_BOOK_COLLECT_DATA;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<CUserClickBookCollectData, Object> infoMap = Maps.newEnumMap(CUserClickBookCollectData.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                CUserClickBookCollectData data = CUserClickBookCollectData.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == data) {
                    continue;
                }

                String value = keyAndValue[1];

                if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.STRING_TYPE) {
                    infoMap.put(data, value);
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.INTEGER_TYPE) {
                    infoMap.put(data, Integer.parseInt(value));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.LIST_STRING_TYPE) {
                    infoMap.put(data, stringToStringList(value, data.getFeatureSize()));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(data, Double.parseDouble(value));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取携程click数据 解析 ERROR", e);
            QMonitor.recordOne("stringToCUserClickBookCollectDataMapERROR");
        }

        return infoMap;
    }

    /**
     * libsvm格式字符串转换成 poi 的下单距离价格
     */
    public static EnumMap<UserPoiOrderDistancePriceData, Object> stringToUserPoiOrderDistancePriceDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_USER_POI_ORDER_DISTANCE_PRICE_DATA;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<UserPoiOrderDistancePriceData, Object> infoMap = Maps.newEnumMap(UserPoiOrderDistancePriceData.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                UserPoiOrderDistancePriceData data = UserPoiOrderDistancePriceData.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == data) {
                    continue;
                }

                String value = keyAndValue[1];

                if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.STRING_TYPE) {
                    infoMap.put(data, value);
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.INTEGER_TYPE) {
                    infoMap.put(data, Integer.parseInt(value));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.LIST_STRING_TYPE) {
                    infoMap.put(data, stringToStringList(value, data.getFeatureSize()));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(data, Double.parseDouble(value));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取POI 的下单距离价格数据 解析 ERROR", e);
            QMonitor.recordOne("stringToUserPoiOrderDistancePriceDataMapERROR");
        }

        return infoMap;
    }

    /**
     * libsvm格式字符串转换成 机酒火民的下单数据
     */
    public static EnumMap<UserFlightHotelTrainBnbOrderData, Object> stringToUserFlightHotelTrainBnbDataMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return EMPTY_MAP_USER_FLIGHT_HOTEL_TRAIN_BNB_DATA;
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, BLANK);

        EnumMap<UserFlightHotelTrainBnbOrderData, Object> infoMap = Maps.newEnumMap(UserFlightHotelTrainBnbOrderData.class);
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                UserFlightHotelTrainBnbOrderData data = UserFlightHotelTrainBnbOrderData.matchIndex(keyAndValue[0]);
                // 添加新的特征，枚举还没有添加时
                if (null == data) {
                    continue;
                }

                String value = keyAndValue[1];

                if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.STRING_TYPE) {
                    infoMap.put(data, value);
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.INTEGER_TYPE) {
                    infoMap.put(data, Integer.parseInt(value));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.LIST_STRING_TYPE) {
                    infoMap.put(data, stringToStringList(value, data.getFeatureSize()));
                } else if (data.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE) {
                    infoMap.put(data, Double.parseDouble(value));
                }
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取用户的机酒火民价格数据 解析 ERROR", e);
            QMonitor.recordOne("stringToUserFlightHotelTrainBnbDataMapERROR");
        }

        return infoMap;
    }

    public static List<String> stringToStringList(String value, int size) {

        if (StringUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }

        List<String> cList = COMMA_SPLITTER.splitToList(value);
        if (CollectionUtils.isEmpty(cList)) {
            return Lists.newArrayList();
        }

        int subSize = Math.min(cList.size(), Math.max(size, 1));

        return cList.subList(0, subSize);
    }

    /**
     * 快筛的用户搜索query下符串转换map，  格式：单人床:10,情侣:5
     */
    public static Map<String, Double> fastFilterQueryFeatureToMap(String libSvmStr) {
        if (StringUtils.isBlank(libSvmStr)) {
            return Collections.emptyMap();
        }

        String[] keyValueArray = StringUtils.split(libSvmStr, COMA);

        Map<String, Double> infoMap = new HashMap<>();
        try {

            for (String keyValueItem : keyValueArray) {
                String[] keyAndValue = StringUtils.split(keyValueItem, COLON);
                if (keyAndValue.length != 2) {
                    continue;
                }

                String value = keyAndValue[1];
                infoMap.put(keyAndValue[0], Double.parseDouble(value));
            }
            return infoMap;
        } catch (Exception e) {
            log.error("从redis获取用户的快筛时的query下的筛选项点击次数解析ERROR", e);
            QMonitor.recordOne("fastFilterQueryFeatureToMapERROR");
        }

        return infoMap;
    }
}
