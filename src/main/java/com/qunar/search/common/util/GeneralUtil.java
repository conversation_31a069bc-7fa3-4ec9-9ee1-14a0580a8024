package com.qunar.search.common.util;


import com.qunar.search.common.enums.FeatureIndexEnum;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.model.feature.ModelHotelFeature;
import com.qunar.search.common.model.feature.ModelRequestFeature;
import com.qunar.search.common.model.feature.ModelUserFeature;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.FeatureConstants.ONE;
import static com.qunar.search.common.constants.FeatureConstants.ZERO;

/**
 * 通用util
 */
public class GeneralUtil {

    /**
     * 是否景区搜索意图 "ctripScenicZone"
     */

    public static boolean isHaveCtripScenicZone (ModelRequestFeature requestFeature){

        if (CollectionUtils.isNotEmpty(requestFeature.getQueryIntention())) {
            for (String intention : requestFeature.getQueryIntention()) {
                if(StringUtils.equals(intention,"ctripScenicZone")){
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 特征转换 484 - 489
     */
    public static Map<Integer, Object> convertorFeature484To489(Map<Integer, Object> resultMap,
                                                ModelUserFeature userFeature,
                                                ModelHotelFeature hotelFeature){

        int hotelRealGrade = HotelDangciType.parse(hotelFeature.getDangci()).getPriority() / 10;

        // 484 用户24h实时点击当前酒店档次占比
        resultMap.put(FeatureIndexEnum.D45_484.getIndex(),
                userFeature.getUserRealtimeClickFeature24h().getGradeRateMap().getOrDefault(hotelRealGrade, ZERO));

        // 485 用户实时点击当前酒店星级占比
        int hotelStars = hotelFeature.getStars();
        resultMap.put(FeatureIndexEnum.D45_485.getIndex(),
                userFeature.getUserRealtimeClickFeature().getStarRateMap().getOrDefault(hotelStars, ZERO));

        // 486 用户实时点击当前酒店品牌占比
        String hotelBrand = hotelFeature.getHotelBranch();
        resultMap.put(FeatureIndexEnum.D45_486.getIndex(),
                userFeature.getUserRealtimeClickFeature24h().getBrandRateMap().getOrDefault(hotelBrand, ZERO));

        // 487 用户实时点击当前酒店商圈占比
        double realClickTradRate = ZERO;
        String hotelTrading = hotelFeature.getTradingArea();
        if (StringUtils.isNotBlank(hotelTrading)) {
            realClickTradRate += userFeature.getUserRealtimeClickFeature24h().getTradingAreaRateMap().getOrDefault(hotelTrading, ZERO);
        }
        resultMap.put(FeatureIndexEnum.D45_487.getIndex(), realClickTradRate);

        // 488 用户实时点击当前酒店类型占比
        double realTimeClickTypeRate = ZERO;
        String[] hotelTypes = hotelFeature.getHotelType();
        if (hotelTypes != null && hotelTypes.length != 0) {
            Map<String, Double> typeRateMap = userFeature.getUserRealtimeClickFeature24h().getTypeRateMap();
            for (String type : hotelTypes) {
                realTimeClickTypeRate += typeRateMap.getOrDefault(type, ZERO);
            }
            realTimeClickTypeRate = realTimeClickTypeRate / hotelTypes.length;
        }
        resultMap.put(FeatureIndexEnum.D45_488.getIndex(), realTimeClickTypeRate);

        // 489 用户同城下24h实时点击当前酒店占比
        Map<String, Map<String, Double>> cityHotelRateMap = userFeature.getUserRealtimeClickFeature24h().getCityHotelRateMap();
        String cityFromSeq = MobileUtils.getCityFromSeq(hotelFeature.getHotelSeq());
        if (StringUtils.isNotEmpty(cityFromSeq) && cityHotelRateMap.containsKey(cityFromSeq)) {
            resultMap.put(FeatureIndexEnum.D45_489.getIndex(),
                    cityHotelRateMap.get(cityFromSeq).getOrDefault(hotelFeature.getHotelSeq(), ZERO));
        }

        // 490 用户24h内同城下最近点击的是否是当前酒店
        List<String> userRealTimeClickSeq24Hour = userFeature.getUserRealTimeClickSeq24Hour();
        List<String> userRealTimeClickSeq24HourSameCity = userRealTimeClickSeq24Hour.stream()
                .filter(s -> StringUtils.equals(MobileUtils.getCityFromSeq(s), cityFromSeq))
                .collect(Collectors.toList());
        if (!userRealTimeClickSeq24HourSameCity.isEmpty()) {
            resultMap.put(FeatureIndexEnum.D45_490.getIndex(),
                    StringUtils.equals(hotelFeature.getHotelSeq(), userRealTimeClickSeq24HourSameCity.get(0)) ? ONE : ZERO);
        }

        return resultMap;

    }


}
