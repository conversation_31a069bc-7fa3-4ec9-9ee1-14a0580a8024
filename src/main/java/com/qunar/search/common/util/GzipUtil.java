package com.qunar.search.common.util;

import com.qunar.hotel.qmonitor.QMonitor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.compress.utils.IOUtils;
import qunar.agile.Closer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 对字符串用gzip格式进行压缩/解压缩.
 */
@Slf4j
public class GzipUtil {
	public static final int BUFFER_SIZE = 1024;

    /**
     * 压缩比率，基于经验值
     */
    private static final int COMPRESS_RADIO = 10;

	public static byte[] compress(String str) {
		if (str == null || str.length() == 0) {
			return null;
		}
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		GZIPOutputStream gzip = null;
		try {
			gzip = new GZIPOutputStream(outputStream);
			gzip.write(str.getBytes("UTF-8"));
			gzip.close();
		} catch (Exception e) {
			log.error("compress exception ", e);
		} finally {
			Closer.close(gzip);
		}
		return outputStream.toByteArray();
	}

	public static String decompress(byte b[]) throws UnsupportedEncodingException {
		if (b == null || b.length <= 0) {
			return null;
		}
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		ByteArrayInputStream in = new ByteArrayInputStream(b);
		GZIPInputStream gunzip = null;
		try {
			gunzip = new GZIPInputStream(in);
			byte[] buffer = new byte[BUFFER_SIZE];
			int n;
			while ((n = gunzip.read(buffer)) != -1) {
				out.write(buffer, 0, n);
			}
			gunzip.close();
		} catch (Exception e) {
			log.error("decompress exception ", e);
		} finally {
			Closer.close(gunzip);
		}
		return out.toString("UTF-8");
	}

    /**
     * 压缩
     */
    public static byte[] compressByGZip(byte[] rawInput) {
        GzipCompressorOutputStream gzipCompressorOutputStream = null;
        ByteArrayInputStream byteArrayInputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(rawInput);
            byteArrayOutputStream = new ByteArrayOutputStream();
            gzipCompressorOutputStream = new GzipCompressorOutputStream(byteArrayOutputStream);
            IOUtils.copy(byteArrayInputStream, gzipCompressorOutputStream);
            gzipCompressorOutputStream.finish();
            gzipCompressorOutputStream.flush();
            byteArrayOutputStream.flush();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            log.error("compress exception", e);
            QMonitor.recordOne("compressException");
        } finally {
            if (gzipCompressorOutputStream != null) {
                try {
                    gzipCompressorOutputStream.close();
                } catch (IOException e) {
                    log.error("compress output close exception", e);
                    QMonitor.recordOne("gzipCompressorOutputStreamCloseException");
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    log.error("compress input close exception");
                    QMonitor.recordOne("byteArrayOutputStreamCloseException");
                }
            }
            if (byteArrayInputStream != null) {
                try {
                    byteArrayInputStream.close();
                } catch (IOException e) {
                    log.error("compress input close exception", e);
                    QMonitor.recordOne("byteArrayInputStreamCloseException");
                }
            }
        }
        return null;
    }

    public static byte[] decompressByGZip(byte[] compressInput) {
        return decompressByGZip(compressInput, COMPRESS_RADIO);
    }

    /**
     * 解压缩
     */
    public static byte[] decompressByGZip(byte[] compressInput, int radio) {
        GzipCompressorInputStream gzipCompressorInputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(compressInput);
            gzipCompressorInputStream = new GzipCompressorInputStream(byteArrayInputStream);
            // 设置足够大的缓冲区
            byteArrayOutputStream = new ByteArrayOutputStream(compressInput.length * radio);
            IOUtils.copy(gzipCompressorInputStream, byteArrayOutputStream);
            byteArrayOutputStream.flush();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            log.error("decompress", e);
            QMonitor.recordOne("decompressException");
        } finally {
            if (gzipCompressorInputStream != null) {
                try {
                    gzipCompressorInputStream.close();
                } catch (IOException e) {
                    log.error("decompress input close exception");
                    QMonitor.recordOne("gzipCompressorInputStreamCloseException");
                }
            }
            if (byteArrayInputStream != null) {
                try {
                    byteArrayInputStream.close();
                } catch (IOException e) {
                    log.error("decompress input close exception");
                    QMonitor.recordOne("byteArrayInputStreamCloseException");
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    log.error("decompress output close exception");
                    QMonitor.recordOne("byteArrayOutputStreamCloseException");
                }
            }

        }
        return null;
    }

}
