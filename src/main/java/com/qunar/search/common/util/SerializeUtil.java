package com.qunar.search.common.util;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * serialization/deserialization for feature log
 *
 * <AUTHOR>
 * @date 2018-01-12
 */
public class SerializeUtil {

    private static ObjectMapper mapper = new ObjectMapper();

    private static final Logger logger = LoggerFactory.getLogger(SerializeUtil.class);

    static {
        //  deserialization
        mapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        mapper.configure(JsonParser.Feature.ALLOW_NON_NUMERIC_NUMBERS, true);
        mapper.getFactory().enable(JsonFactory.Feature.INTERN_FIELD_NAMES);
        mapper.getFactory().enable(JsonFactory.Feature.CANONICALIZE_FIELD_NAMES);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        //serialization
//        mapper.configure(JsonGenerator.Feature.QUOTE_FIELD_NAMES, false);
        mapper.configure(JsonGenerator.Feature.QUOTE_NON_NUMERIC_NUMBERS, false);
        mapper.setSerializationInclusion(Include.NON_DEFAULT);

    }

    /**
     * Feature -> json
     *
     * @param object
     * @return
     */
    public static String serialize(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * json -> Feature
     *
     * @param json
     * @param clazz
     * @return
     */
    public static <T> T deserialize(String json, Class<T> clazz) {
        try {
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            logger.error("decode json error: {}", json);
            throw new RuntimeException(e.getMessage() + ";json:" + json, e);
        }
    }

    /**
     * json -> Feature
     *
     * @param json
     * @param typeReference
     * @return
     */
    public static <T> T deserialize(String json, TypeReference<T> typeReference) {
        try {
            return mapper.readValue(json, typeReference);
        } catch (IOException e) {
            logger.error("decode json error: {}", json);
            throw new RuntimeException(e.getMessage() + ";json:" + json, e);
        }
    }

}
