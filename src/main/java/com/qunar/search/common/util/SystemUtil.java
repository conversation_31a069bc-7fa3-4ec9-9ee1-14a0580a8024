package com.qunar.search.common.util;

import qunar.agile.LocalHost;

import java.util.regex.Pattern;

public class SystemUtil {
    private static final HostEnv env;

    private static final Pattern QUNAR_HOSTNAME_PATTERN = Pattern.compile(".*\\.cn\\d(\\.qunar\\.com)?");

    static {
        String hostName = hostName();
        if (QUNAR_HOSTNAME_PATTERN.matcher(hostName).find()) {
            if (hostName.contains(".dev.")) {
                env = HostEnv.DEV;
            } else if (hostName.contains(".beta.")) {
                env = HostEnv.BETA;
            } else {
                env = HostEnv.PROD;
            }
        } else {
            env = HostEnv.LOCAL;
        }
    }

    enum HostEnv {
        LOCAL, DEV, BETA, PROD
    }

    public static boolean isDev() {
        return HostEnv.DEV.equals(env);
    }

    public static boolean isBeta() {
        return HostEnv.BETA.equals(env);
    }

    public static boolean isProd() {
        return HostEnv.PROD.equals(env);
    }

    public static String hostName() {
        return LocalHost.getHostName();
    }
}
