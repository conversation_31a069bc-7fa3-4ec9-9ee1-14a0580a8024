package com.qunar.search.common.util;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.util.concurrent.RateLimiter;
import com.qunar.redis.storage.Sedis3;
import com.qunar.search.common.redis.AbstractRedisRetryListener;
import com.qunar.search.common.redis.RetryableRedis;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class RedisUtils {

    public static RetryableRedis buildRetryableRedisClient(Sedis3 sedis3, long waitTime, int retryCount, String metricName) {
        return new RetryableRedis(sedis3, getRetryer(waitTime, retryCount, sedis3.getNamespace(), metricName));
    }

    private static Retryer<Object> getRetryer(long waitTime, int retryCount, String nameSpace, String metricName) {
        return RetryerBuilder
                .newBuilder()
                // 抛出runtime异常、checked异常时都会重试，但是抛出error不会重试。
                .retryIfException()
                // 固定等待一定时间后重试
                .withWaitStrategy(WaitStrategies.fixedWait(waitTime, TimeUnit.MILLISECONDS))
                // 重试次数超过一定值后则停止重试
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryCount))
                .withRetryListener(new AbstractRedisRetryListener() {
                    @Override
                    public String metricName() {
                        return  metricName;
                    }

                    @Override
                    public String redisNameSpace() {
                        return nameSpace;
                    }
                })
                .build();
    }

    /**
     * 删除 redis 数据
     */
    public static void deleteBigHashTypeKey(RetryableRedis redisClient, String key,
                                            int lengthThreshold, int scanCount,
                                            RateLimiter limiter,
                                            CountDownLatch latch, Logger logger) {
        try {
            Long len = redisClient.hlen(key.getBytes());
            if (len <= 0) {
                return;
            }
            // 如果长度比较短，则直接删除
            if (len <= lengthThreshold) {
                redisClient.del(key.getBytes());
                return;
            }
            ScanParams params = new ScanParams().count(scanCount);
            String cursor = "0";

            do {
                limiter.acquire();
                ScanResult<Map.Entry<byte[], byte[]>> scanResult = redisClient.hscan(key.getBytes(), cursor.getBytes(), params);
                List<Map.Entry<byte[], byte[]>> entryList = scanResult.getResult();
                List<byte[]> fields = entryList.stream().map(Map.Entry::getKey).collect(Collectors.toList());
                if (!fields.isEmpty()) {
                    // 批量删除
                    redisClient.hdel(key.getBytes(), fields.toArray(new byte[0][]));
                }
                cursor = scanResult.getStringCursor();
            } while (!StringUtils.equals(cursor, "0"));
        } catch (Exception e) {
            logger.warn("deleteBigHashTypeKey error, key: {}", key, e);
        } finally {
            latch.countDown();
        }

    }

    public static void main(String[] args) {
        Long a = 1736010981228602L;
        Long b = 1736134025843000L;


        System.out.println(micConvertDate(a));
        System.out.println(micConvertDate(b));
    }

    /**
     * 微秒转成yy-mm-dd HH:mm:ss
     * @param mic
     * @return
     */
    public static String micConvertDate(Long mic){
        //微秒转成yy-mm-dd HH:mm:ss
        return new SimpleDateFormat("yy-MM-dd HH:mm:ss").format(new Date(mic / 1000));
    }
}
