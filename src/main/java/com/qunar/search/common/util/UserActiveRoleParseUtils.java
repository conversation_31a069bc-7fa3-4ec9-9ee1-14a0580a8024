package com.qunar.search.common.util;

import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.UserActiveRole;
import com.qunar.search.common.conf.HpriceParamConfig;
import com.qunar.search.common.constants.CommonConstants;
import com.qunar.search.common.enums.UserIdentityBitIndex;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Set;

/**
 * 用户活动角色解析工具类
 *
 * <AUTHOR>
 */
public class UserActiveRoleParseUtils {
    private static final Logger log = LoggerFactory.getLogger(UserActiveRoleParseUtils.class);

    /**
     * 解析用户角色信息
     *
     * @param request
     * @param userActiveRole
     */
    public static void parseReq(HttpServletRequest request, UserActiveRole userActiveRole) {
        // 夜宵酒店标识
        boolean isLM = "1".equals(StringUtils.trimToEmpty(request.getParameter("isLM")));
        if (isLM) {
            userActiveRole.isLM = true;
        }

        boolean isIncludeExcess = "1".equals(StringUtils.trimToEmpty(request.getParameter("isIncludeExcess")));
        if (isIncludeExcess) {
            userActiveRole.includeExcess = true;
        }

        String isStudent = Strings.getString(request.getParameter("isStudent"), "0");
        boolean isStu = "1".equals(isStudent);
        if (isStu) {
            userActiveRole.student = true;
        }
        String needConpon = Strings.getString(request.getParameter("needConpon"), "0");
        if ("1".equals(needConpon)) {
            userActiveRole.needConpon = true;
        }
        // 交叉推荐限时特惠
        boolean crossRecomm = "4".equals(StringUtils.trimToNull(request.getParameter("searchType")));
        if (crossRecomm) {
            userActiveRole.limitTime = true;
        }

        // 语音
        if (StringUtils.isNotBlank(request.getParameter("isVoiceList"))
                && "1".equals(request.getParameter("isVoiceList").trim())) {
            userActiveRole.voiceList = true;
        }

        // 新用户标识
        String isNewUser = Strings.getString(request.getParameter("isNewUser"), "0");
        if ("1".equals(isNewUser)) {
            userActiveRole.newUser = true;
        }
        // 是否要车券报价
        if ("1".equals(StringUtils.trimToNull(request.getParameter("needTaxiCoupon")))) {
            userActiveRole.needTaxiCoupon = true;
        }
        //普通用户标识
        try {
            String userBit = StringUtils.trimToNull(request.getParameter("userBit"));
            if (NumberUtils.isNumber(userBit)) {
                long userBitVal = Long.parseLong(userBit);
                userActiveRole.userBit = userBitVal;
                if (BitUtils.isOne(userBitVal, 6)) {
                    QMonitor.recordOne("UserBit_TOUCH");
                }
            }
        } catch (Exception e) {
            log.error("get userBit exception", e);
        }
        // 五折大促活动标识用位标识
        String userIdentityBit = StringUtils.trimToNull(request.getParameter("userIdentityBit"));
        if (NumberUtils.isNumber(userIdentityBit)) {
            long userIdentityVal = Long.parseLong(userIdentityBit);
            // 第1位为1代表是五折活动
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.HALF_PRICE_USER.getBitIndex() + 1)) {
                userActiveRole.isFivePercent = true;
            }
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.CREDIT_USER.getBitIndex() + 1)) {
                // 第2位为1代表授信,第2位0为非授信
                userActiveRole.fivePercentIsCredit = true;
            }
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.HALF_PRICE_NEW_USER.getBitIndex() + 1)) {
                // 第3位为1代表新用户,第3位0为老用户
                userActiveRole.fivePercentNewUser = true;
            } else {
                // 此时代表老用户有折扣
                userActiveRole.isFivePercent = true;
            }
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.FLIGHT_TRAIN_USER.getBitIndex() + 1)) {
                // 第4位为1代表是机火用户,第4位0不是机火用户
                userActiveRole.planeTrainUser = true;
            }
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.LIMIT_USER.getBitIndex() + 1)) {
                // 第5位为1代表是刷机用户
                userActiveRole.isFivePercent = false;
            }
            if (BitUtils.isOne(userIdentityVal, UserIdentityBitIndex.FLIGHT_TRAIN_RED_PACKAGE.getBitIndex() + 1)) {
                // 第7位为1代表有机火红包的用户(曾经下过机火订单)
                userActiveRole.planeTrainRedBagUser = true;
            }
            userActiveRole.userIdentityBit = userIdentityVal;
        }
        try {
            // 用户可用绑卡+答题红包最大额度
            String maxLimitProBalance = StringUtils.trimToNull(request.getParameter("maxLimitProBalance"));
            if (NumberUtils.isNumber(maxLimitProBalance)) {
                int maxLimitProBalanceVal = Integer.parseInt(maxLimitProBalance);
                userActiveRole.maxLimitProBalance = maxLimitProBalanceVal;
            }
        } catch (Exception e) {
            log.error("get maxLimitProBalance exception", e);
        }
        // 是否签到红包首单
        if ("1".equals(StringUtils.trimToNull(request.getParameter("isFirstOrder")))) {
            // 签到红包金额
            String signInGiftMoney = StringUtils.trimToNull(request.getParameter("signInGiftMoney"));
            if (StringUtils.isNotBlank(signInGiftMoney) && NumberUtils.isNumber(signInGiftMoney)) {
                userActiveRole.isRedBagFirstOrder = true;
                userActiveRole.signInGiftMoney = (int) Double.parseDouble(signInGiftMoney);
            }
        }
        try {
            // 星券黑名单
            String starid = request.getParameter("starblacklist");
            processBlackList(starid, userActiveRole.starblacklist);
        } catch (Exception e) {
            log.error("processBlackList exception", e);
        }
        // "优惠码分享"url参数解析
        parseParamActivityIds(request.getParameter("activityIds"), userActiveRole.activityIdsSet);

        try {
            // A转B报价屏蔽参数解析
            parseParamA2BSeqs(request.getParameter("abHotelSeq"), userActiveRole.a2bSeqs);
        } catch (Exception e) {
            log.error("get a2bSeqs exception", e);
        }
        userActiveRole.a2bShieldAll = "1".equals(StringUtils.trimToNull(request.getParameter("abHotelShieldAll")));
        userActiveRole.isLt = "1".equals(StringUtils.trimToNull(request.getParameter("lt")));
        //获取用户坐标
        userActiveRole.latlng = StringUtils.trimToNull(request.getParameter("currCoord"));
        // 获取请求的终端来源
        if (CommonConstants.TERMINAL_TOUCH.equals(StringUtils.trimToNull(request.getParameter("terminal")))) {
            userActiveRole.touch = true;
        }

        String source = StringUtils.trimToNull(request.getParameter("source"));
        if (StringUtils.isNotEmpty(source)){
                userActiveRole.source = source;
        }

        String querySource = StringUtils.trimToNull(request.getParameter("querySource"));
        if (StringUtils.isNotEmpty(querySource)) {
            userActiveRole.querySource = querySource;
        }

        try {
            if (StringUtils.trimToNull(request.getParameter("maxDistance")) != null) {
                userActiveRole.maxDistance = Integer.parseInt(StringUtils.trimToNull(request
                        .getParameter("maxDistance")));
            }
            if (StringUtils.trimToNull(request.getParameter("minDistance")) != null) {
                userActiveRole.minDistance = Integer.parseInt(StringUtils.trimToNull(request
                        .getParameter("minDistance")));
            }
        } catch (Exception e) {
            log.error("get maxDistance minDistance exception", e);
        }

        String abtest = StringUtils.trimToNull(request.getParameter("abtest"));
        userActiveRole.abtest = (StringUtils.isEmpty(abtest) ? "" : abtest);
        //抓取标识，不传就标识为未知
        userActiveRole.crawl = NumberUtils.toInt(request.getParameter("isCrawl"), 5);

        QMonitor.recordOne("crawl_" + userActiveRole.crawl);

        String needPrice = StringUtils.trimToNull(request.getParameter("needPrice"));
        //needPrice=0代表不需要报价属性
        if("0".equals(needPrice)){
            userActiveRole.needPrice = false;
            QMonitor.recordOne("RankNotNeedPrice");
        }
    }

    /**
     * 星券黑名单处理
     *
     * @param blacklist
     * @param blackListSet
     */
    private static void processBlackList(String blacklist, Set<String> blackListSet) {
        if (StringUtils.isBlank(blacklist)) {
            return;
        }
        String[] ids = org.apache.commons.lang3.StringUtils.split(blacklist,",");
        int maxCount = 20;
        Map<String, Integer> props = HpriceParamConfig.getAllProps();
        if (props != null && props.get(CommonConstants.STARBLACKMAXSEQNUM) != null) {
            maxCount = props.get(CommonConstants.STARBLACKMAXSEQNUM);
        }
        int count = 0;
        for (String id : ids) {
            blackListSet.add(id);
            count++;
            if (count >= maxCount) {
                QMonitor.recordOne("starBlackSeqNum");
                break;
            }
        }
    }

    /**
     * 将url参数中的activityIds解析到集合中去
     *
     * @param activityIds    a1,a2,...
     * @param activityIdsSet {@link Set}
     */
    private static void parseParamActivityIds(String activityIds, Set<String> activityIdsSet) {
        if (!StringUtils.isBlank(activityIds)) {
            String[] activityIdsArr = StringUtils.split(activityIds, ",");
            for (String activityId : activityIdsArr) {
                activityIdsSet.add(activityId);
            }
        }
    }

    /**
     * 解析请求中A转B酒店seq集合到用户角色中去
     *
     * @param a2bSeqs   seq1,seq2,...
     * @param a2bSeqSet
     */
    private static void parseParamA2BSeqs(String a2bSeqs, Set<String> a2bSeqSet) {
        if (!StringUtils.isBlank(a2bSeqs)) {
            String[] seqArr = StringUtils.split(a2bSeqs, ",");
            int count = 0;
            Map<String, Integer> props = HpriceParamConfig.getAllProps();
            int maxCount = 20;
            if (props != null && props.get(CommonConstants.A2BMAXSEQNUM) != null) {
                maxCount = props.get(CommonConstants.A2BMAXSEQNUM);
            }
            for (String seq : seqArr) {
                a2bSeqSet.add(seq);
                count++;
                if (count >= maxCount) {
                    QMonitor.recordOne("A2BSeqNum");
                    break;
                }
            }
        }
    }

}
