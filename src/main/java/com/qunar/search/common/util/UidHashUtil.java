package com.qunar.search.common.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;

public class UidHashUtil {
    /**
     * 对字符串进行hash，然后对结果%100取余。 如果取余结果在percent和percent2之间,则返回true,否则返回false
     * 
     * @param string
     * @param percent
     * @return
     */
    public static boolean match(String string, int percent, int percent2, int[] hashArray) {
        if (StringUtils.isEmpty(string)) {
            return false;
        }
        int rem = rem(string);
        rem = hashArray[rem];
        if (rem > percent && rem <= percent2) {
            return true;
        }
        return false;
    }

    private static int hash(String string) {
        return (DigestUtils.md5Hex(string)).hashCode();//使用md5计算hash
    }

    private static int rem(String string) {
        int a = hash(string);
        if (a < 0) {
            a = a * -1;
        }
        return a % 100;
    }

}
