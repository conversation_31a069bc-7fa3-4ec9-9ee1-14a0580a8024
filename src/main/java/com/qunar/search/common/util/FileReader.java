package com.qunar.search.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class FileReader {
	private static Logger log = LoggerFactory.getLogger(FileReader.class);
	
    public static List<String> reader (String modelfileName, Class clazz) {
   	 List<String> tmp = new ArrayList<String>();
   	 BufferedReader dr = null;
   	 try {
   		dr = new BufferedReader(new InputStreamReader(clazz.getResourceAsStream(modelfileName), "utf-8"));
			String line= null;
			while ((line = dr.readLine()) != null) {
				tmp.add(line);								
			}
   	  } catch (Exception e) {
			log.error("load file " + modelfileName + " error", e);
	  } finally {
		 if (dr != null) {
			 try {
				 dr.close();
			 } catch (Exception e) {
				 log.error("close stream " + modelfileName + " error", e);
			}
		 }
	  }
   	  return tmp;
    }
}
