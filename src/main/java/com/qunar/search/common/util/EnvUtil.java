package com.qunar.search.common.util;

import qunar.management.ServerManager;

public class EnvUtil {

    public static Type getEnv() {
        String deployEnv = ServerManager.getInstance().getAppConfig().getServer().getDeployEnv();

        // 遍历枚举类型，自动判断匹配的环境
        for (Type envType : Type.values()) {
            if (deployEnv.contains(envType.name())) {
                return envType;
            }
        }

        // 未匹配到时返回默认值
        return Type.unknown;
    }

    public static enum Type {
        beta,
        noah,
        prepare,
        prod,
        simulation,
        unknown;

        private Type() {
        }

        /**
         * 判断是否是线上环境
         * @return 如果是 prod 或 prepare 环境，返回 true；否则返回 false
         */
        public boolean isProd() {
            return this == prod || this == prepare;
        }
    }
}
