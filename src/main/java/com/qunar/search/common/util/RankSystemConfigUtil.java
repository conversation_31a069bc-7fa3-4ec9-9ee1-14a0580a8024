package com.qunar.search.common.util;

import com.qunar.search.common.cache.CityInfoCache;
import com.qunar.search.common.conf.RankSystemConfig;
import com.qunar.search.common.enums.MobileRankSearchBusinessSwitchConfig;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**支持跟SystemConfig相关的util功能
 * 主要是要吧SystemConfig相关问题跟MobileUtils分离.
 * Created by fangxue.zhang on 2016/4/18.
 */
public class RankSystemConfigUtil {

    private static Logger LOGGER = LoggerFactory.getLogger(RankSystemConfigUtil.class);

    private static RankSystemConfig systemConfig;

    static {
        systemConfig = (RankSystemConfig) SpringContextUtil.getBean("systemConfig");
    }


    /**
     * RankSystemConfigUtil和QconfigUtils都是对一个spring bean操作
     * 删掉一个，合到一个类里
     */
    public static int getCommonConfig(String key, int defaultValue) {
        try {
            String commonValue = systemConfig.getCommonConfigValue(key);
            if (NumberUtils.isNumber(commonValue)) {
                return Integer.parseInt(commonValue);
            }
        } catch (Exception e) {
            LOGGER.error("parse commonConfig error {}", e);
            return defaultValue;
        }
        return defaultValue;
    }

    public static String getCommonValue(String key) {
        return systemConfig.getCommonConfigValue(key);
    }

    /**
     * 判断用户请求是否是当天入住
     *
     * @param fromDate
     * @return isCuDay
     */
    public static boolean isCuDay(String fromDate, String cityUrl, boolean isMainLand) {
        boolean isCuDay = false;
        try {
            boolean isDestTimeOpen = MobileRankSearchBusinessSwitchConfig.INTERNATIONAL_DEST_TIME.isOpen();
            String checkInDate = DateUtil.tranTo8(fromDate);
            int today = 0;
            if (isMainLand || !isDestTimeOpen) {
                today = DateUtil.getTodayYyyyMMdd();
            } else {
                today = Numbers.toInt(DateUtil.getTime8(CityInfoCache.getCurDestTime(cityUrl)));
            }
            int checkInDay = 0;
            if (NumberUtils.isNumber(checkInDate)) {
                try {
                    checkInDay = Integer.parseInt(checkInDate);
                    isCuDay = today >= checkInDay;
                } catch (Exception e) {
                    LOGGER.error("checkInDate transform integer error {}", e);
                }
            }
        } catch (Exception e) {
            LOGGER.error("check isCuDay error, fromDate={}, cityUrl={}, isMainLand={}",
                    fromDate, cityUrl, isMainLand, e);
        }
        return isCuDay;
    }


    public static int getDestCurDate8(String cityUrl, boolean isMainLand) {
        try {
            int today = 0;
            boolean isDestTimeOpen = MobileRankSearchBusinessSwitchConfig.INTERNATIONAL_DEST_TIME.isOpen();
            if (isMainLand || !isDestTimeOpen) {
                today = DateUtil.getTodayYyyyMMdd();
            } else {
                today = Numbers.toInt(DateUtil.getTime8(CityInfoCache.getCurDestTime(cityUrl)));
            }
            return today;
        } catch (Exception e) {
            LOGGER.error("get Dest Cur Date error, cityUrl={}, isMainLand={}", cityUrl, isMainLand, e);
            return DateUtil.getTodayYyyyMMdd();
        }
    }

    /**
     * 判断是不是钟点房折扣有效区间
     *
     * @param hourlyRoomDiscount
     * @return
     */
    public static boolean isEffectiveDiscount(double hourlyRoomDiscount) {
        String configDiscount = systemConfig.getCommonConfigValue("hourlyRoomDiscount");
        if (NumberUtils.isNumber(configDiscount)) {
            double configValue = Double.parseDouble(configDiscount);
            if (hourlyRoomDiscount > 0 && hourlyRoomDiscount <= configValue) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断钟点房有效折扣酒店数量是不是在有效范围内
     *
     * @param hotelSum
     * @return
     */
    public static boolean isEffectiveHotelSum(int hotelSum) {
        String hourlyDiscountHotelSum = systemConfig.getCommonConfigValue("hourlyDiscountHotelSum");
        if (NumberUtils.isNumber(hourlyDiscountHotelSum)) {
            int hourlyDiscountHotelSumVal = Integer.parseInt(hourlyDiscountHotelSum);
            if (hotelSum >= hourlyDiscountHotelSumVal) {
                return true;
            }
        }
        return false;
    }

}
