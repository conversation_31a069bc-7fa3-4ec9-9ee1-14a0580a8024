package com.qunar.search.common.util;

import com.google.common.collect.Lists;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HttpRequestResult;
import com.qunar.search.common.bean.ResponseResult;
import com.qunar.search.common.enums.ErrorEnum;
import com.qunar.search.common.enums.RequestEnum;

import java.util.List;
import java.util.concurrent.Future;

/**
 *
 * Created by den<PERSON><PERSON><PERSON> on 17-3-17.
 */
public class HttpRequestTaskUtil {

    /**
     * 对于多个future, 进行wait操作, 并处理错误状态, 装填PriceResult的状态
     * @param futures
     * @param responseResult
     */
    public static void executeTasks(List<Future<HttpRequestResult>> futures, ResponseResult responseResult, RequestEnum requestEnum){
        responseResult.setRet(true);

        int taskSize = futures.size();
        List<ErrorEnum> errorEnumList = Lists.newArrayListWithCapacity(taskSize);
        int completedCnt = 0; // 执行完毕的任务数量
        int successCnt = 0; // 执行成功的任务数量

        for (Future<HttpRequestResult> future : futures) {
            // 超时取消, HttpRequestTask并不会因为运行时抛异常导致cancelled
            if (future.isCancelled()) {
                continue;
            }
            try {
                completedCnt++;
                HttpRequestResult httpRequestResult = future.get();
                if (httpRequestResult.getErrorEnum() != null) {
                    errorEnumList.add(httpRequestResult.getErrorEnum());
                } else {
                    successCnt++;
                }
            } catch (Exception e) {
                errorEnumList.add(ErrorEnum.ERROR_EXCEPTION);
            }
        }
        // 全部task都没有执行完毕
        if (completedCnt == 0) {
            QMonitor.recordOne("no_task_completed_" + requestEnum.getName());
            responseResult.setRet(false);
            responseResult.setErrorEnum(ErrorEnum.ERROR_TIMEOUT);
            return;
        }
        // 全部task都执行错误
        if (successCnt == 0) {
            QMonitor.recordOne("no_task_succeed_" + requestEnum.getName());
            responseResult.setRet(false);
            responseResult.setErrorEnum(ErrorEnum.getErrorBySort(errorEnumList));
            return;
        }

        // 只有部分task执行完毕
        if (completedCnt < taskSize) {
            QMonitor.recordOne("only_partial_tasks_completed_" + requestEnum.getName());
        } else if (successCnt < taskSize) {
            // 只有部分task执行成功
            QMonitor.recordOne("only_partial_tasks_succeed_" + requestEnum.getName());
        }
    }
}
