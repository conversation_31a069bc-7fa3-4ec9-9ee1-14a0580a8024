package com.qunar.search.common.util;

import com.google.common.base.Joiner;
import com.ning.http.client.AsyncCompletionHandler;
import com.ning.http.client.AsyncCompletionHandlerBase;
import com.ning.http.client.AsyncHandler;
import com.ning.http.client.HttpResponseBodyPart;
import com.ning.http.client.HttpResponseHeaders;
import com.ning.http.client.HttpResponseStatus;
import com.ning.http.client.Response;
import com.qunar.hotel.qmonitor.QMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import qunar.agile.Strings;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;
import qunar.metrics.Metrics;
import qunar.metrics.Timer;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2021/2/1
 */
@Slf4j
public class AsyncHttpUtil {

    private static final Joiner joiner = Joiner.on("_").skipNulls();
    private static QunarAsyncClient qunarAsyncClient;

    static {
        qunarAsyncClient = new QunarAsyncClient();
        qunarAsyncClient.setMaximumConnectionsTotal(1500);
        qunarAsyncClient.setMaximumConnectionsPerHost(300);
        qunarAsyncClient.setConnectionTimeoutInMs(1000);
        qunarAsyncClient.setWebSocketIdleTimeoutInMs(1000);
        qunarAsyncClient.setRequestTimeoutInMs(2000);
        qunarAsyncClient.setReadTimeoutInMs(3000);
    }

    public static Future<Response> asyncPost(String url, Map<String, String> params, String code) {
        return post(url, params, code, new AsyncCompletionHandlerBase());
    }

    public static <T> Future<T> post(String url, Map<String, String> params, String code, AsyncCompletionHandler<T> handler) {
        QHttpOption option = convertParamsList(params);
        return asyncPost(url, option, code, handler);
    }

    public static Future<Response> asyncPost(String url, String paramBody, String code) {
        return post(url, paramBody, code, new AsyncCompletionHandlerBase());
    }

    public static <T> Future<T> post(String url, String json, String code, AsyncCompletionHandler<T> handler) {
        QHttpOption option = new QHttpOption();
        option.addHeader("Content-Type", ContentType.APPLICATION_JSON.toString());
        option.setPostBodyData(json);
        return asyncPost(url, option, code, handler);
    }

    public static <T> Future<T> get(String url, String monitorCode, AsyncCompletionHandler<T> handler) {
        if (Strings.isEmpty(url)) {
            log.warn("asyncHttp client url is empty. url:{}", url);
            return null;
        }
        long start = System.currentTimeMillis();
        try {
            return qunarAsyncClient.get(url, new MonitorHandler<>(url, monitorCode, handler));
        } catch (IOException e) {
            String errorMonitor = buildErrorMonitorName(monitorCode);
            log.error(errorMonitor, e);
            QMonitor.recordOne(errorMonitor);
        } finally {
            QMonitor.recordQuantile(buildMonitorName(monitorCode), System.currentTimeMillis() - start);
        }
        return null;
    }

    private static <T> Future<T> asyncPost(String url, QHttpOption option, String code, AsyncCompletionHandler<T> handler) {
        long start = System.currentTimeMillis();
        try {
            return qunarAsyncClient.post(url, option, new MonitorHandler<>(url, code, handler));
        } catch (IOException e) {
            String errorMonitor = buildErrorMonitorName(code);
            log.error(errorMonitor, e);
            QMonitor.recordOne(errorMonitor);
        } finally {
            QMonitor.recordQuantile(buildMonitorName(code), System.currentTimeMillis() - start);
        }
        return null;
    }


    /**
     * @param code
     * @return
     */
    private static String buildErrorMonitorName(String code) {
        return monitorName(code, "error");
    }

    private static String monitorName(String code, String state) {
        return joiner.join("http_async", code, StringUtils.defaultIfBlank(state, null));
    }

    /**
     * @param code
     * @return
     */
    private static String buildMonitorName(String code) {
        return monitorName(code, null);
    }

    public static Future<Response> asyncGet(String url, String code) {
        return get(url, code, new AsyncCompletionHandlerBase());
    }

    private static QHttpOption convertParamsList(Map<String, String> params) {
        QHttpOption option = new QHttpOption();
        params.forEach(option::addPostFormData);
        return option;
    }

    @Slf4j
    public static final class MonitorHandler<T> implements AsyncHandler<T> {
        private static final String MONITOR_NAME = "async_http";
        private static final String MONITOR_ERROR_NAME = "async_http_error";

        private final URL url;
        private final AsyncHandler<T> proxy;
        private final Timer.Context context;
        private String code;

        public MonitorHandler(String url, String code, AsyncHandler<T> proxy) throws MalformedURLException {
            this.url = new URL(url);
            this.code = code;
            this.proxy = proxy;
            this.context = Metrics.timer(MONITOR_NAME).tag("server", this.url.getHost()).tag("path", this.url.getPath()).get()
                    .time();
        }

        @Override
        public void onThrowable(Throwable t) {
            try {
                Metrics.counter(MONITOR_ERROR_NAME).tag("server", this.url.getHost()).tag("path", this.url.getPath()).get()
                        .inc();
                QMonitor.recordOne(monitorName(code, t.getClass().getSimpleName()));
                this.context.stop();
            } catch (Exception e) {
                log.error("error, url:{}", this.url, e);
            }
            this.proxy.onThrowable(t);
        }

        @Override
        public STATE onBodyPartReceived(HttpResponseBodyPart bodyPart) throws Exception {
            return this.proxy.onBodyPartReceived(bodyPart);
        }

        @Override
        public STATE onStatusReceived(HttpResponseStatus responseStatus) throws Exception {
            return this.proxy.onStatusReceived(responseStatus);
        }

        @Override
        public STATE onHeadersReceived(HttpResponseHeaders headers) throws Exception {
            return this.proxy.onHeadersReceived(headers);
        }

        @Override
        public T onCompleted() throws Exception {
            try {
                this.context.stop();
            } catch (Exception ignore) {
                // 这里的异常忽略掉
            }
            return this.proxy.onCompleted();
        }
    }
}