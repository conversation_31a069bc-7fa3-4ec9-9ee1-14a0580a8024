package com.qunar.search.common.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolUtils {

    public static ThreadPoolExecutor createThreadPool(int threadCount, String threadNamePrefix) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(threadCount, threadCount, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10),
                new ThreadFactoryBuilder().setNameFormat(threadNamePrefix + "-thread-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        // 设置这个标识的目的是：即使由于异常原因导致线程池没有被shutdown，线程池中的线程在超时时间到达后也能被销毁。
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    /**
     * 使用默认拒绝策略
     * @param threadCount
     * @param threadNamePrefix
     * @return
     */
    public static ThreadPoolExecutor createThreadPoolWithDefaultRejectPolicy(int threadCount, String threadNamePrefix) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(threadCount, threadCount, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10),
                new ThreadFactoryBuilder().setNameFormat(threadNamePrefix + "-thread-%d").build());

        // 设置这个标识的目的是：即使由于异常原因导致线程池没有被shutdown，线程池中的线程在超时时间到达后也能被销毁。
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    /**
     *  自定义拒绝策略
     * @param threadCount
     * @param threadNamePrefix
     * @return
     */
    public static ThreadPoolExecutor createThreadPoolWithDefineRejectPolicy(int threadCount, String threadNamePrefix, RejectedExecutionHandler handler) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(threadCount, threadCount, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10),
                new ThreadFactoryBuilder().setNameFormat(threadNamePrefix + "-thread-%d").build(),
                handler
                );
        // 设置这个标识的目的是：即使由于异常原因导致线程池没有被shutdown，线程池中的线程在超时时间到达后也能被销毁。
        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

}
