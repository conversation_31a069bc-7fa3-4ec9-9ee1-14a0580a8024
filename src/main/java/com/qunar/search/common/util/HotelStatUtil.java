package com.qunar.search.common.util;

import com.qunar.search.common.bean.HotelStat;
import com.qunar.search.common.enums.FeatureType;
import com.qunar.search.common.service.HotelStatDataService;

import java.util.EnumMap;

import static com.qunar.search.common.enums.FeatureType.*;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/8/27
 */
public class HotelStatUtil {

	public static int getAllOrderPv7(String seq){
		HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(seq);
		if(hotelStat == null || null == hotelStat.getOfflineFeature()){
			return 0;
		}
		return hotelStat.getOfflineFeature().getOrDefault(ADR_ORD_PV_7_DAY, 0.0).intValue() +
				hotelStat.getOfflineFeature().getOrDefault(IOS_ORD_PV_7_DAY, 0.0).intValue();
	}

	public static double getProfitScore(String seq){
		HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(seq);
		return getProfitScore(hotelStat);
	}

	public static double getProfitScore(HotelStat hotelStat){
		if(hotelStat == null){
			return 0d;
		}
		return hotelStat.getProfit();
	}


	public static int getOrderPv42(String seq){
		HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(seq);
		return getOrderPv42(hotelStat);
	}

	public static int getOrderPv42(HotelStat hotelStat){
		if(hotelStat == null || null == hotelStat.getOfflineFeature()){
			return 0;
		}
		return (hotelStat.getOfflineFeature().getOrDefault(ADR_ORD_PV_21_DAY, 0.0).intValue() +
				hotelStat.getOfflineFeature().getOrDefault(IOS_ORD_PV_21_DAY, 0.0).intValue()) * 2;
	}

    /**
     * 酒店离线数据get
     * @param seq 酒店seq
     * @param key 特征key
     * @return value值
     */
	public static Double getOfflineFeatureValue(String seq, FeatureType key) {
		HotelStat hotelStat = HotelStatDataService.getOnlyReadHotelStat(seq);
		if (null == hotelStat) {
			return null;
		}

		EnumMap<FeatureType, Double> feature = hotelStat.getOfflineFeature();
		if (null == feature ) {
            return null;
		}

        return feature.get(key);
	}

	/**
	 * 酒店离线数据get
	 * @param seq 酒店seq
	 * @param key 特征key
	 * @return value值
	 */
	public static double getOfflineFeatureValue(String seq, FeatureType key, double defaultValue) {
		Double value = getOfflineFeatureValue(seq, key);
		return (null == value) ? defaultValue : value;
	}
}
