package com.qunar.search.common.util;

import qunar.management.ServerManager;

public class RankServerUtil {

    private static final String SIMULATION = "simulation";

    private static final String BETA = "beta";

    /**
     * 是否是仿真环境
     * @return
     */
    public static boolean isSimulationEnv() {
        //仿真环境的getEnv()是prod,subEnv()是simulation
        return ServerManager.getInstance().getAppConfig().getServer().getSubEnv().contains(SIMULATION);
    }

    /**
     * 是否是beta环境（包含noah）
     * @return
     */
    public static boolean isBetaEnv() {
        return BETA.equals(ServerManager.getInstance().getAppConfig().getServer().getEnv());
    }


}
