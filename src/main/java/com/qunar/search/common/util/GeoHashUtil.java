package com.qunar.search.common.util;

import java.util.BitSet;
import java.util.HashMap;

public class GeoHashUtil {

    public static final double MINLAT = -90; // 最小纬度
    public static final double MAXLAT = 90;  // 最大纬度
    public static final double MINLNG = -180;  // 最小经度
    public static final double MAXLNG = 180;  // 最大经度

    private static int latLength = 15; //纬度单独编码长度
    private static int lngLength = 15; //经度度单独编码长度

    private static double minLat;
    private static double minLng;

    private final static char[] digits = { '0', '1', '2', '3', '4', '5', '6', '7', '8',
            '9', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p',
            'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };

    //定义编码映射关系
    final static HashMap<Character, Integer> lookup = new HashMap<Character, Integer>();
    //初始化编码映射内容
    static {
        int i = 0;
        for (char c : digits)
            lookup.put(c, i++);
    }


    /**
     * 经纬度转换成gephash
     * @param latitude 维度
     * @param longitude 经度
     * @param geoSize geohash 长度
     * @param taskType 1: 只返回当前坐标对应的geohash 编码
     *                 2：返回当前坐标对应的周围9个geohash 编码,逗号隔开
     *                 3: 根据geohash 反解码 经纬度,逗号隔开 参数：latitude 表示geohash 值
     *                 4: 根据geohash 返回周围9个geohash 编码,逗号隔开  参数：latitude 表示geohash 值
     * @return
     */
    public static String evaluate(String latitude,String longitude, int geoSize,int taskType) {

        try {
            if (taskType == 2){
                //设置 经纬度的编码长度
                sethashLength( geoSize);
                double latitude_double = Double.valueOf(latitude);
                double longitude_double = Double.valueOf(longitude);
                // 拿到周围geo
                String arroundGeoHash = getArroundGeoHash(latitude_double, longitude_double);
                return arroundGeoHash;

            } else if(taskType == 3){
                double[] decode = decode(latitude); // 解码得到经纬度
                return decode[0] + "," + decode[1];

            } else if(taskType == 4){
                double[] decode = decode(latitude); // 解码得到经纬度
                // 设置 经纬度的编码长度
                sethashLength( latitude.length());
                // 拿到周围geo
                String arroundGeoHash = getArroundGeoHash(decode[0], decode[1]);
                return arroundGeoHash;
            } else {
                // taskType == 1
                // 设置 经纬度的编码长度
                sethashLength( geoSize);
                double latitude_double = Double.valueOf(latitude);
                double longitude_double = Double.valueOf(longitude);
                String encode = encode(latitude_double, longitude_double);
                return encode;
            }
        } catch (Exception e){
            return "-1"; // 转化报错
        }

    }


    /**
     * 获取当前编码
     * @param lat
     * @param lon
     * @return
     */
    public static String encode(double lat, double lon) {
        BitSet latbits = getBits(lat, -90, 90,latLength);
        BitSet lonbits = getBits(lon, -180, 180,lngLength);
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < lngLength; i++) {
            buffer.append( (lonbits.get(i))?'1':'0');
            if(i < latLength){
                buffer.append( (latbits.get(i))?'1':'0');
            }
        }
        String code = base32(Long.parseLong(buffer.toString(), 2));
        return code;
    }

    /**
     * 获取周围9个区域的编码
     * @param lat
     * @param lon
     * @return
     */
    public static String getArroundGeoHash(double lat, double lon){

        setMinLatLng(); // 设置经纬度最小单位

        String resultString = "";
        double uplat = lat + minLat;
        double downLat = lat - minLat;

        double leftlng = lon - minLng;
        double rightLng = lon + minLng;

        String leftUp = encode(uplat, leftlng);
        resultString = leftUp + ",";

        String leftMid = encode(lat, leftlng);
        resultString += leftMid + ",";

        String leftDown = encode(downLat, leftlng);
        resultString += leftDown + ",";

        String midUp = encode(uplat, lon);
        resultString += midUp + ",";

        String midMid = encode(lat, lon);
        resultString += midMid + ",";

        String midDown = encode(downLat, lon);
        resultString += midDown + ",";

        String rightUp = encode(uplat, rightLng);
        resultString += rightUp + ",";

        String rightMid = encode(lat, rightLng);
        resultString += rightMid + ",";

        String rightDown = encode(downLat, rightLng);
        resultString += rightDown ;

        //Log.i("okunu", "getArroundGeoHash list = " + list.toString());
        return resultString;
    }

    //根据经纬度和范围，获取对应的二进制
    private static BitSet getBits(double lat, double floor, double ceiling, int len) {
        BitSet buffer = new BitSet(len);
        for (int i = 0; i < len; i++) {
            double mid = (floor + ceiling) / 2;
            if (lat >= mid) {
                buffer.set(i);
                floor = mid;
            } else {
                ceiling = mid;
            }
        }
        return buffer;
    }

    //将经纬度合并后的二进制进行指定的32位编码
    private static String base32(long i) {
        char[] buf = new char[65];
        int charPos = 64;
        boolean negative = (i < 0);
        if (!negative){
            i = -i;
        }
        while (i <= -32) {
            buf[charPos--] = digits[(int) (-(i % 32))];
            i /= 32;
        }
        buf[charPos] = digits[(int) (-i)];
        if (negative){
            buf[--charPos] = '-';
        }
        return new String(buf, charPos, (65 - charPos));
    }

    // 设置经纬度的最小单位
    private static void setMinLatLng() {
        minLat = MAXLAT - MINLAT;
        for (int i = 0; i < latLength; i++) {
            minLat /= 2.0;
        }
        minLng = MAXLNG - MINLNG;
        for (int i = 0; i < lngLength; i++) {
            minLng /= 2.0;
        }
    }

    /**
     * @param length
     * @return
     * @Author:lulei
     * @Description: 设置经纬度转化为geohash长度
     */
    public static boolean sethashLength(int length) {
        if (length < 1) {
            return false;
        }
        latLength = (length * 5) / 2;
        if (length % 2 == 0) {
            lngLength = latLength;
        } else {
            lngLength = latLength + 1;
        }
//        setMinLatLng();
        return true;
    }


    //根据二进制和范围解码
    private static double decode(BitSet bs, double floor, double ceiling) {
        double mid = 0;
        for (int i=0; i<bs.length(); i++) {
            mid = (floor + ceiling) / 2;
            if (bs.get(i))
                floor = mid;
            else
                ceiling = mid;
        }
        return mid;
    }

    //对编码后的字符串解码
    public static double[] decode(String geohash) {
        StringBuilder buffer = new StringBuilder();
        for (char c : geohash.toCharArray()) {
            int i = lookup.get(c) + 32;
            buffer.append( Integer.toString(i, 2).substring(1) );
        }

        BitSet lonset = new BitSet();
        BitSet latset = new BitSet();

        //偶数位，经度
        int j =0;
        for (int i=0; i< lngLength*2;i+=2) {
            boolean isSet = false;
            if ( i < buffer.length() )
                isSet = buffer.charAt(i) == '1';
            lonset.set(j++, isSet);
        }

        //奇数位，纬度
        j=0;
        for (int i=1; i< latLength*2;i+=2) {
            boolean isSet = false;
            if ( i < buffer.length() )
                isSet = buffer.charAt(i) == '1';
            latset.set(j++, isSet);
        }

        double lon = decode(lonset, -180, 180);
        double lat = decode(latset, -90, 90);

        return new double[] {lat, lon};
    }

}
