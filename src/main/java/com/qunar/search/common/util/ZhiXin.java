package com.qunar.search.common.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算置信区间的工具代码.
 */

public class ZhiXin {

    private static final Logger log = LoggerFactory.getLogger(ZhiXin.class);

    private static final double POW = Math.pow(1.96, 2.0);

	public static double getZhiXin (double p, double n) {
		double zhixin = 0;
		//zhixin = (p + 1 / (2 * n) * 1.96 ^ 2 - 1.96 * ( p * (1 - p) / n + 1.96 ^ 2 / ( 4 * n ^ 2 ) )  ^ 0.5 ) / ( 1 + 1 / n * 1.96 ^ 2 )
		//zhixin = (p + 1 / (2 * n) * 1.96 ^ 2 - 1.96 * ( p * (1 - p) / n + 1.96 ^ 2 / ( 4 * n ^ 2 ) )  ^ 0.5 ) / ( 1 + 1 / n * 1.96 ^ 2 )
		//=(H3+1/(2*C3)*1.96^2-1.96*(H3*(1-H3)/C3+1.96^2/(4*C3^2))^0.5)/(1+1/C3*1.96^2)

		if(p - 1.0 >= 0.00001){
			p = 0.9;
		}

		zhixin = (p + (1.0 / (2.0 * n)) * POW - 1.96 * Math.pow((p * (1.0 - p) / n + POW / (4.0 * Math.pow(n, 2.0))), 0.5))
				/
				( 1.0 + (1.0 / n) * POW );

		if (Double.isNaN(zhixin)){
			zhixin = 0.0;
		}
		return zhixin;
	}

	public static double getP (int booking, int pv) {
		double p = 0;
		p = (double)booking / (double)pv;
		return p;
	}

	public static double getScore (double zhixin, int w) {
		double score = 0;
		score = zhixin * (double)w;
		return score;
	}

	public static double getSpScoreAdr (double p, double n) {
		double score = 0;
		score = 400.0 + (p * n / (n + (300.0 - n) / 5.0)) * 30000.0;
		return score;
	}

	public static double getSpScoreIos (double p, double n) {
		double score = 0;
		score = 450.0 + (p * n / (n + (300.0 - n) / 5.0)) * 30000.0;
		return score;
	}

	public static double getLowScore (double pv) {
		double score = 0;
	    score = 600.0 * (100.0 - pv) / 100.0;
	    return score;
	}


    /**
     *
     * @param n 样本量
     * @param m 正样本数
     * @param za 统计系数
     * @return 威尔逊置信区间下沿
     */
    public static double getWilsonLow(Double n, Double m,Double za) {
        if(n == null){
            return 0.0;
        }
        if( m == null ){
            return 0.0;
        }
        if(za == null){
            log.error("传入的统计常量是无效的");
            return -1.0;
        }
        double p = m * 1.0D / n;
        return (p + za * za / (2 * n) - za * Math.sqrt((p * (1.0D - p) + za * za / (4 * n)) / n)) / (1.0D + za * za / n);

    }

	public static void main (String[] args) {
		System.out.println(ZhiXin.getP(57, 12844));
		System.out.println(ZhiXin.getZhiXin(2.0, 1.0));
		System.out.println(ZhiXin.getZhiXin((double) 57 / (double) 12844, 12844));
		System.out.println(ZhiXin.getScore(ZhiXin.getZhiXin(0.0044, 12844), 150000));
	}

}
