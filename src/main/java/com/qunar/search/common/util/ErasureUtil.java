package com.qunar.search.common.util;

/**
 * Created by liqing.zhan on 2016/6/12.
 */
public class ErasureUtil {
    public static void erasureToValue(int start, int count, double[] features, double value) {
        if (features == null || features.length == 0) {
            return;
        }

        if (start < 0 || count <= 0) {
            return;
        }

        int thisEnd = Math.min(start + count, features.length);

        for (int i = start; i < thisEnd; i++) {
            features[i] = value;
        }
    }

}

