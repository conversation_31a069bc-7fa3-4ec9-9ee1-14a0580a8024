package com.qunar.search.common.util;

import com.qunar.search.common.bean.UserActiveRole;
import com.qunar.search.common.enums.UserStatus;

/**
 * 获取调取render或者sort接口的queryparam对象的util类
 * 
 * <AUTHOR>
 *
 */
public class GainPriceApiQueryUtil {

    public static long parseLogicBit(UserActiveRole userActRole, long lb) {
        long logicBit = lb;
        if (userActRole.fivePercentNewUser) {
            // 如果是新用户传bit28为1标识。
            logicBit |= 1l << UserStatus.LogicBit.FIVE_PERCENT_NEW_USER.getType();
        } else {
            // 如果是老用户传bit20为1标识。
            logicBit |= 1l << UserStatus.LogicBit.FIVE_PERCENT_OLD_USER.getType();
        }
        if (userActRole.isFivePercent) {
            logicBit |= 1l << UserStatus.LogicBit.IS_FIVE_PERCENT.getType();
            if (userActRole.fivePercentIsCredit) {
                logicBit |= 1l << UserStatus.LogicBit.FIVE_PERCENT_IS_CREDIT.getType();
            } else {
                logicBit |= 1l << UserStatus.LogicBit.FIVE_PERCENT_IS_NOT_CREDIT.getType();
            }
            if (userActRole.planeTrainUser) {
                logicBit |= 1l << UserStatus.LogicBit.PLANE_TRAIN_USER.getType();
            }
        }
        if (userActRole.includeExcess) {
            logicBit |= 1l << UserStatus.LogicBit.INCLUDE_EXCESS.getType();
        }
        return logicBit;
    }

    public static long parseMinPriceBit(UserActiveRole userActRole, String isvipuser) {
        // 原始报价 默认计算 mobileMinAvailablePrice
        long minPriceBit = 0L;
        if (userActRole.isLM) {
            // 夜销用户:1
            minPriceBit |= 1L;
        }
        if (userActRole.voiceList) {
            // 语音:1
            minPriceBit |= 1L << UserStatus.UserBit.VOICE_LIST.getType();
        }
        if (userActRole.includeExcess) {
            // 超返:1
            minPriceBit |= 1L << UserStatus.UserBit.INCLUDE_EXCESS.getType();
        }
        if (userActRole.needConpon) {
            // 星券:1
            minPriceBit |= 1L << UserStatus.UserBit.NEED_CONPON.getType();
        }
        if (userActRole.newUser) {
            // 无线酒店新用户:1
            minPriceBit |= 1L << UserStatus.UserBit.NEW_USER.getType();
        }
        if (userActRole.touch) {
            // 来自touch的请求用户:1
            minPriceBit |= 1L << UserStatus.UserBit.TOUCH_USER.getType();
        }
        if (userActRole.isFivePercent) {
            // 五折用户:1
            minPriceBit |= 1L << UserStatus.UserBit.IS_FIVE_PERCENT.getType();
        }
        if (userActRole.fivePercentIsCredit) {
            // 授信用户:1
            minPriceBit |= 1L << UserStatus.UserBit.FIVE_PERCENT_IS_CREDIT.getType();
        }
        if (userActRole.a2bShieldAll) {
            // A2B报价屏蔽全部:1
            minPriceBit |= 1L << UserStatus.UserBit.A2B_SHIELD_ALL.getType();
        }
        if (userActRole.isLt) {
            // 模拟坐标:1
            minPriceBit |= 1L << UserStatus.UserBit.IS_LT.getType();
        }
        if (userActRole.isRedBagFirstOrder) {
            // 签到红包首单:1
            minPriceBit |= 1L << UserStatus.UserBit.IS_RED_BAG_FIRST_ORDER.getType();
        }
        if ("1".equals(isvipuser)) {
            // vipUser:1
            minPriceBit |= 1L << UserStatus.UserBit.IS_VIP_USER.getType();
        }

        //透传普通用户身份给twell
        minPriceBit |= userActRole.userBit; 
        return minPriceBit;
    }

}
