package com.qunar.search.common.util;

import java.text.NumberFormat;

/**
 * 用来做一些格式化工作.
 */

public class Formats {

	private static final String[] byteUnits = { "B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB" };

	/**
	 * @param valor
	 * @return 字节长度的文字表现
	 */
	public static String byteFormat(long valor) {
		
		String p ="";
		if(valor <0){
			valor = Math.abs(valor);
			p="-";
		}
		
		int potencia = 0;
		int proxima = 0;
		boolean cond1;
		boolean cond2;
		proxima = potencia + 1;
		cond1 = (Math.pow(2, potencia * 10) <= valor);
		cond2 = (valor < Math.pow(2, proxima * 10));
		potencia++;

		while (!(cond1 && cond2)) {
			proxima = potencia + 1;
			cond1 = (Math.pow(2, potencia * 10) <= valor);
			cond2 = (valor < Math.pow(2, proxima * 10));
			potencia++;
		}
		potencia--;
		return (p+NumberFormat.getInstance().format(valor / Math.pow(2, potencia * 10)) + " " + byteUnits[potencia]);
	}

	public static String parseTimeFormat(long value) {
		if (value < 1000)
			return value + " 毫秒";
		else if (value < 60000)
			return (value / 1000F) + " 秒";
		else if (value < 3600000L) {
			int v = (int) value / 1000;
			return (v / 60) + "分" + (v % 60) + "秒";
		}else if (value < 86400000L) {
			int v = (int) value / 60000;
			return (v / 60) + "小时" + (v % 60) + "分";
		}else if (value < 2592000000L){
			int v = (int) (value / 3600000);
			return (v / 24) + "天" + (v % 24) + "小时";
		}else{
			int v = (int) (value / 86400000L);
			return v + "天";
		}
	}
	
	
	
	public static String fixedNumber(double num,int digit){
		int dec = (int)Math.pow(10,digit);
		double l = Math.round(num *dec);
		while(dec > 0 && l %10 == 0){
			dec /=10;
			l /=10;
		}
		if(dec == 1)
			return Long.toString((long)l);
		else
			return Double.toString(l/dec);
	}

	
	
	public static void main(String[] args) {
		System.out.println(parseTimeFormat(29 * 24 * 3600000L));
	}
}
