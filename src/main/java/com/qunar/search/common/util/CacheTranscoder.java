package com.qunar.search.common.util;


import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Pool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FastByteArrayOutputStream;
import qunar.agile.Closer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * kryo 4X
 * 基于最新版本调研 4X序列化速度以及体积均优于protoStaff
 *
 * <AUTHOR>
 * @date 2018/4/26
 */
public class CacheTranscoder {

    private static final Logger logger = LoggerFactory.getLogger(CacheTranscoder.class);

	private static Pool<Kryo> kryoPool = new Pool<Kryo>(true, false, 8) {
		protected Kryo create() {
			Kryo kryo = new Kryo();
			kryo.setReferences(false);
			kryo.setRegistrationRequired(false);
			return kryo;
		}
	};

	/**
     * @param t
     * @param key
     * @param <T>
     * @return
     */
    public static <T> byte[] encode(T t, String key) {
        Kryo kryo = kryoPool.obtain();
		FastByteArrayOutputStream bos = null;
		Output output = null;
        try {
			bos = new FastByteArrayOutputStream();
			output = new Output(bos);
            kryo.writeObject(output, t);
            output.flush();
			return compress(bos.toByteArray(), key);
        } catch (Exception e) {
            logger.error("encode error:{}", key, e);
        } finally {
			Closer.close(output, bos);
			kryoPool.free(kryo);
        }
        return null;
    }


    /**
     * @param clazz
     * @param rawData
     * @param key
     * @param <T>
     * @return
     */
    public static <T> T decode(Class<T> clazz, byte[] rawData, String key) {
		Kryo kryo = kryoPool.obtain();
		Input input = null;
        try {
            byte[] data = uncompress(rawData, key);
            if(data == null){
            	return null;
			}
			input = new Input(data);
            return kryo.readObject(input, clazz);
        } catch (Exception e) {
            logger.error("kryo decode error:{}", key, e);
        } finally {
			Closer.close(input);
			kryoPool.free(kryo);
        }
        return null;
    }


    private static byte[] compress(byte[] value, String key) {
        ByteArrayOutputStream out = null;
        GZIPOutputStream gzip = null;
        try {
            out = new ByteArrayOutputStream();
            gzip = new GZIPOutputStream(out);
            gzip.write(value);
        } catch (IOException e) {
            logger.error("compress error {}", key, e);
        } finally {
            Closer.close(gzip, out);
        }
        return out.toByteArray();
    }


    private static byte[] uncompress(byte[] bytes, String key) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        GZIPInputStream unGzip = null;
        try {
            unGzip = new GZIPInputStream(in);
            byte[] buffer = new byte[256];
            int n;
            while ((n = unGzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            logger.error("uncompress error:{}", key, e);
        } finally {
            Closer.close(unGzip, out);
        }
        return out.toByteArray();
    }


}
