package com.qunar.search.common.util;

import org.apache.http.HttpVersion;
import org.apache.http.client.params.CookiePolicy;
import org.apache.http.client.params.HttpClientParams;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.params.HttpProtocolParams;
import qunar.hc.QunarClient;
import qunar.hc.tools.Strategies;

/**
 * http相关工具.
 */
public class HttpClientUtils {

    private static QunarClient priceClient = null;
    private static QunarClient searchClient = null;
	private static QunarClient querySuggestClient = null;
    private static QunarClient fixedPriceClient = null;
    private static QunarClient wrapperWeightClient = null;
    private static QunarClient monitorClient = null;
    private static QunarClient lowPriceClient = null;
    private static QunarClient quickPriceClient = null;
    private static QunarClient toSellLmClient = null;
    private static QunarClient orderDetailClient = null;
    private static QunarClient couponClient = null;
    private static QunarClient recommedsClient = null;
    private static QunarClient ebDataClient = null;
    private static QunarClient hlistCityClient = null;
	private static QunarClient prewarmClient = null;
	private static QunarClient boothSvrClient = null;


	static {
        priceClient = newClient(10000, 30000, 250, 250, 5000);
        fixedPriceClient = newClient(50, 1000, 100, 100, 5000);
        searchClient = newClient(700, 700, 200, 200, 5000);
		querySuggestClient = newClient(1000, 3000, 200, 200, 5000);
        wrapperWeightClient = newClient(10000, 20000, 5, 1, 5000);
        monitorClient = newClient(1000, 3000, 200, 200, 5000);
        lowPriceClient = newClient(500, 1000, 250, 250, 5000);
        quickPriceClient = newClient(500, 1000, 250, 250, 5000);
        toSellLmClient = newClient(5000, 5000, 250, 250, 5000);
        orderDetailClient = newClient(5000, 2000, 250, 250, 5000);
        couponClient = newClient(2000, 2000, 300, 250, 5000);
        recommedsClient = newClient(1000, 3000, 200, 200, 5000);
        ebDataClient = newClient(1000, 3000, 200, 200, 5000);
        hlistCityClient = newClient(1000, 3000, 200, 200, 5000);
		prewarmClient = newClient(500, 500, 200, 200, 5000);
    	boothSvrClient = newClient(50, 1000, 200, 200, 5000);
	}

    public static QunarClient newClient(int cTimeout, int sTimeout, int maxCon, int maxConPerRoute, int keepAlive) {
        HttpParams params = new BasicHttpParams();

        HttpConnectionParams.setConnectionTimeout(params, cTimeout);
        HttpConnectionParams.setSoTimeout(params, sTimeout);
        HttpProtocolParams.setVersion(params, HttpVersion.HTTP_1_1);
        HttpProtocolParams.setUserAgent(params, "Chrome/5.0.342.9 Safari/533.2");
        HttpClientParams.setCookiePolicy(params, CookiePolicy.BROWSER_COMPATIBILITY);

        SchemeRegistry schemeRegistry = new SchemeRegistry();
        schemeRegistry.register(new Scheme("http", 80, PlainSocketFactory.getSocketFactory()));

        ThreadSafeClientConnManager cm = new ThreadSafeClientConnManager(schemeRegistry);
        cm.setMaxTotal(maxCon);
        cm.setDefaultMaxPerRoute(maxConPerRoute);
        QunarClient client = new QunarClient(cm, params);
        Strategies.keepAlive(client, keepAlive);

        return client;
    }

    public static QunarClient getPriceClient() {
        return priceClient;
    }

    public static QunarClient getSearchClient() {
        return searchClient;
    }

    public static QunarClient getFixedPriceClient() {
        return fixedPriceClient;
    }

	public static QunarClient getWrapperWeightClient() {
		return wrapperWeightClient;
	}

	public static void setWrapperWeightClient(QunarClient wrapperWeightClient) {
		HttpClientUtils.wrapperWeightClient = wrapperWeightClient;
	}

	public static QunarClient getMonitorClient() {
		return monitorClient;
	}

	public static void setMonitorClient(QunarClient monitorClient) {
		HttpClientUtils.monitorClient = monitorClient;
	}

	public static QunarClient getLowPriceClient() {
		return lowPriceClient;
	}

	public static void setLowPriceClient(QunarClient lowPriceClient) {
		HttpClientUtils.lowPriceClient = lowPriceClient;
	}

	public static QunarClient getQuickPriceClient() {
		return quickPriceClient;
	}

	public static void setQuickPriceClient(QunarClient quickPriceClient) {
		HttpClientUtils.quickPriceClient = quickPriceClient;
	}

	public static QunarClient getToSellLmClient() {
		return toSellLmClient;
	}

	public static void setToSellLmClient(QunarClient toSellLmClient) {
		HttpClientUtils.toSellLmClient = toSellLmClient;
	}

	public static QunarClient getOrderDetailClient() {
		return orderDetailClient;
	}

	public static void setOrderDetailClient(QunarClient orderDetailClient) {
		HttpClientUtils.orderDetailClient = orderDetailClient;
	}

	public static QunarClient getCouponClient() {
		return couponClient;
	}

	public static void setCouponClient(QunarClient couponClient) {
		HttpClientUtils.couponClient = couponClient;
	}

	public static QunarClient getRecommedsClient() {
		return recommedsClient;
	}

	public static void setRecommedsClient(QunarClient recommedsClient) {
		HttpClientUtils.recommedsClient = recommedsClient;
	}

	public static QunarClient getQuerySuggestClient() {
		return querySuggestClient;
	}

	public static void setQuerySuggestClient(QunarClient querySuggestClient) {
		HttpClientUtils.querySuggestClient = querySuggestClient;
	}

    public static QunarClient getEbDataClient(){
        return ebDataClient;
    }

    public static QunarClient getHlistCityClient() {
        return hlistCityClient;
    }
	public static QunarClient getPrewarmClient() {
		return prewarmClient;
	}

	public static QunarClient getBoothSvrClient() {
    	return boothSvrClient;
	}
}
