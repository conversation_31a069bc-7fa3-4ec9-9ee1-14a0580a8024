package com.qunar.search.common.util;

import qunar.tc.qtracer.QTraceClient;
import qunar.tc.qtracer.impl.QTraceClientGetter;

/**
 * <AUTHOR>
 */
public class RequestUtil {

    private static final QTraceClient client = QTraceClientGetter.getClient();

    /**
     * 压测请求traceId前缀
     */
    public static final String TC_PRESSURE_TRACE_PREFIX = "cm_csp_";
    /**
     * 判断请求是否是压测流量
     */
    public static boolean isPressureTest() {
        String currentTraceId = client.getCurrentTraceId();
        return currentTraceId != null && currentTraceId.startsWith(TC_PRESSURE_TRACE_PREFIX);
    }
}
