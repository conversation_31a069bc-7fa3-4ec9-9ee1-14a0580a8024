package com.qunar.search.common.util;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunar.search.common.bean.HotelItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import qunar.tc.qtracer.QTracer;

import java.util.*;
import java.util.function.*;

public class RankHelper {
	/**
	 * 用于调试的hotel_seq
	 */
	private static final String DEBUG_CHECK_HOTEL = "debug_checkhotel";

	private static final String DEBUG_KEY = "rank_debug";

	public static final String SCORES_KEY = "score";

	/**
	 * 酒店上下文
	 */
	private static final ThreadLocal<Map<String, Map<String, Object>>> hotelContext = new ThreadLocal<>();
	/**
	 * 请求上下文
	 */
	private static final ThreadLocal<Map<String, Object>> requestContext = new ThreadLocal<>();

	public static void clear() {
		hotelContext.remove();
		requestContext.remove();
		QTracer.addTraceContext(DEBUG_KEY, "off");
	}

	public static <T> T getOrDefault(String key, T defaultValue) {
		if (isHelper()) {
			Map<String, Object> context = requestContext.get();
			return (T) context.getOrDefault(key, defaultValue);
		}

		return defaultValue;
	}

	public static Map<String, Object> getRequestContext() {
		return isDebugEnable() ? (Map)requestContext.get() : Collections.emptyMap();
	}

	public static void put(String key, Object value) {
		if (isHelper()) {
			requestContext.get().put(key, value);
		}
	}

	public static Optional<Map<String, Object>> getHotelContext(String seq) {
		if (isHelper()) {
			return Optional.of(hotelContext.get().computeIfAbsent(seq, k -> Maps.newLinkedHashMap()));
		}

		return Optional.empty();
	}

	public static Optional<Map<String, Map<String, Object>>> getHotelContext() {
		if (isDebugEnable()) {
			return Optional.ofNullable(hotelContext.get());
		}

		return Optional.empty();
	}

	public static void putHotelContext(String hotelSeq, String key, Object value) {
		if (isDebugEnable()) {
			getHotelContext(hotelSeq).ifPresent(m -> m.put(key, value));
		}
	}

	public static boolean isHelper() {
		return isDebugEnable();
	}

	public static void setDebugHotels(String hotelSeqSet) {
		if (StringUtils.isBlank(hotelSeqSet)) {
			return;
		}
		put(DEBUG_CHECK_HOTEL, Sets.newLinkedHashSet(Splitter.on(",").split(hotelSeqSet)));
	}

	public static Set<String> getDebugHotel() {
		return getOrDefault(DEBUG_CHECK_HOTEL, Collections.emptySet());
	}

	public static void debugOpen() {
		QTracer.addTraceContext(DEBUG_KEY, "on");
		requestContext.set(Maps.newConcurrentMap());
		hotelContext.set(Maps.newConcurrentMap());
	}

	public static boolean isDebugEnable() {
		return StringUtils.equalsIgnoreCase(QTracer.getTraceContextByKey(DEBUG_KEY), "on");
	}

	public static void ifDebugEnable(DebugFunction log) {
		if (isDebugEnable()) {
			log.apply();
		}
	}

	public static void ifHasDebugHotel(Consumer<String> consumer) {
		Set<String> seqSet = getDebugHotel();
		if (CollectionUtils.isEmpty(seqSet)) {
			return;
		}

		for (String seq : seqSet){
			consumer.accept(seq);
		}
	}

	public static void ifMatchDebugHotel(String seq, DebugFunction log) {
		ifHasDebugHotel(s -> {
			if (s.equals(seq)) {
				log.apply();
			}
		});
	}

	public static void ifMatchDebugHotel(HotelItem hotelItem, DebugFunction log) {
		ifMatchDebugHotel(hotelItem.getHotelSEQ(), log);
	}

	public static <T> void indexOfDebugHotel(List<T> coll, Function<T, String> fn,
			BiConsumer<T, Integer> consumer) {
		if (!isDebugEnable()) {
			return;
		}
		for (int i = 0; i < coll.size(); i++) {
			T t = coll.get(i);
			int index = i;
			ifHasDebugHotel(s -> {
				if (s.equals(fn.apply(t))) {
					consumer.accept(t, index);
				}
			});
		}
	}

	public static void indexOfDebugHotel(List<HotelItem> list, BiConsumer<HotelItem, Integer> consumer) {
		indexOfDebugHotel(list, HotelItem::getHotelSEQ, consumer);
	}

	public static <T> void valueOfDebugHotel(List<T> coll,
			Function<T, String> keyFunction, Consumer<T> consumer) {
		if (!isDebugEnable()) {
			return;
		}
		for (T t : coll) {
			ifHasDebugHotel(s -> {
				String key = keyFunction.apply(t);
				if (s.equals(key)) {
					consumer.accept(t);
				}
			});
		}
	}

	public static void valueOfDebugHotel(List<HotelItem> coll,
			Consumer<HotelItem> consumer) {
		if (!isDebugEnable()) {
			return;
		}
		valueOfDebugHotel(coll, HotelItem::getHotelSEQ, consumer);
	}

	public static <T> void valueOfDebugHotel(Map<String, T> itemMap, Function<T, String> fn,
			Consumer<T> consumer) {
		if (!isDebugEnable()) {
			return;
		}
		valueOfDebugHotel(Lists.newArrayList(itemMap.values()), fn, consumer);
	}

	public static void valueOfDebugHotel(Map<String, HotelItem> itemMap,
			Consumer<HotelItem> consumer) {
		if (!isDebugEnable()) {
			return;
		}
		valueOfDebugHotel(Lists.newArrayList(itemMap.values()), HotelItem::getHotelSEQ, consumer);
	}

	@FunctionalInterface
	public interface DebugFunction {
		void apply();
	}

	public static void recordHotelPosition(List<HotelItem> hotelItems, String key) {
		if (org.apache.commons.collections.CollectionUtils.isEmpty(hotelItems)) {
			return;
		}

		if (!isDebugEnable()) {
			return;
		}

		// 如果有debug酒店，只记录debug酒店的位置, 否则记录所有酒店的位置
		if (org.apache.commons.collections.CollectionUtils.isNotEmpty(getDebugHotel())) {
			indexOfDebugHotel(hotelItems, (h, i) -> {
				Optional<Map<String, Object>> hotelContext = getHotelContext(h.getHotelSEQ());
				Map map = (Map) hotelContext.get().computeIfAbsent("position", k -> Maps.newLinkedHashMap());
				map.put(key, i + 1);
			});
		} else {
			for (int i = 0; i < hotelItems.size(); i++) {
				Optional<Map<String, Object>> hotelContext = getHotelContext(hotelItems.get(i).getHotelSEQ());
				Map map = (Map) hotelContext.get().computeIfAbsent("position", k -> Maps.newLinkedHashMap());
				map.put(key, i + 1);
			}
		}
	}

	/**
	 * 记录调试酒店的信息, 可以跨线程
	 * @return
	 * @param <V>
	 */
	public static <V> TriConsumer<String, String, V> recordIfMatch() {
		if (!isDebugEnable()) {
			return DO_NOTHING_TRI_CONSUMER;
		}

		Set<String> debugHotel = getDebugHotel();
		Optional<Map<String, Map<String, Object>>> hotelContext = getHotelContext();
		Map<String, Map<String, Object>> map = hotelContext.get();

		if (CollectionUtils.isNotEmpty(debugHotel)) {
			return (hotelSeq, key, obj) -> {
				if (debugHotel.contains(hotelSeq)) {
					map.computeIfAbsent(hotelSeq, k -> Maps.newLinkedHashMap()).put(key, obj);
				}
			};
		} else {
			return (hotelSeq, key, obj) -> map.computeIfAbsent(hotelSeq, k -> Maps.newLinkedHashMap()).put(key, obj);
		}
	}

	public static <V> DebugBiConsumer<String, V> recordMap(String key) {
		if (isDebugEnable()) {
			Map map = (Map) requestContext.get().computeIfAbsent(key, k -> Collections.synchronizedMap(Maps.newLinkedHashMap()));
			return map::put;
		}
		return DO_NOTHING_BI_CONSUMER;
	}

	public static <V> DebugConsumer<V> recordList(String key) {
		if (isDebugEnable()) {
			List o = (List) requestContext.get().computeIfAbsent(key, k -> Collections.synchronizedList(Lists.newArrayList()));
			return o::add;
		}

		return DO_NOTHING_CONSUMER;
	}

	private static final DebugBiConsumer DO_NOTHING_BI_CONSUMER = (o, o2) -> {};

	private static final DebugConsumer DO_NOTHING_CONSUMER = t -> {};

	private static final TriConsumer DO_NOTHING_TRI_CONSUMER = (o, o2, o3) -> {};

	@FunctionalInterface
	public interface DebugBiConsumer<T, U> {
		void put(T t, U u);
	}

	@FunctionalInterface
	public interface DebugConsumer<T> {
		void add(T t);
	}

	@FunctionalInterface
	public interface TriConsumer<T, U, V> {
		void accept(T t, U u, V v);
	}

}