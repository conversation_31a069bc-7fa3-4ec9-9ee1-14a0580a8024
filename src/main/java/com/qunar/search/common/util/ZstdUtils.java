package com.qunar.search.common.util;

import com.github.luben.zstd.Zstd;
import org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;

public class ZstdUtils {

    public static byte[] zstdCompress(String originalStr){
        byte[] array = originalStr.getBytes(StandardCharsets.UTF_8);
        return Zstd.compress(array);
    }

    public static String zstdDecompress(byte[] compressArray){
        int size = (int) Zstd.decompressedSize(compressArray);
        byte[] array = new byte[size];
        Zstd.decompress(array, compressArray);
        return new String(array, StandardCharsets.UTF_8);
    }

    public static String zstdAndBase64Compress(String originalStr){
        byte[] bytes = zstdCompress(originalStr);
        return Base64.encodeBase64String(bytes);
    }

    public static String zstdAndBase64Decompress(String compressStr){
        byte[] bytes1 = Base64.decodeBase64(compressStr);
        return zstdDecompress(bytes1);
    }
}
