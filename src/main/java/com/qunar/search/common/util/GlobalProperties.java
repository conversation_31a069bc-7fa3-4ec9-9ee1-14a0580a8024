package com.qunar.search.common.util;

import com.google.common.io.Resources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Reader;
import java.util.Properties;

public class GlobalProperties {
    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalProperties.class);

    public static final String APPLICATION_PROPERTIES = "application.properties";
    public static final String APPLICATION_SERVER_PROPERTIES = "application.server.properties";
    private static Properties p = new Properties();

    static {
        try (Reader reader = PropertiesUtil.asReaderIgnoreException(APPLICATION_PROPERTIES)) {
            if (reader!=null) {
                p.load(reader);
            }
        } catch (IOException e) {
            LOGGER.warn("加载application.properties失败", e);
        }
        try (Reader reader = PropertiesUtil.asReaderIgnoreException(APPLICATION_SERVER_PROPERTIES)) {
            if (reader!=null) {
                p.load(reader);
            }
        } catch (Exception e) {
            //ignore
        }
    }

    public static boolean has(String key) {
        return p.containsKey(key);
    }

    public static String get(String key) {
        return get(key, "");
    }

    public static String get(String key, String defaultValue) {
        return p.getProperty(key, defaultValue);
    }
}
