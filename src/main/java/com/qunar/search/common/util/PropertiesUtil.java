package com.qunar.search.common.util;

import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.Properties;

public class PropertiesUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(PropertiesUtil.class);

    /**
     * 只支持classpath下文件读取，不支持绝对路径文件加载
     *
     * @param file
     * @return
     */
    public static Properties asProp(String file) {
        Properties p = new Properties();
        try (Reader reader = new InputStreamReader(Resources.getResource(file).openStream(), Charsets.UTF_8)) {
            p.load(reader);
        } catch (IOException e) {
            LOGGER.error("加载" + file + "失败", e);
        }
        return null;
    }

    public static Reader asReader(String file) {
        try {
            return new InputStreamReader(Resources.getResource(file).openStream(), Charsets.UTF_8);
        } catch (IOException e) {
            LOGGER.error("加载" + file + "失败", e);
        }
        return null;
    }

    public static Properties asPropIgnoreException(String file) {
        try {
            return asProp(file);
        } catch (Exception e) {
            LOGGER.warn("加载" + file + "失败");
        }
        return null;
    }

    public static Reader asReaderIgnoreException(String file) {
        try {
            return asReader(file);
        } catch (Exception e) {
            LOGGER.warn("加载" + file + "失败");
        }
        return null;
    }
}
