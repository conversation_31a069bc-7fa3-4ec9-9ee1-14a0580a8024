package com.qunar.search.common.util;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.conf.HotelBucketsConfig;
import com.qunar.search.common.enums.MobileRoomStatus;
import com.qunar.search.common.enums.MobileSortingGroup;
import com.qunar.search.common.update.LogLevelUpdater;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MobileUtils {

	private static final Joiner.MapJoiner PARAM_JOINER = Joiner.on("&").withKeyValueSeparator("=");
	private static Logger LOGGER = LoggerFactory.getLogger(MobileUtils.class);

    //
    private static final MobileSortingGroup DEFAULT_MOBILE_SORTING_GROUP = MobileSortingGroup.NO_PRICE;

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");


    public static String getCityFromSeq(String seq){
        int pos = seq.lastIndexOf("_");
        String city = "";
        if (pos > 0) {
            city = seq.substring(0, pos);
        }
        return city;
    }

    /**
     * 普通酒店房型分组，分为5个组
     *
     * @param hi
     * @return
     */
    public static MobileSortingGroup getSortingGroup(HotelItem hi) {

        if (hi == null || hi.info==null) {
            return DEFAULT_MOBILE_SORTING_GROUP;
        }

        // 根据房态返回房态分组
        final MobileRoomStatus mrs = hi.mobileRoomStatus;
        switch (mrs){
            case RECOMMAND:
                return MobileSortingGroup.BOOKABLE;
            case BOOKABLE:
                return MobileSortingGroup.BOOKABLE;
            case TEL_BOOKABLE:
                return MobileSortingGroup.TEL_BOOKABLE;
            case FULL_ROOM:
                if (hi.price == Integer.MAX_VALUE || hi.price <= 0) {
                    return MobileSortingGroup.NO_PRICE;
                } else {
                    return MobileSortingGroup.UNBOOKABLE_A;
                }
            case NO_PRICE:
                if (hi.price == Integer.MAX_VALUE || hi.price <= 0) {
                    return MobileSortingGroup.NO_PRICE;
                } else {
                    return MobileSortingGroup.UNBOOKABLE_A;
                }
            case STATIC_HOTEL:
                if (hi.price == Integer.MAX_VALUE || hi.price <= 0) {
                    return MobileSortingGroup.NO_PRICE;
                } else {
                    return MobileSortingGroup.UNBOOKABLE_B;
                }
            default:
                return DEFAULT_MOBILE_SORTING_GROUP;
        }
    }

    /**
     *
     */
    public static Map<MobileSortingGroup, List<HotelItem>> group(List<HotelItem> list) {
        return group(list, null);
    }

    public static Map<MobileSortingGroup, List<HotelItem>> group(List<HotelItem> items,
            Comparator<HotelItem> comparator) {
        final Map<MobileSortingGroup, List<HotelItem>> r = new HashMap<MobileSortingGroup, List<HotelItem>>();
        try {

            // 把hotel根据房态分组
            if (items != null) {
                int bigCityHotelBookableCnt = 0;

                for (HotelItem hi : items) {
                    MobileSortingGroup msg = MobileUtils.getSortingGroup(hi);
                    List<HotelItem> list = r.get(msg);
                    if (list == null) {
                        list = new ArrayList<HotelItem>();
                        r.put(msg, list);
                    }
                    if (MobileSortingGroup.BOOKABLE.equals(msg)) {
                        if (StringUtils.isNotEmpty(hi.getHotelSEQ()) && hi.getHotelSEQ().contains("_")) {
                            String cityUrl = hi.getHotelSEQ().substring(0, hi.getHotelSEQ().lastIndexOf("_"));
                            String cities = RankSystemConfigUtil.getCommonValue("price_cities");
                            if (cities != null && cities.contains(cityUrl)) {
                                ++bigCityHotelBookableCnt;
                            }
                        }
                    }
                    list.add(hi);
                }

                int hotelItemsTotalCnt = items.size();
                int bookableCnt = 0;
                List<HotelItem> bookableItems = r.get(MobileSortingGroup.BOOKABLE);
                bookableCnt = bookableItems == null ? 0 : bookableItems.size();

                QMonitor.incrRecord("HotelBookable", bookableCnt);
                QMonitor.incrRecord("HotelNotBookable", hotelItemsTotalCnt - bookableCnt);
                QMonitor.incrRecord("BigCityHotelBookable", bigCityHotelBookableCnt);
            }

            // UserRealtimeClickFeature24h按照指定的comparator进行排序
            for (MobileSortingGroup msg : MobileSortingGroup.values()) {
                if (!r.containsKey(msg)) {
                    r.put(msg, Collections.<HotelItem> emptyList());
                } else {
                    if (comparator != null) {
                        Collections.sort(r.get(msg), comparator);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("failed to group", e);

            r.clear();
            for (MobileSortingGroup msg : MobileSortingGroup.values()) {
                r.put(msg, Collections.<HotelItem> emptyList());
            }
            r.put(MobileSortingGroup.BOOKABLE, items);
        }
        return r;
    }

    /**
     * merge结果集
     *
     * @param values
     * @return
     */
    public static List<HotelItem> mergeGroupResultList(Collection<List<HotelItem>> values) {
        if (values == null || values.size() == 0) {
            return new ArrayList<HotelItem>();
        }
        List<HotelItem> allList = new ArrayList<HotelItem>();
        for (List<HotelItem> hotelList : values) {
            allList.addAll(hotelList);
        }
        return allList;
    }

    /**
     * 判断uid是否在黑名单
     *
     * @param uid
     * @param config
     * @return isBlackUid
     */
    public static boolean isBlack(String uid, HotelBucketsConfig config) {
        boolean isBlackUid = false;
        HotelBucketsConfig hbConfig = config;
        if (hbConfig == null) {
            return isBlackUid;
        }
        Set<String> blackUidList = hbConfig.getUidBlackSet();
        if (blackUidList != null && blackUidList.contains(uid)) {
            isBlackUid = true;
        } else {
            Set<Pattern> blackUidRegexSet = hbConfig.getUidBlackRegexSet();
            if (blackUidRegexSet != null) {
                Iterator<Pattern> regexIter = blackUidRegexSet.iterator();
                if (regexIter != null) {
                    while (regexIter.hasNext()) {
                        Pattern uidRegex = regexIter.next();
                        if (null == uidRegex) {
                            continue;
                        }
                        Matcher mer = uidRegex.matcher(uid);
                        if (mer.find()) {
                            isBlackUid = true;
                            break;
                        }
                    }
                }
            }
        }
        return isBlackUid;
    }

    /**
     * 计算折扣信息
     * @param item
     * @return
     */
    public static double calZhekou(HotelItem item) {
        int minPrice = -1;
        int originPrice = -1;
		minPrice = item.mobileMinAvailablePrice;
		originPrice = item.originalPriceForMin;
        if (LogLevelUpdater.getSysIsDebug()) {
            StringBuilder sbd = new StringBuilder();
            sbd.append("zhekou##").append(item.info.hotelSEQ).append("##minPrice:").append(minPrice)
                    .append("##originPrice:").append(originPrice);
            LOGGER.debug(sbd.toString());
        }
        if (minPrice > originPrice || minPrice <= 0 || minPrice >= Integer.MAX_VALUE || originPrice >= Integer.MAX_VALUE
                || originPrice <= 0) {
            return Integer.MAX_VALUE;
        }

        double zhekou = (1.0 * minPrice) / originPrice;
        return zhekou;
    }

	/**
	 * 获取requst中的参数
	 *
	 * @param request
	 * @return
	 */
	public static String generateParameter(HttpServletRequest request) {
		Map<String, String[]> parameterMap = request.getParameterMap();
		Map<String, String> map = parameterMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> {
			String[] values = e.getValue();
			if (values.length > 0) {
				try {
					return URLEncoder.encode(values[0], "UTF-8");
				} catch (UnsupportedEncodingException e1) { //NOSONAR
				}
			}
			return "";
		}));

		return PARAM_JOINER.join(map);
	}

    /**
     * 将httpRequest中的参数提取出来
     *
     * @return
     */
    public static Map<String, String> extractHttpRequestParametersToMap(HttpServletRequest request) {
        try {
            Map<String, String> paramsMap = Maps.newHashMap();
            if (request == null) return paramsMap;
            Enumeration names = request.getParameterNames();
            if (names == null) return paramsMap;
            while (names.hasMoreElements()) {
                String key = (String) names.nextElement();
                String[] values = request.getParameterValues(key);
                if (values != null && values.length > 0) {
                    paramsMap.put(key, values[0]);
                }
            }
            return paramsMap;
        } catch (Exception e) {
            LOGGER.error("extract http request parameters error.", e);
        }
        return Maps.newHashMap();
    }

    /**
     * 将日期字符串转换成DateTime对象，防止NPE异常
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     * @return DateTime对象，解析失败时返回null
     */
    public static DateTime toDateTime(String dateStr) {
        try {
            return DateTime.parse(dateStr, DATE_FORMATTER);
        } catch (Exception ignored) {
        }
        return null;
    }
}
