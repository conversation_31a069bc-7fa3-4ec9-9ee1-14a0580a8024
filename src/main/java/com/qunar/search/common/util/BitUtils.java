package com.qunar.search.common.util;

import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

/**
 * Bit操作工具类
 */
public class BitUtils {
	
	/**
	 * 设置整数的某个比特位，兼容老接口
	 *
	 * @param source  需要设置比特位的整数
	 * @param pos 第几位，从1开始
	 * @param zero 若为true, 将pos比特位置为0, 否则置为1
     * @return int
	 */
	public static int changeBitValue(int source, int pos, boolean zero) {
		if (zero) {
			/** 置为0 */
			return clearBit(source, pos);
		}

		return source | (1 << pos - 1);
	}

	/**
	 * 设置长整数的某个比特位，兼容老接口
	 *
	 * @param source  需要设置比特位的整数
	 * @param pos 第几位，从1开始
	 * @param zero 若为true, 将pos比特位置为0, 否则置为1
	 * @return int
	 */
	public static long changeBitValue(long source, int pos, boolean zero) {
		if (zero) {
			/** 置为0 */
			return source & (Long.MAX_VALUE ^ (1L << (pos - 1)));
		}

		return source | (1L << pos - 1);
	}

    /**
	 * 设置bit位为1，考虑性能，index不做运行时检查
     *
	 * @param source 需要设置比特位的整数
	 * @param bitIndex bit位置，从1开始
	 */
    public static int flagBit(int source, int bitIndex) {
        assert bitIndex <= 32 && bitIndex > 0;

        return source | (1 << bitIndex - 1);
    }

    /**
	 * 设置bit位为0，不做运行时检查
	 * @param source 需要设置比特位的整数
	 * @param bitIndex bit位置，从1开始
	 */
    public static int clearBit(int source, int bitIndex) {
        assert bitIndex <= 32 && bitIndex > 0;

		return source & (Integer.MAX_VALUE ^ (1 << (bitIndex - 1)));
    }

	/**
	 * 判断bit位是否为1，兼容老接口
	 *
	 * @param source 需要判断比特位的整数
	 * @param bitIndex bit位置，从1开始
	 * @return 如果bit位为1返回TRUE，为0返回FALSE
	 */
	public static boolean getBit(int source, int bitIndex) {
		assert bitIndex <= 32 && bitIndex > 0;

		if ((source >>> (bitIndex - 1) & 0x1) == 1) {
			return true;
		}

		return false;
	}

	/**
	 * 判断bit位是否为1，兼容老接口
	 * 
	 * @param source 需要判断比特位的整数
	 * @param bitIndex bit位置，从1开始
	 * @return 如果bit位为1返回TRUE，为0返回FALSE
	 */
	public static boolean isOne(int source, int bitIndex) {
	    return getBit(source, bitIndex);
	}
	
	/**
	 * 判断num的第bit位是否为1
	 * 
	 * @param source 需要判断比特位的整数
	 * @param bit bit位置，从1开始
	 * @return boolean
	 */
	public static boolean isOne(long source, int bit) {
		if ((source >>> (bit - 1) & 0x1) == 1) {
			return true;
		}

		return false;
	}

    /**
     * 查询哪些位是 1
     *
     * @param target 待处理的对象
     * @return target 中 1 的位置，从 0 开始; target 必须大于 0, 否则返回空列表
     */
    public static List<String> getOnePositions(long target) {
        if (target <= 0) {
            return Collections.emptyList();
        }
        List<String> bitSet = Lists.newArrayList();
        for (int i = 0; i < Long.SIZE; i++) {
            if (target == 0) {
                break;
            }
            if ((target & 1L) == 1) {
                bitSet.add(String.valueOf(i));
            }
            target >>= 1;
        }
        return bitSet;
    }
}
