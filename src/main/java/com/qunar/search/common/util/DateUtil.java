package com.qunar.search.common.util;

import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 新开一个DateUtil工具类, 以后使用这个, 后续替换掉旧的util.
 * Created by fangxue.zhang on 2015/4/22.
 */
public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 日期格式化
     */
    public static final String yyyyMMdd="yyyyMMdd";
    public static final String yyyy_MM_dd = "yyyy-MM-dd";
    public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String yyyy_MM_dd_HH_mm_ss_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String HH_mm = "HH:mm";

    /**
     * 把Date按照pattern格式化, pattern可以使外部传入的, 也可以是DateUtil提供的.
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String toString(Date date, String pattern) {
        return FastDateFormat.getInstance(pattern).format(date);
    }

    /**
     * 获取今天0点的时间.
     *
     * @return
     */
    public static Date getTodayStartTime() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 把当前时间向后推days天
     *
     * @param date
     * @param days
     * @return
     */
    public static Date backDays(Date date, int days) {
        if (days <= 0) {
            return date;
        }
        return forwardDays(date, -days);
    }


    /**
     * 把日期向前推, 注意这里不考虑days是否是负数, 如果是负数, 就是向后推days天
     * @param date
     * @param days
     * @return
     */
    public static Date forwardDays(Date date, int days){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

	/**
	 * 获取当前日期的 yyyyMMdd 格式的int
	 *
	 * @return
	 */
	public static int getTodayYyyyMMdd() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		return Integer.parseInt(sdf.format(new Date()));
	}

	/**
	 * 把ymd日期加上n，反回。 n为+1则为当天的明天，-1则为当天的昨天。
	 *
	 * @param ymd
	 * @param n
	 * @return
	 */
	public static int addDay(int ymd, int n) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Date date = null;
		try {
			date = sdf.parse(String.valueOf(ymd));
		} catch (ParseException e) {
			e.printStackTrace();
			return -1;
		}

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.DAY_OF_MONTH, n);

		return Integer.parseInt(sdf.format(cal.getTime()));
	}

	public static int getWeekDay (int ymd) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Date date = null;
		try {
			date = sdf.parse(String.valueOf(ymd));
		} catch (ParseException e) {
			e.printStackTrace();
			return -1;
		}

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
		return week;
	}

	/**
	 * 把ymd日期加上n，反回。 n为+1则为当天的明天，-1则为当天的昨天。
	 *
	 * @param ymd
	 * @param n
	 * @return
	 */
	public static int addMonth(int month, int n) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		Date date = null;
		try {
			date = sdf.parse(String.valueOf(month));
		} catch (ParseException e) {
			e.printStackTrace();
			return -1;
		}

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, n);

		return Integer.parseInt(sdf.format(cal.getTime()));
	}

	/**
	 * 把ymd日期加上n，反回。 n为+1则为当天的明天，-1则为当天的昨天。
	 *
	 * @param ymd
	 * @param n
	 * @return
	 */
	public static String addDay10(String ymd, int n) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date = null;
		try {
			date = sdf.parse(ymd);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.DAY_OF_MONTH, n);

		return sdf.format(cal.getTime());
	}

	/**
	 * 获取当前日期的 yyyy-MM-dd 格式的String
	 *
	 * @return
	 */
	public static String getToday10() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(new Date());
	}

	/**
	 * 获取当前日期的 yyyy-MM-dd 格式的String
	 *
	 * @return
	 */
	public static String getMonth() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		return sdf.format(new Date());
	}

	/**
	 * yyyy-MM-dd HH:mm:ss
	 *
	 * @param source
	 * @return
	 */
	public static Date getTime14(String source) {
		Date dstTime;
		try {
			dstTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(source);
		} catch (ParseException e) {
			e.printStackTrace();
			throw new IllegalArgumentException("source:" + source
					+ " 作为日期参数传入不合法");
		}
		return dstTime;
	}

	/**
	 * yyyy-MM-dd HH:mm:ss
	 *
	 * @param source
	 * @return
	 */
	public static Date getTime10(String source) {
		Date dstTime;
		try {
			dstTime = new SimpleDateFormat("yyyy-MM-dd").parse(source);
		} catch (ParseException e) {
			//e.printStackTrace();
			throw new IllegalArgumentException("source:" + source
					+ " 作为日期参数传入不合法");
		}
		return dstTime;
	}

	/**
	 * yyyy-MM-dd HH:mm:ss
	 *
	 * @param source
	 * @return
	 */
	public static String tranTo10(int day) {
		Date dstTime = null;
		try {
			dstTime = new SimpleDateFormat("yyyyMMdd").parse(day + "");
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return getTime10(dstTime);
	}

	public static String tranTo8(String day) {
		Date dstTime = null;
		try {
			dstTime = new SimpleDateFormat("yyyy-MM-dd").parse(day + "");
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return getTime8(dstTime);
	}

	/**
	 * 返回source的字符串信息: yyyyMMdd
	 */
	public static String getTime8(Date source) {
		if (source == null) {
			source = new Date();
		}
		return new SimpleDateFormat("yyyyMMdd").format(source);
	}

	/**
	 * 返回source的字符串信息: yyyy-MM-dd
	 */
	public static String getTime10(Date source) {
		if (source == null) {
			source = new Date();
		}
		return new SimpleDateFormat("yyyy-MM-dd").format(source);
	}

	public static List<String> getDateList(String sdate, String edate) {
		sdate = sdate.trim();
		edate = edate.trim();
		DateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		List<String> dateList = new ArrayList<String>();
		try {
			Date sd = sdf.parse(sdate);
			Date ed = sdf.parse(edate);
			int days = Integer.valueOf((ed.getTime() - sd.getTime())
					/ (1000 * 24 * 3600) + "");
			Date tmpDate = sdf.parse(sdate);
			for (int i = 0; i < days + 1; i++) {
				dateList.add(sdf.format(tmpDate));
				tmpDate.setTime(tmpDate.getTime() + (1000 * 3600 * 24));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dateList;
	}

	public static String valDateStr(String date) {
		String dd = "";
		try {
			if (date.indexOf("-") > -1 && date.length() == 10) {
				dd = date.replace("-", "");
			} else if (date.length() == 8) {
				dd = date.substring(0, 4) + "-" + date.substring(4, 6) + "-"
						+ date.substring(6, 8);
			} else {
				dd = date;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dd;
	}

	public static String valDateStr2(String date) {
		String dd = "";
		try {
			if (date.indexOf("-") > -1) {
				dd = date.replace("-", "");
			} else {
				dd = date.substring(0, 4) + "-" + date.substring(4, 6) + "-"
						+ date.substring(6, 8) + " 00:00:00";
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dd;
	}

	public static String valDateStrYMD(String date) {
		String dd = "";
		try {
			if (date.indexOf("-") > -1) {
				String day = date.replace("-", "");
				dd = day.substring(0, 8);
			} else {
				dd = date.substring(0, 4) + "-" + date.substring(4, 6) + "-"
						+ date.substring(6, 8) + " 00:00:00";
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dd;
	}

	public static boolean isNotRightTime(int start, int end) {
		boolean isOK = true;
		String nowTime = nowHour().substring(0, 2);
		if ((Integer.parseInt(nowTime) >= start)
				&& (Integer.parseInt(nowTime) <= end)) {
			isOK = false;
		}
		return isOK;
	}

	public static String getPreHour (int i) {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHH");
		Date d=new Date();
		d.setHours(d.getHours() + i);
		return sdf.format(d);
	}

	public static String getPreHour10 (int i) {
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd-HH");
		Date d=new Date();
		d.setHours(d.getHours() + i);
		return sdf.format(d);
	}

	public static String nowHour() {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(new Date().getTime());
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHH");
		return dateFormat.format(c.getTime());
	}

	public static Date afterDay(Date date, int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, day);
		return calendar.getTime();
	}

	public static int getSubDay (String fromDate, String toDate) {
		try {
			Date dateFrom = getTime10(fromDate);
			Date dateTo = getTime10(toDate);
			long timeFrom = dateFrom.getTime();
			long timeTo = dateTo.getTime();
			long subTime = timeTo - timeFrom;
			if (subTime <= 0) {
				return 0;
			}
			long sub = subTime/1000/3600/24;
			return (int)sub;
		} catch (Exception e) {
			return 0;
		}
	}
}