package com.qunar.search.common.parser.impl;

import com.google.common.collect.Lists;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.filter.Filter;
import com.qunar.search.common.parser.SingleFilterParser;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.qunar.search.common.constants.CommonConstants.COMMA_SPLITTER;


public class HotelTypeFilterParser implements SingleFilterParser {

    protected static class HotelTypeFilter implements Filter<HotelItem> {
        private String[] hotelType;

        public HotelTypeFilter(String[] hotelType) {
            this.hotelType = hotelType;
        }

        @Override
        public boolean accept(HotelItem o) {
            String[] htArray = o.info.getHotelType();

            if (htArray == null || htArray.length == 0) return false;

            Arrays.sort(htArray);
            int hit = -1;
            for (String t : hotelType) {
                if (t.equals(RANK_COMFORT_LUXURY_HOTEL) || t.equals(RANK_ECOMONIC_CHAIN_HOTEL)) {
                    continue;
                }
                hit = Arrays.binarySearch(htArray, t);
                if (hit >= 0) {
                    return true;
                }
            }
            return false;
        }
    }

    public static final String RANK_ECOMONIC_CHAIN_HOTEL = "RANK_ECOMOMIC_CHAIN_HOTEL";
    public static final String RANK_COMFORT_LUXURY_HOTEL = "RANK_COMFORT_LUXURY_HOTEL";
    public static final String RANK_ECOMONIC_CHAIN_HOTEL_V = "经济连锁";
    public static final String RANK_COMFORT_LUXURY_HOTEL_V = "舒适豪华";
    public static final String CHAIN_HOTEL = "CHAIN_HOTEL";

    /**
     * HOTELTYPE:
     * "BUSINESS_HOTEL", "ECOMOMIC_HOTEL", "EXPO_HOTEL", "RESORT_HOTEL",
     * "FAMILY_HOTEL", "RENTAL_HOTEL", "EXCELLENT_HOTEL", "SUBJECT_HOTEL",
     * "GUZHEN_HOTEL", "CHAIN_HOTEL", "VILLAGE_HOTEL", "GUEST_HOTEL", "BEST_HOTEL"
     * NEW_ADDED :
     * "RANK_ECOMOMIC_CHAIN_HOTEL", "RANK_COMFORT_LUXURY_HOTEL"
     *
     * @param request HttpServletRequest
     * @return Filter<HotelItem>
     */
    @Override
    public Filter<HotelItem> parse(HttpServletRequest request) {
        String hotelType = StringUtils.trimToNull(request.getParameter("hotelType"));
        if (hotelType == null) {
            return null;
        }
        List<String> hotelTypeList = null;
        hotelTypeList = Lists.newArrayList(COMMA_SPLITTER.split(hotelType));
        if (hotelTypeList == null || hotelTypeList.size() == 0) {
            return null;
        }
        boolean isChainHotel = false;
        Collections.sort(hotelTypeList);
        int pos = Collections.binarySearch(hotelTypeList, RANK_ECOMONIC_CHAIN_HOTEL);
        if (pos >= 0) {
            isChainHotel = true;
        }
        pos = Collections.binarySearch(hotelTypeList, RANK_COMFORT_LUXURY_HOTEL);
        if (pos >= 0) {
            isChainHotel = true;
        }
        pos = Collections.binarySearch(hotelTypeList, CHAIN_HOTEL);
        if (pos < 0 && isChainHotel) {
            hotelTypeList.add(CHAIN_HOTEL);
        }
        return new HotelTypeFilter(hotelTypeList.toArray(new String[hotelTypeList.size()]));
    }
}
