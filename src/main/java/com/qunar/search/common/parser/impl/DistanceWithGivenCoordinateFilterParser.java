package com.qunar.search.common.parser.impl;

import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.filter.Filter;
import com.qunar.search.common.gis.GLatLng;
import com.qunar.search.common.parser.SingleFilterParser;
import com.qunar.search.common.util.CommonTools;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.agile.Numbers;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

public class DistanceWithGivenCoordinateFilterParser implements SingleFilterParser {
	private static Logger log = LoggerFactory.getLogger(DistanceWithGivenCoordinateFilterParser.class);
	
	private static final int DEFAULT_DISTANCE = 100000; // 默认距离100公里

	protected static class DistanceWithGivenCoordinateFilter implements
            Filter<HotelItem> {

		//查询的坐标点
		private GLatLng coordLatLng;
		//距离范围
		private double distance;
		
		private boolean isFilter = true;
		

		private boolean channelCity = false;
		
		private boolean isCooKeySearch = false;
		
		public DistanceWithGivenCoordinateFilter(GLatLng coordLatLng,
				double distance, boolean isFilter, boolean channelCity, boolean isCooKeySearch) {

			this.coordLatLng = coordLatLng;
			this.distance = distance;
			this.isFilter = isFilter;
			this.channelCity = channelCity;
			this.isCooKeySearch = isCooKeySearch;
		}

		@Override
		public boolean accept(HotelItem e) {
			GLatLng gll = e.info.getLatLng();
			if (gll != null && coordLatLng != null) {
				if (this.channelCity || this.isCooKeySearch) {
					double dist = gll.distance(coordLatLng) * 1000L;
					e.corrDistance = dist;
				}
				e.normalCorrDistance = CommonTools.getNormalDistance((int) e.corrDistance);
			}
			if (this.isFilter) {
				if (gll == null || coordLatLng == null) {
					return false;
				}
				if (e.corrDistance <= distance || Optional.ofNullable(e.getFilterInfo()).map(t-> t.containsKey("hotelName".intern())).orElse(false))
					return true;
				return false;
			} else {
				if (e.distance <= distance)
					return true;
				return false;
			}
		}

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.qunar.hotel.bean.Parser#parse(javax.servlet.http.HttpServletRequest)
	 */
	@Override
	public Filter<HotelItem> parse(HttpServletRequest request) {
		if (!accept(request))
			return null;
		//坐标点
		String coordinateStr = StringUtils.trimToNull(request.getParameter("coord"));
		GLatLng coordLatLng = GLatLng.getLatLng(coordinateStr);

		//距离坐标点的距离
		String distanceStr = StringUtils.trimToNull(request
				.getParameter("coordDist"));
		boolean isFilter = "1".equals((String) request.getAttribute("isCooFilter"));

		boolean channelCity = "1".equals((String) request.getAttribute("channelCity"));

		boolean isCooKeySearch = "1".equals((String) request.getAttribute("isCooKeySearch"));
		if(channelCity && Numbers.toInt(distanceStr, DEFAULT_DISTANCE) >= DEFAULT_DISTANCE){
			isFilter = false;
		}

		log.info("isCooFilter : " + isFilter);
		int distance = Numbers.toInt(distanceStr, DEFAULT_DISTANCE);
		return new DistanceWithGivenCoordinateFilter(coordLatLng,
				distance * 1.0, isFilter, channelCity, isCooKeySearch);
	}

	public static final boolean accept(HttpServletRequest request) {
		String coordinateStr = StringUtils.trimToNull(request.getParameter("coord"));
		
		if (coordinateStr == null)
			return false;
		GLatLng coordLatLng = GLatLng.getLatLng(coordinateStr);
		if (coordLatLng == null)
			return false;
		return true;
	}
}
