package com.qunar.search.common.base;

import com.qunar.search.common.util.SpringContextUtil;
import qunar.rpc.dubbo.SpringContextAdaptor;

/**基于QunarServer实现有一个SpringContextAdaptor, 这个是一个实现了Service类.
 * SpringContextAdaptorEx用来支持在spring容器内部注入SpringContextAdaptor
 * SpringContextAdaptor的使用可以看一下data-center-api.
 * Created by fangxue.zhang on 2016/4/14.
 */
public class SpringContextAdaptorEx extends SpringContextAdaptor {
	@Override
	public <T> T getBean(String name) {
		Object bean = SpringContextUtil.getBean(name);
		if (bean == null) {
			return null;
		}
		return (T) bean;
	}
}
