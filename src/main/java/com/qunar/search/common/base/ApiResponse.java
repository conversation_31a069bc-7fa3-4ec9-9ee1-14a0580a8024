package com.qunar.search.common.base;

import java.io.Serializable;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/12/17
 */
public class ApiResponse<T> implements Serializable {
	private static final long serialVersionUID = 5241526151768786394L;

	private int status;
	private String message;
	private T data;


	public ApiResponse() {
	}

	private ApiResponse(T t) {
		this.status = 0;
		this.data = t;
	}

	private ApiResponse(String errmsg, T t) {
		this.status = -1;
		this.message = errmsg;
		this.data = t;
	}

	public static <T> ApiResponse<T> returnSuccess() {
		return new ApiResponse<T>(null);
	}

	public static <T> ApiResponse<T> returnSuccess(T t) {
		return new ApiResponse<T>(t);
	}

	public static <T> ApiResponse<T> returnFail(String errmsg) {
		return new ApiResponse<T>(errmsg, null);
	}

	public static <T> ApiResponse<T> returnFail(String errmsg, T t) {
		return new ApiResponse<T>(errmsg, t);
	}


	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
}
