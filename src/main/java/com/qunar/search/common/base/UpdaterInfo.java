package com.qunar.search.common.base;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于定时任务的注解，主要用来指定表名和版本号字段名
 *
 * <AUTHOR>
 * @date 2020/6/18
 */
@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface UpdaterInfo {

    /**
     * 用来指定DB中的表名
     *
     * @return 表名
     */
    String tableName();

    /**
     * 用于指定版本号的字段名称，默认是"version"
     *
     * @return 版本号字段名
     */
    String versionField() default "version";
}
