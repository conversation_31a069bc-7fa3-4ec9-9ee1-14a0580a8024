package com.qunar.search.common.base;


import com.qunar.search.common.enums.Env;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * search定期加载任务
 *
 * <AUTHOR>
 * @date 2018/6/11
 */
@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface SearchDataTask {

    String name() default "";

    /**
     * 是否需要初始化时调用
	 * 已废弃
     *
     * @return
     */
    boolean isNeedInit() default true;

    /**
     * 是否有schedule
     *
     * @return
     */
    boolean hasSchedule() default true;


    long initialDelay() default -1L;

	long fixedDelay() default -1L;

    /**
     * 调度的时间单位，默认是分钟
     */
    TimeUnit timeUnit() default TimeUnit.MINUTES;

	/**
	 * 是否等待基础数据加载
	 * @return
	 */
    boolean afterBaseDataFinish() default false;

    /**
     * 国内国际
     *
     * @return
     */
    Env env() default Env.C;
}
