package com.qunar.search.common.base;

import org.apache.http.client.methods.AbortableHttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.concurrent.Nameable;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 主要是用来约束实现类, 需要回收资源. 在recycle里面回收资源.
 * <AUTHOR>
 * @version $id$
 */

public abstract class NameableCallable<V> implements Nameable, Callable<V> {

    private static Logger log = LoggerFactory.getLogger(NameableCallable.class);

    private volatile AbortableHttpRequest httpRequest = null;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean needCancel = new AtomicBoolean(false);


    /**
     * 回收逻辑, 如果因为超时, 或者被cancel掉了. 而还有http请求在执行, 那么就abort掉.
     */
    public void recycle(){
        if (running.get() && needCancel.get()) {
            if (httpRequest != null) {
                try {
                    httpRequest.abort();
                    log.warn("http request to call rt has been aborted");
                } catch (Exception e) {
                    log.warn("abort pending http request to call rt error");
                }
            }
        }
    }

    protected void setHttpRequest(AbortableHttpRequest httpRequest) {
        this.httpRequest = httpRequest;
    }

    protected void setRunning(boolean running){
        this.running.set(running);
    }

    protected void setNeedCancel(boolean needCancel){
        this.needCancel.set(needCancel);
    }
}
