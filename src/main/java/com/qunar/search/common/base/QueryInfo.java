package com.qunar.search.common.base;



import com.qunar.search.common.bean.PoiInfo;
import com.qunar.search.common.enums.FilterType;
import com.qunar.search.common.enums.QueryAckType;

import java.util.List;
import java.util.Set;

/**
 * 用来描述一个请求需要提供的字段.
 */


public interface QueryInfo {

    String QUERY_TYPE_KEY = "qtype";

    /**
     * 目前Qtype 数据针对多关键词不准确 国内用RECALL_TYPE替代QTYPE(兼容Query的其他使用方)
     */
    String RECALL_TYPE = "recall_type";

	String getOriginalQuery();

    String getNormalizedQuery();

    String getResultQuery();

    Set<FilterType> getFilterTypes();

    PoiInfo getPosition(String poiName);

    Set<String> getFilterKeys(FilterType ftype);

    boolean fullMath();

    Set<String> getQueryType();

    List<String> getRecallType();

    QueryAckType getQueryAckType();
}
