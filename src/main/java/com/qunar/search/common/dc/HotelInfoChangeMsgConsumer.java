package com.qunar.search.common.dc;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.hotel.open.pojo.SyncHotel;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.redis.storage.Sedis3;
import com.qunar.search.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.management.ServerManager;
import qunar.tc.qconfig.client.spring.QMapConfig;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.OffsetResetType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 接收qhotel的酒店基础数据变更消息，将syncHotel转成hotelInfo，然后更新内存。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=653597545
 *
 * <AUTHOR>
 */
public class HotelInfoChangeMsgConsumer {

    private static final DateTimeFormatter FULL_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    private static final Logger hotelInfoLog = LoggerFactory.getLogger("hotelInfoLog");

    private volatile boolean ready = false;

    @QMapConfig(value = "hotel_info_sync.properties", key = "qmq.enable", defaultValue = "true")
    private boolean enable;

    @QMapConfig(value = "hotel_info_sync.properties", key = "qmq.enablePrintLog", defaultValue = "true")
    private boolean enablePrintLog;

    @QMapConfig(value = "hotel_info_sync.properties", key = "qmq.enableSendCallBackMsg", defaultValue = "true")
    private boolean enableSendCallBackMsg;

    /**
     * redis存放发送回调消息的标识的过期时间，单位是秒
     */
    @QMapConfig(value = "hotel_info_sync.properties", key = "qmq.expireTime", defaultValue = "10")
    private int expireTime;

    @Resource(name = "producer")
    private MessageProducer producer;

    @Resource(name = "hotelInfoSedis")
    private Sedis3 hotelInfoSedis;

    public void setReady(boolean ready) {
        this.ready = ready;
    }

    @QmqConsumer(prefix = "qunar.hotel.qhotel.hotelinfo.publish.changed", isBroadcast = true, offsetResetType = OffsetResetType.LATEST)
    public void onMessage(Message message) {
        if (!ready) {
            return;
        }

        if (!enable) {
            return;
        }

        long start = System.currentTimeMillis();
        String pullStart = LocalDateTime.now().format(FULL_TIME_FORMATTER);
        try {
            String syncHotelStr = message.getStringProperty("syncHotel");

            SyncHotel syncHotel = JsonUtils.toBean(syncHotelStr, SyncHotel.class);

            String seq = syncHotel.getHotelSeq();

            if (enablePrintLog) {
                hotelInfoLog.info("msgId:{}, seq:{}, 状态:{}, updateTime:{}", message.getMessageId(), seq, syncHotel.getOperatingStatusName(), syncHotel.getUpdated());
            }

            HotelInfoHolder.updateWithLock(syncHotel);

            // 监控更新时间的延迟, 延迟的单位是秒。syncHotel.getUpdated() 是微妙精度的时间戳。
            QMonitor.recordQuantile("hotelInfoChangeMsgUpdateDelay", (System.currentTimeMillis() - syncHotel.getUpdated() / 1000) / 1000);

            if (enableSendCallBackMsg) {
                // 由于这里用的是广播方式来消费，应用的每台机器都会收到消息。给qhotel发送回调消息的时候，每个应用针对某个消息id，只发一次回调消息即可。不然会对qhotel那边造成较大压力。
                String appCode = ServerManager.getInstance().getAppConfig().getName();
                String key = StringUtils.join("call_back_", appCode, "_",  message.getMessageId());

                // NX: set if not exists; XX: set if already exist
                // EX: seconds PX: milliseconds
                String result = hotelInfoSedis.set(key, "1", "NX", "EX", expireTime);

                // 如果返回值不为OK，说明这个消息id已经发送过回调消息，不需要再发了。
                if (!StringUtils.equalsIgnoreCase(result, "OK")) {
                    return;
                }

                String pullEnd = LocalDateTime.now().format(FULL_TIME_FORMATTER);
                // 给qhotel发送数据同步回调消息。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=669386653
                Message callBackMessage = producer.generateMessage("hotel.sync.call.back.message");
                Map<String, Object> data = Maps.newHashMap();
                data.put("seqs", Lists.newArrayList(seq));
                data.put("pullStart", pullStart);
                data.put("pullEnd", pullEnd);
                callBackMessage.setProperty("data", JsonUtils.toJson(data));
                callBackMessage.setProperty("dataType", "hotel_info_update");
                producer.sendMessage(callBackMessage);
                QMonitor.recordOne("sendHotelInfoCallBackMsg");
            }
        } catch (Throwable e) {
            QMonitor.recordOne("handleHotelInfoChangeMsgError");
            hotelInfoLog.error("处理酒店基础数据变更消息出错。msg:{}", message, e);
        } finally {
            QMonitor.recordQuantile("handleHotelInfoChangeMsg", System.currentTimeMillis() - start);
        }
    }

}
