package com.qunar.search.common.dc;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunar.hotel.open.pojo.SyncHotel;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelInfo;
import com.qunar.search.common.dc.attr.CompositeAttrHandler;
import com.qunar.search.common.dc.attr.HandleException;
import com.qunar.search.common.dc.attr.HotelAttrHandler;
import com.qunar.search.common.dc.attr.HotelAttrsConfig;
import com.qunar.search.common.enums.HotelDangciType;
import com.qunar.search.common.enums.HotelOperatingStatus;
import com.qunar.search.common.enums.HotelStarsType;
import com.qunar.search.common.util.CommonTools;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.qunar.search.common.constants.CommonConstants.PIPELINE_JOINER;
import static com.qunar.search.common.constants.CommonConstants.PIPE_SPLITTER;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

/**
 * qhotel的SyncHotel和rank的HotelInfo的转换逻辑。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=662387902
 *
 * 大部分转换代码从报价那边的DC系统迁移而来。
 */
@Slf4j
public class HotelInfoUtils {

    /**
     * 酒店装修时间默认值
     */
    private static final int HOTEL_FITMENT_YEAR = 1980;

    private static Map<Integer, String> operatingStatusMap = Maps.newHashMap();

    private static Map<String, String> infoMap = Maps.newHashMap();

    private static Set<String> facilitySet = new HashSet<String>();

    private static Map<String, String> stationPackupMap = new HashMap<String, String>();

    private static Map<String, String> hasInternetAccessMap = new HashMap<String, String>();

    private static Map<String, String> facilityMap = new HashMap<String, String>();

    private static final String[] EMPTY_STRING_ARRAY = new String[]{};

    // 静态代码块里的代码从DC系统迁移而来
    static {
        stationPackupMap.put("0", "Y");
        stationPackupMap.put("1", "Y");
        stationPackupMap.put("2", "N");
        stationPackupMap.put("3", "N");

        operatingStatusMap.put(0, "营业中");
        operatingStatusMap.put(1, "筹建中");
        operatingStatusMap.put(2, "暂停营业");
        operatingStatusMap.put(3, "已停业");
        operatingStatusMap.put(4, "删除");

        hasInternetAccessMap.put("0", "免费");
        hasInternetAccessMap.put("1", "收费");
        hasInternetAccessMap.put("2", "无");
        hasInternetAccessMap.put("3", "不确定");
        hasInternetAccessMap.put("4", "部分收费");
        hasInternetAccessMap.put("5", "部分房间有且免费");
        hasInternetAccessMap.put("6", "部分房间有且收费");
        hasInternetAccessMap.put("7", "部分房间有且部分免费");
        hasInternetAccessMap.put("Y", "Y");

        facilityMap.put("0", "Y");
        facilityMap.put("1", "N");
        facilityMap.put("2", "");
        facilityMap.put("Y", "Y");
        facilityMap.put("N", "N");

        infoMap.put("enName", "HotelEnName");
        infoMap.put("alias", "HotelAlias");
        infoMap.put("whenBuilt", "WhenBuilt");
        infoMap.put("whenFitment", "whenFitment");
        infoMap.put("numberFloors", "NumberFloors");
        infoMap.put("numberRooms", "NumberRooms");
        infoMap.put("faxNumber", "FaxNumber");
        infoMap.put("ppc", "ppc");
        infoMap.put("chain", "IS_CHAIN");
        infoMap.put("around", "HotelAround");
        infoMap.put("thumbnailId", "imageID");
        infoMap.put("meal", "hasBreakfast");
        infoMap.put("mealPrice", "HotelMealPrice");
        infoMap.put("hasChineseRestaurant", "hasChineseRestaurant");
        infoMap.put("hasWesternRestaurant", "hasWesternRestaurant");
        infoMap.put("hasParkingArea", "HasParkingArea");
        infoMap.put("has_parking_area", "HasParkingArea");
        infoMap.put("hasBusinessCenter", "HasBusinessCenter");
        infoMap.put("hasMeetingOrBanquet_space", "HasMeetingORBanquetSpace");
        infoMap.put("hasMeetingOrBanquetSpace", "HasMeetingORBanquetSpace");
        infoMap.put("hasNonSmokingAvailable", "HasNonSmokingAvailable");
        infoMap.put("hasStationPickUp", "HasStationPickUp");
        infoMap.put("hasAirportPickUp", "HasAirportPickUp");
        infoMap.put("hasCarRent", "hasCarRent");
        infoMap.put("hasWifiAccess", "WifiAccess");
        infoMap.put("hasSauna", "HasSauna");
        infoMap.put("hasIndoorPool", "HasIndoorPool");
        infoMap.put("hasOutdoorPool", "HasOutdoorPool");
        infoMap.put("hasFitnessRoom", "HasFitnessRoom");
        infoMap.put("hasPub", "hasPub");
        infoMap.put("hasSpa", "hasSPA");
        infoMap.put("hasChessRoom", "HasChessRoom");
        infoMap.put("hasHotSpring", "HasHotSpring");
        infoMap.put("internetAccess", "HasInternetAccess");
        infoMap.put("hasHairDryer", "HasHairDryer");
        infoMap.put("hasAirConditioning", "HasAirConditioning");
        infoMap.put("hasGuestLaundromat", "HasGuestLaundromat");
        infoMap.put("hasChildCare", "HasChildCare");
        infoMap.put("hasWakeUpService", "HasWakeUpService");
        infoMap.put("hasHandicApaccessible", "HasHandicApaccessible");
        infoMap.put("hasInternationalPhoneCall", "hasInternationalPhoneCall");
        infoMap.put("hasFreeNationalCall", "hasFreeTollCall");
        infoMap.put("hasFreeLocalCall", "hasFreeLocalCall");
        infoMap.put("hasCarryPet", "isPetAllowed");
        infoMap.put("hasLeftLuggage", "hasLeftLuggage");
        infoMap.put("hasHeater", "hasHeater");
        infoMap.put("hotelTag", "tag");
        infoMap.put("hasHeatWater", "has24hotwater");

        infoMap.put("bossId", "BossId");
        infoMap.put("bossName", "BossName");
        infoMap.put("bossSex", "BossSex");
        infoMap.put("bossMobileNum", "BossMobPhoneN");
        infoMap.put("bossWeiboUrl", "BossWeibourl");
        infoMap.put("bossQQNum", "BossQQ");
        infoMap.put("bossCity", "BossCity");
        infoMap.put("bossIntro", "BossIntro");
        infoMap.put("childPolicyDetail","childCommonBiz");

        facilitySet.add("hasChineseRestaurant");
        facilitySet.add("hasWesternResaurant");
        facilitySet.add("hasWesternRestaurant");
        facilitySet.add("hasParkingArea");
        facilitySet.add("hasBusinessCenter");
        facilitySet.add("hasMeetingOrBanquetSpace");
        facilitySet.add("hasMeetingOrBanquet_space");
        facilitySet.add("hasNonSmokingAvailable");
        facilitySet.add("hasCarRent");
        facilitySet.add("hasWifiAccess");
        facilitySet.add("hasSauna");
        facilitySet.add("hasIndoorPool");
        facilitySet.add("hasOutdoorPool");
        facilitySet.add("hasFitnessRoom");
        facilitySet.add("hasPub");
        facilitySet.add("hasSpa");
        facilitySet.add("hasChessRoom");
        facilitySet.add("hasHotSpring");
        facilitySet.add("hasHairDryer");
        facilitySet.add("hasAirConditioning");
        facilitySet.add("hasGuestLaundromat");
        facilitySet.add("hasChildCare");
        facilitySet.add("hasWakeUpService");
        facilitySet.add("hasHandicApaccessible");
        facilitySet.add("hasInternationalPhoneCall");
        facilitySet.add("hasFreeNationalCall");
        facilitySet.add("hasFreeLocalCall");
        facilitySet.add("hasCarryPet");
        facilitySet.add("hasLeftLuggage");
        facilitySet.add("hasHeater");
        facilitySet.add("hasHeatWater");// Y-N
        facilitySet.add("has_parking_area");
    }

    public static HotelInfo covertSyncHotelToHotelInfo(SyncHotel syncHotel) {
        return covertSyncHotelToHotelInfo(syncHotel, false);
    }

    public static HotelInfo covertSyncHotelToHotelInfo(SyncHotel syncHotel, boolean notNeedInternAttrs) {
        if (syncHotel == null) {
            return null;
        }

        String hotelSeq = syncHotel.getHotelSeq();

        try {
            String operatingStatusName = operatingStatusMap.get(syncHotel.getOperatingStatus());

            if (StringUtils.isEmpty(operatingStatusName) || "删除".equals(operatingStatusName) || "已停业".equals(operatingStatusName)) {
                return null;
            }

            if (StringUtils.isEmpty(syncHotel.getName()) || StringUtils.isEmpty(syncHotel.getHotelSeq())) {
                return null;
            }

            HotelInfo hotelInfo = new HotelInfo(hotelSeq);

            hotelInfo.setQhotelUpdateTime(syncHotel.getUpdated());

            hotelInfo.setHotelName(syncHotel.getName());

            // 酒店营业状态
            hotelInfo.setHotelStatus(HotelOperatingStatus.parse(operatingStatusName));

            String hotelStar = syncHotel.getStar() == null ? "0" : syncHotel.getStar().toString();
            hotelInfo.setStars(NumberUtils.toInt(hotelStar) * 2);
            hotelInfo.setHotelStar(hotelStar);
            hotelInfo.setHotelStarsType(HotelStarsType.parse(NumberUtils.toInt(hotelStar)));

            hotelInfo.setHotelAddress(syncHotel.getAddress());

            if (syncHotel.getGrade() != null) {
                int dangci = syncHotel.getGrade();
                hotelInfo.setHotelDangciType(HotelDangciType.parse(dangci));
                hotelInfo.setHasDangci(dangci > 0);
            }

            if (syncHotel.getLowestPrice() != null && syncHotel.getLowestPrice() != 0) {
                hotelInfo.setMiniRetailPrice(syncHotel.getLowestPrice());
            } else {
                hotelInfo.setMiniRetailPrice(-1);
            }

            if (syncHotel.getTypes() != null) {
                if (notNeedInternAttrs) {
                    hotelInfo.setHotelTypeArr(syncHotel.getTypes());
                } else {
                    hotelInfo.setHotelTypeArr(internStringList(syncHotel.getTypes()));
                }
            } else {
                hotelInfo.setHotelTypeArr(EMPTY_STRING_ARRAY);
            }

            hotelInfo.setLl(CommonTools.getLatLng(syncHotel.getGooglePoint()));
            hotelInfo.setImageId(syncHotel.getThumbnailId());

            String onlineChannel = "";
            if (syncHotel.getAttrs() != null) {
                onlineChannel = (String)syncHotel.getAttrs().get("onlineChannel");
            }

            hotelInfo.setBNB(StringUtils.contains(onlineChannel, "bnb"));

            if (StringUtils.isNotBlank(onlineChannel)) {
                hotelInfo.setOnlineChannel(StringUtils.split(onlineChannel, "|"));
            } else {
                hotelInfo.setOnlineChannel(EMPTY_STRING_ARRAY);
            }

            if (syncHotel.getCityTags() != null) {
                if (notNeedInternAttrs) {
                    hotelInfo.setCityTagArr(syncHotel.getCityTags());
                } else {
                    hotelInfo.setCityTagArr(internStringList(syncHotel.getCityTags()));
                }
            }

            Map<String, Set<String>> setAttrMap = Maps.newHashMap();

            Map<String, String> props = convertAttrsToProps(syncHotel.getAttrs(), syncHotel.getHotelSeq(), setAttrMap, notNeedInternAttrs);

            String whenBuilt = null;
            String whenFitment = null;
            if (syncHotel.getAttrs() != null) {
                whenBuilt = syncHotel.getAttrs().getOrDefault("whenBuilt", "").toString();
                whenFitment = syncHotel.getAttrs().getOrDefault("whenFitment", "").toString();

                // 酒店儿童政策
                if (null != syncHotel.getAttrs().get("childAndExtraBedPolicy")) {
                    Map<String, Object> childPolicyMap =  JsonUtils.fromJson(String.valueOf(syncHotel.getAttrs().get("childAndExtraBedPolicy")), JsonUtils.buildMapType(Map.class, String.class, Object.class));
                    if (null != childPolicyMap && null != childPolicyMap.get("allowChildrenToStay"))
                        props.put("allowChildrenToStay", String.valueOf(childPolicyMap.get("allowChildrenToStay")));
                }
            }

            // 计算酒店装修时间距现在的年数
            hotelInfo.setFitmentYearDiff(getFitmentYearDiff(whenBuilt, whenFitment));

            int builtYear = NumberUtils.toInt(whenBuilt, 0);
            int fitmentYear = NumberUtils.toInt(whenFitment, 0);

            int lastYear = new DateTime().minusYears(1).getYear();
            if (builtYear >= lastYear || fitmentYear >= lastYear) {
                props.put("isNewHotel", "Y");
            }

            String phoneNumber = StringUtils.strip(syncHotel.getPhoneNumber(), "-");
            if (StringUtils.isNotBlank(phoneNumber)) {
                props.put("PhoneNumber", phoneNumber);
            }

            if (StringUtils.isNotBlank(syncHotel.getArea())) {
                props.put("HotelArea", syncHotel.getArea());
            }

            if (StringUtils.isNotBlank(syncHotel.getOneSentence())) {
                props.put("oneSentence", syncHotel.getOneSentence());
            }

            if (StringUtils.isNotBlank(syncHotel.getCommentSummary())) {
                props.put("commentSummary", syncHotel.getCommentSummary());
            }

            if (syncHotel.getTradingAreas() != null && ArrayUtils.isNotEmpty(syncHotel.getTradingAreas())) {
                props.put("tradingArea", PIPELINE_JOINER.join(syncHotel.getTradingAreas()));
            }

            if (notNeedInternAttrs) {
                props.put("WhenBuilt", defaultIfBlank(whenBuilt, ""));
                props.put("whenFitment", defaultIfBlank(whenFitment, ""));
                props.put("hotelBrand", defaultIfBlank(syncHotel.getBrand(), ""));
            } else {
                props.put("WhenBuilt", defaultIfBlank(whenBuilt, "").intern());
                props.put("whenFitment", defaultIfBlank(whenFitment, "").intern());
                props.put("hotelBrand", defaultIfBlank(syncHotel.getBrand(), "").intern());
            }


            hotelInfo.setProps(props);

            if (MapUtils.isNotEmpty(setAttrMap)) {
                setAttrMap.forEach(hotelInfo::putSetProp);
            }

            Set<String> tags = hotelInfo.getSetProp("inlandCommonTag");

            // 提前计算好是否酒店新店
            boolean isNewHotel = CollectionUtils.isNotEmpty(tags) && tags.contains("24");
            hotelInfo.setNewHotel(isNewHotel);

            CompositeAttrHandler compositeAttrHandler = HotelAttrHandler.getCompositeAttrHandler();
            if (compositeAttrHandler != null) {
                compositeAttrHandler.handle(props, hotelInfo);
            }

            return hotelInfo;
        } catch (Exception e) {
            QMonitor.recordOne("SyncHotelCovertToHotelInfoError");
            log.error("SyncHotel转成Rank的HotelInfo发生错误，seq:{}", hotelSeq, e);
        }

        return null;
    }

    private static Map<String, String> convertAttrsToProps(Map<String, Object> attrs, String hotelSeq, Map<String, Set<String>> setAttrMap, boolean notNeedInternAttrs) {
        if (attrs == null) {
            return Maps.newHashMap();
        }

        Map<String, String> props = Maps.newHashMapWithExpectedSize(HotelAttrsConfig.needAttrs().size());

        boolean hasRestaurant = false;
        boolean isWebFree = false;

        for (Map.Entry<String, Object> attr : attrs.entrySet()) {
            String key = attr.getKey();
            String value;
            if (attr.getValue() == null || (value = attr.getValue().toString()).isEmpty()) {
                continue;
            }
            // 将qhotel的原始字段名做映射，历史逻辑就是这样，从DC系统的代码迁移而来。
            String key2 = infoMap.get(key) == null ? key : infoMap.get(key);
            if (key2.length() > 50) {
                continue;
            }

            // 将attr中酒店特征相关内容添加至 hotelFeatureTags属性
            if ("hotelFeatureTags".equals(key2)) {
                Set<String> hotelFeatureTagSet = Sets.newHashSet(PIPE_SPLITTER.split(value));
                for (HotelAttrsConfig.AddToHotelFeatureConfig config : HotelAttrsConfig.getAddToHotelFeatureConfigSet()) {
                    if (attrs.getOrDefault(config.getAttrKey(), StringUtils.EMPTY).equals(config.getAttrValue())) {
                        hotelFeatureTagSet.add(config.getFeatureName());
                    }
                }
                value = PIPELINE_JOINER.join(hotelFeatureTagSet);
            }
            // 将setAttrHandler处理流程提前至needAttrs().contains()判断之前，将ctripHotelFeatureTags的值转换后添加至setAttrMap中，原始数据不作保存
            if (HotelAttrHandler.hasSetAttrHandler(key2)) {
                try {
                    HotelAttrHandler.getSetAttrHandler(key2).handle(value, setAttrMap::put);
                } catch (HandleException e) {
                    log.error("{}的Attr:{}转换失败：{}", hotelSeq, key2, value, e);
                }
            }

            if (!HotelAttrsConfig.needAttrs().contains(key2)) {
                continue;
            }

            if ("hasStationPickUp".equals(key)) {
                props.put("hasStationPickUpQhotel", value);
                value = stationPackupMap.get(value);
            } else if ("internetAccess".equals(key)) {
                if ("0".equals(value) || "5".equals(value) || "7".equals(value)) {
                    isWebFree = true;
                }
                value = hasInternetAccessMap.get(value);
            } else if (facilitySet.contains(key)) {
                value = facilityMap.get(value);
            }

            if ("hasBreakfast".equals(key2)) {
                if ("0".equals(value) || "1".equals(value)) {
                    if ("0".equals(value)) {
                        props.put("HotelMealPrice", "0");
                    }
                    value = "Y";
                } else {
                    value = "N";
                }
            } else if ("hasWesternRestaurant".equals(key2) && "Y".equals(value)) {
                hasRestaurant = true;
            } else if ("hasChineseRestaurant".equals(key2) && "Y".equals(value)) {
                hasRestaurant = true;
            } else if ("HasAirportPickUp".equals(key2)) {
                props.put("hasAirportPickUpQhotel", value);
                if ("0".equals(value) || "1".equals(value)) {
                    value = "Y";
                } else {
                    value = "N";
                }
            }

            if (HotelAttrHandler.hasAttrHandler(key2)) {
                try {
                    HotelAttrHandler.getAttrHandler(key2).handle(value, props::put);
                } catch (HandleException e) {
                    log.error("{}的Attr:{}转换失败：{}", hotelSeq, key2, value, e);
                }
            } else {
                if (notNeedInternAttrs || HotelAttrsConfig.notNeedInternAttrs().contains(key2)) {
                    props.put(key2, value);
                } else {
                    props.put(key2, value.intern());
                }
            }
        }

        props.put("isWebFree", isWebFree ? "Y" : "N");
        props.put("HasRestaurant", hasRestaurant ? "Y" : "N");

        return props;
    }

    private static String[] internStringList(String[] list) {
        String[] newList = new String[list.length];
        for (int i = 0; i < list.length; i++) {
            newList[i] = list[i].intern();
        }
        return newList;
    }

    private static double getFitmentYearDiff(String whenBuilt, String whenFitment) {
        double fitmentYearDiff = 0;
        try {
            int builtYear = Math.max(NumberUtils.toInt(whenBuilt, HOTEL_FITMENT_YEAR), HOTEL_FITMENT_YEAR);
            int fitmentYear =  Math.max(NumberUtils.toInt(whenFitment, HOTEL_FITMENT_YEAR), HOTEL_FITMENT_YEAR);
            int thisYear = new DateTime().getYear();

            int max = Math.max(builtYear, fitmentYear);
            if (max <= thisYear && max >= HOTEL_FITMENT_YEAR){
                fitmentYearDiff = thisYear - max;
            }
        } catch (Exception e) {
            log.error("getFitmentYearDiff error:",e);
        }

        return fitmentYearDiff;
    }

}
