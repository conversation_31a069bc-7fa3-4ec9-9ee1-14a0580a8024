package com.qunar.search.common.dc.attr;

import java.util.Set;
import java.util.function.BiConsumer;

/**
 * Set类型的属性的解析
 */
public interface SetAttrHandler {
    /**
     * att属性的key
     *
     * @return
     */
    String getAttrName();

    /**
     * 原数据转换到缓存对象
     *
     * @param value
     * @return
     */
    void handle(String value, BiConsumer<String, Set<String>> putMethod) throws HandleException;
}
