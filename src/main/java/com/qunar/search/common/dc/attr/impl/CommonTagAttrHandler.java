package com.qunar.search.common.dc.attr.impl;

import com.google.common.collect.ImmutableSet;
import com.qunar.search.common.dc.attr.AttrHandler;

import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;

import static com.qunar.search.common.constants.CommonConstants.BNB_FLAG;
import static com.qunar.search.common.constants.CommonConstants.PIPE_SPLITTER;
import static com.qunar.search.common.update.HotelPropertyDict.HotelAttrKey.INLAND_COMMONTAG;

public class CommonTagAttrHandler implements AttrHandler {
	/**
	 * 默认情况下，民宿酒店大类包含主题类型为别墅、酒店式公寓、青年旅舍、客栈、民宿
	 */
	private static final Set<String> DEFAULT_BNB_THEME_NUMBER_SET = ImmutableSet.of("13", "14", "17", "18", "19");

	@Override
	public String getAttrName() {
		return INLAND_COMMONTAG.getName();
	}

	@Override
	public void handle(String value, BiConsumer<String, String> consumer) {
		List<String> themeNumberList = PIPE_SPLITTER.splitToList(value);
		consumer.accept(INLAND_COMMONTAG.getName(), value);
		if (themeNumberList.stream().anyMatch(getBnbThemeNumberSet()::contains)) {
			consumer.accept(BNB_FLAG, "1");
		}
	}

	/**
	 * 获取属于民宿大类的主题类型编号集合
	 */
	private Set<String> getBnbThemeNumberSet() {
		return DEFAULT_BNB_THEME_NUMBER_SET;
	}
}
