package com.qunar.search.common.dc.attr.impl;

import com.qunar.search.common.dc.attr.AttrHandler;
import org.apache.commons.lang.StringUtils;

import java.util.function.BiConsumer;

public class VideosAttrHandler implements AttrHandler {
	@Override
	public String getAttrName() {
		return "videos";
	}

	@Override
	public void handle(String value, BiConsumer<String, String> putMethod) {
		if (StringUtils.contains(value, "\"version\":\"0\"")) {
			putMethod.accept("hasVideo", "1");
		}
	}
}
