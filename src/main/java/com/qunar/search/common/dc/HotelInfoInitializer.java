package com.qunar.search.common.dc;

import com.google.common.util.concurrent.RateLimiter;
import com.qunar.hotel.open.pojo.SyncHotel;
import com.qunar.hotel.open.service.info.SyncService;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.redis.storage.Sedis3;
import com.qunar.search.common.bean.HotelInfo;
import com.qunar.search.common.constants.RedisConstant;
import com.qunar.search.common.dc.attr.HotelAttrsConfig;
import com.qunar.search.common.redis.RetryableRedis;
import com.qunar.search.common.util.HotelInfoForRedisUtils;
import com.qunar.search.common.util.RedisUtils;
import com.qunar.search.common.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.spring.QMapConfig;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 酒店基础数据初始化器。从redis加载数据到内存。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=653597545
 */
@Slf4j
public class HotelInfoInitializer {

    private static final String REDIS_RETRY_METRIC_NAME = "hotelInfoRedisHasError";

    /**
     * 启动时，同步酒店基础数据的线程个数
     */
    @QMapConfig(value = "hotel_info_sync.properties", key = "init.threadCount", defaultValue = "12")
    private volatile int threadCount;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.hScanSize", defaultValue = "10")
    private volatile int hScanSize;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.hotelCountThreshold", defaultValue = "1300000")
    private volatile int hotelCountThreshold;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.enableIncrement", defaultValue = "true")
    private volatile boolean enableIncrement;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.needRemoveUnusedData", defaultValue = "false")
    private volatile boolean needRemoveUnusedData;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.incrementSyncCountLimit", defaultValue = "20000")
    private volatile int incrementSyncCountLimit;

    /**
     * 访问redis异常时的最小重试间隔，单位是ms
     */
    @QMapConfig(value = "hotel_info_sync.properties", key = "init.redisRetryWaitTime", defaultValue = "1500")
    private static volatile int redisRetryWaitTime;

    /**
     * 访问redis异常时的最大重试次数
     */
    @QMapConfig(value = "hotel_info_sync.properties", key = "init.redisRetryCount", defaultValue = "4")
    private static volatile int redisRetryCount;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.enableRedisRateLimit", defaultValue = "true")
    private volatile boolean enableRedisRateLimit;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.select.version", defaultValue = "")
    private volatile String selectVersion;

    /**
     * 访问redis的限流器
     */
    private static final RateLimiter redisRateLimiter = RateLimiter.create(200);

    @Resource(name = "compressHotelInfoSedis")
    private Sedis3 compressHotelInfoSedis;

    @Resource(name = "infoSyncService")
    private SyncService infoSyncService;

    @QMapConfig(value = "hotel_info_sync.properties", key = "init.redis.qps.limit", defaultValue = "200")
    public void onChange(double qps) {
        redisRateLimiter.setRate(qps);
    }

    public void sync(Set<String> cityCodeSet, boolean isInit) {
        final long startTime = System.currentTimeMillis();

        ThreadPoolExecutor executor = ThreadPoolUtils.createThreadPool(threadCount, "hotelInfoInit");

        // 同步过程中是否发生错误
        AtomicBoolean syncError = new AtomicBoolean(false);
        AtomicInteger successCount = new AtomicInteger(0);

        // 访问redis加上重试
        RetryableRedis retryableRedis = RedisUtils.buildRetryableRedisClient(compressHotelInfoSedis, redisRetryWaitTime, redisRetryCount, REDIS_RETRY_METRIC_NAME);

        String version;
        if (StringUtils.isNotBlank(selectVersion)){
            version = selectVersion;
        } else {
            version = compressHotelInfoSedis.lindex(RedisConstant.COMPRESS_VERSION_LIST_KEY, 0);
        }
        if (version == null) {
            throw new IllegalStateException("酒店基础数据压缩版本不存在");
        }
        try {
            for (String cityCode : cityCodeSet) {
                executor.execute(() -> syncByHScan(cityCode, syncError, successCount, retryableRedis, version));
            }
            executor.shutdown();

            long start = System.currentTimeMillis();
            boolean isTerminated = executor.awaitTermination(10, TimeUnit.MINUTES);
            log.info("hotelInfoSync 等待所有任务执行完，耗时:{}ms, isTerminated:{}", System.currentTimeMillis() - start, isTerminated);
        } catch (Exception e) {
            QMonitor.recordOne("initHotelInfoFromRedisError");
            log.error("hotelInfoSync 从redis加载酒店基础数据异常", e);
            syncError.set(true);
        } finally {
            log.info("hotelInfoSync 完成酒店基础数据从redis同步，城市数量：{}, 酒店数量:{}, 耗时:{}ms", cityCodeSet.size(), successCount.get(), System.currentTimeMillis() - startTime);
        }

        // 如果在同步过程中发生错误，则抛出异常，阻止服务启动。
        if (syncError.get()) {
            throw new IllegalStateException("hotelInfoSync 酒店基础数据同步过程中发生错误");
        }

        if (isInit && successCount.get() < hotelCountThreshold) {
            log.error("hotelInfoSync 启动时同步到的酒店数量是:{}, 小于阈值:{}, 检验不通过！", successCount.get(), hotelCountThreshold);
            throw new IllegalStateException("hotelInfoSync 同步到的酒店数量低于阈值，酒店基础数据初始化失败");
        }

        if (enableIncrement) {
            ThreadPoolExecutor incSyncThreadPool = ThreadPoolUtils.createThreadPool(1, "hotelInfoIncSync");

            // 增量同步做数据补偿，放到单独的线程异步执行。
            incSyncThreadPool.execute(() -> incrementSync(Long.parseLong(version), incrementSyncCountLimit));

            incSyncThreadPool.shutdown();
        }
    }

    private void syncByHScan(String cityCode, AtomicBoolean syncError, AtomicInteger successCount, RetryableRedis retryableRedis, String version) {
        final long start = System.currentTimeMillis();
        String key = StringUtils.join(RedisConstant.COMPRESS_PREFIX, cityCode, version);
        int count = 0;
        try {
            if (!retryableRedis.exists(key.getBytes())) {
                return;
            }
            Long beforeSize = retryableRedis.hlen(key.getBytes());
            ScanParams params = new ScanParams().count(hScanSize);
            String cursor = "0";
            do {
                if (enableRedisRateLimit) {
                    // 访问redis限速
                    redisRateLimiter.acquire();
                }
                ScanResult<Map.Entry<byte[], byte[]>> scanResult = retryableRedis.hscan(key.getBytes(), cursor.getBytes(), params);
                for (Map.Entry<byte[], byte[]> entry : scanResult.getResult()) {

                    List<HotelInfo> hotelInfos = HotelInfoForRedisUtils.dataDecompress(entry.getValue());
                    if (CollectionUtils.isEmpty(hotelInfos)) {
                        log.warn("hotelInfoSync dataDecompressAndReplacePropsFields 返回null");
                        continue;
                    }
                    count = count + hotelInfos.size();
                    if (needRemoveUnusedData) {
                        hotelInfos.forEach(this::removeUnusedData);
                    }
                    // 把存在大量重复值的字段放到字符串常量池，从而减少对内存的占用
                    hotelInfos.forEach(this::internAttrs);
                    hotelInfos.forEach(hotelInfo -> HotelInfoHolder.updateWithLock(hotelInfo.getHotelSEQ(), hotelInfo));

                }
                cursor = scanResult.getStringCursor();

            } while (!StringUtils.equals(cursor, "0"));
            Long afterSize = retryableRedis.hlen(key.getBytes());
            //防止数据同步过程中，压缩数据被修改/删除
            if ((beforeSize == null || afterSize == null) || !beforeSize.equals(afterSize)) {
                throw new IllegalStateException("hotelInfoSync 同步酒店数据后，redis中该key的size发生变化，请检查！");
            }
        } catch (Throwable e) {
            QMonitor.recordOne("initHotelInfoSyncByHScanError");
            log.error("hotelInfoSync 从redis同步酒店数据异常。cityCode:{}", cityCode, e);
            syncError.set(true);
        } finally {
            successCount.getAndAdd(count);
            log.info("hotelInfoSync {} 酒店基础数据从redis同步完成，数量:{}, 耗时{}ms", cityCode, count, System.currentTimeMillis() - start);
        }
    }


    /**
     * 移除一些不需要的一些字段，减少对内存的占用。
     *
     * @param hotelInfo hotelInfo
     */
    private void removeUnusedData(HotelInfo hotelInfo) {
        if (hotelInfo == null) {
            return;
        }

        hotelInfo.setSetProps(Collections.emptyMap());
        hotelInfo.setThemes(null);
        hotelInfo.setHotelTagSet(null);
        hotelInfo.setCommentIds(null);

        final Iterator<Map.Entry<String, String>> iterator = hotelInfo.getProps().entrySet().iterator();
        Set<String> needAttrs = HotelAttrsConfig.needAttrs();

        while (iterator.hasNext()) {
            final Map.Entry<String, String> entry = iterator.next();
            if (!needAttrs.contains(entry.getKey())) {
                iterator.remove();
            }
        }
    }

    private void internAttrs(HotelInfo hotelInfo) {
        if (hotelInfo == null) {
            return;
        }

        final Iterator<Map.Entry<String, String>> iterator = hotelInfo.getProps().entrySet().iterator();
        Set<String> notNeedInternAttrs = HotelAttrsConfig.notNeedInternAttrs();

        while (iterator.hasNext()) {
            final Map.Entry<String, String> entry = iterator.next();
            if (!notNeedInternAttrs.contains(entry.getKey())) {
                // 把存在大量重复值的字段放到字符串常量池，从而减少对内存的占用
                entry.setValue(entry.getValue().intern());
            }
        }
    }

    private void incrementSync(long version, int countLimit) {
        log.info("hotelInfoSync 启动时增量同步酒店基础数据，version:{}, countLimit:{}", version, countLimit);
        final long start = System.currentTimeMillis();
        int totalCount = 0;
        long currentVersion = version;
        long maxVersion;

        try {
            do {
                long t = System.currentTimeMillis();
                List<SyncHotel> syncHotelList = infoSyncService.syncHotelByVersion(currentVersion);
                if (CollectionUtils.isEmpty(syncHotelList)) {
                    log.info("hotelInfoSync 从qhotel同步到的酒店列表是空的。version:{}", currentVersion);
                    break;
                }

                log.info("hotelInfoSync version:{}, 条数:{}, 耗时:{}ms", currentVersion, syncHotelList.size(), System.currentTimeMillis() - t);

                totalCount += syncHotelList.size();

                for (SyncHotel syncHotel : syncHotelList) {
                   HotelInfoHolder.updateWithLock(syncHotel);
                }

                maxVersion = syncHotelList.get(syncHotelList.size() - 1).getUpdated();

                if (maxVersion == currentVersion) {
                    log.info("hotelInfoSync 前后2次版本相同，说明已同步完所有数据，退出循环。version:{}", maxVersion);
                    break;
                }

                currentVersion = maxVersion;
            } while (totalCount <= countLimit);

            //说明压缩任务长时间没有执行，这段时间的变更超过了incrementSyncCountLimit，应停止启动,因为内存中加载不全数据
            if (totalCount > countLimit) {
                throw new IllegalStateException("从qhotel增量同步到的酒店数量超过限制" + countLimit + "，请检查压缩任务是否正常执行");
            }
        } catch (Throwable e) {
            QMonitor.recordOne("initHotelInfoIncSyncError");
            log.error("hotelInfoSync 启动时增量同步基础数据异常", e);
        }

        log.info("hotelInfoSync 启动时增量同步酒店基础数据，共{}条酒店数据，耗时: {} ms", totalCount, System.currentTimeMillis() - start);
    }
}
