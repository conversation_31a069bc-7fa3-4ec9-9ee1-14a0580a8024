package com.qunar.search.common.dc.attr.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.qunar.search.common.bean.HotelThemeFeature;
import com.qunar.search.common.dc.attr.AttrHandler;
import com.qunar.search.common.dc.attr.HandleException;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.io.IOException;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Stream;

import static com.qunar.search.common.constants.CommonConstants.PIPELINE_JOINER;

@Slf4j
public class HotelFeaturesAttrHandler implements AttrHandler {
	@Override
	public String getAttrName() {
		return "hotelFeatures";
	}

	@Override
	public void handle(String value, BiConsumer<String, String> consumer) throws HandleException {
		try {
			TypeReference<List<HotelThemeFeature>> type = new TypeReference<List<HotelThemeFeature>>() {
			};
			List<HotelThemeFeature> list = JsonUtils.getObjectMapperInstance().readValue(value, type);
			Map<String, Integer> hotelThemeMap = Maps.newHashMap();
			for (HotelThemeFeature hotelThemeFeature : list) {
				hotelThemeMap.put(hotelThemeFeature.getId(), hotelThemeFeature.getDisplayInfo().getOrder());
			}
			if (MapUtils.isEmpty(hotelThemeMap)) {
				return;
			}
			Map<String, Integer> sortedMap = sortByValue(hotelThemeMap);
			consumer.accept("themeCharacter", PIPELINE_JOINER.join(sortedMap.keySet()));
		} catch (IOException e) {
			throw new HandleException(e);
		}
	}

	/**
	 * Map按照value逆序排列
	 * @param map
	 * @param <K>
	 * @param <V>
	 * @return
	 */
	public <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
		Map<K, V> result = new LinkedHashMap<>();
		Stream<Map.Entry<K, V>> st = map.entrySet().stream();
		Comparator<Map.Entry<K, V>> comparator = (h1, h2) -> h2.getValue().compareTo(h1.getValue());
		st.sorted(comparator).forEach(e -> result.put(e.getKey(), e.getValue()));
		return result;
	}
}
