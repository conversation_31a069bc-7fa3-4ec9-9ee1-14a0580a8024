package com.qunar.search.common.dc.attr.impl;

import com.google.common.base.Splitter;
import com.qunar.search.common.dc.attr.HandleException;
import com.qunar.search.common.dc.attr.SetAttrHandler;
import org.apache.commons.lang.StringUtils;

import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

public class DefaultSetAttrHandler implements SetAttrHandler {
    private final Splitter splitter;

    private final String attrName;
    private final String propName;
    private final boolean intern;

    public DefaultSetAttrHandler(String attrName, String separator) {
        this(attrName, separator, attrName);
    }

    public DefaultSetAttrHandler(String attrName, String separator, String propName) {
        this.attrName = attrName;
        this.splitter = Splitter.on(separator).omitEmptyStrings().trimResults();
        this.propName = propName;
        this.intern = true;
    }

    public DefaultSetAttrHandler(Splitter splitter, String attrName, String propName, boolean intern) {
        this.splitter = splitter;
        this.attrName = attrName;
        this.propName = propName;
        this.intern = intern;
    }

    @Override
    public String getAttrName() {
        return attrName;
    }

    @Override
    public void handle(String value, BiConsumer<String, Set<String>> putMethod) throws HandleException {
        if (StringUtils.isBlank(value) || putMethod == null) {
            return;
        }

        Iterable<String> iterable = splitter.split(value);
        putMethod.accept(propName, StreamSupport.stream(iterable.spliterator(), false).filter(StringUtils::isNotBlank).map(s -> {
            if (intern) {
                return s.intern();
            }
            return s;
        }).collect(Collectors.toSet()));
    }
}
