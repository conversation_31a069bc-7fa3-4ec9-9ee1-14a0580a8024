package com.qunar.search.common.dc;

import com.google.common.collect.Lists;
import com.qunar.hotel.open.pojo.SyncHotel;
import com.qunar.hotel.open.service.info.SyncService;
import com.qunar.hotel.qmonitor.QMonitor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.OffsetResetType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.List;

/**
 * 接收rank自己发送的强制更新某些酒店基础数据的消息
 */
public class HotelInfoForcedUpdateMsgConsumer {

    private static final Logger hotelInfoLog = LoggerFactory.getLogger("hotelInfoLog");

    @Resource(name = "infoSyncService")
    private SyncService infoSyncService;

    @QmqConsumer(prefix = "hotel.rank.hotelInfo.forcedUpdate", isBroadcast = true, offsetResetType = OffsetResetType.LATEST)
    public void onMessage(Message message) {
        long start = System.currentTimeMillis();
        try {
            String seqsStr = message.getStringProperty("seqs");

            hotelInfoLog.info("强制更新。msgId:{}, seqs:{}", message.getMessageId(), seqsStr);

            String[] seqs = StringUtils.split(seqsStr, ",");

            List<SyncHotel> syncHotelList = infoSyncService.getBySeqs(Lists.newArrayList(seqs));

            if (CollectionUtils.isEmpty(syncHotelList)) {
                return;
            }

            for (SyncHotel syncHotel : syncHotelList) {
                hotelInfoLog.info("强制更新。msgId:{}, seq:{}, 状态:{}, updateTime:{}", message.getMessageId(), syncHotel.getHotelSeq(), syncHotel.getOperatingStatusName(), syncHotel.getUpdated());
                HotelInfoHolder.updateWithLock(syncHotel);
            }
        } catch (Throwable e) {
            QMonitor.recordOne("handleHotelInfoForcedUpdateMsgError");
            hotelInfoLog.error("处理强制更新酒店基础数据消息出错。msg:{}", message, e);
        } finally {
            QMonitor.recordQuantile("handleHotelInfoForcedUpdateMsg", System.currentTimeMillis() - start);
        }
    }

}
