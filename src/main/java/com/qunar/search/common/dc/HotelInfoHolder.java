package com.qunar.search.common.dc;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunar.hotel.open.pojo.SyncHotel;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.HotelInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.metrics.Metrics;

import java.util.*;

public class HotelInfoHolder {
	private static final Map<String, HotelInfo> holder = Maps.newConcurrentMap();

	private static final Map<String, Object> hotelLockMap = Maps.newConcurrentMap();

	private static final Logger hotelInfoLog = LoggerFactory.getLogger("hotelInfoLog");

	/**
	 * 城市下酒店列表
	 */
	private static final Map<String, Set<HotelInfo>> cityHolder = Maps.newConcurrentMap();

	static {
		Metrics.gauge("hotelInfoCache").tag("name", "hotel").call(holder::size);
		Metrics.gauge("hotelInfoCache").tag("name", "city").call(cityHolder::size);
	}

	private HotelInfoHolder() {
	}

	public static Map<String, HotelInfo> getHolder() {
		return Collections.unmodifiableMap(holder);
	}

	public static int size() {
		return holder.size();
	}

	private static Object getLock(String hotelSeq) {
		return hotelLockMap.computeIfAbsent(hotelSeq, k -> new Object());
	}

	public static void updateWithLock(String hotelSeq, HotelInfo hotelInfo) {
		if (StringUtils.isEmpty(hotelSeq)) {
			return;
		}
		Object lock = HotelInfoHolder.getLock(hotelSeq);
		synchronized (lock) {
			update(hotelSeq, hotelInfo);
		}
	}

	protected static void update(String hotelSeq, HotelInfo hotelInfo) {
		if (hotelInfo == null) {
			HotelInfoHolder.remove(hotelSeq);
		} else {
			HotelInfoHolder.addHotel(hotelInfo);
		}
	}

	public static void updateWithLock(SyncHotel syncHotel) {
		if (syncHotel == null || !syncHotel.getIsInland()) {
			return;
		}

		String hotelSeq = syncHotel.getHotelSeq();
		Object lock = HotelInfoHolder.getLock(hotelSeq);

		synchronized (lock) {
			HotelInfo currentHotelInfo = HotelInfoHolder.getHotel(hotelSeq);
			if (currentHotelInfo != null && syncHotel.getUpdated() <= currentHotelInfo.getQhotelUpdateTime()) {
				QMonitor.recordOne("syncHotelDataIsOld");
				hotelInfoLog.warn("同步到的酒店数据不是最新的。seq:{}, updateTime:{}, memoryUpdateTime:{}", syncHotel.getHotelSeq(), syncHotel.getUpdated(), currentHotelInfo.getQhotelUpdateTime());
				return;
			}

			HotelInfo hotelInfo = HotelInfoUtils.covertSyncHotelToHotelInfo(syncHotel);

			HotelInfoHolder.update(hotelSeq, hotelInfo);
		}
	}

	/**
	 * 注意这个方法不是线程安全的。
	 * @param hotelInfo
	 * @return
	 */
	private static HotelInfo addHotel(final HotelInfo hotelInfo) {
		String hotelSeq = hotelInfo.getHotelSEQ();
		holder.put(hotelSeq, hotelInfo);

		String[] citys = hotelInfo.getCityTag();
		if (citys != null) {
			for (String city : citys) {
				Set<HotelInfo> hotelSet = cityHolder.computeIfAbsent(city, k -> Sets.newConcurrentHashSet());
				// 注意，这里需要先删除再添加，才能用新数据替换掉老数据。
				hotelSet.remove(hotelInfo);
				hotelSet.add(hotelInfo);
			}
		}

		return hotelInfo;
	}

	private static HotelInfo remove(String hotelSeq) {
		HotelInfo hotelInfo = holder.remove(hotelSeq);
		if (hotelInfo != null) {
			String[] citys = hotelInfo.getCityTag();
			if (citys != null) {
				for (String city : citys) {
					Set<HotelInfo> hotels = cityHolder.get(city);
					if (hotels != null) {
						hotels.remove(hotelInfo);
						if (CollectionUtils.isEmpty(hotels)) {
							cityHolder.remove(city);
						} else {
							cityHolder.put(city, hotels);
						}
					}
				}
			}
		}
		return hotelInfo;
	}

	public static HotelInfo getHotel(String hotelSeq) {
		return holder.get(hotelSeq);
	}

	public static Collection<HotelInfo> getHotelByCity(String city) {
		Set<HotelInfo> hotelInfoSet = cityHolder.get(city);
		if (hotelInfoSet == null) {
			return Collections.emptySet();
		}
		return Collections.unmodifiableSet(hotelInfoSet);
	}

	public static int hotelCount(String city) {
		Set<HotelInfo> hotelInfoSet = cityHolder.get(city);
		if (CollectionUtils.isEmpty(hotelInfoSet)) {
			return 0;
		} else {
			return hotelInfoSet.size();
		}
	}

	public static Set<String> getCitySet() {
		return Collections.unmodifiableSet(cityHolder.keySet());
	}

	public static Map<String, Set<HotelInfo>> getCityHolder() {
		return Collections.unmodifiableMap(cityHolder);
	}

	public static int citySize() {
		return cityHolder.size();
	}

}