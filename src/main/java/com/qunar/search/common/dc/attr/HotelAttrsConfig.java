package com.qunar.search.common.dc.attr;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunar.search.common.conf.annotation.QTomlConfig;
import com.qunar.search.common.constants.CommonConstants;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.tomlj.TomlArray;
import org.tomlj.TomlParseResult;
import org.tomlj.TomlTable;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
/**
 * 从DC加载的酒店数据
 */

public class HotelAttrsConfig {
	private static volatile Set<String> notNeedInternAttrs = Sets.newHashSet();
	private static volatile Set<String> needProps = Sets.newHashSet();
	private static volatile Map<String, String> attrs = Maps.newHashMap();
	private static Set<AddToHotelFeatureConfig> addToHotelFeatureConfigSet = Sets.newHashSet();

	@QTomlConfig(value = "hotel_attr.toml")
	public void load(TomlParseResult result) {
		Map<String, String> attrs = Maps.newHashMap();
		Set<String> needProps = ImmutableSet.of();
		Set<String> notNeedInternAttrs = ImmutableSet.of();
		Set<AddToHotelFeatureConfig> addToHotelFeatureConfigSet = Sets.newHashSet();

		TomlTable attrMap = result.getTable("AttrMap");
		if (attrMap != null) {
			for (String key : attrMap.dottedKeySet()) {
				attrs.put(key, attrMap.getString(key));
			}
		}

		TomlArray needProperty = result.getArray("needProperty");
		if (needProperty != null) {
			needProps = needProperty.toList().stream().map(this::objToStr).collect(Collectors.toSet());
		}

		TomlArray notNeedIntern = result.getArray("notNeedIntern");
		if (notNeedIntern != null) {
			notNeedInternAttrs = notNeedIntern.toList().stream().map(this::objToStr).collect(Collectors.toSet());
		}

		// 将attr中酒店特征相关内容添加至 hotelFeatureTags
		// 配置示例："hasElevator" = "1|电梯",相应字段含义"attr字段名称" = "期望value值|酒店特征名称"。表示指定attr字段取值符合预期，则将对应名称添加至hotelFeatureTags属性
		TomlTable addToHotelFeatureConfig = result.getTable("addToHotelFeatureConfig");
		if (addToHotelFeatureConfig != null) {
			for (String key : addToHotelFeatureConfig.dottedKeySet()) {
				String valueStr = addToHotelFeatureConfig.getString(key);
				if (StringUtils.isEmpty(valueStr)) {
					continue;
				}
				List<String> values = CommonConstants.PIPE_SPLITTER.splitToList(valueStr);
				if (values.size() != 2) {
					continue;
				}
				addToHotelFeatureConfigSet.add(AddToHotelFeatureConfig.builder().attrKey(key).attrValue(values.get(0)).featureName(values.get(1)).build());
			}
		}

		setAttrs(ImmutableMap.copyOf(attrs));
		setNeedProps(ImmutableSet.copyOf(needProps));
		setNotNeedInternAttrs(ImmutableSet.copyOf(notNeedInternAttrs));
		setAddToHotelFeatureConfigSet(addToHotelFeatureConfigSet);
	}

	@Data
	@Builder
	public static class AddToHotelFeatureConfig {
		private String attrKey;
		private String attrValue;
		private String featureName;
	}

	public static String getNameByCode(String code) {
		return attrs.get(code);
	}

	private static void setAttrs(Map<String, String> attrs) {
		HotelAttrsConfig.attrs = attrs;
	}

	private static void setNotNeedInternAttrs(Set<String> notNeedInternAttrs) {
		HotelAttrsConfig.notNeedInternAttrs = notNeedInternAttrs;
	}

	private static void setNeedProps(Set<String> needProps) {
		HotelAttrsConfig.needProps = needProps;
	}

	private String objToStr(Object object) {
		return object != null ? (String) object : "";
	}

	public static Set<String> needAttrs() {
		return needProps;
	}

	public static Set<String> notNeedInternAttrs() {
		return notNeedInternAttrs;
	}

	public static Set<AddToHotelFeatureConfig> getAddToHotelFeatureConfigSet() {
		return addToHotelFeatureConfigSet;
	}

	public static void setAddToHotelFeatureConfigSet(Set<AddToHotelFeatureConfig> addToHotelFeatureConfigSet) {
		HotelAttrsConfig.addToHotelFeatureConfigSet = addToHotelFeatureConfigSet;
	}
}