package com.qunar.search.common.dc.attr;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public final class HotelAttrHandler implements InitializingBean {
    public static final Map<String, AttrHandler> handlerMap = Maps.newHashMap();
    private static final Map<String, SetAttrHandler> setHandlerMap = Maps.newHashMap();

    @Autowired(required = false)
    private List<AttrHandler> handlers;
    @Autowired(required = false)
    private List<SetAttrHandler> setAttrHandlers;
    private static CompositeAttrHandler compositeAttrHandler;

    private HotelAttrHandler() {
    }

    public static boolean hasAttrHandler(String key) {
        return handlerMap.containsKey(key);
    }

    public static AttrHandler getAttrHandler(String key) {
        return handlerMap.get(key);
    }

    public static boolean hasSetAttrHandler(String key) {
        return setHandlerMap.containsKey(key);
    }

    public static SetAttrHandler getSetAttrHandler(String key) {
        return setHandlerMap.get(key);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtils.isNotEmpty(handlers)) {
            for (AttrHandler handler : handlers) {
                if (StringUtils.isNotBlank(handler.getAttrName())) {
                    Preconditions.checkState(!handlerMap.containsKey(handler.getAttrName()), "%s的处理器已被注册",
                            handler.getAttrName());
                    handlerMap.put(handler.getAttrName(), handler);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(setAttrHandlers)) {
            for (SetAttrHandler handler : setAttrHandlers) {
                if (StringUtils.isBlank(handler.getAttrName())) {
                    continue;
                }
                Preconditions.checkState(!setHandlerMap.containsKey(handler.getAttrName()), "%s的处理器已被注册",
                        handler.getAttrName());

                setHandlerMap.put(handler.getAttrName(), handler);
            }
        }
    }

    @Autowired(required = false)
    public void setCompositeAttrHandler(CompositeAttrHandler compositeAttrHandler) {
        HotelAttrHandler.compositeAttrHandler = compositeAttrHandler;
    }

    public static CompositeAttrHandler getCompositeAttrHandler() {
        return compositeAttrHandler;
    }
}