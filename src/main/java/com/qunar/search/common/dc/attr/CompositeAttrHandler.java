package com.qunar.search.common.dc.attr;

import com.qunar.search.common.bean.HotelInfo;
import com.qunar.search.common.enums.HotelTypeEnum;

import java.util.*;

import static com.qunar.search.common.update.HotelPropertyDict.HotelAttrKey.COMMENTSTAG;
import static com.qunar.search.common.util.CommonTools.*;

/**
 * 一个或多个Attr转换成HotelInfo属性的处理情况
 */
public class CompositeAttrHandler {
    public void handle(Map<String, String> infoHotelAttr, HotelInfo hotelInfo) {
        hotelInfo.setThemes(getThemeList(infoHotelAttr));
        hotelInfo.setCommentIds(getCommentLabels(infoHotelAttr.get(COMMENTSTAG.getName())));
        hotelInfo.setHotelTagSet(getHotelTagSet(infoHotelAttr.get("tag")));
        hotelInfo.setTradingAreaSet(getTradingArea(infoHotelAttr.get("tradingArea")));

        Set<String> hotelFeatureTags = hotelInfo.getSetProps().get("hotelFeatureTags");

        hotelInfo.setScreenHotelType(getScreenHotelTypeList( hotelFeatureTags ));

    }


    public List<String> getScreenHotelTypeList (Set<String> themes){

        List<String> lists = new ArrayList<>();

        if( !Objects.isNull(themes) && !themes.isEmpty() ){
            for (String theme: themes) {
                 if( HotelTypeEnum.chHotelTypeMAP.containsKey(theme) ){
                     lists.add(theme);
                 }
            }
        }
        return lists;
    }
}