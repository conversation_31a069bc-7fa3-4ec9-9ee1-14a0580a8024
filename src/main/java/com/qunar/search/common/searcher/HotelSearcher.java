package com.qunar.search.common.searcher;

import com.qunar.search.common.base.QueryInfo;
import com.qunar.search.common.bean.HotelItem;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * searcher的描述
 */
public interface HotelSearcher {

    String getQuery();

    String getKey();

    LinkedHashMap<QueryInfo, List<HotelItem>> getGroupedResultMap();

	/**
	 * 只有关键字召回有赋值，而且也没有使用
	 */
	@Deprecated
    boolean isLocation();

    void doSearch() throws Exception;

    String getCityUrl();

    List<String> getCityList();
}