package com.qunar.search.common.server;


import com.qunar.search.common.bean.PoolBean;
import com.qunar.search.common.util.Numbers;
import com.qunar.search.common.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.concurrent.ManagedThreadPool;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.*;

/**
 * 一个支持线程池的类.
 */
public class ExecutorManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExecutorManager.class);

    private static LinkedHashMap<String, ThreadPoolExecutor> executors = new LinkedHashMap<String, ThreadPoolExecutor>();

    /**
     * spring 加载线程池
     *
     * @param list
     */
    public void setExecutors(List<PoolBean> list) {
        if (list != null) {
            for (PoolBean poolBean : list) {
                if (poolBean != null) {
                    String name = poolBean.getName();
                    int corePoolSize = poolBean.getCorePoolSize();
                    int maximumPoolSize = poolBean.getMaximumPoolSize();
                    long keepAliveTime = poolBean.getKeepAliveTime();

                    // 以下为可选参数
                    int queueSize = Numbers.toInt(poolBean.getQueueSize() + "", 0);
                    boolean rejectDiscard = Strings.getBoolean(poolBean.isRejectDiscard() + "", false);

                    BlockingQueue<Runnable> queue;
                    if (queueSize == 0)
                        queue = new SynchronousQueue<Runnable>();
                    else
                        queue = new ArrayBlockingQueue<Runnable>(queueSize);

                    ManagedThreadPool pool = new ManagedThreadPool(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, queue, new NamedTreadFactory(name));
                    //任务拒绝做的事情
                    if (rejectDiscard)
                        pool.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());

                    executors.put(name, pool);
                }
            }
        }
    }

    public void destroy() {
        for (ThreadPoolExecutor exec : executors.values()) {
            try {
                exec.shutdown();
            } catch (Exception e) {
                LOGGER.error("ExecutorManager destroy", e);
            }
        }
    }

    public static ThreadPoolExecutor getExecutor(String name) {
        return executors.get(name);
    }
}
