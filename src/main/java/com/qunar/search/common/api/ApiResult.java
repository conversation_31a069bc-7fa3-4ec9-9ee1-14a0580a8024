package com.qunar.search.common.api;

/**目前只在pc业务里面使用, 用来描述给跟前端定义的公用协议.
 * Created by fangxue.zhang on 2016/4/15.
 */
public class ApiResult<T> {
    private boolean ret;
    private T data;
    private String errorMsg;
    private int errorCode;

    public void setRet(boolean ret) {
        this.ret = ret;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public T getData() {
        return data;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public boolean isRet() {
        return ret;
    }
}