package com.qunar.search.common.service;

import com.qunar.search.common.bean.HotelStat;
import com.qunar.search.common.model.memory.DataVersion;
import lombok.Getter;
import qunar.metrics.Metrics;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**酒店的一个离线统计数据的存储服务
 * Created by fangxue.zhang on 2016/9/1.
 */

public class HotelStatDataService {

	/**
	 * 特征版本信息
	 */
	@Getter
	private static DataVersion dataVersion = new DataVersion();

	private static Map<String, HotelStat> hotelStatMap = new ConcurrentHashMap<>();

	static {
		Metrics.gauge("hotelStatMapSize").call(() -> hotelStatMap.size());
	}

	public static Map<String, HotelStat> getHotelStatMap() {
		return hotelStatMap;
	}


	/**
	 * 从内存中获取这个对象, 这个get函数保证数据一定要有.
	 * @param hotelSeq
	 * @return
	 */
	public static HotelStat getWritableHotelStat(String hotelSeq) {
		return getHotelStatMap().computeIfAbsent(hotelSeq, HotelStat::new);
	}

	/**
	 * 只会读取数据, 不会set字段, 所以不需要在map里面没有的时候, new一个放进去.
	 * @return
	 */
	public static HotelStat getOnlyReadHotelStat(String hotelSeq) {
		return getHotelStatMap().get(hotelSeq);
	}
}
