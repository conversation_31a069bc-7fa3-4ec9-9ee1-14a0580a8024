package com.qunar.search.common.service;

import com.google.common.hash.Hashing;
import com.qunar.redis.storage.Sedis3;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * @author: shuailei.lei
 * @create: 2025-03-11
 * @description: render报价缓存redis集群服务
 **/
@Component
public class RenderCacheRedisService {

    @Resource(name = "rankRenderPriceInfoNode01")
    private Sedis3 rankRenderPriceInfoNode01;

    @Resource(name = "rankRenderPriceInfoNode02")
    private Sedis3 rankRenderPriceInfoNode02;

    @Resource(name = "rankRenderPriceInfoNode03")
    private Sedis3 rankRenderPriceInfoNode03;

    @Resource(name = "rankRenderPriceInfoNode04")
    private Sedis3 rankRenderPriceInfoNode04;

    @Resource(name = "rankRenderPriceInfoNode05")
    private Sedis3 rankRenderPriceInfoNode05;

    @Resource(name = "rankRenderPriceInfoNode06")
    private Sedis3 rankRenderPriceInfoNode06;

    @Resource(name = "rankRenderPriceInfoNode07")
    private Sedis3 rankRenderPriceInfoNode07;

    @Resource(name = "rankRenderPriceInfoNode08")
    private Sedis3 rankRenderPriceInfoNode08;

    @Resource(name = "rankRenderPriceInfoNode09")
    private Sedis3 rankRenderPriceInfoNode09;

    @Resource(name = "rankRenderPriceInfoNode10")
    private Sedis3 rankRenderPriceInfoNode10;

    /**
     * 获取指定redis实例
     * @param shardNumber 分片序号
     */
    public Sedis3 getRenderInfoRedisByShard(int shardNumber) {
        switch (shardNumber) {
            case 0: return rankRenderPriceInfoNode01;
            case 1: return rankRenderPriceInfoNode02;
            case 2: return rankRenderPriceInfoNode03;
            case 3: return rankRenderPriceInfoNode04;
            case 4: return rankRenderPriceInfoNode05;
            case 5: return rankRenderPriceInfoNode06;
            case 6: return rankRenderPriceInfoNode07;
            case 7: return rankRenderPriceInfoNode08;
            case 8: return rankRenderPriceInfoNode09;
            case 9: return rankRenderPriceInfoNode10;
            default: throw new IllegalArgumentException("get render info sedis by shard, invalid shard index: " + shardNumber);
        }
    }

    /**
     * 获取指定key的redis分片序号
     * @param key 键
     * @return 分片序号
     */
    public int getRedisShardNumber(String key) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("get render info sedis by shard, key cannot be null or blank");
        }
        int hash = Hashing.murmur3_32().hashString(key, StandardCharsets.UTF_8).asInt();
        return Math.abs(hash) % 10;
    }
}
