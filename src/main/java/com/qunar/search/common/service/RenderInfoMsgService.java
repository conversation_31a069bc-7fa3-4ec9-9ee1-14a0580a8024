package com.qunar.search.common.service;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Lists;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.bean.DiscountsDetail;
import com.qunar.search.common.bean.HotelItem;
import com.qunar.search.common.bean.RenderCoreInfo;
import com.qunar.search.common.bean.RenderInfoMsg;
import com.qunar.search.common.enums.IdentityOfNewOrOldEnum;
import com.qunar.search.common.enums.RenderInfoSourceEnum;
import com.qunar.search.common.price.render.RenderPriceItem;
import com.qunar.search.common.price.render.RenderPriceResult;
import com.qunar.search.common.price.render.RenderPriceService;
import com.qunar.search.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;

import java.util.*;

import static com.qunar.search.common.conf.RenderInfoCacheConfig.*;
import static com.qunar.search.common.constants.RenderCacheConstants.MULTI_POINT_HOTEL_ACTIVITY_ID;

/**
 * 提供构造QMQ消息，和解析QMQ消息方法
 */
@Slf4j
public class RenderInfoMsgService {

    public static final String RENDER_INFO_MSG_SUBJECT = "exposure.hotel.render.info.subject"; // 曝光酒店render信息subject

    //public static final String TEST_SUBJECT = "exposure.hotel.render.info.subject.simulation";

    public static final String MSG_KEY_DATA = "data";

    private static final String BASE_PRICE_DATA_KEY = "ccpsPreBasePrice";

    private static final JavaType RESPONSE_TYPE = JsonUtils.buildCollectionType(List.class, DiscountsDetail.class);

    /**
     * Render 报价发 QMQ
     *
     * @param fromDate          入店时间
     * @param toDate            离店时间
     * @param identityCodes     用户全身份 json
     * @param couponKey       用户拥有券的key
     * @param renderPriceResult 报价返回结果
     * @param producer          消息 producer
     * @param multiPointOfPrice 多倍积分对应的价格
     * @param filterIdentitySet 需要发QMQ消息的身份列表
     */
    public static void sendRenderInfoMsg(RenderInfoSourceEnum sourceEnum, String fromDate, String toDate,
                                         Set<String> identityCodes, String couponKey,
                                         RenderPriceResult renderPriceResult,
                                         Map<String, HotelItem> hotelMap,
                                         MessageProducer producer,
                                         Set<String> filterIdentitySet,
                                         IdentityOfNewOrOldEnum identityOfNewOrOld,
                                         Integer multiPointOfPrice,
                                         Integer userMultiPoint,
                                         Map<String, Integer> cacheMinPriceMap,
                                         Map<String, Integer> cacheCommissionMap,
                                         Map<String, String> cachePriceTraceIdMap,
                                         Map<String, String> cacheCommissionTraceIdMap,
                                         Map<String, String> beatMap,
                                         Map<String, Boolean> validBeatMap
                                         ) {
        long start = System.currentTimeMillis();
        try {
            // 空报价判断
            if (MapUtils.isEmpty(renderPriceResult.getRenderPriceItemMap())) {
                QMonitor.recordOne("render.price.result.hotels.empty");
                return;
            }

            // 用户身份信息空
            if (CollectionUtils.isEmpty(identityCodes)) {
                QMonitor.recordOne("render.info.msg.user.identity.empty");
                return;
            }

            // 身份判断
            String identity;
            Optional<String> any = filterIdentitySet.stream().filter(identityCodes::contains).findAny();
            if (any.isPresent()) {
                identity = any.get();
            } else {
                QMonitor.recordOne("exposure.render.info.identity.not.match");
                return; // 无指定身份
            }

            // 限流
            if (!RenderPriceService.getRateLimiter().tryAcquire()) {
                QMonitor.recordOne("render.info.send.msg.rate.limit");
                log.warn("render.info.send.msg.rate.limit");
                return;
            }

            // Render 报价发 QMQ
            List<RenderCoreInfo> renderCoreInfos = parseRenderInfo(renderPriceResult.getRenderPriceItemMap(), hotelMap,
                    cacheMinPriceMap, cacheCommissionMap, cachePriceTraceIdMap, cacheCommissionTraceIdMap, beatMap, validBeatMap, sourceEnum);
            if (!CollectionUtils.isEmpty(renderCoreInfos)) {
                Message message = producer.generateMessage(RENDER_INFO_MSG_SUBJECT);
                message.setProperty(MSG_KEY_DATA,
                        JsonUtils.toJson(
                                RenderInfoMsg.builder()
                                        .newOrOldFlag(identityOfNewOrOld.getFlag())
                                        .multiPointOfPrice(multiPointOfPrice)
                                        .sourceCode(sourceEnum.code)
                                        .fromDate(fromDate)
                                        .toDate(toDate)
                                        .identity(identity)
                                        .couponKey(couponKey)
                                        .userMultiPoint(userMultiPoint)
                                        .renderCoreInfos(renderCoreInfos)
                                        .build()
                        )
                );
                producer.sendMessage(message);
                QMonitor.recordOne("render.info.send.msg.success");
            }
        } catch (Exception e) {
            log.error("RenderInfoMsgService sendRenderInfoMsg error:{}", e.getMessage(), e);
            QMonitor.recordOne("render.info.send.msg.failure");
        } finally {
            QMonitor.recordOne("render.info.send.msg.finally", System.currentTimeMillis() - start);
        }
    }

    /**
     * 解析报价信息的 QMQ Msg
     *
     * @param message QMQ Message
     * @return 可能为 null
     */
    public static RenderInfoMsg parseRenderInfoMsg(Message message) {
        return JsonUtils.fromJson(message.getStringProperty(MSG_KEY_DATA), RenderInfoMsg.class);
    }

    /**
     * 构造发送QMQ的报价信息Msg
     *
     * @param renderPriceItemMap 报价返回的酒店报价信息
     */
    public static List<RenderCoreInfo> parseRenderInfo(Map<String, RenderPriceItem> renderPriceItemMap, Map<String, HotelItem> hotelMap,
                                                       Map<String, Integer> cacheMinPriceMap, Map<String, Integer> cacheCommissionMap,
                                                       Map<String, String> cachePriceTraceIdMap, Map<String, String> cacheCommissonTraceIdMap,
                                                       Map<String, String> beatMap, Map<String, Boolean> validBeatMap,
                                                       RenderInfoSourceEnum sourceEnum) {
        List<RenderCoreInfo> renderCoreInfos = Lists.newArrayListWithCapacity(renderPriceItemMap.size());
        renderPriceItemMap.forEach((seq, renderPriceItem) -> {
            Map<String, String> extendsMap = renderPriceItem.getExtendsMap();
            if (MapUtils.isEmpty(extendsMap)) {
                return;
            }
            int renderMinPrice = renderPriceItem.getCustomMinAvailablePrice();
            String bizCouponPrice = extendsMap.get(bizCouponPriceKey);
            String commission = extendsMap.get(commissionKey);
            String linePrice = extendsMap.get(linePriceKey);
            String beat = extendsMap.get(beatKey);

            String newBeat = MapUtils.isNotEmpty(beatMap) ? beatMap.getOrDefault(seq, StringUtils.EMPTY) : StringUtils.EMPTY;
            Boolean isValidBeat = MapUtils.isNotEmpty(validBeatMap) ? validBeatMap.get(seq) : null;

            int basePrice = getAvgBasePrice(extendsMap);

            if (basePrice == 0 || renderMinPrice == 0 || renderMinPrice == Integer.MAX_VALUE
                    || StringUtils.isAnyBlank(bizCouponPrice, commission, linePrice, beat)) {
                return;
            }

            RenderCoreInfo renderCoreInfo;
            if (addRenderDiffInfo && sourceEnum == RenderInfoSourceEnum.HOTEL_SEARCH) {
                HotelItem hotelItem = MapUtils.isNotEmpty(hotelMap) ? hotelMap.get(seq) : null;
                Integer cacheCommission = MapUtils.isNotEmpty(cacheCommissionMap) ? cacheCommissionMap.get(seq) : null;
                int cacheMinPrice = MapUtils.isNotEmpty(cacheMinPriceMap) ? cacheMinPriceMap.getOrDefault(seq, 0) : 0;
                String cachePriceTraceId = MapUtils.isNotEmpty(cachePriceTraceIdMap) ? cachePriceTraceIdMap.getOrDefault(seq, StringUtils.EMPTY) : StringUtils.EMPTY;
                String cacheCommissionTraceId = MapUtils.isNotEmpty(cacheCommissonTraceIdMap) ? cacheCommissonTraceIdMap.getOrDefault(seq, StringUtils.EMPTY) : StringUtils.EMPTY;
                List<DiscountsDetail> discountsDetail = parseDiscountsDetail(extendsMap.get(discountsDetailKey));
                renderCoreInfo = RenderCoreInfo.builder()
                        .seq(seq)
                        .commission(commission)
                        .priceAfterMerchant(bizCouponPrice)
                        .dispOrgPrice(linePrice)
                        .beat(beat)
                        .beatV2(newBeat)
                        .isValidBeat(isValidBeat)
                        .renderMinPrice(renderMinPrice)
                        .basePrice(basePrice)
                        .multiPointHotel(renderPriceItem.getActivityIds() != null && renderPriceItem.getActivityIds().contains(MULTI_POINT_HOTEL_ACTIVITY_ID))
                        .roomId(renderPriceItem.getRoomId())
                        .discountsDetail(discountsDetail)
                        .cacheMinPrice(cacheMinPrice)
                        .cacheCommission(cacheCommission)
                        .cachePriceTraceId(cachePriceTraceId)
                        .cacheCommissionTraceId(cacheCommissionTraceId)
                        .renderCacheLevel(hotelItem != null ? hotelItem.getRenderCacheLevel() : "")
                        .redisHitKey(hotelItem != null ? hotelItem.getRedisHitKey() : "")
                        .redisFirstTryKey(hotelItem != null ? hotelItem.getRedisFirstTryKey() : "")
                        .redisFirstTryLevel(hotelItem != null ? hotelItem.getRedisFirstTryLevel() : "")
                        .build();
            } else {
                renderCoreInfo = RenderCoreInfo.builder()
                        .seq(seq)
                        .commission(commission)
                        .priceAfterMerchant(bizCouponPrice)
                        .dispOrgPrice(linePrice)
                        .beat(beat)
                        .beatV2(newBeat)
                        .isValidBeat(isValidBeat)
                        .renderMinPrice(renderMinPrice)
                        .basePrice(basePrice)
                        .multiPointHotel(renderPriceItem.getActivityIds() != null && renderPriceItem.getActivityIds().contains(MULTI_POINT_HOTEL_ACTIVITY_ID))
                        .build();
            }
            renderCoreInfos.add(renderCoreInfo);
        });

        return renderCoreInfos;
    }

    /**
     * 获取原始底价
     */
    public static int getAvgBasePrice(Map<String, String> extendsMap) {
        if (MapUtils.isEmpty(extendsMap)) {
            return 0;
        }

        double[] basePriceArray = JsonUtils.fromJson(extendsMap.get(BASE_PRICE_DATA_KEY), double[].class);
        if (basePriceArray == null || basePriceArray.length == 0) {
            return 0;
        }

        double sumBasePrice = 0;
        for (double price : basePriceArray) {
            sumBasePrice += price;
        }
        return (int) Math.ceil(sumBasePrice / basePriceArray.length);
    }

    /**
     * 获取优惠明细
     */
    public static List<DiscountsDetail> parseDiscountsDetail(String jsonStr) {
        try {
            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            return JsonUtils.toBean(jsonStr, RESPONSE_TYPE);
        } catch (Exception exception) {
            log.error("解析render报价优惠明细出错");
            QMonitor.recordOne("parseDiscountsDetailError");
        }
        return null;
    }
}
