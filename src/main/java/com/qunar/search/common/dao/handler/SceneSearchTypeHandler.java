package com.qunar.search.common.dao.handler;

import com.qunar.search.common.enums.SceneSearch;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * SceneSearchTypeHandler
 *
 * <AUTHOR>
 * @date 17-1-20.
 */
public class SceneSearchTypeHandler implements TypeHandler<SceneSearch>{

    @Override
    public SceneSearch getResult(CallableStatement callableStatement, int i) throws SQLException {
        return SceneSearch.valueOfByName(callableStatement.getString(i));
    }

    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, SceneSearch sceneSearch, JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, sceneSearch.getName());
    }

    @Override
    public SceneSearch getResult(ResultSet resultSet, String s) throws SQLException {
        return SceneSearch.valueOfByName(resultSet.getString(s));
    }

    @Override
    public SceneSearch getResult(ResultSet resultSet, int i) throws SQLException {
        return SceneSearch.valueOfByName(resultSet.getString(i));
    }
}
