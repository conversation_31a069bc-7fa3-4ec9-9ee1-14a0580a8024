package com.qunar.search.common.dao;

import com.qunar.search.common.bean.HotelOfficialScore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HotelOfficialScoreDao {
	@Select("select max(log_day) from hotel_official_score")
	Integer selectMaxVersion();

	@Select("select hotel_seq as hotelSeq, official_score as officialScore, history_checkin_num as historyCheckinNum from hotel_official_score where log_day = #{version}")
	@ResultType(value = HotelOfficialScore.class)
	List<HotelOfficialScore> selectOfficialScore(@Param("version") int version);
}
