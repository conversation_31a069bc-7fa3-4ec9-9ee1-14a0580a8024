package com.qunar.search.common.dao;

import com.qunar.search.common.bean.SaleRank;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/4
 */
@Repository
public interface HotelPopularityDao {

    /**
     * max version
     *
     * @return
     */
    @Select("select max(version) from international_hotel_popularity_rank")
    Integer selectMaxVersion();


    /**
     * selectInfo
     *
     * @param version
     * @return
     */
    @Select("select hotel_seq as hotelSeq,city_code as cityCode,city_rank as cityRank,hotel_trading_area as area,area_rank as areaRank from international_hotel_popularity_rank where version=#{version}")
    @ResultType(value = SaleRank.class)
    List<SaleRank> selectInfo(@Param("version") int version);
}
