package com.qunar.search.common.dao;

import com.qunar.search.common.profit.entity.HotelServiceQualityDefault;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**加载酒店服务质量在各个城市的默认值.
 * Created by fangxue.zhang on 2016/6/22.
 */

@Repository
public interface HotelServiceQualityDefaultMapper {


    /**
     * 从数据库查询数据.
     * @param dt
     * @return
     */
    List<HotelServiceQualityDefault> getHotelServiceQualityDefaults(@Param("dt") String dt);
}
