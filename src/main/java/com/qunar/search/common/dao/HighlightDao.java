package com.qunar.search.common.dao;

import com.qunar.search.common.bean.Highlight;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HighlightDao {
	@Select("select max(log_day) from high_light limit 1")
	public Integer maxVersion();

	@Select("select hotel_seq as seq,hotel_grade as grade,score_scale as scoreScale,best_tag as tag,full_scale as fullScale,base_word as baseWord,trip_type as tripType from high_light where log_day=#{logDay}")
	@ResultType(value = Highlight.class)
	public List<Highlight> findData(@Param("logDay") Integer maxVersion);
}
