package com.qunar.search.common.dao;

import com.qunar.search.common.bean.UgcInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UgcInfoDao {

    /**
     * 查DB中最大版本。
     *
     * @return 最大更新时间和最大seq拼接的结果
     */
    @Select("select concat(update_time, '|', hotel_seq) as version from hotel_ugc_info order by update_time desc, hotel_seq desc limit 1")
    @ResultType(value = String.class)
    String getMaxVersion();


    /**
     * 增量查询UGC数据。 由于MySQL当中默认的时间类型（datetime和timestamp）的精度是秒，为了避免在同一秒内并发读写导致漏数据的问题，加上了 update_time < now() - interval 2 second，读2秒以前更新的数据
     *
     * @param lastMod 上一次同步到的最大更新时间
     * @param fromSeq 上一次同步到的最大seq
     * @param limit 条数限制
     * @return ugc数据列表
     */
    @Select("select hotel_seq as hotelSeq, comment_score as commentScore, adjust_comment_score as adjustCommentScore, comment_count as commentCount, adjust_comment_count as adjustCommentCount, update_time as updateTime " +
            "FROM hotel_ugc_info where ( (update_time = #{lastMod} and hotel_seq > #{fromSeq}) or update_time > #{lastMod} ) and update_time < now() - interval 2 second ORDER BY update_time, hotel_seq limit #{limit}")
    @ResultType(UgcInfo.class)
    List<UgcInfo> queryUgcInfo(@Param("lastMod") String lastMod, @Param("fromSeq")String fromSeq, @Param("limit")int limit);

}
