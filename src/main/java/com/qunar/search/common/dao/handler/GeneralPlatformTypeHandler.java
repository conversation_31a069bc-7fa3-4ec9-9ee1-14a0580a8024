package com.qunar.search.common.dao.handler;

import com.qunar.search.common.enums.GeneralPlatformType;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * GeneralPlatformTypeHandler
 *
 * <AUTHOR>
 * @date 17-1-20.
 */
public class GeneralPlatformTypeHandler implements TypeHandler<GeneralPlatformType>{


    @Override
    public GeneralPlatformType getResult(CallableStatement callableStatement, int i) throws SQLException {
        return GeneralPlatformType.valueOfByName(callableStatement.getString(i));
    }

    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, GeneralPlatformType platformType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, platformType.getName());
    }

    @Override
    public GeneralPlatformType getResult(ResultSet resultSet, String s) throws SQLException {
        return GeneralPlatformType.valueOfByName(resultSet.getString(s));
    }

    @Override
    public GeneralPlatformType getResult(ResultSet resultSet, int i) throws SQLException {
        return GeneralPlatformType.valueOfByName(resultSet.getString(i));
    }
}
