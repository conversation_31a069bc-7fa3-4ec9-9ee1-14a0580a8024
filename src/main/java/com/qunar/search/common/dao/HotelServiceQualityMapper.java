package com.qunar.search.common.dao;

import com.qunar.search.common.profit.entity.HotelServiceQuality;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**加载酒店服务相关数据.
 * Created by fangxue.zhang on 2016/6/12.
 */


@Repository
public interface HotelServiceQualityMapper {

    /**
     * 加载数据到内存
     * @param dt
     */
    List<HotelServiceQuality> load(@Param("dt") String dt);
}
