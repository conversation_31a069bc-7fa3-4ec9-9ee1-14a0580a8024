package com.qunar.search.common.dao;

import com.qunar.search.common.bean.SaleRank;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleRankDao{
	@Select("select   max(log_day) from   international_hotel_sales")
	Integer selectMaxVersion();

	@Select("select hotel_seq as hotelSeq, city_code as cityCode, city_rank as cityRank, hotel_trading_area as area, area_rank as areaRank from international_hotel_sales where log_day = #{0}")
	List<SaleRank> findSaleRank(int version);

	/**
	 * 国内数据
	 * @return
	 */
	@Select("select max(log_day) from domestic_hotel_sales")
	Integer selectDomesticMaxVersion();

	/**
	 * 国内销量数据
	 * @return
	 */
	@Select("select hotel_seq as hotelSeq, city_code as cityCode, hotel_grade as grade, grade_rank as gradeRank, hotel_trading_area as area, area_rank as areaRank from domestic_hotel_sales where log_day = #{0}")
	List<SaleRank> findDomesticSaleRank(int version);
}
