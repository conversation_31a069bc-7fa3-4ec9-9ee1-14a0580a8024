package com.qunar.search.common.dao.handler;

import com.qunar.search.common.enums.SortType;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * SortTypeHandler
 *
 * <AUTHOR>
 * @date 17-1-19.
 */
public class SortTypeHandler implements TypeHandler<SortType>{
    @Override
    public SortType getResult(CallableStatement callableStatement, int i) throws SQLException {
        return SortType.valueOfByName(callableStatement.getString(i));
    }

    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, SortType sortType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, sortType.getName());
    }

    @Override
    public SortType getResult(ResultSet resultSet, String s) throws SQLException {
        return SortType.valueOfByName(resultSet.getString(s));
    }

    @Override
    public SortType getResult(ResultSet resultSet, int i) throws SQLException {
        return SortType.valueOfByName(resultSet.getString(i));
    }
}
