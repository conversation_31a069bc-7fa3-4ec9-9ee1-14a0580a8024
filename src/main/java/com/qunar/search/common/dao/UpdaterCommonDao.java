package com.qunar.search.common.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/18
 */
public interface UpdaterCommonDao {

    /**
     * 查询最大版本
     *
     * @param versionField 版本号字段名
     * @param table 表名
     * @return 最大版本
     */
    @Select("select max(${versionField}) from ${table}")
    Integer selectMaxVersion(@Param("versionField") String versionField, @Param("table") String table);

    /**
     * 查询DB中存在几个版本
     *
     * @param versionField 版本号字段名
     * @param table 表名
     * @return 版本列表
     */
    @Select("select distinct ${versionField} from ${table}")
    List<Integer> selectVersionList(@Param("versionField") String versionField, @Param("table") String table);

    /**
     * 查询指定版本下的数量
     *
     * @param versionField 版本号字段名
     * @param table 表名
     * @param version 版本号
     * @return 该版本下的数量
     */
    @Select("select count(*) from ${table} where ${versionField} = #{version}")
    int selectCountByVersion(@Param("versionField") String versionField, @Param("table") String table, @Param("version") int version);

}
