package com.qunar.search.common.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface HotelBrandDao {
	/**
	 * max version
	 *
	 * @return
	 */
	@Select("select max(version) from international_hotel_brand_group_name")
	Integer selectMaxVersion();


	/**
	 * selectInfo
	 *
	 * @param version
	 * @return
	 */
	@Select("select hotel_seq as hotelSeq,hotel_group_name as groupName from international_hotel_brand_group_name where version=#{version}")
	List<Map<String, String>> selectInfo(@Param("version") int version);
}
