package com.qunar.search.common.feature;

import com.google.common.collect.Lists;
import com.qunar.search.common.bean.HotelInfo;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 用户点击，收藏，订单相关的酒店特征
 */
public class UserHotel implements Serializable {
    private static final long serialVersionUID = 1L;
    private String seq;
    private int dangci = 0; //档次，档次跟星级是不是只留一个就行了.
    private int star;
    private String brand;
    private String tradingArea; //商圈
    private List<String> types = Lists.newLinkedList();
    
    public UserHotel() {
    }

    public UserHotel(HotelInfo info) {
        this.seq = info.hotelSEQ;
        this.brand = info.getProperty("hotelBrand");
        this.dangci = info.getDangci();
        this.star = info.getStars();
        this.tradingArea = info.getProperty("tradingArea");
        this.types.addAll(Arrays.asList(info.getHotelType()));
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public int getDangci() {
        return dangci;
    }

    public void setDangci(int dangci) {
        this.dangci = dangci;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getTradingArea() {
        return tradingArea;
    }

    public void setTradingArea(String tradingArea) {
        this.tradingArea = tradingArea;
    }

    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    public static class FavoriteHotel extends UserHotel {
        private static final long serialVersionUID = 1L;

        public FavoriteHotel() {
        }
        
        public FavoriteHotel(HotelInfo info) {
            super(info);
        }

        private int favoriteDate;//收藏时间

        public int getFavoriteDate() {
            return favoriteDate;
        }

        public void setFavoriteDate(int favoriteDate) {
            this.favoriteDate = favoriteDate;
        }
    }

    public static class OrderHotel extends UserHotel {
        private static final long serialVersionUID = 1L;
        private double price;
        private int roomNum;
        private String orderDate;
        
        public OrderHotel() {
        }

        public OrderHotel(HotelInfo info) {
            super(info);
        }

        public double getPrice() {
            return price;
        }

        public void setPrice(double price) {
            this.price = price;
        }

        public int getRoomNum() {
            return roomNum;
        }

        public void setRoomNum(int roomNum) {
            this.roomNum = roomNum;
        }

        public String getOrderDate() {
            return orderDate;
        }

        public void setOrderDate(String orderDate) {
            this.orderDate = orderDate;
        }

    }

    /**
     * 实时点击的酒店
     */
    public static class RealtimeClickHotel extends UserHotel {
        private static final long serialVersionUID = 1L;
        private long clickTime;
        
        public RealtimeClickHotel() {
        }

        public RealtimeClickHotel(HotelInfo info) {
            super(info);
        }

        public long getClickTime() {
            return clickTime;
        }

        public void setClickTime(long clickTime) {
            this.clickTime = clickTime;
        }
    }

    public static class ClickHotel extends UserHotel {
        private static final long serialVersionUID = 1L;
        int clickCount = 0;
        
        public ClickHotel() {
        }

        public ClickHotel(HotelInfo info) {
            super(info);
        }

        public int getClickCount() {
            return clickCount;
        }

        public void setClickCount(int clickCount) {
            this.clickCount = clickCount;
        }
    }
}
