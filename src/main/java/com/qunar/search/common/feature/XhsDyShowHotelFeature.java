package com.qunar.search.common.feature;


import lombok.Data;

import java.io.Serializable;

/**
 * 用户在小红书或抖音浏览酒店特征
 */
@Data
public class XhsDyShowHotelFeature implements Serializable {

    /**
     * 用户来源的渠道
     * redbook：小红书，douyin：抖音,kuaishou:快手,wxvideo:微信短视频
     * https://wiki.corp.qunar.com/pages/viewpage.action?pageId=1026630380
     */
    private String channel;

    /**
     * 我们平台的查询码(引流码)
     */
    private String queryCode;

    /**
     * 活动码
     */
    private String activityCode;

    /**
     * 货品码,酒店seq
     */
    private String productCode;

    /**
     * 酒店价格
     */
    private double productCodePrice;

}
