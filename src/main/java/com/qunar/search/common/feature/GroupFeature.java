package com.qunar.search.common.feature;

import com.qunar.search.common.constants.CommonConstants;
import lombok.Data;

import java.io.Serializable;


/**
 * 用于精排的 group feature
 */
@Data
public class GroupFeature implements Serializable {

    private double userClickHotel3WeekMaxCount = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 用户三周内点击酒店的最大次数
    private double userClickHotelRealTimeMaxRate = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 用户实时点击当前酒店占比最大值
    private double hotelMaxPrice = CommonConstants.HOTEL_MIN_PRICE; // 酒店最高报价
    private double hotelAvgPrice= CommonConstants.HOTEL_MAX_PRICE; // 酒店平均报价
    private double hotelMinPrice = CommonConstants.HOTEL_MAX_PRICE; // 酒店最低报价
    private double poiHotelMinDistance = CommonConstants.MAX_DISTANCE; // poi到酒店的最短距离
    private double userHotelMinDistance = CommonConstants.MAX_DISTANCE; // 用户到酒店的最短距离
    private double hotelMinSellPriceDivideOrgPrice = 1.0; // 酒店 卖价/原始价 最小值
    private double hotel24hOrderMedianPrice = CommonConstants.HOTEL_MIN_PRICE; // 酒店24h订单价格中位数最大值
    private double hotelSearchMaxCount6week = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店6周最大搜索量
    private double hotelMaxCommentCount = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店最大评论数
    private double hotelMaxClickCount = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店点击量最大值
    private double hotel7DayMaxCtr = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店近7天曝光点击率最大值
    private double hotel3MonthOrderCount = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店三个月最大单量
    private double hotel3MonthMaxS2o = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店3个月最大转化率
    private double hotel60DayMaxPVS2o = CommonConstants.GROUP_FEATURE_MIN_VALUE; // 酒店60天得pv_s2o（曝光）最大值

    private int totalNums = CommonConstants.TOTAL_NUMS; // 精排候选酒店数量

}