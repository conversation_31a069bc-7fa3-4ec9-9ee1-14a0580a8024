package com.qunar.search.common.feature;

import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;

@Data
public class QueryStatisFeature implements Serializable {

    /**
     * query词下总订单
     */
    private int totalOrderNum;

    /**
     * query 词下订单平均展示价格
     */
    private double avgOrderShowPrice;

    /**
     * query 词下酒店及其订单
     */
    private Map<String,Integer> hotelOrderNumMap = Collections.emptyMap();


}
