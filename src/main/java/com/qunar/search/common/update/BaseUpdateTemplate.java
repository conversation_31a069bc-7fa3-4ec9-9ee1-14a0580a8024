package com.qunar.search.common.update;

import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.metrics.Metrics;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

public abstract class BaseUpdateTemplate<K> {
	protected final Logger LOGGER = LoggerFactory.getLogger(getClass());

    /**
     * 最后一次更新时的版本
     */
	protected AtomicReference<K> lastVersion;
	protected Class<K> keyClass;

    /**
     * 当前数据量
     */
	protected volatile int size;

    /**
     * 该定时任务是否开启了调度。true:开启，false:关闭
     */
	private volatile boolean enableSchedule;

    /**
     * 分页相关参数
     */
    protected volatile int pageSize = 500;
    protected boolean enablePaging = false;

    /**
     * 该定时任务最后执行的时间
     */
	private AtomicLong lastExecuteTime = new AtomicLong(0);

    /**
     * 该定时任务最后执行的时候所花费的时间，单位是ms
     */
    private AtomicLong costTime = new AtomicLong(0);

    /**
     * 该定时任务最后一次执行时，数据校验是否通过。true:通过，false:不通过
     */
    private volatile boolean verifyPassed;

	public BaseUpdateTemplate() {
		this.lastVersion = new AtomicReference<>();
		keyClass = getKeyClass();
	}

    public boolean isEnableSchedule() {
        return enableSchedule;
    }

    public void setEnableSchedule(boolean enableSchedule) {
        this.enableSchedule = enableSchedule;
    }
    
    /**
     * 获取分页大小
     * @return 分页大小
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 是否启用分页
     * @return 是否启用分页
     */
    public boolean isEnablePaging() {
        return enablePaging;
    }

    /**
     * 设置是否启用分页
     * @param enablePaging 是否启用分页
     */
    public void setEnablePaging(boolean enablePaging) {
        this.enablePaging = enablePaging;
    }

    public AtomicLong getLastExecuteTime() {
        return lastExecuteTime;
    }

    public AtomicLong getCostTime() {
        return costTime;
    }

    public boolean isVerifyPassed() {
        return verifyPassed;
    }

    /**
	 * 获取泛型类型
	 * @return
	 */
	public Class getGenericityType(Class superClass, Class clazz, int index){
		Type superclass = topSuperClass(superClass, clazz);
		if (superclass == null) {
			return null;
		}
		Type[] arguments = ((ParameterizedType) superclass).getActualTypeArguments();
		return  (Class) arguments[index];
	}

	private Type topSuperClass(Class superClass, Class clazz) {
		Class c = clazz;
		while (c != null) {
			Class superclass = c.getSuperclass();
			if (superclass == superClass) {
				return c.getGenericSuperclass();
			}
			c = superclass;
		}
		return null;
	}

	protected abstract K maxVersion();

	public final void update() {
		try {
			K maxVersion = maxVersion();
			if (needUpdate(maxVersion)) {
				update(maxVersion);
			}
		} catch (RuntimeException e) {
			LOGGER.error("任务执行失败", e);
		}
	}

	abstract protected boolean needUpdate(K version);

	abstract protected boolean processWork(K version, boolean force);

	public final void update(K version) {
		update(version, false);
	}

	public final void update(K version, boolean forced) {
		if (version == null) {
			return;
		}

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		// 执行数据更新逻辑，并返回数据校验是否通过
		boolean verifyPassed = processWork(version, forced);
        if (!verifyPassed) {
            String updater = this.getClass().getSimpleName();
            LOGGER.error("{} 数据校验不通过!", updater);
            Metrics.meter("cacheVerify").tag("updater", updater).get().mark();
        }

		stopWatch.stop();

		lastExecuteTime.set(System.currentTimeMillis());
		costTime.set(stopWatch.getTime());
		this.verifyPassed = verifyPassed;

        LOGGER.info(
                "Update Finish. verifyPassed:{}, enablePaging:{}, pageSize:{}, lastVersion:{}, version:{}, size:{}. used {}ms.",
                verifyPassed, enablePaging, getPageSize(), lastVersion.get(), version, this.size, stopWatch.getTime());
	}


	public K getVersion() {
		return lastVersion.get();
	}

	public abstract Class<K> getKeyClass();

    /**
     * 获取该定时任务的中文描述，用于说明该定时任务的用途
     *
     * @return 中文描述
     */
	public String getDesc() {
	    return "无";
    }

    /**
     * 数据是否有版本号
     *
     * @return 如果有版本号则返回true,否则返回false
     */
	public abstract boolean hasVersion();

	public int size() {
		return this.size;
	}

	public boolean needMonitor() {
		return true;
	}

	public void clean() {
		throw new RuntimeException("未实现Clean方法");
	}

	public Object check(String value) {
		throw new RuntimeException("未实现Check方法");
	}
}
