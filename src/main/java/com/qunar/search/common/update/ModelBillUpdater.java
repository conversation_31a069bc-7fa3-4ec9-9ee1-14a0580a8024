package com.qunar.search.common.update;

import com.google.common.collect.Maps;
import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.search.common.base.SearchDataTask;
import com.qunar.search.common.base.UpdaterInfo;
import com.qunar.search.common.bean.ModelBill;
import com.qunar.search.common.dao.ModelBillDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qunar.concurrent.Nameable;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service("modelBillUpdater")
@UpdaterInfo(tableName = "model_bill", versionField = "log_day")
public class ModelBillUpdater extends UpdaterTemplate<ModelBill> {

	private static final Logger log = LoggerFactory.getLogger(ModelBillUpdater.class);
	private static volatile Map<String, Double> avgMapRef = Maps.newConcurrentMap();
	private static volatile Map<String, Double> dispersionMapRef = Maps.newConcurrentMap();
	private static String NAME = "modelBillUpdater";
	private static final double defaultModelBill = 0;

	@Autowired
	private ModelBillDao modelBillDao;

	@Override
	protected Integer maxVersion() {
		Integer vers = modelBillDao.selectMaxVersion();
		if (vers != null) {
			return vers;
		}
		return 0;
	}

	@Override
	protected List<ModelBill> findData(Integer maxVersion) {
		return modelBillDao.selectModelBillList(maxVersion);
	}

	@Override
	protected void process(List<ModelBill> list) {
		Map<String, Double> avgMap = Maps.newConcurrentMap();
		Map<String, Double> dispersionMap = Maps.newConcurrentMap();
		for (ModelBill modelBill : list) {
			String model = modelBill.getModel();
			double avgPrice = modelBill.getAvg_price();
			double dispersionPrice = modelBill.getDispersion_price();

			avgMap.put(model, avgPrice);
			dispersionMap.put(model, dispersionPrice);
			log.debug("Modifying clicks in updating model:" + model);
		}
		setPriceMap(avgMap, dispersionMap);
	}

	@SearchDataTask(initialDelay = 11, fixedDelay = 15)
	public void run() {
		update();
	}

	private static void setPriceMap(Map<String, Double> avgMap, Map<String, Double> dispersionMap) {
		avgMapRef = avgMap;
		dispersionMapRef = dispersionMap;
	}

	public static double getHotelModelAvg(String model) {
		Double click = avgMapRef.get(model);
		if (click == null) {
			return defaultModelBill;
		} else {
			return click.doubleValue();
		}
	}

	public static double getHotelModelDispersion(String model) {
		Double click = dispersionMapRef.get(model);
		if (click == null) {
			return defaultModelBill;
		} else {
			return click.doubleValue();
		}
	}

    @Override
    public String getDesc() {
        return "手机型号订单数据";
    }
}