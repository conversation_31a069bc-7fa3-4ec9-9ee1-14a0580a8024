package com.qunar.search.common.update;

import com.qunar.search.common.conf.RankSystemConfig;

import qunar.metrics.Metrics;

import org.apache.commons.collections.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public abstract class AbstractUpdateTemplate<K, V> extends BaseUpdateTemplate<K> {

	/**
	 * 获取指定版本的 ID 范围，包含最小和最大 ID 值
	 * 子类必须实现此方法
	 * 
	 * @param version 版本号
	 * @return ID 范围对象，如果不存在返回 null
	 */
	protected IdRange getIdRangeByVersion(K version) {
		throw new UnsupportedOperationException("子类必须实现 getIdRangeByVersion 方法");
	}

	protected int findCount(K maxVersion) {
		throw new UnsupportedOperationException("分页加载：子类必须实现 findCount 方法");
	}

	protected abstract List<V> findData(K maxVersion);

	public int getPageSize() {
		return RankSystemConfig.getUpdaterPageSize(this.getClass().getSimpleName());
	}
	
	/**
	 * 基于ID范围的分页查询数据，默认实现调用原方法，子类可以覆盖此方法提供分页实现
	 * @param maxVersion 最大版本号
	 * @param minId 当前页的最小 ID（包含）
	 * @param maxId 当前页的最大 ID（不包含）
	 * @return 分页查询结果
	 * @throws IllegalStateException 如果启用了分页但子类没有覆盖此方法
	 */
	protected List<V> findDataByIdRange(K maxVersion, Long minId, Long maxId) {
		throw new UnsupportedOperationException("启用了分页查询，但子类未覆盖findDataByIdRange方法提供真正的分页实现。请覆盖findDataByIdRange方法或者关闭分页功能。");
	}

	protected abstract void process(List<V> t);
	
	/**
	 * 从数据对象中获取ID值，用于分页，子类应该覆盖此方法
	 * @param v 数据对象
	 * @return ID值
	 */
	protected Long getIdValue(V v) {
		throw new UnsupportedOperationException("子类必须实现 getIdValue 方法");
	}

	protected boolean verify(List<V> dataList) {
	    if (CollectionUtils.isEmpty(dataList)) {
	        return false;
        }
        int newSize = dataList.size();
        return this.size == 0 || ((float) Math.abs(this.size - newSize) / this.size) < RankSystemConfig.getUpdateChangeRate();
    }

	@Override
	public Class<K> getKeyClass() {
		return getGenericityType(AbstractUpdateTemplate.class, this.getClass(), 0);
	}

	@Override
	public final boolean processWork(K version, boolean forced) {
		if (enablePaging) {
			return processWorkWithPaging(version, forced);
		} else {
			return processWorkWithoutPaging(version, forced);
		}
	}
	
	/**
	 * 不使用分页的处理逻辑
	 * @param version 版本号
	 * @param forced 是否强制更新
	 * @return 验证是否通过
	 */
	private boolean processWorkWithoutPaging(K version, boolean forced) {
		List<V> t = findData(version);
		boolean verifyPassed = verify(t);
		if (forced || (CollectionUtils.isNotEmpty(t) && verifyPassed)) {
			this.size = t.size();
			process(t);
			lastVersion.set(version);
		}

		return verifyPassed;
	}
	
	/**
	 * 使用分页的处理逻辑
	 * @param version 版本号
	 * @param forced 是否强制更新
	 * @return 验证是否通过
	 */
	private boolean processWorkWithPaging(K version, boolean forced) {
		// 获取数据ID范围
		IdRange idRange = getIdRangeByVersion(version);
		if (idRange == null || idRange.getMinId() == null || idRange.getMaxId() == null) {
			LOGGER.warn("无法获取数据ID范围，版本: {}", version);
			return false;
		}

		int pageSize = getPageSize();
		
		Long minId = idRange.getMinId();
		Long maxId = idRange.getMaxId();

		int expectedCount = findCount(version);
		
		// 分页查询处理
		final AtomicReference<Long> lastMaxIdRef = new AtomicReference<>(minId);
		boolean hasMoreData = true;
		List<V> allData = new ArrayList<>();
		
		while (hasMoreData) {
			Long currentLastMaxId = lastMaxIdRef.get();
			
			// 计算当前页的最大 ID（不包含）
			Long currentMaxId = currentLastMaxId + pageSize;
			// 查询当前页数据
			List<V> pageData = findDataByIdRange(version, currentLastMaxId, currentMaxId);
			
			if (CollectionUtils.isEmpty(pageData)) {
				// 如果当前页没有数据，检查是否已经到达数据范围的末尾, 避免ID不连续丢数据
				// 实际测试只有 hotel_tag_relation ID 不连续
				if(currentMaxId > maxId) {
					LOGGER.info("已到达数据范围末尾，停止查询，当前ID: {}, 最大ID: {}", currentMaxId, maxId);
					hasMoreData = false;
				} else {
					// 如果未到达末尾但当前页没有数据，继续查询下一页
					LOGGER.debug("当前页无数据，但未到达数据范围末尾，继续查询下一页");
					lastMaxIdRef.set(currentMaxId);
				}
				continue;
			} else {
				// 有数据，确保继续查询
				hasMoreData = true;
			}
			
			allData.addAll(pageData);
			
			// 更新 lastMaxId
			if (!pageData.isEmpty()) {
				V lastItem = pageData.get(pageData.size() - 1);
				Long idValue = getIdValue(lastItem);
				if (idValue != null) {
					// 由于新的查询使用 id >= lastMaxId，为避免重复处理同一条记录，这里需要 +1
					lastMaxIdRef.set(idValue + 1);
				}
			}
		}
		
		// 数据量校验
		int actualCount = allData.size();
		if (expectedCount > 0 && actualCount != expectedCount) {
			LOGGER.error("加载数据量({}条)与预期数据量({}条)不符", actualCount, expectedCount);
			String updater = this.getClass().getSimpleName();
			Metrics.meter("load_data_count_mismatch").tag("updater", updater).get().mark();
		}
		
		boolean verifyPassed = verify(allData);
		if (forced || (CollectionUtils.isNotEmpty(allData) && verifyPassed)) {
			this.size = allData.size();
			process(allData);
			lastVersion.set(version);
		}

		return verifyPassed;
	}
}
