package com.qunar.search.common.update;

import org.apache.ibatis.session.ResultHandler;

/**
 * 支持分页查询的Map流式更新模板抽象类
 * 继承此类的子类必须实现分页所需的所有方法
 * 
 * @param <V> 数据类型
 * @param <F> 存储类型
 */
public abstract class AbstractPagingMapStreamUpdateTemplate<V, F> extends MapStreamUpdateTemplate<V, F> {
    
    /**
     * 构造函数，默认启用分页
     */
    public AbstractPagingMapStreamUpdateTemplate() {
        super();
        setEnablePaging(true);
    }
    
    /**
     * 从数据对象中获取ID值，用于分页
     * 子类必须实现此方法
     * 
     * @param v 数据对象
     * @return ID值
     */
    @Override
    protected abstract Long getIdValue(V v);
    
    /**
     * 基于ID范围的分页查询数据
     * 子类必须实现此方法
     * 
     * @param maxVersion 最大版本号
     * @param minId 当前页的最小ID（包含）
     * @param maxId 当前页的最大ID（不包含）
     * @param resultHandler 结果处理器
     */
    @Override
    protected abstract void findDataByIdRange(Integer maxVersion, Long minId, Long maxId, ResultHandler resultHandler);

    protected abstract IdRange getIdRangeByVersion(Integer version);
}
