package com.qunar.search.common.update;

import com.google.common.hash.BloomFilter;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/7/16
 */
public abstract class AbstractMapStreamUpdateTemplate<K, V, F> extends AbstractStreamUpdateTemplate<K, V> {

	protected abstract void addOrUpdateMap(ConcurrentHashMap<String, F> data, V v);

	protected abstract ConcurrentHashMap<String, F> data();

	protected abstract void deleteOneFromMap(ConcurrentHashMap<String, F> data, String key, F f);
	
	@Override
	public Class<K> getKeyClass(){
		return getGenericityType(AbstractMapStreamUpdateTemplate.class, this.getClass(), 0);
	}

	@Override
	protected final void deleteData(BloomFilter<String> bloomFilter){
		Iterator<Map.Entry<String, F>> entries = data().entrySet().iterator();
		while (entries.hasNext()) {
			Map.Entry<String, F> entry = entries.next();
			String key = entry.getKey();
			if(!bloomFilter.mightContain(key) && data().get(key) != null){
				deleteOneFromMap(data(), key, entry.getValue());
			}
		}
	}
	@Override
	protected final void addOrUpdate(V v){
		addOrUpdateMap(data(), v);
	}

	@Override
	public void clean() {
		data().clear();
	}
}
