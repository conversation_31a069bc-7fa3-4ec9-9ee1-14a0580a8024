package com.qunar.search.common.update;

import com.google.common.hash.BloomFilter;

import java.util.Iterator;
import java.util.Set;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/7/16
 */
public abstract class AbstractSetStreamUpdateTemplate<V> extends AbstractStreamUpdateTemplate<Integer, V> {

	protected abstract void addOrUpdateSet(Set<String> data, V v);

	protected abstract Set<String> data();


	@Override
	protected final void deleteData(BloomFilter<String> bloomFilter){
		Iterator<String> iterator = data().iterator();
		while(iterator.hasNext()){
			String f = iterator.next();
			if(!bloomFilter.mightContain(f)){
				iterator.remove();
			}
		}
	}


	@Override
	protected final void addOrUpdate(V v){
		addOrUpdateSet(data(), v);
	}

}
