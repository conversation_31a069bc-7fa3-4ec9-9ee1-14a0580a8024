package com.qunar.search.common.update;

/**
 * 流式加载模板
 Created with IntelliJ IDEA. User: guolifei Date: 2019/7/16
 */
public abstract class MapStreamUpdateTemplate<V, F> extends AbstractMapStreamUpdateTemplate<Integer, V, F>{
	@Override
	protected boolean needUpdate(Integer version) {
		if (lastVersion.get() == null) {
			return true;
		}
		return version != null && version > lastVersion.get();
	}

    @Override
    public boolean hasVersion() {
        return true;
    }
}
