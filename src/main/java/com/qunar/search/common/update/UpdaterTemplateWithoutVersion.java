package com.qunar.search.common.update;

import java.util.List;

public abstract class UpdaterTemplateWithoutVersion<V> extends AbstractUpdateTemplate<Object, V> {
	@Override
	protected Object maxVersion() {
		return 0;
	}

	@Override
	protected List<V> findData(Object maxVersion) {
		return findData();
	}

	abstract protected List<V> findData();

	@Override
	protected boolean needUpdate(Object version) {
		return true;
	}

    @Override
    protected boolean verify(List<V> dataList) {
	    return true;
    }

    @Override
    public boolean hasVersion() {
        return false;
    }
}
