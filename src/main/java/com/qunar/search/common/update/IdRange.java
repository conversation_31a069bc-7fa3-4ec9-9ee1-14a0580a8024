package com.qunar.search.common.update;

/**
 * ID 范围类，包含最小和最大 ID 值
 */
public class IdRange {
    private Long minId;
    private Long maxId;
    
    /**
     * 无参构造函数，供 MyBatis 使用
     */
    public IdRange() {
    }
    
    public IdRange(Long minId, Long maxId) {
        this.minId = minId;
        this.maxId = maxId;
    }
    
    public Long getMinId() {
        return minId;
    }
    
    public Long getMaxId() {
        return maxId;
    }
}
