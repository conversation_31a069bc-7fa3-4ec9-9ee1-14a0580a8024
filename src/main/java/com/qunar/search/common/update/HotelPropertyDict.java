package com.qunar.search.common.update;

import com.google.common.collect.*;
import com.qunar.hotel.info.dict.logic.DictService;
import com.qunar.search.common.base.SearchDataTask;
import com.qunar.search.common.constants.CommonConstants;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.qunar.search.common.update.HotelPropertyDict.HotelAttrKey.*;

/**
 * 酒店属性code/name字典
 */
@Service
public class HotelPropertyDict implements Runnable {

	private static Logger log = LoggerFactory.getLogger(HotelPropertyDict.class);
	private static final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);

	/**
	 * 从基础数据获取的数据字典，包含主题、评论  <code,name>
	 */
	private static Map<HotelProperty, BiMap<String, String>> PROP_DICT = ImmutableMap.of();

	@QMapConfig(value = "search_common.properties", key = "hotel.attr.inlandCommonTag.forbidden")
	private Set<String> inlandCommonTagForbidden = ImmutableSet.of();

	private Boolean useHotelCategory = null;

	@Resource
	private DictService dictService;

	public void init() {
		run();
	}

	@SearchDataTask(initialDelay = 0, fixedDelay = 60)
	public void run() {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		try {
			Map<HotelProperty, BiMap<String, String>> temp = Maps.newEnumMap(HotelProperty.class);
			for (HotelProperty property : HotelProperty.values()) {
				BiMap<String, String> tempDict = HashBiMap.create();
				Set<HotelAttrKey> keys = property.getHotelAttrKeys();
				for (HotelAttrKey key : keys) {
					Map<String, String> dict = dictService.getDict(key.getName());
					if (MapUtils.isEmpty(dict)) {
						continue;
					}

					dict.forEach((k, v) -> {
						if (useHotelCategory != null && useHotelCategory) {
							if (key == INLAND_COMMONTAG && inlandCommonTagForbidden.contains(k)) {
								return;
							}
						} else if (key == HOTEL_CATEGORY) {
							return;
						}

						tempDict.forcePut(key.getUniqId(k).intern(), v);
					});
				}

				tempDict.inverse();
				temp.put(property, tempDict);
			}

			setPropDict(temp);
			stopWatch.stop();
			log.info("hotelPropertyMappingUpdater cost {} ms.", stopWatch.getTime());
		} catch (Exception e) {
			log.error("hotelPropertyMappingUpdater init error:", e);
		}
	}

	private static void setPropDict(Map<HotelProperty, BiMap<String, String>> propDict) {
		PROP_DICT = propDict;
	}

	/**
	 * 通过标签code获取标签中文名称
	 */
	public static String getNameByCode(HotelProperty property, String code) {
		return Optional.ofNullable(PROP_DICT.get(property)).map(d -> d.get(code)).orElse("");
	}

	/**
	 * 通过中文标签得到对应的标签代码
	 */
	public static String getCodeByName(HotelProperty property, String name) {
		return Optional.ofNullable(PROP_DICT.get(property)).map(BiMap::inverse).map(d -> d.get(name)).orElse("");
	}

	public enum HotelAttrKey {
		INLAND_COMMONTAG("inlandCommonTag", "T"), INLAND_OPERATORTAG("inlandOperatorTag", "O"), HOTEL_CATEGORY(
				"hotelCategory", "C"), COMMENTSTAG("commentsTag", "");

		private String name;

		private String prefixCode;

		HotelAttrKey(String name, String prefixCode) {
			this.name = name;
			this.prefixCode = prefixCode;
		}

		public String getName() {
			return name;
		}

		public String getUniqId(String id) {
			if (StringUtils.isBlank(this.prefixCode)) {
				return id;
			}
			return CommonConstants.UNDERLINE_JOINER.join(this.prefixCode, id);
		}
	}

	public enum HotelProperty {

		THEME(INLAND_COMMONTAG, INLAND_OPERATORTAG, HOTEL_CATEGORY),

		GuestImpression(COMMENTSTAG);

		Set<HotelAttrKey> hotelAttrKeys;

		HotelProperty(HotelAttrKey... keys) {
			this.hotelAttrKeys = Sets.newHashSet(keys);
		}

		public Set<HotelAttrKey> getHotelAttrKeys() {
			return hotelAttrKeys;
		}
	}

	@QMapConfig(value = "search_common.properties", key = "hotel.attr.category.over.commontag")
	public void setUseHotelCategory(boolean useHotelCategory) {
		boolean notFirstInit = this.useHotelCategory != null;
		this.useHotelCategory = useHotelCategory;
		if (notFirstInit) {
			scheduledExecutorService.submit(this);
		}
	}
}