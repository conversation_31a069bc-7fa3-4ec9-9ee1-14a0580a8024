package com.qunar.search.common.update;

/**
 * 更新模板类，用于处理数据更新的通用逻辑
 * @param <T> 数据类型
 */
public abstract class UpdaterTemplate<T> extends AbstractUpdateTemplate<Integer, T> {

	@Override
	protected boolean needUpdate(Integer version) {
		if (lastVersion.get() == null && version != null) {
			return true;
		}
		return version != null && version > lastVersion.get();
	}

    @Override
    public boolean hasVersion() {
        return true;
    }
}
