package com.qunar.search.common.update;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.qunar.search.common.conf.RankSystemConfig;
import org.apache.ibatis.session.ResultHandler;

import java.nio.charset.Charset;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 Created with IntelliJ IDEA. User: guolifei Date: 2019/7/16
 */
public abstract class AbstractStreamUpdateTemplate<K, V> extends BaseUpdateTemplate<K> {
	
	/**
	 * 获取指定版本的 ID 范围，包含最小和最大 ID 值
	 * 子类必须实现此方法
	 * 
	 * @param version 版本号
	 * @return ID 范围对象，如果不存在返回 null
	 */
	protected IdRange getIdRangeByVersion(K version){
		throw new UnsupportedOperationException("子类必须实现 getIdRangeByVersion 方法");
	}
	
	private static final double FPP = 0.00000001;

	protected volatile boolean inited = false;
	
	protected abstract int findCount(K maxVersion);

	protected abstract void findData(K maxVersion, ResultHandler resultHandler);

	public int getPageSize() {
		return RankSystemConfig.getUpdaterPageSize(this.getClass().getSimpleName());
	}
	
	/**
	 * 基于ID范围的分页查询数据
	 * @param maxVersion 最大版本号
	 * @param minId 当前页的最小 ID（包含）
	 * @param maxId 当前页的最大 ID（不包含）
	 * @param resultHandler 结果处理器
	 */
	protected void findDataByIdRange(K maxVersion, Long minId, Long maxId, ResultHandler resultHandler) {
		throw new UnsupportedOperationException("子类必须实现 findDataByIdRange 方法");
	}

	// 从数据对象中获取ID值，用于分页，子类应该覆盖此方法
	protected Long getIdValue(V v) {
		throw new UnsupportedOperationException("子类必须实现 getIdValue 方法");
	}
	
	protected abstract int sleepTime();

	protected abstract String key(V v);

	protected abstract void deleteData(BloomFilter<String> bloomFilter);

	protected abstract void addOrUpdate(V v);

    protected boolean verify(int newSize) {
        return this.size == 0 || ((float) Math.abs(this.size - newSize) / this.size) < RankSystemConfig.getUpdateChangeRate();
    }

	protected int batchSize() {
		return RankSystemConfig.getStreamUpdaterBatchSizeMap().getOrDefault(this.getClass().getSimpleName(), RankSystemConfig.getStreamUpdaterDefaultBatchSize());
	}

	protected int initSleepTime() {
		return RankSystemConfig.getStreamUpdaterInitSleepTimeMap().getOrDefault(this.getClass().getSimpleName(), RankSystemConfig.getStreamUpdaterDefaultInitSleepTime());
	}

	public Class<K> getKeyClass(){
		return getGenericityType(AbstractStreamUpdateTemplate.class, this.getClass(), 0);
	}

	@Override
	protected boolean processWork(K version, boolean forced) {
		int count = findCount(version);
		boolean verifyPassed = verify(count);
		if (forced || verifyPassed) {
			findDataAndHandler(version, count);
			lastVersion.set(version);
			this.size = count;
		}
		return verifyPassed;
	}

	private void findDataAndHandler(K maxVersion, int count){
		AtomicInteger atomic = new AtomicInteger(0);
		BloomFilter<String> bloomFilter = BloomFilter.create(Funnels.stringFunnel(Charset.defaultCharset()), count, FPP);
		
		if (enablePaging) {
			// 获取数据ID范围
			IdRange idRange = getIdRangeByVersion(maxVersion);
			if (idRange == null || idRange.getMinId() == null || idRange.getMaxId() == null) {
				LOGGER.warn("无法获取数据ID范围，版本: {}", maxVersion);
				return;
			}

			int pageSize = getPageSize();
			
			Long minId = idRange.getMinId();
			Long maxId = idRange.getMaxId();
			
			// 分页查询处理
			final AtomicReference<Long> lastMaxIdRef = new AtomicReference<>(minId);
			boolean hasMoreData = true;
			
			LOGGER.info("开始分页加载数据，ID范围: {} - {}, 预期数据量: {}", minId, maxId, count);
			
			while (hasMoreData) {
				Long currentLastMaxId = lastMaxIdRef.get();
				
				// 计算当前页的最大 ID（不包含）
				Long currentMaxId = currentLastMaxId + pageSize;
				LOGGER.debug("分页查询，当前页范围: {} - {}", currentLastMaxId, currentMaxId);
				
				// 查询当前页数据
				final AtomicBoolean hasData = new AtomicBoolean(false);
				
				try {
					findDataByIdRange(maxVersion, currentLastMaxId, currentMaxId, context -> {
						hasData.set(true);
						@SuppressWarnings("unchecked")
						V v = (V) context.getResultObject();
						
						// 更新 lastMaxId，确保递增
						Long idValue = getIdValue(v);
						if (idValue != null && idValue > lastMaxIdRef.get()) {
							// 由于新的查询使用 id >= lastMaxId，为避免重复处理同一条记录，这里需要 +1
							lastMaxIdRef.set(idValue + 1);
						}
						
						String key = key(v);
						bloomFilter.put(key);
						addOrUpdate(v);
						
						int i = atomic.incrementAndGet();
						if (i % batchSize() == 0) {
							int sleepTime;
							if (!inited) {
								sleepTime = initSleepTime();
							} else {
								sleepTime = sleepTime();
							}
							try {
								Thread.sleep(sleepTime);
							} catch (InterruptedException e) {
								LOGGER.error("流式加载数据异常", e);
								// 重新设置中断状态
								Thread.currentThread().interrupt();
								// 中断整个处理过程
								throw new RuntimeException("线程被中断，停止处理", e);
							} catch (Exception e) {
								LOGGER.error("流式加载数据异常", e);
							}
						}
					});
				} catch (Exception e) {
					LOGGER.error("分页查询异常", e);
					// 如果是由中断引起的异常，则中断整个处理
					if (e instanceof RuntimeException && e.getCause() instanceof InterruptedException) {
						break;
					}
				}
				
				// 判断是否还有更多数据
				if (!hasData.get()) {
					// 如果当前页没有数据，检查是否已经到达数据范围的末尾，避免ID不连续丢数据
					if (currentMaxId > maxId) {
						LOGGER.info("已到达数据范围末尾，停止查询，当前ID: {}, 最大ID: {}", currentMaxId, maxId);
						hasMoreData = false;
					} else {
						// 如果未到达末尾但当前页没有数据，继续查询下一页
						LOGGER.debug("当前页无数据，但未到达数据范围末尾，继续查询下一页");
						lastMaxIdRef.set(currentMaxId);
					}
					continue;
				} else {
					// 有数据，确保继续查询
					hasMoreData = true;
				}
				
				// 如果查询没有更新lastMaxId（可能是所有记录ID相同），手动增加以避免死循环
				if (currentLastMaxId.equals(lastMaxIdRef.get())) {
					LOGGER.warn("查询未更新lastMaxId，手动增加以避免死循环，当前值: {}", lastMaxIdRef.get());
					lastMaxIdRef.set(currentMaxId);
				}
			}
		} else {
			// 原有的一次性查询处理
			findData(maxVersion, context -> {
				@SuppressWarnings("unchecked")
				V v = (V) context.getResultObject();
				String key = key(v);
				bloomFilter.put(key);
				addOrUpdate(v);
				int i = atomic.incrementAndGet();
				if (i % batchSize() == 0) {
					int sleepTime;
					// 系统启动的时候，为了缩短执行时间，一般用较短的休眠时间
					if (!inited) {
						sleepTime = initSleepTime();
					} else {
						// 正常定时调度和手动调度时，用较长的休眠时间
						sleepTime = sleepTime();
					}
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						LOGGER.error("流式加载数据异常", e);
						// 重新设置中断状态
						Thread.currentThread().interrupt();
						// 中断整个处理过程
						throw new RuntimeException("线程被中断，停止处理", e);
					} catch (Exception e) {
						LOGGER.error("流式加载数据异常", e);
					}
				}
			});
		}
		
		deleteData(bloomFilter);
		if(!inited){
			inited = true;
		}
	}
}
