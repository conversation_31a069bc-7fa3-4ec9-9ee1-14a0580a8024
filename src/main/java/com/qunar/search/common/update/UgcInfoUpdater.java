package com.qunar.search.common.update;

import com.qunar.search.common.base.SearchDataTask;
import com.qunar.search.common.base.UpdaterInfo;
import com.qunar.search.common.bean.UgcInfo;
import com.qunar.search.common.dao.UgcInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 酒店点评分和评论数等数据定时从DB增量同步。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=653597545
 */
@UpdaterInfo(tableName = "hotel_ugc_info")
@Slf4j
public class UgcInfoUpdater extends UpdaterTemplateWithoutVersion<String> {

    private static final FastDateFormat FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    private static final ConcurrentHashMap<String, UgcInfo> UGC_INFO_CACHE = new ConcurrentHashMap<>();

    private volatile String ugcVersion = "1970-01-01 08:00:00|0";

    @QMapConfig(value = "hotel_info_sync.properties", key = "ugc.sync.limit", defaultValue = "10000")
    private volatile int limit;

    @QMapConfig(value = "hotel_info_sync.properties", key = "ugc.sync.sleepIfRunning", defaultValue = "1000")
    private volatile int sleepIfRunning = 1000;

    @QMapConfig(value = "hotel_info_sync.properties", key = "ugc.sync.sleepBeforeRunning", defaultValue = "100")
    private volatile int sleepBeforeRunning = 100;

    private static volatile boolean running = false;

    @Resource
    private UgcInfoDao ugcInfoDao;

    public static float getCommentScore(String hotelSeq) {
        UgcInfo ugcInfo = getUgcInfo(hotelSeq);
        if (ugcInfo == null) {
            return 0F;
        }
        return ugcInfo.getCommentScore();
    }

    public static float getCommentScore(String hotelSeq, boolean useAdjustCommentScore) {
        UgcInfo ugcInfo = getUgcInfo(hotelSeq);
        if (ugcInfo == null) {
            return 0F;
        }
        if (useAdjustCommentScore) {
            return ugcInfo.getAdjustCommentScore();
        }
        return ugcInfo.getCommentScore();
    }

    public static int getCommentCount(String hotelSeq) {
        UgcInfo ugcInfo = getUgcInfo(hotelSeq);
        if (ugcInfo == null) {
            return 0;
        }
        return ugcInfo.getCommentCount();
    }

    public static int getCommentCount(String hotelSeq, boolean useAdjustCommentCount) {
        UgcInfo ugcInfo = getUgcInfo(hotelSeq);
        if (ugcInfo == null) {
            return 0;
        }
        // 鉴于主站内部特殊逻辑，实验组使用commentCount字段，对照组使用adjustCommentCount字段
        if (useAdjustCommentCount) {
            return ugcInfo.getCommentCount();
        }
        return ugcInfo.getAdjustCommentCount();
    }

    public static UgcInfo getUgcInfo(String hotelSeq) {
        return UGC_INFO_CACHE.get(hotelSeq);
    }

    @SearchDataTask(initialDelay = 28, fixedDelay = 28, timeUnit = TimeUnit.SECONDS)
    public void run() {
        update();
    }

    @Override
    protected List<String> findData() {
        long start = System.currentTimeMillis();
        String dbVersion = ugcInfoDao.getMaxVersion();
        log.info("db中UGC数据最大版本：{}，内存中版本：{}", dbVersion, ugcVersion);
        if (StringUtils.isBlank(dbVersion)) {
            return Collections.emptyList();
        }

        if (dbVersion.compareTo(ugcVersion) <= 0) {
            log.info("UGC数据没有发生变更!");
            return Collections.emptyList();
        }

        int totalCount = 0;

        String[] array = StringUtils.split(ugcVersion, "|");
        String lastMod = array[0];
        String fromSeq = array[1];

        while (true) {
            long t = System.currentTimeMillis();

            List<UgcInfo> ugcInfoList = ugcInfoDao.queryUgcInfo(lastMod, fromSeq, limit);

            totalCount += ugcInfoList.size();
            log.info("lastMod:{}, fromSeq:{}, dataSize:{}, 耗时:{}ms", lastMod, fromSeq, ugcInfoList.size(), System.currentTimeMillis() - t);

            if (CollectionUtils.isEmpty(ugcInfoList)) {
                break;
            }

            for (UgcInfo ugcInfo : ugcInfoList) {
                ugcInfo.setHotelSeq(ugcInfo.getHotelSeq().intern());
                UGC_INFO_CACHE.put(ugcInfo.getHotelSeq().intern(), ugcInfo);
            }

            UgcInfo lastUgcInfo = ugcInfoList.get(ugcInfoList.size() - 1);

            lastMod = FORMAT.format(lastUgcInfo.getUpdateTime());
            fromSeq = lastUgcInfo.getHotelSeq();

            ugcVersion = StringUtils.join(lastMod, "|", fromSeq);

            try {
                TimeUnit.MILLISECONDS.sleep(running ? sleepIfRunning : sleepBeforeRunning);
            } catch (InterruptedException ignore) {
            }
        }

        log.info("UGC数据增量同步完成，总数量：{}, 耗时：{}ms", totalCount, System.currentTimeMillis() - start);

        running = true;

        return Collections.emptyList();
    }

    @Override
    protected void process(List<String> t) {
    }

    @Override
    public int size() {
        return UGC_INFO_CACHE.size();
    }

    @Override
    public String getDesc() {
        return "UGC点评分和评论数等数据";
    }

    @Override
    public Object check(String value) {
        return UGC_INFO_CACHE.get(value);
    }

}
