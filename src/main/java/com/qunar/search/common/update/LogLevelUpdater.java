package com.qunar.search.common.update;

import com.qunar.search.common.base.SearchDataTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qunar.concurrent.Nameable;

/**
 * 定时更新日志级别是否为debug
 * <AUTHOR>
 *
 */
public class LogLevelUpdater implements Runnable, Nameable {
	private static final Logger log = LoggerFactory.getLogger(LogLevelUpdater.class);
	private static final Logger busiLog = LoggerFactory.getLogger("businesslog");
	private static boolean SYSTEM_LOG_ISDEBUG = false;
	private static boolean BUSI_LOG_ISDEBUG = false;
	private static String NAME = "logLevelUpdater";
	public static boolean getSysIsDebug() {
		return SYSTEM_LOG_ISDEBUG;
	}

    public static void setSystemLogIsdebug(boolean systemLogIsdebug) {
        SYSTEM_LOG_ISDEBUG = systemLogIsdebug;
    }

    public static boolean getBusiIsDebug() {
		return BUSI_LOG_ISDEBUG;
	}
	private static LogLevelUpdater instance = null;

	@SearchDataTask(initialDelay = 2,fixedDelay = 1)
	public void init () {
		reLoadIsDebug();
	}
	
	private LogLevelUpdater(){};
	
    public static LogLevelUpdater getInstance() {
    	if (instance == null)
			instance = new LogLevelUpdater();
		return instance;
    }
    
	@Override
	public void run() {
		reLoadIsDebug();
	}

	public static void reLoadIsDebug () {
		try {
			SYSTEM_LOG_ISDEBUG = log.isDebugEnabled();
			BUSI_LOG_ISDEBUG = busiLog.isDebugEnabled();
		} catch (Exception e) {
			log.error("load IsDebug error", e);
		} 
	}
    @Override
    public String getName() {
        return NAME;
    }
}
