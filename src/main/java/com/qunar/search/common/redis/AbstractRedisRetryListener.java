package com.qunar.search.common.redis;

import com.github.rholder.retry.Attempt;
import com.github.rholder.retry.RetryListener;
import com.qunar.hotel.qmonitor.QMonitor;
import lombok.extern.slf4j.Slf4j;
import qunar.metrics.Metrics;

/**
 * <AUTHOR>
 * @Date 2023/5/17
 */
@Slf4j
public abstract class AbstractRedisRetryListener implements RetryListener {

    private String name;

    public abstract String metricName ();

    public abstract String redisNameSpace();

    @Override
    public <V> void onRetry(Attempt<V> attempt) {
        if (attempt.hasException()) {
            QMonitor.recordOne(metricName());
            log.error("访问{}redis 有异常：",redisNameSpace() , attempt.getExceptionCause());
        }

        long attemptNumber = attempt.getAttemptNumber();
        // 第一次重试其实是第一次调用
        if (attemptNumber <= 1) {
            return;
        }

        Metrics.counter( "retry_" + redisNameSpace()).tag("attemptNumber", String.valueOf(attemptNumber)).get().inc();

        log.warn("访问{} redis发生重试，重试次数：{}", redisNameSpace(), attempt.getAttemptNumber());

    }

}
