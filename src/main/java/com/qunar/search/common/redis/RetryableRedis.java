package com.qunar.search.common.redis;

import com.github.rholder.retry.Retryer;
import com.qunar.redis.storage.Sedis3;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 对sedis3包一层，支持重试
 *
 * <AUTHOR>
 * @date 2020/7/6
 */
public class RetryableRedis {

    private final Sedis3 sedis3;

    private final Retryer<Object> retryer;

    public RetryableRedis(Sedis3 sedis3, Retryer<Object> retryer) {
        this.sedis3 = sedis3;
        this.retryer = retryer;
    }

    public Boolean exists(String key) throws Exception {
        return (Boolean) retryer.call(() -> sedis3.exists(key));
    }

    public Long del(String key) throws Exception {
        return (Long)retryer.call(() -> sedis3.del(key));
    }

    public Long del(byte[] key) throws Exception {
        return (Long)retryer.call(() -> sedis3.del(key));
    }

    public Boolean exists(byte[] key) throws Exception {
        return (Boolean) retryer.call(() -> sedis3.exists(key));
    }

    public Long hlen(byte[] key) throws Exception {
        return (Long)retryer.call(() -> sedis3.hlen(key));
    }

    @SuppressWarnings("unchecked")
    public Set<byte[]> hkeys(byte[] key) throws Exception {
        return (Set<byte[]>)retryer.call(() -> sedis3.hkeys(key));
    }

    @SuppressWarnings("unchecked")
    public Map<byte[], byte[]> hgetAll(byte[] key) throws Exception {
        return (Map<byte[], byte[]>)retryer.call(() -> sedis3.hgetAll(key));
    }

    @SuppressWarnings("unchecked")
    public List<byte[]> hmget(byte[] key, byte[]... fields) throws Exception {
        return (List<byte[]>)retryer.call(() -> sedis3.hmget(key, fields));
    }

    @SuppressWarnings("unchecked")
    public Set<String> hkeys(String key) throws Exception {
        return (Set<String>)retryer.call(() -> sedis3.hkeys(key));
    }

    public Long hdel(String key, String... fields) throws Exception {
        return (Long)retryer.call(() -> sedis3.hdel(key, fields));
    }

    public Long hdel(byte[] key, byte[]... fields) throws Exception {
        return (Long)retryer.call(() -> sedis3.hdel(key, fields));
    }

    public Long hincrBy(String key, String field, long value) throws Exception {
        return (Long) retryer.call(() -> sedis3.hincrBy(key, field, value));
    }

    public String setex(String key, int seconds, String value) throws Exception {
        return (String)retryer.call(() -> sedis3.setex(key, seconds, value));
    }

    public String set(String key, String value) throws Exception {
        return (String)retryer.call(() -> sedis3.set(key, value));
    }

    public String get(String key) throws Exception {
        return (String)retryer.call(() -> sedis3.get(key));
    }

    @SuppressWarnings("unchecked")
    public List<String> hmget(String key, String... fields) throws Exception {
        return (List<String>)retryer.call(() -> sedis3.hmget(key, fields));
    }

    public String hmset(String key, Map<String, String> hash) throws Exception {
        return (String)retryer.call(() -> sedis3.hmset(key, hash));
    }

    public Long ttl(String key) throws Exception {
        return (Long)retryer.call(() -> sedis3.ttl(key));
    }

    public Long expire(String key, int seconds) throws Exception {
        return (Long)retryer.call(() -> sedis3.expire(key, seconds));
    }

    public Long hset(String key, String field, String value) throws Exception {
        return (Long)retryer.call(() -> sedis3.hset(key, field, value));
    }

    public Long hset(byte[] key, byte[] field, byte[] value) throws Exception {
        return (Long)retryer.call(() -> sedis3.hset(key, field, value));
    }

    public ScanResult<Map.Entry<String, String>> hscan(final String key, final String cursor, final ScanParams params) throws Exception {
        return (ScanResult<Map.Entry<String, String>>)retryer.call(() -> sedis3.hscan(key, cursor, params));
    }

    public ScanResult<Map.Entry<byte[], byte[]>> hscan(final byte[] key, final byte[] cursor, final ScanParams params) throws Exception {
        return (ScanResult<Map.Entry<byte[], byte[]>>)retryer.call(() -> sedis3.hscan(key, cursor, params));
    }

    public Long incr(String key) throws Exception {
        return (Long)retryer.call(() -> sedis3.incr(key));
    }

    public Long incrBy(String key, Long step) throws Exception {
        return (Long)retryer.call(() -> sedis3.incrBy(key, step));
    }

    public String lindex(String key, long index) throws Exception {
        return (String) retryer.call(() -> sedis3.lindex(key, index));
    }

    public Long llen(String key) throws Exception {
        return (Long) retryer.call(() -> sedis3.llen(key));
    }

    public Long lpush(String key, String... strings) throws Exception {
        return (Long) retryer.call(() -> sedis3.lpush(key, strings));
    }

    public String rpop(String key) throws Exception {
        return (String) retryer.call(() -> sedis3.rpop(key));
    }

}
