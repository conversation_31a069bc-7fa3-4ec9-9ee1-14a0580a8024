package com.qunar.search.common.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒店类型
 * dim_hotel_info_v3 表中 hotel_type 字段
 * 一个酒店可能对应多个类型
 * 配置文件 search_common.properties： hotel_type_list=酒店公寓,民宿,青年旅舍,特色住宿,客栈,别墅,农家乐
 * 酒店类型 ECOMOMIC_HOTEL=青年旅社,RENTAL_HOTEL=酒店式公寓,GUZHEN_HOTEL=客栈,VILLAGE_HOTEL=乡村别墅,FARM_HOTEL=农家院,NOVELTY_HOTEL=新奇住宿
 */
public enum HotelTypeEnum {


    NORMAL_HOTEL(0,"NORMAL_HOTEL","正常酒店"),
    RENTAL_HOTEL(1,"RENTAL_HOTEL","酒店公寓"),
    HOMESTAY(2,"HOMESTAY","民宿"),
    ECOMOMIC_HOTEL(3,"ECOMOMIC_HOTEL","青年旅舍"),
    NOVELTY_HOTEL(4,"NOVELTY_HOTEL","特色住宿"),
    GUZHEN_HOTEL(5,"GUZHEN_HOTEL","客栈"),
    VILLAGE_HOTEL(6,"VILLAGE_HOTEL","别墅"),
    FARM_HOTEL(7,"FARM_HOTEL","农家乐");

    private final Integer number;
    private final String typeName;
    private final String chHotelType;
    HotelTypeEnum(int number, String typeName, String chHotelType) {
        this.number = number;
        this.typeName = typeName;
        this.chHotelType = chHotelType;
    }
    public Integer getNumber() {
        return number;
    }
    public String getTypeName() {
        return typeName;
    }

    public String getChHotelType() {
        return chHotelType;
    }

    public static final Map<String, HotelTypeEnum> chHotelTypeMAP;
    public static final Map<String, HotelTypeEnum> typeNameMAP;

    static {
        chHotelTypeMAP = Arrays.stream(HotelTypeEnum.values()).collect(Collectors.toMap(f -> f.chHotelType, f -> f, (a, b) -> a));
        typeNameMAP = Arrays.stream(HotelTypeEnum.values()).collect(Collectors.toMap(f -> f.typeName, f -> f, (a, b) -> a));
    }

    /**
     * 根据 英文类型名称查 HotelTypeEnum
     */
    public static HotelTypeEnum parseByTypeName(String typeName) {
        try{
            for(HotelTypeEnum hotelTypeEnum : HotelTypeEnum.values()){
                if( Objects.equals(hotelTypeEnum.getTypeName(), typeName) ){
                    return hotelTypeEnum;
                }
            }
        }catch(Exception e){
            return NORMAL_HOTEL;
        }

        return NORMAL_HOTEL;
    }


    /**
     * 根据 中文类型名称查 HotelTypeEnum
     */
    public static HotelTypeEnum parseByChHotelType(String chHotelType) {
        try{
            for(HotelTypeEnum hotelTypeEnum : HotelTypeEnum.values()){
                if( Objects.equals(hotelTypeEnum.getChHotelType(),chHotelType) ){
                    return hotelTypeEnum;
                }
            }
        }catch(Exception e){
            return NORMAL_HOTEL;
        }

        return NORMAL_HOTEL;
    }



    /**
     * 根据 中文/英文 类型名称查 HotelTypeEnum
     */
    public static HotelTypeEnum parseByChHotelTypeOrTypeName (String chHotelType) {
        try{

            if(chHotelTypeMAP.keySet().contains(chHotelType) ){
                return chHotelTypeMAP.get(chHotelType);
            }

            if(typeNameMAP.keySet().contains(chHotelType) ){
                return typeNameMAP.get(chHotelType);
            }

        }catch(Exception e){
            return NORMAL_HOTEL;
        }

        return NORMAL_HOTEL;
    }

}
