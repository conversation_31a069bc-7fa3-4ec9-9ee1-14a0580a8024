package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 第三方引流频道
 * <AUTHOR>
 *
 */
public enum MediaChannel {
    redbook("小红书", 1, "redbook"),
    douyin("抖音", 2, "douyin"),
    zhihu("知乎", 3, "zhihu"),
    kua<PERSON><PERSON>("快手", 4, "kuaishou"),
    wxvideo("微信短视频", 5, "wxvideo"),
    yuchiagent("鱼池渠道", 6, "yuchiagent"),
    vedio("短视频", 7, "vedio"),
    error("错误频道", 0, "error");

    private final String channelName;
    private final int index;
    private final String code;

    MediaChannel(String channelName, int index, String code) {
        this.channelName = channelName;
        this.index = index;
        this.code = code;
    }

	public String getChannelName() {
		return channelName;
	}

	public int getChannelIndex() {
		return index;
	}

    public static MediaChannel get(String name) {
        for (MediaChannel mediaChannel : MediaChannel.values()) {
            if (StringUtils.equals(mediaChannel.channelName, name) || StringUtils.equals(mediaChannel.code, name)) {
                return mediaChannel;
            }
        }
        return error;
    }

    public String getCode() {
        return code;
    }
}