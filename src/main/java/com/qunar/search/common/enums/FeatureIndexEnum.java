package com.qunar.search.common.enums;


import com.google.common.collect.Maps;
import com.qunar.search.common.model.dnn.DnnConstants;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.CommonConstants.*;
import static com.qunar.search.common.model.dnn.DnnConstants.COLON;

/**
 * 特征索引枚举
 * todo 其他没有枚举出来的特征统统按照double 类型处理
 */
@Getter
public enum FeatureIndexEnum {

    /**
     * 未知特征 - 模型不用
     */
    FEATURE_UNKNOWN("unknown", 0, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * 从xgb继承过来的特征
     */
    DNN_INDEX_1("用户户过去800天预订该酒店的次数(mppb_oder_status_in(0,2))", 1, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_2("过去半年用户在该城市最近一次预订的是否该酒店", 2, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_6("近三周点击该酒店的次数", 6, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_14("实时点击价格(列表页12h最近100家均值)", 14, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_45("(酒店180天历史订单价格均值-用户800天内订单价格均值)/用户800天内订单价格均值", 45, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_46("酒店180天历史订单价格均值-用户800天内订单价格均值", 46, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_97("4天之内收藏过该酒店", 97, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_98("4天之前收藏过该酒店", 98, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_99("酒店近3周的订单量(old统计)", 99, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_100("酒店评分(old统计)", 100, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_101("酒店报价", 101, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_102("酒店原始价格—酒店报价", 102, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_103("酒店７天转化率特征(置信)", 103, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_104("酒店在关键词下订单量(改成40天)", 104, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_105("poi到酒店的距离(poiHotelDistance)", 105, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_106("同城或商圈场景下，酒店距离用户或者坐标的距离", 106, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_114("酒店6个月订单量", 114, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_115("酒店３个月订单量(is_valid=1)", 115, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_119("酒店6周搜索量(3周?)", 119, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_123("酒店3个月转化率(置信)(搜索的pv_s2o，不是展现的pv_s2o)", 123, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_125("酒店3周转化率(置信)(搜索的pv_s2o，不是展现的pv_s2o)", 125, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_132("酒店评论数", 132, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_135("酒店评论标签交通负向个数", 135, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_151("酒店ecrm-rate(去位置偏置的转化率)s2o", 151, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_156("酒店24h订单价格中位数", 156, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_157("酒店30天订单间夜价格中位数", 157, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_158("酒店90天订单间夜价格中位数", 158, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_187("adr/ios_not_same_city(酒店-城市中心距离),其他(-1)", 187, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_188("adr/ios_key(酒店-用户距离),其他(-1)", 188, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_189("酒店档次(这一部分影响192,199,206)", 189, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_190("用户历史点击酒店次数(2周)", 190, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_192("用户历史点击当前酒店档次占比", 192, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_197("用户历史点击当前酒店占比", 197, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_199("1月(31天)内收藏酒店最大档次与当前酒店档次差", 199, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_205("用户实时点击总的次数(12h)", 205, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_206("用户实时点击次数最多的档次与当前酒店档次差", 206, FeatureValueTypeEnum.DOUBLE_TYPE),

    DNN_INDEX_212("用户实时点击当前酒店占比", 212, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_213("实时query筛选到该酒店的次数(24h)", 213, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_214("实时query筛选到该酒店的query占比(24h)", 214, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_220("用户历史180天订单量档次中，该酒店档次占比，4星及以上看档次，以下看价格(2星：0-100-200-，3星：0-400-)", 220, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_221("用户历史180天订单量中，该酒店占比", 221, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_226("用户历史180天订单量城市中，该城市占比", 226, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_227("用户历史365天订单量", 227, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_229("用户历史365天订单量档次中，该酒店档次占比，4星及以上看档次，以下看价格(2星：0-100-200-，3星：0-400-)", 229, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_230("用户历史365天订单量中，该酒店占比", 230, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_235("用户历史365天订单量城市中，该城市占比", 235, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_237("用户历史800天订单量中档次最多的订单档次与当前酒店档次差", 237, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_238("用户历史800天订单量档次中，该酒店档次占比，4星及以上看档次，以下看价格(2星：0-100-200-，3星：0-400-)", 238, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_239("用户历史800天订单量中，该酒店占比", 239, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_246("用户点击酒店与当前酒店的embedding相似度最小值", 246, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_247("用户点击酒店与当前酒店的embedding相似度最大值", 247, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_248("商业区价格", 248, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_249("酒店价格-商业区价格", 249, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_250("(酒店价格-商业区价格)/商业区价格", 250, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_264("价格敏感度(偏好值)", 264, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_287("酒店近3个月曝光点击率", 287, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_288("酒店近3个月曝光点击率(置信)", 288, FeatureValueTypeEnum.DOUBLE_TYPE),

    D25_HOUTLE_7DAY_CTR_289("酒店近7天曝光点击率", 289, FeatureValueTypeEnum.DOUBLE_TYPE),

    DNN_INDEX_290("酒店近7天曝光点击率(置信)", 290, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_291("用户预定的天数(天数1-8)", 291, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_292("用户入住天数对应的酒店入住天数的历史订单数(天数1-8)", 292, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_295("用户入住那天是否是周末", 295, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_296("用户入住那天是工作日/周末时，对应的酒店的历史工作日/周末的销售订单占比", 296, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_297("用户预订日期内包含节假日占比", 297, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_298("酒店历史订单(工作日间夜+节假日间夜)占比", 298, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_299("用户预定日期内的节假日占比-酒店历史订单节假日占比(间夜)", 299, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_300("酒店60天得pv曝光量", 300, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_303("酒店7天得pv曝光量", 303, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_304("酒店60天得pv_s2o(曝光)", 304, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_305("酒店21天得pv_s2o(曝光)", 305, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_306("酒店14天得pv_s2o(曝光)", 306, FeatureValueTypeEnum.DOUBLE_TYPE),

    D27_HOUTLE_7DAY_S2O_307("酒店近7天pv_s2o", 307, FeatureValueTypeEnum.DOUBLE_TYPE),

    DNN_INDEX_308("当前场景酒店7天的s2o(pv维度)", 308, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_309("当前场景酒店14天的s2o(pv维度)", 309, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_310("当前场景酒店21天的s2o(pv维度)", 310, FeatureValueTypeEnum.DOUBLE_TYPE),
    DNN_INDEX_311("当前场景酒店60天的s2o(pv维度)", 311, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D34
     */
    D34_QM_PRICECOMPARE_335("比价info", 335, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_ORDERS_RATE_336("可售房型历史订单占比", 336, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_SCENE_7DAY_CTR_337("7天对应场景曝光CTR", 337, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_SCENE_21DAY_CTR_338("21天对应场景曝光CTR", 338, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_SCENE_60DAY_CTR_339("60天对应场景曝光CTR", 339, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_POS_COEC_CTR_7DAY_340("7天去位置偏置曝光CTR", 340, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_POS_COEC_CTR_14DAY_341("14天去位置偏置曝光CTR", 341, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_POS_COEC_CTR_21DAY_342("21天去位置偏置曝光CTR", 342, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_CITY_7DAY_SHOW_RAT_343("该酒店7天非扶持曝光量占该城市非扶持曝光量占比", 343, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_CITY_7DAY_CLICK_RAT_344("该酒店7天点击量占该城市点击量占比", 344, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_CITY_7DAY_ORDER_RAT_345("该酒店7天订单量占该城市订单量占比", 345, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_FITMENT_YEARS_DIFF_346("当前时间于酒店装修或开业时间年份差", 346, FeatureValueTypeEnum.DOUBLE_TYPE),
    D34_HOTEL_CITY_CODE_347(" 酒店基础信息所在城市", 347, FeatureValueTypeEnum.STRING_TYPE),
    D34_HOTEL_7DAY_CVR_348("酒店7天的cvr(s2o/ctr)直接用统计好的307特征位除以289特征位", 348, FeatureValueTypeEnum.DOUBLE_TYPE),

    HOTEL_CURRENT("当前酒店seq", 349, FeatureValueTypeEnum.STRING_TYPE),
    HOTEL_LIST_24H_REALTIME_CLICK("用户24h实时点击酒店序列seq", 350, FeatureValueTypeEnum.LIST_STRING_TYPE),
    HOTEL_LIST_24H_REALTIME_CLICK_CITY("用户24h实时点击同城酒店序列seq", 351, FeatureValueTypeEnum.LIST_STRING_TYPE),
    HOTEL_LIST_48H_REALTIME_CLICK("用户48h实时点击酒店序列seq", 352, FeatureValueTypeEnum.LIST_STRING_TYPE),
    HOTEL_LIST_48H_REALTIME_CLICK_CITY("用户48h实时点击同城酒店序列seq", 353, FeatureValueTypeEnum.LIST_STRING_TYPE),

    D34_HOTEL_DISCOUNTRATE_354("酒店折扣率（预估render报价/sort报价）", 354, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D35  geo维度的数据
     */
    D35_355("酒店对应的geo_id范围内的酒店数占当前城市酒店数量的比例", 355, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_356("酒店对应的geo_id范围内的过去7天订单量占当前城市订单的比例", 356, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_357("酒店对应的geo_id 范围内的 过去7天展示量占当前城市 的比例", 357, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_358("酒店对应的 geo_id 范围内的 过去7天点击量占当前城市 的比例", 358, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_359("酒店对应的 geo_id 范围内 7天 展示价格 / 当前城市展示均价", 359, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_360("酒店周围9个geo_id 范围内的 酒店数占当前城市酒店数量的比例", 360, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_361("酒店周围9个geo_id 范围内的 过去7天订单量占当前城市 订单的比例", 361, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_362("酒店周围9个geo_id 范围内的 过去7天展示量占当前城市 的比例", 362, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_363("酒店周围9个geo_id 范围内的 过去7天点击量占当前城市 的比例", 363, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_364("酒店周围9个geo_id 范围内 7天 展示价格 / 当前城市展示均价", 364, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_365("poi对应的geo_id 范围内，当前酒店 过去7天订单占比", 365, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_366("poi对应的geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价", 366, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_367("poi对应的geo_id 范围内，当前酒店CTR / geo_id 平均CTR", 367, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_368("poi周围9个 geo_id 范围内，当前酒店 过去7天订单占比", 368, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_369("poi周围9个 geo_id 范围内，当前酒店价格 / geo_id 过去一天展示均价", 369, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_370("poi周围9个 geo_id 范围内，当前酒店CTR / geo_id 平均CTR", 370, FeatureValueTypeEnum.DOUBLE_TYPE),
    D35_371("酒店预估价格/当前城市展示价格", 371, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D36 的数据
     */
    D36_372("酒店7天的uv_ctcvr", 372, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_373("酒店21天的uv_ctcvr", 373, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_374("酒店90天的uv_ctcvr", 374, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_375("酒店7天的uv_ctr", 375, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_376("酒店21天的uv_ctr", 376, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_377("酒店90天的uv_ctr", 377, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_378("酒店分场景7天的uv_ctcvr", 378, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_379("酒店分场景21天的uv_ctcvr", 379, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_380("酒店分场景90天的uv_ctcvr", 380, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_381("酒店分场景7天的uv_ctr", 381, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_382("酒店分场景21天的uv_ctr", 382, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_383("酒店分场景90天的uv_ctr", 383, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_384("酒店7天内的下单用户数占该城市总下单用户总数的比", 384, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_385("酒店21天内的下单用户数占该城市总下单用户总数的比", 385, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_386("酒店90天内的下单用户数占该城市总下单用户总数的比", 386, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_387("酒店靠近地铁，则21天内近地铁的订单占城市所有订单占比，否则为0", 387, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_388("酒店靠近机场，则21天内近机场的订单占城市所有订单占比，否则为0", 388, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_389("酒店靠近火车站，则21天内近火车站的订单占城市所有订单占比，否则为0", 389, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_390("酒店靠近医院，则21天内近医院的订单占城市所有订单占比，否则为0", 390, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_391("酒店靠近大学，则21天内近大学的订单占城市所有订单占比，否则为0", 391, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_392("酒店靠近景区，则21天内近景区的订单占城市所有订单占比，否则为0", 392, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_393("用户搜索城市是否用户常住地", 393, FeatureValueTypeEnum.DOUBLE_TYPE),
    D36_394("用户当前请求小时(0-23)", 394, FeatureValueTypeEnum.INTEGER_TYPE),
    D36_395("入住和请求相差的天数(-1-8)+1", 395, FeatureValueTypeEnum.INTEGER_TYPE),

    /**
     * D37的特征
     * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=558568724
     */
    D37_400("当前酒店sort报价 / 用户实时点击平均价格", 400, FeatureValueTypeEnum.DOUBLE_TYPE),
    D37_401("当前酒店和同城最近点击三家酒店的平均距离", 401, FeatureValueTypeEnum.DOUBLE_TYPE),
    D37_402("当前酒店在用户实时点击五家酒店中的平均点击-下单关联性", 402, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D39 特征
     */
    D39_404("平台(adr、ios)的索引", 404, FeatureValueTypeEnum.INTEGER_TYPE),
    D39_405("场景(same_city、nearby、poi_key等)的索引", 405, FeatureValueTypeEnum.INTEGER_TYPE),

    /**
     * D40 特征
     */
    D40_410("酒店类型",410,FeatureValueTypeEnum.INTEGER_TYPE),  // HotelTypeEnum
    D40_411("酒店是否在景区内",411,FeatureValueTypeEnum.INTEGER_TYPE),

    /**
     * D41 特征
     */
    D41_412("用户三周内点击酒店的最大次数",412,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_413("用户实时点击当前酒店占比最大值",413,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_414("酒店最高报价",414,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_415("酒店平均报价",415,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_416("酒店最低报价",416,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_417("poi到酒店的最短距离",417,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_418("用户到酒店的最短距离",418,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_419("酒店卖价/原始价最小值",419,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_420("酒店24h订单价格中位数最大值",420,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_421("酒店6周最大搜索量",421,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_422("酒店最大评论数",422,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_423("酒店点击量占比最大值",423,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_424("酒店近7天曝光点击率最大值",424,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_425("酒店三个月最大单量",425,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_426("酒店3个月最大转化率",426,FeatureValueTypeEnum.DOUBLE_TYPE),
    D41_427("酒店60天得pv_s2o最大值",427,FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D42 特征
     */
    D42_430("请求城市是否地级市", 430, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_431("请求城市是否县级市", 431, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_432("常驻人口在该行政区（县级市）下的7天订单销量占比", 432, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_433("非常驻人口在该行政区（县级市）下的7天订单销量占比", 433, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_434("该城市（地级市）常驻用户7天订单量", 434, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_435("该城市（地级市）非常驻用户7天订单量", 435, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_436("该城市（地级市）常驻用户7天订单占所有订单的比例", 436, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_437("该城市（地级市）非常驻用户7天订单占所有订单的比例", 437, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_438("请求城市（地级/县级市）7天内被下单的间夜价格的标准差", 438, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_439("请求城市（地级/县级市）7天内被下单的间夜价格的平均值", 439, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_440("请求城市（地级/县级市）7天内被下单的间夜价格的最小值", 440, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_441("请求城市（地级/县级市）7天内被下单的间夜价格的中位数", 441, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_442("请求城市（地级/县级市）7天内被下单的间夜价格的最大值", 442, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_443("请求城市（地级/县级市）下的酒店个数", 443, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_444("请求城市（地级/县级市）下档次1（经济型）酒店的个数", 444, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_445("请求城市（地级/县级市）下档次2（其他）酒店的个数", 445, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_446("请求城市（地级/县级市）下档次3（舒适）酒店的个数", 446, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_447("请求城市（地级/县级市）下档次4（高档）酒店的个数", 447, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_448("请求城市（地级/县级市）下档次5（豪华）酒店的个数", 448, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_449("酒店在7天内常驻人口下的ctr", 449, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_450("酒店在7天内常驻人口下的ctcvr", 450, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_451("酒店在7天内非常驻人口下的ctr", 451, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_452("酒店在7天内非常驻人口下的ctcvr", 452, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_453("酒店在（意图非poi）下的s2d", 453, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_454("酒店在（意图非poi）下的s2o", 454, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_455("酒店在（query非poi）下的s2d", 455, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_456("酒店在（query非poi）下的s2o", 456, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_457("是否提前订", 457, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_458("用户3天内在请求城市下的点击价格均值", 458, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_459("用户3天内在请求城市下的点击价格中位数", 459, FeatureValueTypeEnum.DOUBLE_TYPE),
    D42_460("(用户3天内在请求城市下的点击价格均值-酒店价格)/用户3天内点击搜索城市价格均值", 460, FeatureValueTypeEnum.DOUBLE_TYPE),
    /**
     * D43 特征
     * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=641507692
     */
    D43_461("用户三周内点击酒店的次数/用户三周内点击酒店的最大次数",461,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_462("用户实时点击当前酒店占比/组内对应特征的最大值",462,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_463("酒店报价/组内酒店最高报价",463,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_464("酒店报价/组内酒店平均报价",464,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_465("酒店报价/组内酒店最低报价",465,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_466("poi到酒店的距离/组内对应特征的最小值",466,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_467("用户到酒店的距离/组内对应特征的最小值",467,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_468("(酒店卖价/原始价)/(酒店卖价/原始价最小值)",468,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_469("酒店24h订单价格中位数/组内对应特征的最大值",469,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_470("酒店6周搜索量/组内酒店6周最大搜索量",470,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_471("酒店评论数/组内酒店最大评论数",471,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_472("酒店点击量占比/组内酒店点击量占比最大值",472,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_473("酒店近7天曝光点击率/组内酒店7天曝光点击率最大值",473,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_474("酒店三个月单量/组内酒店三个月最大单量",474,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_475("酒店3个月转化率/组内酒店3个月最大转化率",475,FeatureValueTypeEnum.DOUBLE_TYPE),
    D43_476("酒店60天得pv_s2o/组内酒店60天得pv_s2o最大值",476,FeatureValueTypeEnum.DOUBLE_TYPE),
    // D44
    D44_477("query的召回意图个数", 477, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_478("query（主题）下的订单量gauss_rank", 478, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_479("query（类型）下的订单量gauss_rank", 479, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_480("筛选是主题下的订单量gauss_rank均值", 480, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_481("筛选的主题个数", 481, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_482("筛选是类型下的订单量gauss_rank均值", 482, FeatureValueTypeEnum.DOUBLE_TYPE),
    D44_483("筛选的类型个数", 483, FeatureValueTypeEnum.DOUBLE_TYPE),

    D45_484("用户24h内实时点击当前酒店档次占比", 484, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_485("用户24h内实时点击当前酒店星级占比", 485, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_486("用户24h内实时点击当前酒店品牌占比", 486, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_487("用户24h内实时点击当前酒店商圈占比", 487, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_488("用户24h内实时点击当前酒店类型占比", 488, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_489("用户24h内同城下实时点击当前酒店占比", 489, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_490("用户24h内同城下最近点击的是否是当前酒店", 490, FeatureValueTypeEnum.DOUBLE_TYPE),
    D45_491("酒店三个月单量RankGuass值", 491, FeatureValueTypeEnum.DOUBLE_TYPE),

    D46_492("用户优享会等级", 492, FeatureValueTypeEnum.INTEGER_TYPE),
    D46_493("Q支付价格-C支付价格", 493, FeatureValueTypeEnum.DOUBLE_TYPE),
    D46_494("Q是否beatC", 494, FeatureValueTypeEnum.DOUBLE_TYPE),
    D46_495("QCbeat率", 495, FeatureValueTypeEnum.DOUBLE_TYPE),
    D46_496("Q支付价格-E支付价格", 496, FeatureValueTypeEnum.DOUBLE_TYPE),
    D46_497("Q是否beatE", 497, FeatureValueTypeEnum.DOUBLE_TYPE),
    D46_498("QEbeat率", 498, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D47 特征 (入住日期维度的统计特征)
     * https://wiki.corp.qunar.com/confluence/pages/resumedraft.action?draftId=750302719&draftShareId=e8e781d8-0fe8-4ef8-9f49-31ff0a82eb18
     */
    D47_500("请求小时",500,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_501("酒店入住日期当天的单量",501,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_502("酒店入住日期当天一小时内的单量",502,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_503("一小时内的单量占比(502/501)",503,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_504("入住日期当天酒店间夜平均价格",504,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_505("入住日期当天一小时内酒店间夜平均价格",505,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_506("505/504",506,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_507("入住日期当天城市单量",507,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_508("酒店单量占比(501/507)",508,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_509("城市一小时内的单量",509,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_510("酒店一小时内单量占比(502/509)",510,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_511("城市平均间夜价格",511,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_512("酒店间夜价格/城市间夜价格(504/511)",512,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_513("城市前一小时间夜均价",513,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_514("酒店小时间夜价格/城市小时间夜价格(504/513)",514,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_515("geo hase id入住当天订单量",515,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_516("酒店订单占geo id单量占比(501/515)",516,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_517("geo id 占城市单量占比(515/507)",517,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_518("geo 前一小时单量",518,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_519("酒店一小时单量占geo比例(502/518)",519,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_520("geo 占城市一小时单量占比(518/509)",520,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_521("geo 间夜均价",521,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_522("酒店均价/geo 间夜均价(504/521)",522,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_523("geo 均价/ 城市间夜均价(521/511)",523,FeatureValueTypeEnum.DOUBLE_TYPE),
    D47_524("geo 一小时内单量占比(518/515)",524,FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D48 详情页实时特征
     * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=768951311
     */
    D48_525("酒店详情页停留总时长",525,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_526("进入酒店详情页次数", 526,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_527("点击房型次数", 527,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_528("点击房型的平均价格", 528,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_529("点击主图次数", 529,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_530("点击分享次数", 530,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_531("点击收藏次数", 531,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_532("点击距离次数", 532,FeatureValueTypeEnum.DOUBLE_TYPE),
    D48_533("点点击评论次数", 533,FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D49 分销提流金特SA
     */
    D49_534("是否分销提流", 534,FeatureValueTypeEnum.DOUBLE_TYPE),
    D49_535("金特SA标识", 535,FeatureValueTypeEnum.INTEGER_TYPE),
    /**
     * D50 距离优化  以及实时行为
     */
    D50_536("酒店在请求中的距离分桶排序", 536,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_537("酒店在请求中的距离前的酒店数cdf", 537,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_538("酒店在请求中的距离桶的酒店数", 538,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_539("poi类型id", 539,FeatureValueTypeEnum.INTEGER_TYPE),
    D50_540("query意图", 540,FeatureValueTypeEnum.LIST_STRING_TYPE),
    D50_541("点击房型最大价格", 541,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_542("点击房型最小价格", 542,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_543("当前酒店价格/点击房型平均价格-1", 543,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_544("停留时间的占比(所有停留时间)", 544,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_545("该酒店详情页点击占比(所有详情页次数)", 545,FeatureValueTypeEnum.DOUBLE_TYPE),
    D50_546("是否最后一次点击", 546,FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D51 缓存价格替换相关特征
     */
    D51_547("酒店报价", 547, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_548("酒店原始价格-酒店报价", 548, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_549("酒店折扣率(划线价/报价)", 549, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_550("用户下单价格-酒店报价", 550, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_551("(用户下单价格-酒店报价)/酒店报价", 551, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_552("当前酒店报价/用户实时点击平均价格", 552, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_553("当前酒店价格/点击房型平均价格-1", 553, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_554("用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格", 554, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_555("用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格", 555, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_556("用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格", 556, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_557("酒店报价-商业区价格", 557, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_558("(酒店报价-商业区价格)/商业区价格", 558, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_559("poi对应的geo_id范围内，当前酒店价格/geo_id过去一天展示均价", 559, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_560("poi周围9个geo_id范围内，当前酒店价格/geo_id过去一天展示均价", 560, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_561("酒店价格/当前城市展示价格", 561, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_562("酒店报价/组内酒店最高报价", 562, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_563("酒店报价/组内酒店平均报价", 563, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_564("酒店报价/组内酒店最低报价", 564, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_565("缓存的beat值", 565, FeatureValueTypeEnum.DOUBLE_TYPE),
    D51_566("是否有缓存报价", 566, FeatureValueTypeEnum.DOUBLE_TYPE),


    /**
     * D52 离散ID特征
     */
    D52_567("酒店品牌", 567, FeatureValueTypeEnum.STRING_TYPE),
    D52_568("酒店类型", 568, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D52_569("酒店档次", 569, FeatureValueTypeEnum.STRING_TYPE),

    D52_570("请求城市", 570, FeatureValueTypeEnum.STRING_TYPE),
    D52_571("请求地级城市", 571, FeatureValueTypeEnum.STRING_TYPE),
    D52_572("请求地级城市_请求城市", 572, FeatureValueTypeEnum.STRING_TYPE),
    D52_573("宽口径-酒店和点击酒店交叉行为序列", 573, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D52_574("酒店和下单酒店交叉行为序列", 574, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D52_575("宽口径-点击酒店行为序列", 575, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D52_576("下单酒店行为序列", 576, FeatureValueTypeEnum.LIST_STRING_TYPE),

    /**
     * D53 行为序列特征
     */
    D53_577("列表页-实时点击酒店价格序列", 577, FeatureValueTypeEnum.LIST_FLOAT_TYPE),
    D53_578("列表页-实时点击酒店档次序列", 578, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_579("列表页-实时点击酒店品牌序列", 579, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_580("列表页-实时点击酒店类型序列", 580, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_581("列表页-实时点击酒店seq序列", 581, FeatureValueTypeEnum.LIST_STRING_TYPE),

    D53_582("提交订单酒店价格序列", 582, FeatureValueTypeEnum.LIST_FLOAT_TYPE),
    D53_583("提交订单酒店档次序列", 583, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_584("提交订单酒店品牌序列", 584, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_585("提交订单酒店类型序列", 585, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D53_586("提交订单酒店seq序列", 586, FeatureValueTypeEnum.LIST_STRING_TYPE),

    D53_587("列表页-实时点击酒店价格序列的实际长度", 587, FeatureValueTypeEnum.INTEGER_TYPE),
    D53_588("提交订单酒店价格序列实际长度", 588, FeatureValueTypeEnum.INTEGER_TYPE),

    /**
     * D54 用户在C和机酒火民的特征
     */
    D54_589("用户近一年C订单量", 589, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_590("用户近一年C订单酒店数", 590, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_591("用户近一年C最大间夜价格", 591, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_592("用户近一年C最大间夜价格和酒店价格diff比", 592, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_593("用户近一年C平均间夜价格", 593, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_594("用户近一年C平均间夜价格和酒店价格diff比", 594, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_595("用户近一年C最小间夜价格", 595, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_596("用户近一年C最小间夜价格和酒店价格diff比", 596, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_597("用户近一年C最近下单时间距离请求天数", 597, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_598("用户近一年C下单酒店序列", 598, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D54_599("用户近一年C下单酒店中是否包含该酒店", 599, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_600("用户在C的点击序列", 600, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D54_601("用户在C的点击序列是否包含该酒店", 601, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_602("用户在C的book序列", 602, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D54_603("用户在C的book序列是否包含该酒店", 603, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_604("用户在C的收藏序列", 604, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D54_605("用户在C的收藏序列是否包含该酒店", 605, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_606("用户在C的点击平均价格", 606, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_607("用户在C的点击平均价格和该酒店的价格diff比", 607, FeatureValueTypeEnum.DOUBLE_TYPE),

    D54_608("平台酒店所有订单均价", 608, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_609("用户的酒店订单均价", 609, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_610("用户的酒店订单数量", 610, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_611("平台酒店所有订单均价和用户的酒店订单均价diff比", 611, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_612("用户的酒店订单均价和该酒店价格diff比", 612, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_613("平台机票所有订单均价", 613, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_614("用户的机票订单均价", 614, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_615("用户的机票订单数量", 615, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_616("平台机票所有订单均价和用户的机票订单均价diff比", 616, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_617("用户的机票订单均价和该酒店价格diff比", 617, FeatureValueTypeEnum.DOUBLE_TYPE),

    D54_618("平台火车所有订单均价", 618, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_619("用户的火车订单均价", 619, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_620("用户的火车订单数量", 620, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_621("平台火车所有订单均价和用户的火车订单均价diff比", 621, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_622("用户的火车订单均价和该酒店价格diff比", 622, FeatureValueTypeEnum.DOUBLE_TYPE),

    D54_623("平台民宿所有订单均价", 623, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_624("用户的民宿订单均价", 624, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_625("用户的民宿订单数量", 625, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_626("平台民宿所有订单均价和用户的民宿订单均价diff比", 626, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_627("用户的民宿订单均价和该酒店价格diff比", 627, FeatureValueTypeEnum.DOUBLE_TYPE),

    D54_628("poi下的平均点击距离", 628, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_629("poi下的平均点击距离和该酒店距离diff比", 629, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_630("poi关联酒店的点击距离99分位", 630, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_631("poi关联酒店的点击距离99分位和该酒店距离diff比", 631, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_632("poi平均订单距离和该酒店距离diff比", 632, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_633("poi联酒店订单99分位距离和该酒店距离diff比", 633, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_634("poi搜索下的订单距离99分位和该酒店距离diff比", 634, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_635("poi搜索下平均点击价格和该酒店价格diff比", 635, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_636("poi搜索下点击价格的9分位和该酒店价格diff比", 636, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_637("poi搜索下平均订单价格和该酒店价格diff比", 637, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_638("poi搜索下订单价格9分位和该酒店价格diff比", 638, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_639("poi搜索下成单top1酒店", 639, FeatureValueTypeEnum.STRING_TYPE),
    D54_640("poi搜索下成单top1酒店是否是该酒店", 640, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_641("poi搜索下成单top1酒店的距离", 641, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_642("poi搜索下成单top2酒店", 642, FeatureValueTypeEnum.STRING_TYPE),
    D54_643("poi搜索下成单top2酒店是否是该酒店", 643, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_644("poi搜索下成单top2酒店的距离和该酒店距离diff比", 644, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_645("poi搜索下成单top3酒店", 645, FeatureValueTypeEnum.STRING_TYPE),
    D54_646("poi搜索下成单top3酒店是否是该酒店", 646, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_647("poi搜索下成单top3酒店的距离和该酒店距离diff比", 647, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_648("poi搜索下成单top4酒店", 648, FeatureValueTypeEnum.STRING_TYPE),
    D54_649("poi搜索下成单top4酒店是否是该酒店", 649, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_650("poi搜索下成单top4酒店的距离和该酒店距离diff比", 650, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_651("poi搜索下成单top5酒店", 651, FeatureValueTypeEnum.STRING_TYPE),
    D54_652("poi搜索下成单top5酒店是否是该酒店", 652, FeatureValueTypeEnum.DOUBLE_TYPE),
    D54_653("poi搜索下成单top5酒店的距离和该酒店距离diff比", 653, FeatureValueTypeEnum.DOUBLE_TYPE),

    D54_654("加入用户券的酒店缓存报价-考虑多倍积分", 654, FeatureValueTypeEnum.DOUBLE_TYPE),

    // D55 券、活动id相关特征
    D55_655("用户活动id", 655, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_656("用户券id", 656, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_657("酒店活动id", 657, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_658("用户活动和酒店交集活动id", 658, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_659("用户活动和酒店交集活动id报名类型", 659, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_660("用户活动和酒店交集活动id促销类型", 660, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_661("酒店活动id总订单数", 661, FeatureValueTypeEnum.DOUBLE_TYPE),
    D55_662("用户活动和酒店交集活动id订单数占比", 662, FeatureValueTypeEnum.DOUBLE_TYPE),
    D55_663("酒店券id", 663, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_664("用户券和酒店券交集id", 664, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_665("用户券和酒店券id优惠形式", 665, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_666("用户券和酒店券id类型", 666, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_667("酒店券id总订单数", 667, FeatureValueTypeEnum.DOUBLE_TYPE),
    D55_668("用户券和酒店券id订单数占比", 668, FeatureValueTypeEnum.DOUBLE_TYPE),

    D55_669("列表页-实时点击酒店城市序列(2天内50条)", 669, FeatureValueTypeEnum.LIST_STRING_TYPE),
    D55_670("列表页-实时点击酒店时间序列(2天内50条)索引", 670, FeatureValueTypeEnum.LIST_INT_TYPE),

    // D56 query 有关的特征
    D56_671("query词平均订单价格",671, FeatureValueTypeEnum.DOUBLE_TYPE),
    D56_672("当前酒店价格/query词平均订单价格",672, FeatureValueTypeEnum.DOUBLE_TYPE),
    D56_673("当前酒店在query词下成单占比",673, FeatureValueTypeEnum.DOUBLE_TYPE),
    D56_674("该query词下成单总订单数",674, FeatureValueTypeEnum.DOUBLE_TYPE),
    // 小程序和app 区分, 用请求参数中的source 区分
    D56_675("是否app渠道",675, FeatureValueTypeEnum.INTEGER_TYPE),
    D56_676("是否小程序渠道",676, FeatureValueTypeEnum.INTEGER_TYPE),
    D56_677("是否非app和小程序渠道",677, FeatureValueTypeEnum.INTEGER_TYPE),
    // 小红书、抖音数据
    D56_678("当前酒店是否用户在第三方平台浏览酒店",678, FeatureValueTypeEnum.INTEGER_TYPE),
    D56_679("当前酒店价格/第三方平台浏览酒店价格",679, FeatureValueTypeEnum.DOUBLE_TYPE),

    D57_680("当前酒店城市（从seq获取）",680, FeatureValueTypeEnum.STRING_TYPE),
    D57_681("当前酒店价格分桶",681, FeatureValueTypeEnum.INTEGER_TYPE),
    D57_682("实时点击价格分桶序列",682, FeatureValueTypeEnum.LIST_INT_TYPE),

    D57_683("用户30天内周末单量", 683, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_684("用户60天内周末单量", 684, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_685("用户90天内周末单量", 685, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_686("用户30天内周末平均订单价", 686, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_687("用户60天内周末平均订单价", 687, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_688("用户90天内周末平均订单价", 688, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_689("用户90天内周末下单该酒店的单量", 689, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_690("用户90天内周末下单该档次的单量",690, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_691("酒店30天内周末单量",691, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_692("酒店30天内周末订单价格",692, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_693("酒店30天内周末uvctr",693, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_694("酒店30天内周末uvctcvr",694, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_695("酒店30天内周末单量占城市比例",695, FeatureValueTypeEnum.DOUBLE_TYPE),

    D57_696("用户30天内平日单量",696, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_697("用户60天内平日单量",697, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_698("用户90天内平日单量",698, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_699("用户30天内平日平均订单价",699, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_700("用户60天内平日平均订单价",700, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_701("用户90天内平日平均订单价",701, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_702("用户90天内平日下单该酒店的单量",702, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_703("用户90天内平日下单该档次的单量",703, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_704("酒店30天内平日单量",704, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_705("酒店30天内平日订单价格",705, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_706("酒店30天内平日uvctr",706, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_707("酒店30天内平日uvctcvr",707, FeatureValueTypeEnum.DOUBLE_TYPE),
    D57_708("酒店30天内平日单量占城市比例",708, FeatureValueTypeEnum.DOUBLE_TYPE),

    D58_709("用户是否新客",709, FeatureValueTypeEnum.DOUBLE_TYPE),
    D58_710("用户手机型号",710, FeatureValueTypeEnum.STRING_TYPE),
    D58_711("QC交叉用户过去一年在C的平均订单间夜价格", 711, FeatureValueTypeEnum.DOUBLE_TYPE),
    D58_712("QC交叉用户过去一个月在C的平均点击价格", 712, FeatureValueTypeEnum.DOUBLE_TYPE),
    D58_713("酒店是否可以用券", 713, FeatureValueTypeEnum.DOUBLE_TYPE),
    D58_714("QC交叉用户半年内在C点击过当前酒店", 714, FeatureValueTypeEnum.DOUBLE_TYPE),

    /**
     * D59 基于D54_654("加入用户券的酒店缓存报价-考虑多倍积分")替换相关特征
     */
    // from D51
    D59_715("酒店原始价格-(加入用户券的酒店缓存报价-考虑多倍积分) from 548", 715, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_716("酒店折扣率(划线价/报价) from 549", 716, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_717("用户下单价格-(加入用户券的酒店缓存报价-考虑多倍积分) from 550", 717, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_718("(用户下单价格-加入用户券的酒店缓存报价-考虑多倍积分)/(加入用户券的酒店缓存报价-考虑多倍积分) from 551", 718, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_719("当前酒店报价/用户实时点击平均价格 from 552", 719, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_720("当前酒店价格/点击房型平均价格-1 from 553", 720, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_721("用户历史180天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格 from 554", 721, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_722("用户历史365天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格 from 555", 722, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_723("用户历史800天订单量档次中，该酒店档次占比，3星以上看档次，以下看价格 from 556", 723, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_724("酒店报价-商业区价格 from 557", 724, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_725("(酒店报价-商业区价格)/商业区价格 from 558", 725, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_726("poi对应的geo_id范围内，当前酒店价格/geo_id过去一天展示均价 from 559", 726, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_727("poi周围9个geo_id范围内，当前酒店价格/geo_id过去一天展示均价 from 560", 727, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_728("酒店价格/当前城市展示价格 from 561", 728, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_729("酒店报价/组内酒店最高报价 from 562", 729, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_730("酒店报价/组内酒店平均报价 from 563", 730, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_731("酒店报价/组内酒店最低报价 from 564", 731, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_732("是否有缓存报价 from 566", 732, FeatureValueTypeEnum.DOUBLE_TYPE),
    // from D54
    D59_733("用户近一年C最大间夜价格和酒店价格diff比 from 592", 733, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_734("用户近一年C平均间夜价格和酒店价格diff比 from 594", 734, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_735("用户近一年C最小间夜价格和酒店价格diff比 from 596", 735, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_736("用户在C的点击平均价格和该酒店的价格diff比 from 607", 736, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_737("用户的酒店订单均价和该酒店价格diff比 from 612", 737, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_738("用户的机票订单均价和该酒店价格diff比 from 617", 738, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_739("用户的火车订单均价和该酒店价格diff比 from 622", 739, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_740("用户的民宿订单均价和该酒店价格diff比 from 627", 740, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_741("poi搜索下平均点击价格和该酒店价格diff比 from 635", 741, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_742("poi搜索下点击价格的9分位和该酒店价格diff比 from 636", 742, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_743("poi搜索下平均订单价格和该酒店价格diff比 from 637", 743, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_744("poi搜索下订单价格9分位和该酒店价格diff比 from 638", 744, FeatureValueTypeEnum.DOUBLE_TYPE),
    // from D56
    D59_745("当前酒店价格/query词平均订单价格 from 672",745, FeatureValueTypeEnum.DOUBLE_TYPE),
    D59_746("当前酒店价格/第三方平台浏览酒店价格 from 679",746, FeatureValueTypeEnum.DOUBLE_TYPE),
    // from D57
    D59_747("当前酒店价格分桶 - 681",747, FeatureValueTypeEnum.INTEGER_TYPE),
    ;


    /**
     * 特征描述
     */
    private final String desc;

    /**
     * 特征索引
     */
    private final Integer index;

    /**
     * 特征的取值类型
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    FeatureIndexEnum(String desc, int index, FeatureValueTypeEnum featureValueTypeEnum) {
        this.index = index;
        this.desc = desc;
        this.featureValueTypeEnum = featureValueTypeEnum;
    }

    @Getter
    private static final Map<Integer, FeatureIndexEnum> MAPIndex;

    static {
        MAPIndex = Arrays.stream(FeatureIndexEnum.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    FeatureIndexEnum(String desc, int index) {
        this.desc = desc;
        this.index = index;
    }

    /**
     * 根据key的类型将特征map 转换成libSvm 字符串
     */
    public static String toLibSvm(Map<Integer, Object> featureMap) {
        if (MapUtils.isEmpty(featureMap)) {
            return StringUtils.EMPTY;
        }

        List<String> stringList = featureMap.entrySet().stream()
                .sorted((Map.Entry<Integer, Object> a, Map.Entry<Integer, Object> b) -> (a.getKey() - (b.getKey())))
                .map(s -> {
                        FeatureIndexEnum indexEnum = MAPIndex.getOrDefault(s.getKey(), FEATURE_UNKNOWN);
                        if (indexEnum.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE
                                && Math.abs((Double) s.getValue() - 0.0) < DnnConstants.DOUBLE_ZEROS) {
                            return null;
                        }
                        String value = indexEnum.getFeatureValueTypeEnum().toString(s.getValue());
                        return s.getKey() + ":" + value;
        }).collect(Collectors.toList());
        return BLANK_JOINER.join(stringList);
    }

    /**
     * 根据key的类型将 libSvm 字符串 转换成 特征map
     */
    public static Map<Integer, Object> toObjectMap(String libSvmStr) {
        if (StringUtils.isEmpty(libSvmStr)) {
            return Collections.emptyMap();
        }

        List<String> stringList = BLANK_SPLITTER.splitToList(libSvmStr);
        if (CollectionUtils.isEmpty(stringList)) {
            return Collections.emptyMap();
        }

        // 判断第一个或者最后一个是否label
        if (!stringList.get(0).contains(COLON)) {
            stringList.remove(0);
        } else if (!stringList.get(stringList.size() - 1).contains(COLON)){
            stringList.remove(stringList.size() - 1);
        }


        Map<Integer, Object> map = Maps.newHashMap();
        for (String keyValue : stringList) {
            String[] keyAndValue = StringUtils.split(keyValue, COLON);
            if (keyAndValue.length != 2) {
                throw new RuntimeException("libsvm 必须为kv格式二元组");
            }
            int index = Integer.parseInt(keyAndValue[0]);

            FeatureIndexEnum indexEnum = MAPIndex.getOrDefault(index, FEATURE_UNKNOWN);
            Object object = indexEnum.getFeatureValueTypeEnum().toObject(keyAndValue[1]);
            map.put(index, object);
        }
        return map;
    }
}
