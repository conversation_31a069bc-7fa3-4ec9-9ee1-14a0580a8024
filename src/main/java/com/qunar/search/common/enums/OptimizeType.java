package com.qunar.search.common.enums;

/**
 * rank优化场景的类别枚举类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-9-1.
 */
public enum OptimizeType {
    PLC_NSC_EMPTY("地级市异地空搜场景", 1),
    CLC_NSC_EMPTY("非地级市异地空搜场景", 2),
    POI_TRADINGAREA_QUERY("poi和tradingArea关键词搜索场景", 3),
    NOT_POI_TRADINGAREA_QUERY("非poi和tradingArea关键词搜索场景", 4),
    NEARY_BY_NOT_KEY_WORD_QUERY("身边非关键词搜索场景", 5);

    private final int type;
    private final String name;

    OptimizeType(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
