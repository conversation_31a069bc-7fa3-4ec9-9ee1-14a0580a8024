package com.qunar.search.common.enums;

import com.google.common.collect.Range;

/**
 * ProfitRange
 *
 * <AUTHOR>
 * @date 17-01-22.
 */
public enum LiftRateRange {

    LIFT_RATE_MIN_0(Range.atMost(0.0)),
    LIFT_RATE_0_10(Range.openClosed(0.0, 0.1)),
    LIFT_RATE_10_20(Range.openClosed(0.1, 0.2)),
    LIFT_RATE_20_30(Range.openClosed(0.2, 0.3)),
    LIFT_RATE_30_40(Range.openClosed(0.3, 0.4)),
    LIFT_RATE_40_50(Range.openClosed(0.4, 0.5)),
    LIFT_RATE_50_60(Range.openClosed(0.5, 0.6)),
    LIFT_RATE_60_70(Range.openClosed(0.6, 0.7)),
    LIFT_RATE_70_80(Range.openClosed(0.7, 0.8)),
    LIFT_RATE_80_90(Range.openClosed(0.8, 0.9)),
    LIFT_RATE_90_100(Range.openClosed(0.9, 1.0)),
    LIFT_RATE_100_MAX(Range.openClosed(1.0, Double.MAX_VALUE));

    private Range<Double> range;

    LiftRateRange(Range<Double> range) {
        this.range = range;
    }

    public Range<Double> getRange() {
        return range;
    }

    public static LiftRateRange getProfitRange(double d){
        LiftRateRange res = LIFT_RATE_MIN_0;
        for (LiftRateRange liftRateRange : LiftRateRange.values()){
            if (liftRateRange.getRange().contains(d)){
                res = liftRateRange;
                break;
            }
        }
        return res;
    }

}
