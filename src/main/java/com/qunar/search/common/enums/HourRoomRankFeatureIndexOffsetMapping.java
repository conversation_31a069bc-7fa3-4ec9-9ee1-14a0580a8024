package com.qunar.search.common.enums;

/**
 * 钟点房频道排序特征
 *
 * 只有在nearby场景时才会用到这些特征, 其他场景复用全日房的特征及算分逻辑 {@link FeatureIndexOffsetMapping}
 *
 * @author: luming.lv
 * @date: 2016-10-10
 */
public enum HourRoomRankFeatureIndexOffsetMapping {

    /* 酒店距用户的距离 */
    DIST_BETWEEN_USER_AND_HOTEL(10),

    /* 历史订单档次相似度 */
    HISTORY_LEVEL_SIMILARITY(70),

    /* 钟点房价格与全日房价格的比值 */
    PRICE_RATIO(30),

    /* 酒店好评率 */
    GOOD_COMMENT(40),

    /* 酒店订单占城市订单的比率 */
    HOTEL_ORDER_CNT_RATIO(60),

    /* 划分酒店档次，如果是经济连锁或者四星，则置1 */
    HOTEL_DANGCI(70),

    /* 情侣精选 */
    COUPLE_SELECT(70),

    ;

    private int offset;

    HourRoomRankFeatureIndexOffsetMapping(int offset) {
        this.offset = offset;
    }

    public int getOffset() {
        return offset;
    }
}
