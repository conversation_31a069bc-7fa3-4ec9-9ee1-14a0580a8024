package com.qunar.search.common.enums;

/**
 * 距离枚举类
 * 
 * <AUTHOR>
 *
 */
public enum HourRoomDistance {
	ZERO_ONE_KILO(0, "1km内酒店", 0, 1000),
	ONE_THREE_KILO(1, "1-3km内酒店", 1000, 3000),
	THREE_MAX_KILO(2, "大于3km内酒店", 3000, Double.MAX_VALUE);
	int order;
    String desc;
	double start;
	double end;

	HourRoomDistance(int order, String desc, double start, double end) {
	    this.order = order;
		this.desc = desc;
		this.start = start;
		this.end = end;
	}

	public boolean match(double distance) {
		return distance < end && distance >= start;
	}


    public int getOrder() {
        return order;
    }
}
