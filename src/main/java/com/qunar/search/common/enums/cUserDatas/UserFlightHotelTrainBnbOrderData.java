package com.qunar.search.common.enums.cUserDatas;

import com.qunar.search.common.enums.FeatureValueTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 用户在机酒火民宿的订单数据
 *
 * <AUTHOR>
 * @date 2024/11/13
 */
@Getter
public enum UserFlightHotelTrainBnbOrderData {
    hotel_all_avg_order_price(1, "平台酒店订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    hotel_avg_order_price(2, "用户酒店订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    hotel_order_num(3, "用户酒店订单数量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    flight_all_avg_order_price(4, "平台机票订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    flight_avg_order_price(5, "用户机票订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    flight_order_num(6, "用户机票订单数量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    train_all_avg_order_price(7, "平台火车订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    train_avg_order_price(8, "用户火车订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    train_order_num(9, "用户火车订单数量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    bnb_all_avg_order_price(10, "平台民宿订单均价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    bnb_avg_order_price(11, "用户民宿订单数量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    bnb_order_num(12, "用户民宿订单数量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, UserFlightHotelTrainBnbOrderData> MAP;

    static {
        MAP = Arrays.stream(UserFlightHotelTrainBnbOrderData.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    UserFlightHotelTrainBnbOrderData(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static UserFlightHotelTrainBnbOrderData matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static UserFlightHotelTrainBnbOrderData matchIndex(int key) {
        return MAP.get(key);
    }
}
