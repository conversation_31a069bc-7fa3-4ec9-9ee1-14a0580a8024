package com.qunar.search.common.enums;


import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

import java.util.Map;

/**
 * @Description: 排序场景
 * @Author: wenyaol.li
 * @Date: 2019/9/26
 */
@Getter
public enum SortScene {

	/**
	 * 身边场景
	 */
	NEARBY(0, "nearby", "身边空搜"),

	/**
	 * 同城场景
	 */
	SAME_CITY(1, "same_city", "同城空搜"),

	/**
	 * 非同城场景
	 */
	NOT_SAME_CITY(2, "not_same_city", "非同城空搜"),

	/**
	 * poi 关键词场景
	 */
	POI_KEY(3, "poi_key", "poi关键词");

	private int index;
	private String name;
	private String desc;

	SortScene(int index, String name, String desc) {
		this.index = index;
		this.name = name;
		this.desc = desc;
	}

	public boolean match(SortScene sortScene) {
		return sortScene.index == index;
	}

	public static SortScene get(String name) {
		for (SortScene sortScene : SortScene.values()) {
			if (StringUtils.equals(sortScene.name, name)) {
				return sortScene;
			}
		}
		return null;
	}

    public static SortScene parseByDesc(String desc) {
        for (SortScene sortScene : SortScene.values()) {
            if (StringUtils.equals(sortScene.desc, desc)) {
                return sortScene;
            }
        }
        return null;
    }

    public static Map<Integer, SortScene> map() {
        Map<Integer, SortScene> map = Maps.newHashMap();
        for (SortScene sortScene : SortScene.values()) {
            map.put(sortScene.index, sortScene);
        }
        return map;
    }

}
