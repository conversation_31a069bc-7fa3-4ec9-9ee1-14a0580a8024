package com.qunar.search.common.enums.cUserDatas;

import com.qunar.search.common.enums.FeatureValueTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum CUserClickBookCollectData {

    USER_C_CLICK_HOTEL_LIST(1, "用户在C的点击酒店序列", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),
    USER_C_BOOK_HOTEL_LIST(2, "用户在C的book酒店序列", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),
    USER_C_COLLECT_HOTEL_LIST(3, "用户在C的收藏酒店序列", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),
    USER_C_CLICK_AVG_PRICE(4, "用户点击平均价格", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),


    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, CUserClickBookCollectData> MAP;

    static {
        MAP = Arrays.stream(CUserClickBookCollectData.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    CUserClickBookCollectData(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static CUserClickBookCollectData matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static CUserClickBookCollectData matchIndex(int key) {
        return MAP.get(key);
    }
}
