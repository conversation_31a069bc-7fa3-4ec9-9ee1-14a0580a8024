package com.qunar.search.common.enums;

import org.apache.commons.lang.StringUtils;

/**
 * SortType
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public enum SortType {

    ERROR("error", "错误"),
    ASC("asc", "比例升序"),
    DESC("desc", "比例降序"),
    ASC_ORDER("asc_order", "订单升序"),
    DESC_ORDER("desc_order", "订单降序");

    private final String name;
    private final String desc;

    SortType(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public static boolean isValidSortType(SortType sortType){
        boolean result = true;
        if (ERROR == sortType){
            result = false;
        }
        return result;
    }


    public static SortType valueOfByName(String name){
        SortType sortType = ERROR;
        if (StringUtils.isEmpty(name)){
            return sortType;
        }
        for (SortType st : SortType.values()){
            if (st.getName().equals(name)){
                sortType = st;
                break;
            }
        }
        return sortType;
    }
}
