package com.qunar.search.common.enums;

/**
 * 非地级市酒店分类枚举类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-9-1.
 */
public enum CLHotelType {
    SPECIAL_HOTEL("特定citytag酒店", 1),
    NOT_SPECIAL_HOTEL("非特定citytag酒店", 2);

    private final int type;
    private final String name;

    CLHotelType(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
