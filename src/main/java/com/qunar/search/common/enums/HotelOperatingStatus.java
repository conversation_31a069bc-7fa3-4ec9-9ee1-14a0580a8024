package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 酒店的营业状态, 只有1的时候这个酒店才算有效.
 */
public enum HotelOperatingStatus {
    //
    OPEN("营业中", 1),
    UNDER_CONSTRUCTION("筹建中", 2),
    SUSPENDED("暂停营业", 3),
    CLOSED("已停业", 4),
    REMOVED("删除", 5), ;

    private final String value;
    private final int sortingOrder;

    /**
     * @param value
     * @param sortingOrder
     */
    HotelOperatingStatus(String value, int sortingOrder) {
        this.value = value;
        this.sortingOrder = sortingOrder;
    }

    /**
     *
     */
    public String getValue() {
        return value;
    }

    public int getSortingOrder() {
        return sortingOrder;
    }

    /**
     *
     */
    public static HotelOperatingStatus parse(String value) {
        if (StringUtils.isEmpty(value)){
            return null;
        }
        for (HotelOperatingStatus hos : values()) {
            if (hos.getValue().equals(value)) {
                return hos;
            }
        }
        return null;
    }
}
