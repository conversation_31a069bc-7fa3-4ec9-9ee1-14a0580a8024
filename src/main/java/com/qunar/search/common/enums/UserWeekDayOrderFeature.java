package com.qunar.search.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum UserWeekDayOrderFeature {

    DAY_ORDER_NUM_30D(1, "用户30天内平日订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_NUM_60D(2, "用户60天内平日订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_NUM_90D(3, "用户90天内平日订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_PRICE_30D(4, "用户30天内平日平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_PRICE_60D(5, "用户60天内平日平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_PRICE_90D(6, "用户90天内平日平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_NUM_MAP(7, "用户365天内平日下每个酒店的单量", "", FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE, 50),
    DAY_GRADE_MAP(8, "用户365天内平日下每个档次的单量", "", FeatureValueTypeEnum.MAP_INT_DOUBLE_TYPE, 50),

    WEEK_ORDER_NUM_30D(9, "用户30天内周末订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_NUM_60D(10, "用户60天内周末订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_NUM_90D(11, "用户90天内周末订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_PRICE_30D(12, "用户30天内周末平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_PRICE_60D(13, "用户60天内周末平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_PRICE_90D(14, "用户90天内周末平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_NUM_MAP(15, "用户365天内周末下每个酒店的单量", "", FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE, 50),
    WEEK_GRADE_MAP(16, "用户365天内周末下每个档次的单量", "", FeatureValueTypeEnum.MAP_INT_DOUBLE_TYPE, 50),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, UserWeekDayOrderFeature> MAP;

    static {
        MAP = Arrays.stream(UserWeekDayOrderFeature.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    UserWeekDayOrderFeature(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static UserWeekDayOrderFeature matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static UserWeekDayOrderFeature matchIndex(int key) {
        return MAP.get(key);
    }
}
