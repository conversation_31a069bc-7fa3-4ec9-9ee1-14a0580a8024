package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public enum Platform {
	IOS("80", "IOS", "ios"), ADR("60", "ADR", "adr"), OTHER("81", "91", "92");

	String[] prefix;

	Platform(String... prefix) {
		this.prefix = prefix;
	}

	public String[] getPrefix() {
		return prefix;
	}

	public static Optional<Platform> match(String platform) {
		for (Platform p : Platform.values()) {
			for (String prefix : p.getPrefix()) {
				if (StringUtils.equals(platform, prefix)) {
					return Optional.of(p);
				}
			}
		}
		return Optional.empty();
	}

	public static boolean isAdrOrIos(String platform) {
		for (String prefix : Platform.ADR.prefix) {
			if (StringUtils.equals(platform, prefix)) {
				return true;
			}
		}

		for (String prefix : Platform.IOS.prefix) {
			if (StringUtils.equals(platform, prefix)) {
				return true;
			}
		}

		return false;
	}
}
