package com.qunar.search.common.enums;

import com.google.common.collect.Range;

/**
 * ProfitRange
 *
 * <AUTHOR>
 * @date 16-10-13.
 */
public enum ProfitRange {


    PROFIT_MIN_0(Range.lessThan(0.0), 1, "profit_min_0"),
    PROFIT_0_10(Range.closed(0.0, 10.0), 2, "profit_0_10"),
    PROFIT_10_20(Range.openClosed(10.0, 20.0), 3, "profit_10_20"),
    PROFIT_20_30(Range.openClosed(20.0, 30.0), 4, "profit_20_30"),
    PROFIT_30_40(Range.openClosed(30.0, 40.0), 5, "profit_30_40"),
    PROFIT_40_50(Range.openClosed(40.0, 50.0), 6, "profit_40_50"),
    PROFIT_50_60(Range.openClosed(50.0, 60.0), 7, "profit_50_60"),
    PROFIT_60_70(Range.openClosed(60.0, 70.0), 8, "profit_60_70"),
    PROFIT_70_80(Range.openClosed(70.0, 80.0), 9, "profit_70_80"),
    PROFIT_80_90(Range.openClosed(80.0, 90.0), 10, "profit_80_90"),
    PROFIT_90_100(Range.openClosed(90.0, 100.0), 11, "profit_90_100"),
    PROFIT_100_MAX(Range.openClosed(100.0, Double.MAX_VALUE), 12, "profit_100_max");

    private Range<Double> profitRange;
    private int weight;
    private String name;

    ProfitRange(Range<Double> profitRange, int weight, String name) {
        this.profitRange = profitRange;
        this.weight = weight;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Range<Double> getProfitRange() {
        return profitRange;
    }

    public void setProfitRange(Range<Double> profitRange) {
        this.profitRange = profitRange;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public static ProfitRange getProfitRange(double d){
        ProfitRange res = PROFIT_MIN_0;
        for (ProfitRange profitRange : ProfitRange.values()){
            if (profitRange.getProfitRange().contains(d)){
                res = profitRange;
                break;
            }
        }
        return res;
    }

    public static ProfitRange getProfitRange(String name){
        ProfitRange res = null;
        for (ProfitRange profitRange : ProfitRange.values()){
            if (profitRange.getName().equals(name)){
                res = profitRange;
                break;
            }
        }
        return res;
    }

}
