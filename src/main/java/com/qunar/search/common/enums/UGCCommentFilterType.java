package com.qunar.search.common.enums;

/**
 * UGCCommentFilterType
 *
 * <AUTHOR>
 * @date 17-3-20.
 */
public enum UGCCommentFilterType {

    /**
     * 仅Qunar的评论
     */
    UGC_COMMENT_NORMAL("0"),
    /**
     * Qunar的评论+第三方评论
     */
    UGC_COMMENT_TOTAL("1"),
    /**
     * Qunar点评＋第三方点评＋拼接点评
     */
    UGC_COMMENT_ADD_EXTRA_TOTAL("2");

    UGCCommentFilterType(String type) {
        this.type = type;
    }

    private String type;   //评论筛选字段选择

    public String getType() {
        return type;
    }

}
