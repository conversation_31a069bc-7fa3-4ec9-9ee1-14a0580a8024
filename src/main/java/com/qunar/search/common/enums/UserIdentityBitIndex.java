package com.qunar.search.common.enums;

/**
 * UserIdentityBitIndex
 *
 * <AUTHOR>
 * @date 16-8-19.
 */
public enum UserIdentityBitIndex {


    /**
     * 都是按照左移逻辑
     * kylin传过来的参数userIdentityBit
     * Rank和Kylin注释个别地方有不同，按Rank注释来
     */

    HALF_PRICE_USER(0, "五折用户"),
    CREDIT_USER(1, "五折授信用户"),
    HALF_PRICE_NEW_USER(2, "五折新用户"),
    FLIGHT_TRAIN_USER(3, "机火用户"),
    LIMIT_USER(4, "是否受限用户，看不到老用户核心用户报价"),
    FLIGHT_TRAIN_TRADE_USER(5, "机火当日新用户，包房特殊报价"),
    FLIGHT_TRAIN_RED_PACKAGE(6, "机火红包用户"),

    NEW_USER_DISTANCE_PRICE(13, "新用户可见距离报价"),
    RECALL_USER(19, "召回用户");



    private final int bitIndex;
    private final String name;

    UserIdentityBitIndex(int bitIndex, String name) {
        this.bitIndex = bitIndex;
        this.name = name;
    }

    public int getBitIndex() {
        return this.bitIndex;
    }

    public String getName() {
        return this.name;
    }
}
