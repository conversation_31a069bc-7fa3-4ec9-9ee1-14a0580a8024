package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * mobile-rank-search工程中的业务开关
 *
 * qconfigKey : qconfig中配置的业务开关key
 * isOpen     : 业务开关对应的开关状态
 *
 * 开关配置来源:
 * 1. depend-inter-switch.properties
 * 2. search_common.properties
 * 3. mobile_rank_system_config.properties
 *
 * 报价相关的开关没有在这里
 *
 * <strong>
 *  <p>注意: 这个类中的开关状态应该始终来源于qconfig配置, 不应该试图在代码中直接修改任何一个开关的状态<p/>
 * <strong/>
 *
 * @author: luming.lv
 * @date: 2016-09-22
 */
public enum MobileRankSearchBusinessSwitchConfig {

    /**
     * configs from depend-inter-switch.properties
     */
    HOTEL_TO_HOTEL("hotelToHotel", true),

    FAKE_LM("fakeLm", true),

    PLANE_ORDER_DETAIL("planeOrderDetail", true),

    QUICK("quick", true),

    RECOMMAND("recoommend", true),

    CHANNEL_CITY_LOG_OPEN("channelCityLogOpen", true),

    BROWSE_HISTORY_ENTRANCE("browseHistoryEntrance", true),

    MEMCACHE_READ("memcachedRead", true),

    MEMCACHE_WRITE("memcachedWrite", true),

    QUERY_AROUND_MEMCACHE_READ("queryAroundMemcachedRead", true),

    QUERY_AROUND_MEMCACHE_WRITE("queryAroundMemcachedWrite", true),

    REAL_TIME_ROOM_STATUS("realtimeRoomstatusSwitch", true),

    /* 包房自动设置优先级开关开关控制  */
    BAOFANG_AUTO_LEVEL("baofangAutoLevel", true),

    /* 包房自动设置优先级打印业务日志开关控制 */
    BAOFANG_AUTO_LEVEL_LOG("baofangAutoLevelLog", true),

    /* 两屏以后也用逻辑回归排序开关控制 */
    LR_SORT_ALL_HOTELS("lrSortAllHotels", true),

    PRINT_PC_LIST_RESULT("printPCListResultLog", true),

    /* 推荐排序并发sort评估开关 */
    REC_SORT_ACCESS("recSortAccess", true),

    /* 强排序并发sort评估开关 */
    PRICE_SORT_ACCESS("priceSortAccess", true),

    /* 打印sort评估日志开关  */
    PRINT_ACCESS_LOG("printAccessLog", true),

    /* 国际酒店房量紧张开关 */
    ROOM_NERVOUS("roomNervousSwitch", true),

    /* 国际是否使用收益模型 */
    PROFIT_MODEL_FOR_INTERNATIONAL_HOTELS("profitModelForInternationalHotels", true),

    RENDER_NOT_EQ_MONITOR("renderNotEqMonitor", true),

    HOUR_ROOM_SALES_COUNT_OPEN("hourRoomSalesCountOpen", true),

    /**
     * qhotel 开启新接口服务
     */
    QHOTEL_NEW_VERSION_SWITCH("qhotelNewVersionSwitch",false),

    /**
     * configs from search_common.properties
     */
    /* 国际酒店是否显示折扣 */
    ABROAD("ABROAD", true),

    /* 国际酒店采用目的地当地时间 */
    INTERNATIONAL_DEST_TIME("isDestTimeOpen", true),

    /**
     * 是否打开分页调用hprice的逻辑.
     */
    HPRICE_PAGE_SWITCH_KEY("hprice_page_open", false),

    /**
     * configs from mobile_rank_system_config.properties
     */

    REDIS_USERPROFILE("REDIS_USERPROFILE", true),

    REDIS_GPS("REDIS_GPS", true),

    REDIS_REALTIME("REDIS_REALTIME", true),

    REDIS_REALTIME_CLICK("REDIS_REALTIME_CLICK", true),

    REDIS_REALTIME_FILTER("REDIS_REALTIME_FILTER", true),

	REDIS_REALTIME_USERNAME_MAPPING("REDIS_REALTIME_USERNAME_MAPPING", true),

    ZHEKOU_BAOFANG_TUIGUANG("zhekouBaofangTuiguang", true),

    FILTER_SORT_SWITCH("FILTER_SORT_SWITCH",false),

    /* 直通车广告酒店开关 */
    ADVERTISE_IS_OPEN("ADVERTISE_IS_OPEN", true),


    /**
     * configs from depend-inter-switch.properties
     */

    /* 酒店包装  query场景下差异化展示开关 */
    QUERY_DIFF_DISPLAY("isQueryDiffDisplay", true),

    /* 榜单开关 */
    BANGDAN_SWITCH("bangdanSwitchStatus", true),


    /**
     * configs from depend-inter-switch.properties
     */

    ZHEKOU_LABEL("zhekou_label", true),

    CEM_BEAT_PRICE_LABEL("cem_beat_price_label", true),

    BEST_SELLER_LABEL("best_seller_label", true),

    GOOD_REPUTATION_LABEL("good_reputation_label", true),

    POSITION_LABEL("position_label", true),

    /* 灵活排序开关 */
    FLEXIBLE_ADJUST("FLEXIBLE_ADJUST", true),

    /* 拒单页酒店推荐 */
    REFUSE_ORDER_TO_HOTEL("REFUSE_ORDER_TO_HOTEL_SWITCH", true),

    NOT_SAME_CITY_TUIGUANG_LEVEL_RE_RANK_STRATEGY("NOT_SAME_CITY_TUIGUANG_LEVEL_RE_RANK_STRATEGY", true),

    MOCK_USER_FEATURE("MOCK_USER_FEATURE_SWITCH", true),

    PROMOTION_STRATEGY_SWITCH("promotionStrategySwitch", true),
    PROMOTION_BY_RANK_SCORE_STRATEGY_SWITCH("promotionByRankScoreStrategySwitch", true),
    PROMOTION_EXPOSURE_PROFIT_STRATEGY_SWITCH("promotionExposureProfitStrategySwitch", true),
    PROMOTION_EXPOSURE_STRATEGY_SWITCH("promotionExposureStrategySwitch", true),
    PROMOTION_PROFIT_STRATEGY_SWITCH("promotionProfitStrategySwitch", true),
    PROMOTION_UNIFIED_STRATEGY_SWITCH("promotionUnifiedStrategy", true),
    //PromotionClickStrategy
    PROMOTION_CLICK_STRATEGY_SWITCH("PromotionClickStrategySwitch", true),
    PROMOTION_ROUTER_EXCUTER_STRATEGY_SWITCH("promotionRouterExcuterStrategySwitch", true),

    TUIGUANG_EXPOSURE_CACHE_SYNC_SWITCH("tuiguangExposureCacheSyncSwitch", true),

    TOP_ONE_ADJUST_STRATEGY_SWITCH("topOneAdjustStrategySwitch", true),
    UNBOOK_HOTEL_FRONT_STRATEGY_SWITCH("unbookHotelFrontStrategySwitch", true),


    /* 加载数据开关 */
    UPDATE_DATA_HOTEL_ADVERTISE_SWITCH("updateDataHotelAdvertiseSwitch", true),
    /* 加载数据开关end */

    //是否吐城市价格梯度数据
    VIEW_OF_CITY_PRICE_RANGE_SWITCH("viewOfCityPriceRangeSwitch", true),

    REDIS_PLATFORM_USER_PROFILE_SWITCH("redisPlatformUserProfileSwitch", true),

    // 增加场景的欢迎度距离开关
    SAMECITY_POPULARITY_LIMIT_DISTANCE("same_city_popular_limit_distance",true),
    NEARBY_POPULARITY_LIMIT_DISTANCE("nearby_popular_limit_distance",true),
    SAMECITY_CURRENT_POPULARITY_LIMIT_DISTANCE("same_city_current_day_popular_limit_distance",true),
    SAMECITY_NOT_CURRENT_POPULARITY_LIMIT_DISTANCE("same_city_not_current_day_popular_limit_distance",true),

	PAGING_CACHE("paging_cache_switch", false),
    ;

    MobileRankSearchBusinessSwitchConfig(String qconfigKey, boolean status) {
        this.qconfigKey = qconfigKey;
        this.isOpen = status;
    }

    private String qconfigKey;
    private boolean isOpen;

    public boolean isOpen() {
        return isOpen;
    }

    public String getQconfigKey() {
        return qconfigKey;
    }

    /**
     * reload开关状态
     *
     * @param conf
     */
    public static void reloadBusinessSwitchConfig(Map<String, String> conf) {
        if (conf == null) {
            return;
        }
        for (MobileRankSearchBusinessSwitchConfig itemConfig : MobileRankSearchBusinessSwitchConfig.values()) {
            String qconfigValue = conf.get(itemConfig.getQconfigKey());
            if (qconfigValue != null) {
                itemConfig.isOpen = true;
                if (isClose(qconfigValue)) {
                    itemConfig.isOpen = false;
                }
            }
        }
    }

    private static boolean isClose(String value) {
        value = StringUtils.trim(value);
        return "0".equals(value) || "false".equals(StringUtils.lowerCase(value));
    }

    /**
     * 获取该类中所有开关的状态string
     *
     * @return
     */
    public static String configToString() {
        StringBuilder builder = new StringBuilder("MobileRankSearchBusinessSwitchConfig status:");
        for (MobileRankSearchBusinessSwitchConfig itemConfig : MobileRankSearchBusinessSwitchConfig.values()) {
            builder.append(itemConfig.toString()).append(";");
        }
        return builder.toString();
    }

    /**
     * 根据关键词获取开关
     * @param switchName
     * @return
     */
    public static MobileRankSearchBusinessSwitchConfig valueOfQconfigKey(String switchName){
        for (MobileRankSearchBusinessSwitchConfig itemConfig : MobileRankSearchBusinessSwitchConfig.values()) {
            if (itemConfig.getQconfigKey().equals(switchName)){
                return itemConfig;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return qconfigKey + "=" + isOpen;
    }
}
