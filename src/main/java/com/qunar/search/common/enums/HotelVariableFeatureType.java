package com.qunar.search.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2020-08-11 下午4:05
 * @DESCRIPTION 特征数据枚举类
 **/
@Getter
public enum HotelVariableFeatureType {

    ACTIVITY_ID_SET(1, "酒店活动id", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),

    ACTIVITY_BAOMING_TYPE_MAP(2, "酒店活动id的报名类型", "", FeatureValueTypeEnum.MAP_STRING_STRING_TYPE, 100),

    ACTIVITY_CLASSIFICATION_MAP(3, "酒店活动id的促销类型", "", FeatureValueTypeEnum.MAP_STRING_STRING_TYPE, 100),

    ACTIVITY_ORDER_CNT_MAP(4, "酒店活动id的促销订单数", "", FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE, 100),

    PREFER_ID_SET(5, "酒店券id", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),

    PREFER_WAY_MAP(6, "酒店券id优惠形式", "", FeatureValueTypeEnum.MAP_STRING_STRING_TYPE, 100),

    PREFER_VOUCHERUSE_MAP(7, "酒店券id类型", "", FeatureValueTypeEnum.MAP_STRING_STRING_TYPE, 100),

    PREFER_ORDER_CNT_MAP(8, "酒店券id下的订单数", "", FeatureValueTypeEnum.MAP_STRING_DOUBLE_TYPE, 100),

    DAY_ORDER_NUM_7D(9, "酒店7日内平日订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_NUM_30D(10, "酒店30日内平日订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_PRICE_7D(11, "酒店7日内平日平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_PRICE_30D(12, "酒店30日内平日平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_NUM_7D(13, "酒店7日内周末订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_NUM_30D(14, "酒店30日内周末订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_PRICE_7D(15, "酒店7日内周末平均订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_PRICE_30D(16, "酒店30日内周末订单价", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TIQIAN_ORDER_NUM_7D(17, "酒店7日内提前订订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    CUR_DAY_ORDER_NUM_7D(18, "酒店7日内当天订订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TIQIAN_ORDER_NUM_30D(19, "酒店30日内提前订订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    CUR_DAY_ORDER_NUM_30D(20, "酒店30日内当天订订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NO_LIANZHU_ORDER_NUM_7D(21, "酒店7日内非连住订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    LIANZHU_ORDER_NUM_7D(22, "酒店7日内连住订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NO_LIANZHU_ORDER_NUM_30D(23, "酒店30日内非连住订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    LIANZHU_ORDER_NUM_30D(24, "酒店30日内连住订单量", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_UV_CTR_30D(25, "酒店30日内平日uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_UV_CTCVR_30D(26, "酒店30日内平日uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    DAY_ORDER_RATE_CITY_30D(27, "酒店30日内平日订单占城市占比", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_UV_CTR_30D(28, "酒店30日内周末uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_UV_CTCVR_30D(29, "酒店30日内周末uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    WEEK_ORDER_RATE_CITY_30D(30, "酒店30日内周末订单占城市占比", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    SAME_CITY_DAY_UV_CTR_30D(31, "酒店30日内同城平日uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NEARBY_DAY_UV_CTR_30D(32, "酒店30日内身边平日uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NOT_SAME_CITY_DAY_UV_CTR_30D(33, "酒店30日内异地平日uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    POI_DAY_UV_CTR_30D(34, "酒店30日内poi平日uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    SAME_CITY_WEEK_UV_CTR_30D(35, "酒店30日内同城周末uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NEARBY_WEEK_UV_CTR_30D(36, "酒店30日内身边周末uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NOT_SAME_CITY_WEEK_UV_CTR_30D(37, "酒店30日内异地周末uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    POI_WEEK_UV_CTR_30D(38, "酒店30日内poi周末uv_ctr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    SAME_CITY_DAY_UV_CTCVR_30D(39, "酒店30日内同城平日uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NEARBY_DAY_UV_CTCVR_30D(40, "酒店30日内身边平日uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NOT_SAME_CITY_DAY_UV_CTCVR_30D(41, "酒店30日内异地平日uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    POI_DAY_UV_CTCVR_30D(42, "酒店30日内poi平日uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    SAME_CITY_WEEK_UV_CTCVR_30D(43, "酒店30日内同城周末uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NEARBY_WEEK_UV_CTCVR_30D(44, "酒店30日内身边周末uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    NOT_SAME_CITY_WEEK_UV_CTCVR_30D(45, "酒店30日内异地周末uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    POI_WEEK_UV_CTCVR_30D(46, "酒店30日内poi周末uv_ctcvr", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    HOTEL_NO_COUPONS(47, "酒店不可以使用优惠券", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list\map时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, HotelVariableFeatureType> MAP;

    static {
        MAP = Arrays.stream(HotelVariableFeatureType.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    HotelVariableFeatureType(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }
    public static HotelVariableFeatureType matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static HotelVariableFeatureType matchIndex(int key) {
        return MAP.get(key);
    }

}
