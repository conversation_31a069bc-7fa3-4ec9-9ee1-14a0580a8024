package com.qunar.search.common.enums;

/**
 * planeHotel频道酒店排序特征
 *
 * 目前的代码中planeHotel频道与今日特惠频道复用一部分同样的算分逻辑和特征, 这部分相
 * 同的特征统一引用自{@link LastMinuteRankFeatureIndexOffsetMapping}, planeHotel频道中用到的其余
 * 的特征从该类中引用
 *
 * @author: luming.lv
 * @date: 2016-10-11
 */
public enum PlaneHotelRankFeatureIndexOffsetMapping {

    /* 过去半年预订酒店次数 */
    HOTEL_ORDER_CNT_LAST_6_MONTH(100),

    /* 过去半年用户在该城市最后一次订单为当前酒店 */
    LAST_ORDER_IN_CURRENT_CITY(200),

    /* 酒店平均价格与当前phoneType订单价格和关系 */
    SIMILARITY_BETWEEN_HOTEL_AND_PHONE_TYPE_MU_ST(210),

    /* 酒店近三周的订单量分段 */
    ORDER_CNT_OF_HOTEL_IN_LAST_3_WEEKS(300),

    /* 酒店评分, 分13段 */
    COMMENT_SCORE_OF_HOTEL(400),

    /* 酒店距地标关键词的距离 */
    DIST_BETWEEN_HOTEL_AND_POI(600),

    /* 酒店价格与飞机仓位之间的相似度 */
    SIMILARITY_BETWEEN_HOTEL_PRICE_AND_CLASS_TYPE(700),

    /* 酒店星级与飞机仓位之间的相似度 */
    SIMILARITY_BETWEEN_HOTEL_STAR_AND_CLASS_TYPE(750),

    /* 酒店档次与飞机仓位之间的相似度 */
    SIMILARITY_BETWEEN_HOTEL_DANGCI_AND_CLASS_TYPE(770),

    /* 酒店附近有机场 */
    HAS_AIRPORT_NEARBY(790),

    /* 过去三周浏览和点击酒店的次数 */
    SHOW_CLICK_CNT_IN_LAST_3_WEEKS(1000),

    /* 过去三周到达填单页 */
    REACH_ORDER_PAGE_IN_LAST_3_WEEKS(1200),

    /* 过去三周在该酒店下过单 */
    ORDERED_IN_LAST_3_WEEKS(1300),

    /* 酒店历史订单均价与用户历史订单均价距离分段 */
    HISTORY_PRICE_SIMILARITY(2001),

    /* 酒店档次相似度 */
    SIMILARITY_BETWEEN_HOTEL_DANGCI_AND_USER_HISTORY_ORDERS(2014),

    /* 酒店星级相似度 */
    SIMILARITY_BETWEEN_HOTEL_STAR_AND_USER_HISTORY_ORDERS(2015),

    /* 酒店品牌相似度 */
    SIMILARITY_BETWEEN_HOTEL_BRAND_AND_USER_HISTORY_ORDERS(2016),

    /* 酒店类型相似度 */
    SIMILARITY_BETWEEN_HOTEL_TYPE_AND_USER_HISTORY_ORDERS(2017),

    /* 酒店商圈相似度 */
    SIMILARITY_BETWEEN_HOTEL_TRADING_AND_USER_HISTORY_ORDERS(2018),

    /* 4天之前收藏过该酒店 */
    HISTORY_COLLECTION(2019),

    /* 4天之内收藏过该酒店 */
    CURRENT_COLLECTION(2020),

    ;

    private int offset;

    PlaneHotelRankFeatureIndexOffsetMapping(int offset) {
        this.offset = offset;
    }

    public int getOffset() {
        return offset;
    }
}
