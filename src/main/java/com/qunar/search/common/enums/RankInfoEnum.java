package com.qunar.search.common.enums;

import com.qunar.search.common.util.BitUtils;

/**
 * 包含老榜单、挂牌、试睡等逻辑
 * <ul>
 * <li>榜单用于判断榜单类型见HotelBangdanSupport.parseBangdanType</li>
 * <li>挂牌用于回传酒店挂牌类型，见com.qunar.search.service.RankInfoLabel#addTuiguangMedalLabel</li>
 * <li>其它为筛选见RankInfoFilterParser</li>
 * </ul>
 */
public enum RankInfoEnum {
	//缺少0位
    LM_IS_LOW_PRICE(1, "夜宵价是否为全网最低"),
    BNB_SELECT(3, "客栈优选"),
    COUPLE_SELECT(4, "情侣精选"),
    BEST_BARGAIN(5, "最佳性价比"),
    TRAVEL_SELECT(6, "商旅优选酒店"),
    BEST_HOLIDAY(7, "最佳度假酒店"),
    BEST_FOODIE(8, "最佳吃货酒店"),
    BEST_TRAVEL_AROUND(9, "最佳周边游"),
    COUPLE(10, "情侣"),
    TRAVEL(11, "商旅"),
    PARENT_OFFSPRING(12, "亲子"),
    HOLIDAY(13, "度假"),
    BUDGET_TRAVEL(14, "穷游"),
    ALONE_TRAVEL(15, "独自旅行"),
    SEASCAPE(16, "海景"),
    COMPANY_TRAVEL(17, "朋友结伴"),
    CINEMA(18, "电影周边"),
    RESTAURANT(19, "美食周边"),
    SHOPPING(20, "购物周边"),
    ENTERTAINMENT(21, "娱乐周边"),
    LOW_PRICE(22, "低价酒店"),
    LOW_PRICE_HIGH_STAR(23, "低价高星"),
    QUERY_5_DISCOUNT(24, "关键词搜五折大促"),

    BEST_SEASCAPE(26, "最佳海景酒店"),
    BETS_SHOPPING(27, "最佳购物酒店"),
    BEST_COUPLE(28, "最佳情侣酒店"),
    BEST_PARENT_OFFSPRING(29, "最佳亲子酒店"),

    BEST_HEALTH(30, "最佳卫生酒店"),
    BEST_KEZHAN(31, "优选客栈"),
    HOUR_HIGH_STAR_LOW_PRICE(32, "钟点房高星低价"),
    HOT_SPRING(33, "最佳温泉酒店"),
    SMART_TRIP(34, "聪明旅行者之选"),

    HOUR_ROOM_COUPLE(35, "钟点房情侣"),
    HOUR_ROOM_DISCOUNT(36, "钟点房折扣"),
    LM_WALK(37, "夜销步行可达"),
    LM_HIGH_STAR_LOW_PRICE(38, "夜销高星低价"),
    DISNEY(39, "迪士尼套票"),
    BEST_SELECT(40, "优选酒店推荐(综合榜单)"),
    BRAND_RECOMMEND(41, "品牌力荐"),
    BARGAIN_SECKILL(42, "特价频道秒杀"),
    BARGAIN_SALES(43, "特价频道特惠"),
    SLEEP_PRICE(47, "试睡活动"),
    TUIGUANG_SPECIAL(48, "推广特牌酒店"),
    TUIGUANG_GOLD(49, "推广金牌酒店"),
    TUIGUANG_SILVER(50, "推广银牌酒店")
    ;

    private final int bitIndex;
    private final String name;
    private final long value;

    RankInfoEnum(int bitIndex, String name) {
        this.bitIndex = bitIndex;
        this.name = name;
		if (bitIndex == 0) {
			this.value = 1L;
		} else {
			this.value = 1L << bitIndex;
		}
	}

    public int getBitIndex() {
        return bitIndex;
    }

    public String getName() {
        return name;
    }

	public long getValue() {
		return value;
	}

	public boolean isTrue(long input) {
		return (this.value & input) == this.value;
	}

	public long addBit(long input) {
		return this.value | input;
	}

	public long removeBit(long input) {
		return BitUtils.changeBitValue(input, this.bitIndex + 1, true);
	}

	public static boolean matchAll(long input, RankInfoEnum... infos) {
		long state = 0L;
		for (RankInfoEnum info : infos) {
			state = state | info.value;
		}
		return (state & input) == state;
	}

	public static boolean matchAny(long rankInfo, RankInfoEnum... infos) {
		for (RankInfoEnum info : infos) {
			if ((info.getValue() & rankInfo) == rankInfo) {
				return true;
			}
		}
		return false;
	}
}