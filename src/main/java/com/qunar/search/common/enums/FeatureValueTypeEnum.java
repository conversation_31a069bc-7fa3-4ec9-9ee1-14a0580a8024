package com.qunar.search.common.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.qunar.search.common.constants.CommonConstants.*;

public enum FeatureValueTypeEnum {
    /**
     * int 类型 特征
     */
    INTEGER_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return Integer.toString(0);
            }
            return Integer.toString((int) object);
        }

        @Override
        public Object toObject(String value) {
            return Integer.parseInt(value);
        }

        @Override
        public Object toObject(String value, int size)
        {
            return Integer.parseInt(value);
        }
    },

    /**
     * float 类型 特征
     */
    FLOAT_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return Float.toString(0);
            }
            return Float.toString((float) object);
        }

        @Override
        public Object toObject(String value) {
            return Float.parseFloat(value);
        }

        @Override
        public Object toObject(String value, int size) {
            return Float.parseFloat(value);
        }
    },

    /**
     * double 类型特征
     */
    DOUBLE_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return Double.toString(0);
            }
            return Double.toString((double) object);
        }

        @Override
        public Object toObject(String value) {
            return Double.parseDouble(value);
        }

        @Override
        public Object toObject(String value, int size) {
            return Double.parseDouble(value);
        }
    },

    /**
     * string 类型特征
     */
    STRING_TYPE {
        @Override
        public String toString(Object object) {
            return (String) object;
        }

        @Override
        public Object toObject(String value) {
            return value;
        }

        @Override
        public Object toObject(String value, int size) {
            return value;
        }
    },

    /**
     * list int 类型的序列特征
     */
    LIST_INT_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }
            return toString((List) object, INTEGER_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return toList(value, INTEGER_TYPE);
        }

        @Override
        public Object toObject(String value, int size) {
            return toList(value, INTEGER_TYPE, size);
        }
    },

    /**
     * list float 类型的序列特征
     */
    LIST_FLOAT_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }
            return toString((List) object, FLOAT_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return toList(value, FLOAT_TYPE);
        }

        @Override
        public Object toObject(String value, int size) {
            return toList(value, FLOAT_TYPE, size);
        }
    },

    /**
     * list double 类型的序列特征
     */
    LIST_DOUBLE_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }
            return toString((List) object, DOUBLE_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return toList(value, DOUBLE_TYPE);
        }

        @Override
        public Object toObject(String value, int size) {
            return toList(value, DOUBLE_TYPE, size);
        }
    },

    /**
     * list string 类型的序列特征
     */
    LIST_STRING_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }
            return toString((List) object, STRING_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return toList(value, STRING_TYPE);
        }

        @Override
        public Object toObject(String value, int size) {
            return toList(value, STRING_TYPE, size);
        }
    },

    /**
     * map<String,String> 类型的特征
     */
    MAP_STRING_STRING_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }

            return toString((Map) object, STRING_TYPE, STRING_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return stringToMap(value, STRING_TYPE, STRING_TYPE, Integer.MAX_VALUE);
        }

        @Override
        public Object toObject(String value, int size) {
            return stringToMap(value, STRING_TYPE, STRING_TYPE, size);
        }
    },

    /**
     * map<String,DOUBLE> 类型的特征
     */
    MAP_STRING_DOUBLE_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }

            return toString((Map) object, STRING_TYPE, DOUBLE_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return stringToMap(value, STRING_TYPE, DOUBLE_TYPE, Integer.MAX_VALUE);
        }

        @Override
        public Object toObject(String value, int size) {
            return stringToMap(value, STRING_TYPE, DOUBLE_TYPE, size);
        }
    },

    /**
     * map<int,DOUBLE> 类型的特征
     */
    MAP_INT_DOUBLE_TYPE {
        @Override
        public String toString(Object object) {
            if (object == null) {
                return "";
            }

            return toString((Map) object, INTEGER_TYPE, DOUBLE_TYPE);
        }

        @Override
        public Object toObject(String value) {
            return stringToMap(value, INTEGER_TYPE, DOUBLE_TYPE, Integer.MAX_VALUE);
        }

        @Override
        public Object toObject(String value, int size) {
            return stringToMap(value, INTEGER_TYPE, DOUBLE_TYPE, size);
        }
    },

    ;

    public abstract String toString(Object object);

    public abstract Object toObject(String value);

    public abstract Object toObject(String value, int size);

    protected static List<Object> toList(String value, FeatureValueTypeEnum typeEnum) {
        if (StringUtils.isBlank(value)) {
            return Collections.emptyList();
        }

        List<String> stringList = COMMA_SPLITTER.splitToList(value);

        List<Object> objectList = Lists.newArrayList();
        for (String str : stringList) {
            objectList.add(typeEnum.toObject(str));
        }
        return objectList;
    }

    protected static List<Object> toList(String value, FeatureValueTypeEnum typeEnum, int size) {
        if (StringUtils.isBlank(value)) {
            return Collections.emptyList();
        }

        List<String> stringList = COMMA_SPLITTER.splitToList(value);
        int min = Math.min(stringList.size(), size);
        List<String> subStringList = stringList.subList(0, min);
        List<Object> objectList = Lists.newArrayList();
        for (String str : subStringList) {
            objectList.add(typeEnum.toObject(str));
        }
        return objectList;
    }

    protected static String toString(List<Object> list, FeatureValueTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }

        List<Object> objectList = Lists.newArrayList();
        for (Object object : list) {
            objectList.add(typeEnum.toString(object));
        }

        return COMMA_JOINER.join(objectList);
    }

    protected static String toString(Map<Object, Object> map, FeatureValueTypeEnum keyTypeEnum, FeatureValueTypeEnum valueTypeEnum) {
        if (MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }

        List<Object> objectList = Lists.newArrayList();
        for (Map.Entry<Object, Object> en: map.entrySet()) {
            objectList.add(keyTypeEnum.toString(en.getKey()) + AND_AND + valueTypeEnum.toString(en.getValue()));
        }

        return COMMA_JOINER.join(objectList);
    }

    public static Map<Object, Object> stringToMap(String value, FeatureValueTypeEnum keyTypeEnum, FeatureValueTypeEnum valueTypeEnum, int size) {
        if (StringUtils.isEmpty(value)) {
            return Collections.emptyMap();
        }

        List<String> sList = COMMA_SPLITTER.splitToList(value);
        if (org.springframework.util.CollectionUtils.isEmpty(sList)) {
            return Collections.emptyMap();
        }

        Map<Object, Object> map = new HashMap<>();
        int subSize = Math.min(sList.size(), Math.max(size, 1));
        for (int i = 0; i < subSize; i++) {
            List<String> stringList = AND_AND_SPLITTER.splitToList(sList.get(i));
            if (stringList.size() < 2) {
                continue;
            }
            Object key = keyTypeEnum.toObject(stringList.get(0));
            Object val = valueTypeEnum.toObject(stringList.get(1));
            map.put(key, val);
        }
        return map;
    }
}
