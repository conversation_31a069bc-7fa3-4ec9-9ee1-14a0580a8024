package com.qunar.search.common.enums;

/**
 * 酒店档次分类.
 */
public enum HotelDangciType {
	ECONOMY(1,"经济型", 10),
	COMFORT(2,"三星及舒适", 30),
	HIGHEND(3,"四星及高档", 40),
	LUXURY(4,"五星及豪华", 50),
	OTHER(5,"二星及其他", 20);
	
	private final int dangci;
	private final String dangciName;
	private final int priority;

	HotelDangciType(int dangci, String dangciName, int priority) {
		this.dangci = (byte)dangci;
		this.dangciName = dangciName;
		this.priority = priority;
	}

	public int getDangci() {
		return dangci;
	}
	public String getDangciName() {
		return dangciName;
	}

	public int getPriority() {
		return priority;
	}

	public static HotelDangciType parse(int dangci) {
		try{
			for(HotelDangciType hdType : HotelDangciType.values()){
				if(hdType.getDangci() == dangci){
					return hdType;
				}
			}
		}catch(Exception e){
			return OTHER;
		}
		
		return OTHER;
	}
	
}
