package com.qunar.search.common.enums.cUserDatas;

import com.qunar.search.common.enums.FeatureValueTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum CUserOrderData {

    USER_C_ONE_YEAR_ORDER_CNT(1, "用户近一年订单量","", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_HOTEL_CNT(2, "用户近一年订单酒店数","", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_MAX_ROOM_PRICE(3, "用户近一年最大间夜价格","", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_AVG_ROOM_PRICE(4, "用户近一年平均间夜价格","", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_MIN_ROOM_PRICE(5, "用户近一年最小间夜价格","", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_MAX_DATE(6, "用户近一年最近下单时间","", FeatureValueTypeEnum.STRING_TYPE, 0),
    USER_C_ONE_YEAR_ORDER_HOTEL_LISTS(7, "用户近一年下单酒店","", FeatureValueTypeEnum.LIST_STRING_TYPE, 100),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, CUserOrderData> MAP;

    static {
        MAP = Arrays.stream(CUserOrderData.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    CUserOrderData(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static CUserOrderData matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static CUserOrderData matchIndex(int key) {
        return MAP.get(key);
    }
}
