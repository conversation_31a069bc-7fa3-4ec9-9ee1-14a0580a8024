package com.qunar.search.common.enums;

/**
 * Created by andy on 2016/12/28.
 */
public enum UGCCommentType {

    UGC_COMMENT_MORE_THAN_500("500条以上", "UGC_COMMENT_COUNT_MORE_THAN_500", 500),

    UGC_COMMENT_MORE_THAN_100("100条以上", "UGC_COMMENT_COUNT_MORE_THAN_100", 100),

    UGC_COMMENT_MORE_THAN_50("50条以上", "UGC_COMMENT_COUNT_MORE_THAN_50", 50),

    UGC_COMMENT_MORE_THAN_20("20条以上", "UGC_COMMENT_COUNT_MORE_THAN_20", 20);



    UGCCommentType(String name, String key, int count) {
        this.name = name;
        this.key = key;
        this.count = count;
    }

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }

    public int getCount() {
        return count;
    }

    private String name;   //评论类型中文
    private String key;    //评论类型,英文
    private int count;     // 数目
}
