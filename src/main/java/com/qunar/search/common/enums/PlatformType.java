package com.qunar.search.common.enums;

/**平台类型, updated by fangxue.zhang
 * Created by weilin.lian on 2016/8/18.
 */
public enum PlatformType {
    ADR("60", "adr"),
    IOS("80", "ios"),
    IPAD("81", "ipad"),
    TOUCH("91", "touch"),
    PAD_TOUCH("92", "padTouch");
    private final String code;
    private final String desc;

    PlatformType(String codeTmp, String descTmp) {
        this.code = codeTmp;
        this.desc = descTmp;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PlatformType getByCode(String code){
        for(PlatformType platform : values()){
            if(platform.getCode().equals(code))
                return platform;
        }
        return null;
    }
}
