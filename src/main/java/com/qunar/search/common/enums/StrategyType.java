package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * StrategyType
 *
 * <AUTHOR>
 * @date 16-11-15.
 */
public enum StrategyType {


    /* 默认 */
    NO_SUCH_STRATEGY(0, "NoSuchStrategy"),
    /* 黑名单 */
    BLACKLIST_STRAGETY(1, "BlackListStrategy"),
    /* 直通车 */
    ADVERTISE_STRATEGY(2, "AdvertiseStrategy"),
    /* 针对榜单位置策略　*/
    CHANGE_BANGDAN_HOTEL_POSTIONS_STRATEGY(3, "ChangeBangdanHotelPostionsStrategy"),
    /* 定制列表页　*/
    CUSTOMIZE_HOTEL_LIST_STRATEGY(4, "CustomizeHotelListStrategy"),
    /* 针对用户强插订单酒店　*/
    INSERT_SPECIAL_HOTEL_STRATEGY(5, "InsertSpecialHotelStrategy"),
    /* 夜宵推广　*/
    LM_PROMOTION_STRATEGY(6, "LmPromotionStrategy"),
    /* 身边搜索第二位处理　*/
    NEARBY_FORCE_ONE_STRATEGY(7, "NearByForceOneStrategy"),
    /* 酒店推广针对全日房酒店, 钟点房酒店　*/
    PROMOTION_STRATEGY(8, "PromotionStrategy"),
    ZHEKOU_BAOFANG_PROMOTION_STRATEGY(9, "ZhekouBaofangPromotionStrategy"),
    BAOFANG_AUTO_LEVEL_STRATEGY(10, "BaofangAutoLevelStrategy"),
    PROMOTION_BY_RANK_SCORE_STRATEGY(11, "PromotionByRankScoreStrategy"),
    /* 灵活排序 */
    FLEXIBLE_ADJUST_STRATEGY(12, "FlexibleAdjustStrategy"),

    NOT_SAME_CITY_TUIGUANG_LEVEL_RE_RANK_STRATEGY(13, "NotSameCityTuiguangLevelRerankStrategy"),

    /* 酒店推广针对全日房酒店,曝光限制及rank分值*收益　*/
    PROMOTION_EXPOSURE_PROFIT_STRATEGY(14, "PromotionExposureProfitStrategy"),
    PROMOTION_EXPOSURE_STRATEGY(15, "PromotionExposureStrategy"),
    PROMOTION_PROFIT_STRATEGY(16, "PromotionProfitStrategy"),


    TOP_ONE_ADJUST_STRATEGY(17, "TopOneAdjustStrategy"),
    UNBOOK_HOTEL_FRONT_STRATEGY(18, "UnbookHotelFrontStrategy"),

    PROMOTION_UNIFIED_STRATEGY(19, "PromotionUnifiedStrategy"),
    //PromotionClickStrategy
    PROMOTION_CLICK_STRATEGY(20, "PromotionClickStrategy"),
    /* 国际酒店满房强插，紧迫感　*/
    INTERNATIONAL_FULL_ROOM_STRATEGY(21, "InternationalFullRoomStrategy"),
	CONFIGURABLE_STRATEGY(22,"ConfigurableStrategy"),
	//国际限时优惠
	INTERNATIONAL_LIMITED_TIME_DISCOUNT_STRATEGY(23, "InternationalLimitedTimeDiscountStrategy"),
	// 欢迎度策略
	POPULARITY_STRATEGY(24, "PopularityStrategy"),
	// 重点酒店策略
	KEY_HOTEL_STRATEGY(25, "KeyHotelStrategy"),
    //模型策略
    MODEL_SCENE_STRATEGY(26, "ModelSceneStrategy"),
    // 途家民宿扶持策略
    TUJIA_HOMESTAY_HOTEL_HELP_STRATEGY(27, "TujiaHomestayHotelHelpStrategy"),
    // 大搜落地页今日甩房推广活动
    DASOU_SALE_TONIGHT_STRATEGY(28, "DasouSaleTonightStrategy"),
    //促销降权升权自然分策略
    PROMOTION_RULE_STRATEGY(29, "PromotionRuleStrategy"),
    //质量降权升权自然分策略
    QUALITY_SCORE_RULE_STRATEGY(30, "QualityScoreRuleStrategy"),
    //top n 酒店随机排序策略
    TOP_N_RAND_STRATEGY(31, "TopNRandStrategy"),
	//特价频道，售卖广告酒店
	BARGAIN_ADVERTISE_STRATEGY(32, "BargainAdvertiseStrategy")
    ;


    private int type;
    private String name;

    StrategyType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static boolean isValidStrategy(String strategy){
        boolean result = false;
        if (StringUtils.isEmpty(strategy)){
            return result;
        }
        for (StrategyType s : StrategyType.values()){
            if (s != NO_SUCH_STRATEGY && s.getName().equals(strategy)){
                result = true;
                break;
            }
        }
        return result;
    }

}
