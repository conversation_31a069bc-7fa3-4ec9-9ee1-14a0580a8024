package com.qunar.search.common.enums;

/**
 * 按照房态排序的时候, 集中分类.
 */

public enum MobileSortingGroup {

    BOOKABLE("无线可订", 1, 5),
    TEL_BOOKABLE("无线电话", 2, 4),
    UNBOOKABLE_A("无线不可订A组", 3, 3),
    UNBOOKABLE_B("无线不可订B组", 4, 2),
    NO_PRICE("无线无报价", 5, 1);

    private final int order;
    private final String name;
    private final int score;

    MobileSortingGroup(String name, int order, int score) {
        this.name = name;
        this.order = order;
        this.score = score;
    }

    public int getOrder() {
        return order;
    }

    public String getName() {
        return name;
    }

    public int getScore() {
        return score;
    }
}