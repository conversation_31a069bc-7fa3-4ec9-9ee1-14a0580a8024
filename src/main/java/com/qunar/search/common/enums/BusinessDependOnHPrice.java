package com.qunar.search.common.enums;

/**
 * 依赖无线报价接口的各个业务
 *
 * @author: luming.lv
 * @date: 2016-06-16
 */
public enum BusinessDependOnHPrice {

    /**
     * 酒店列表
     */
    HOTEL_LIST("query_hotel_list", "main"),

    /**
     * 榜单列表
     */
    BANGDAN("query_bangdan_list", "hl"),

    /**
     * 钟点房列表
     */
    HOUR_ROOM("query_hour_room_list", "hourly"),

    /**
     * 钟点房推荐夜销酒店
     */
    HOUR_ROOM_REC_LM("query_hour_room_rec_lm_list", "hourreclm"),

    /**
     * 钟点房列表请求全日房列表最低价 用来rank排序
     */
    HOUR_ROOM_HOTEL_MINPRICE_FOR_RANK("query_hour_room_hotelMinPriceForRank", "hourmain"),

    /**
     * 今日特惠列表
     */
    LAST_MINUTE("query_last_minute_list", "lm"),

    /**
     * query ids 接口
     */
    QUERY_IDS("query_ids", "other"),

    /**
     * 夜宵提前预售
     */
    PRE_SELL_LM("query_pre_sell", "prelm"),

    /**
     * 酒店频道 酒店推荐酒店
     */
    HOTEL_TO_HOTEL("query_hotel_to_hotel_list", "hrh"),

//    /* 钟点房频道 酒店推荐酒店 */
//    HOUR_ROOM_HOTEL_TO_HOTEL("query_hour_room_hotel_to_hotel_list"),

    /**
     * 夜宵频道 酒店推荐酒店
     */
    LM_HOTEL_TO_HOTEL("query_lm_hotel_to_hotel_list", "lmhrh"),

    /**
     * query 接口 plane_hotel 频道
     */
    PLANE_HOTEL("query_plane_hotel_list", "ph"),

    /**
     * queryMyHotel 接口浏览历史
     */
    BROWSE_HISTORY("queryMyHotel_browse_history_list", "history"),

    /**
     * queryMyHotel 酒店收藏
     */
    HOTEL_COLLECTION("queryMyHotel_hotel_collection_list", "collect"),

    /**
     * querySpecialHotel 接口
     */
    QUERY_SPECIAL_HOTEL("querySpecialHotel_list", "sph"),

    /**
     * rank_helper调查排序使用
     */
    RANK_HELPER("rank_helper_list", "other"),

    /**
     * app首页卡片酒店列表
     */
    HOME_PAGE_CARD("home_page_card", "hpr"),
    /**
     * 包房酒推酒
     */
    HOST_HOTEL("host_hotel", "hh"),
    /**
     * 折扣专区
     */
    DISCOUNT_HOTEL("discount_hotel", "dsth"),
    /**
     * 位置周边
     */
    LOCAL_TOUR_HOTEL("local_tour_hotel", "lth"),
    /**
     * 景点周边
     */
    LOCAL_TOUR_SIGHT("local_tour_sight", "lts"),
    /**
     * 本单可享
     */
    ORDER_SHARE_HOTEL("order_share_hotel", "osh"),
    /**
     * 特惠酒店
     */
    SPECIAL_OFFER_HOTEL("special_offer_hotel", "soh"),
    /**
     * 主题酒店
     */
    THEME_HOTEL("theme_hotel", "th"),

    /**
     * 钟点房运营位推荐酒店
     */
    HOUR_RECOMMEND_HOTELS("hour_recommend_hotels","hourlyrh"),

    /**
     * innvotion Suggestion优化 加入价格
     */
    SUGGESTION_HOTELS("suggestion_hotels", "sugstn"),

    /**
     * 今日甩房
     */
    LM_LIMIT_BUY("lm_limit_buy", "lmlb"),

    /**
     * 拒单页推荐
     */
    REFUSE_ORDER_TO_HOTEL("refuse_order_to_hotel", "roth"),

    /**
     * 优选酒店
     */
    HOTEL_PREFER("hotel_prefer", "hpfer"),

    /**
     * 搜索框推荐
     */
    SEARCH_BAR_REC("search_bar_rec", "sbr"),

    /**
     * 团队房列表
     */
    TEAM_ROOM_LIST("team_room_list", "trl"),

    /**
     * tujia客栈
     */
    TUJIA_INN("tujia_inn", "tujiaInn"),

    /**
     * poi场景化推荐。https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=565995495
     */
    POI_SCENE_RECOMMEND("poi_scene_recommend", "poiscenerec"),

    /**
     * 默认空
     */
    UNKNOWN("DEFAULT_NULL_SRC", "other");


    private String sourceId;
    private String label;

    BusinessDependOnHPrice(String sourceId, String label) {
        this.sourceId = sourceId;
        this.label = label;
    }

    public String getSourceId() {
        return sourceId;
    }

    public String getLabel() {
        return label;
    }
}
