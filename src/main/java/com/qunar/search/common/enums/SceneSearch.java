package com.qunar.search.common.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * SortScene
 *
 * 提出来用户搜索的场景，后续可以删除多余场景类，合并冗余代码
 * 可以看LrBucket，就这几种场景排序
 *
 * <AUTHOR>
 * @date 16-10-17.
 */
public enum SceneSearch {

    /**
     * 身边空搜
     */
    NEARBY_SEARCH(1, "nearby", "身边空搜"),

    /**
     * 同城空搜
     */
    SAME_CITY_SEARCH(2, "same_city", "同城空搜"),

    /**
     * 非同城空搜
     */
    NOT_SAME_CITY_SEARCH(3, "not_same_city", "非同城空搜"),
    /**
     * poi关键词
     */
    POI_KEY_SEARCH(4, "poi_key", "poi关键词"),

    /**
     * area关键词
     */
    AREA_KEY_SEARCH(5, "area_key", "area关键词"),

    /**
     * 正常关键词搜索，非poi非商圈关键词
     */
    NOT_POI_AREA_KEY_SEARCH(6, "not_poi_area_key", "非poi非商圈关键词"),

    /**
     * 同城当天, 更关注距离特征
     */
    SAME_CITY_CURRENT_DAY(7, "same_city_current_day", "同城当天"),

    /**
     * 异地当天
     */
    SAME_CITY_NOT_CURRENT_DAY(8, "same_city_not_current_day", "同城非当天"),

    /**
     * 关键词场景
     */
    KEY_SEARCH(9, "key", "关键词"),
    /**
     * 默认所有场景
     */
    ALL_SEARCH(10, "all", "所有场景"),
    /**
     *
     */
    ERROR(-1, "error", "错误");

    private int type;
    private String name;
    private String desc;

    SceneSearch(int type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static List<SceneSearch> validScenes4App(){
        List<SceneSearch> sceneSearchList = Lists.newArrayList();
        sceneSearchList.add(NEARBY_SEARCH);
        sceneSearchList.add(SAME_CITY_SEARCH);
        sceneSearchList.add(NOT_SAME_CITY_SEARCH);
        sceneSearchList.add(KEY_SEARCH);
        return sceneSearchList;
    }

    public static boolean isValidScene4App(SceneSearch scene){
        boolean result = false;
        if (validScenes4App().contains(scene)){
            result = true;
        }
        return result;
    }

    public static List<SceneSearch> validScenes4Pc(){
        List<SceneSearch> sceneSearchList = Lists.newArrayList();
        sceneSearchList.add(SAME_CITY_SEARCH);
        sceneSearchList.add(KEY_SEARCH);
        return sceneSearchList;
    }

    public static boolean isValidScene4Pc(SceneSearch scene){
        boolean result = false;
        if (validScenes4Pc().contains(scene)){
            result = true;
        }
        return result;
    }


    public static SceneSearch valueOfByName(String name){
        SceneSearch sceneSearch = ERROR;
        if (StringUtils.isEmpty(name)){
            return sceneSearch;
        }
        for (SceneSearch ss : SceneSearch.values()){
            if (ss.getName().equals(name)){
                sceneSearch = ss;
                break;
            }
        }
        return sceneSearch;
    }















}
