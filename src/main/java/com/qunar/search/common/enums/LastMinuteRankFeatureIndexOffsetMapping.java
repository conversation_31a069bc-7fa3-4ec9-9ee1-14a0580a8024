package com.qunar.search.common.enums;

/**
 * 今日特惠频道rank排序特征
 *
 * 在nearby场景中, 今日特惠频道的算分逻辑有一部分继承自钟点房频道, 这部分在
 * 填充特征时统一从{@link HourRoomRankFeatureIndexOffsetMapping}中引用,
 * 另外有一部分算分逻辑和排序特征与酒店频道复用同一份, 这部分特征引用自{@link FeatureIndexOffsetMapping}
 * 其余独立的部分特征从该类中引用
 *
 * 注意:
 * 这里的大部分特征和酒店频道所用的特征重复{@link FeatureIndexOffsetMapping},
 * 而没有直接复用{@link FeatureIndexOffsetMapping}的原因是目前代码中夜销频道的部分算分逻辑与酒店频道的算分逻辑独立无关
 *
 *
 * @author: luming.lv
 * @date: 2016-10-11
 */
public enum LastMinuteRankFeatureIndexOffsetMapping {

    /* 酒店距用户的距离 */
    DIST_BETWEEN_USER_AND_HOTEL(10),

    /* 过去半年预订酒店次数 */
    HOTEL_ORDER_CNT_LAST_6_MONTH(100),

    /* 用户历史订单档次命中当前酒店 */
    DANGCI_SIMILARITY_OF_HISTORY_ORDER(140),

    /* 用户历史订单星级命中当前酒店 */
    STARS_SIMILARITY_OF_HISTORY_ORDER(150),

    /* 用户历史订单品牌命中当前酒店 */
    BRAND_SIMILARITY_OF_HISTORY_ORDER(160),

    /* 用户历史订单酒店类型命中当前酒店 */
    HOTEL_TYPE_SIMILARITY_OF_HISTORY_ORDER(170),

    /* 用户历史订单商圈命中当前酒店 */
    TRADING_SIMILARITY_OF_HISTORY_ORDER(180),

    /* 过去半年用户在该城市最后一次订单为当前酒店 */
    LAST_ORDER_IN_CURRENT_CITY(200),

    /* 酒店平均价格与当前phoneType订单价格和关系 */
    SIMILARITY_BETWEEN_HOTEL_AND_PHONE_TYPE_MU_ST(210),

    /* 酒店近三周的订单量分段 */
    ORDER_CNT_OF_HOTEL_IN_LAST_3_WEEKS(300),

    /* 酒店评分, 分13段 */
    COMMENT_SCORE_OF_HOTEL(400),

    /* 用户到酒店的距离 */
    DIST_BETWEEN_HOTEL_AND_USER(620),

    /* 过去三周浏览和点击酒店的次数 */
    SHOW_CLICK_CNT_IN_LAST_3_WEEKS(1000),

    /* 过去三周到达填单页 */
    REACH_ORDER_PAGE_IN_LAST_3_WEEKS(1200),

    /* 过去三周在该酒店下过单 */
    ORDERED_IN_LAST_3_WEEKS(1300),

    /* 酒店历史订单均价与用户历史订单均价距离分段 */
    HISTORY_PRICE_SIMILARITY(2001),

    /* 4天之前收藏过该酒店 */
    HISTORY_COLLECTION(2019),

    /* 4天之内收藏过该酒店 */
    CURRENT_COLLECTION(2020),

    ;

    private int offset;

    LastMinuteRankFeatureIndexOffsetMapping(int offset) {
        this.offset = offset;
    }

    public int getOffset() {
        return offset;
    }
}
