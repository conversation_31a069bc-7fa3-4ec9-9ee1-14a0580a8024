package com.qunar.search.common.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2020-08-11 下午4:05
 * @DESCRIPTION 特征数据枚举类
 **/
@Getter
public enum FeatureType {

    ORD_CNT_6_MONTH(1, "酒店6个月订单量", "0"),

    ORD_CNT_3_MONTH(2, "酒店3个月订单量", "1"),

    ORD_CNT_3_WEEK(3, "酒店3周订单量", "2"),

    SEARCH_CNT_6_MONTH(4, "酒店6个月搜索量", "3"),

    SEARCH_CNT_3_MONTH(5, "酒店3个月搜索量", "4"),

    SEARCH_CNT_3_WEEK(6, "酒店3周搜索量", "5"),

    RAW_S2O_6_MONTH(7, "酒店6个月s2o原始值", "6"),

    CTR_S2O_6_MONTH(8, "酒店6个月s2o置信值", "7"),

    RAW_S2O_3_MONTH(9, "酒店3个月s2o原始值", "8"),

    CTR_S2O_3_MONTH(10, "酒店3个月s2o置信值", "9"),

    RAW_S2O_3_WEEK(11, "酒店3周s2o原始值", "10"),

    CTR_S2O_3_WEEK(12, "酒店3周s2o置信值", "11"),

    COMMENT_SCORE(13, "评分", "0"),

    COMMENT_COUNT(14, "评论数", "1"),

    CLEAN_NEG_LABEL(15, "干净度--评论负向标签", "2"),

    CLEAN_POS_LABEL(16, "干净度--评论正向标签", "3"),

    TRAFFIC_NEG_LABEL(17, "交通--评论负向标签", "4"),

    TRAFFIC_POS_LABEL(18, "交通--评论正向标签", "5"),

    COST_PERFORM_NEG_LABEL(19, "性价比--评论负向标签", "6"),

    COST_PERFORM_POS_LABEL(20, "性价比--评论正向标签", "7"),

    SERVICE_NEG_LABEL(21, "服务--评论负向标签", "8"),

    SERVICE_POS_LABEL(22, "服务--评论正向标签", "9"),

    FACILITY_NEG_LABEL(23, "设施--评论负向标签", "10"),

    FACILITY_POS_LABEL(24, "设施--评论正向标签", "11"),

    ANTI_NOISE_NEG_LABEL(25, "安静度--评论负向标签", "12"),

    ANTI_NOISE_POS_LABEL(26, "安静度--评论正向标签", "13"),

    ECRM(27, "偏置信息", "0"),

    MEITUAN_ROOM_PRICE(28, "美团房价", "0"),

    MEITUAN_ORIGIN_ROOM_PRICE(29, "美团原始房价", "1"),

    HOLIDAY_PRICE_WEIGHT(30, "节假日酒店价格加权系数", "0"),

    BIZ_ZONE_MEDIAN_PRICE(31, "商圈价格中位数", "0"),

    ORD_MEDIAN_24H(32, "24小时订单中位数", "0"),

    ORD_MEDIAN_30D(33, "30天订单中位数", "1"),

    ORD_MEDIAN_90D(34, "90天订单中位数", "2"),

    ORD_MEDIAN_400D(35, "400天订单中位数", "3"),

    COMPLAIN_RATE(36, "投诉率", "0"),

    ORD_CNT_6_MONTH_INDEX(37, "酒店6个月订单量,离线分段后索引", "0"),

    ORD_CNT_3_MONTH_INDEX(38, "酒店3个月订单量,离线分段后索引", "1"),

    ORD_CNT_3_WEEK_INDEX(39, "酒店3周订单量,离线分段后索引", "2"),

    S2O_6_MONTH_INDEX(40, "酒店6个月s2o,离线分段后索引", "3"),

    S2O_3_MONTH_INDEX(41, "酒店3个月s2o,离线分段后索引", "4"),

    S2O_3_WEEK_INDEX(42, "酒店3周s2o,离线分段后索引", "5"),

    COMMENT_CNT_INDEX(43, "评论数,离线分段后索引", "0"),

    COMMENT_SCORE_INDEX(44, "评分,离线分段后索引", "1"),

    COMMENT_GOOD_RATE_INDEX(45, "好评率,离线分段后索引", "2"),

    MAN_PREFECT(46, "订单用户偏好--男性", "1"),

    WOMAN_PREFECT(47, "订单用户偏好--女性", "0"),

    GENDER_UN_KNOW_PERFECT(48, "订单用户偏好--性别未知", "-1"),

    AGE_0_25_PREFECT(49, "订单用户偏好--年龄0_25", "0"),

    AGE_25_30_PREFECT(50, "订单用户偏好--年龄25_30", "1"),

    AGE_30_35_PREFECT(51, "订单用户偏好--年龄30_35", "2"),

    AGE_35_40_PREFECT(52, "订单用户偏好--年龄35_40", "3"),

    AGE_40_100_PREFECT(53, "订单用户偏好--年龄40_100", "4"),

    AGE_UN_KNOW_PERFECT(54, "订单用户偏好--年龄未知", "-1"),

    LOW_PRICE_SENS_PREFECT(55, "订单用户偏好--低价格敏感度", "0"),

    HIGH_PRICE_SENS_PREFECT(56, "订单用户偏好--高价格敏感度", "1"),

    PRICE_SENS_UN_KNOW_PERFECT(57, "订单用户偏好--价格敏感度不确定", "-1"),

    HAS_MEITUAN_PREFECT(58, "订单用户偏好--安装美团", "1"),

    NO_MEITUAN_PREFECT(59, "订单用户偏好--未安装美团", "0"),

    MEITUAN_UN_KNOW_PERFECT(60, "订单用户偏好--是否安装美团未知", "-1"),

    HAS_CTRIP_PREFECT(61, "订单用户偏好--安装携程", "1"),

    NO_CTRIP_PREFECT(62, "订单用户偏好--未安装携程", "0"),

    CTRIP_UN_KNOW_PERFECT(63, "订单用户偏好--是否安装携程未知", "-1"),

    HAS_ELONG_PREFECT(64, "订单用户偏好--安装艺龙", "1"),

    NO_ELONG_PREFECT(65, "订单用户偏好--未安装艺龙", "0"),

    ELONG_UN_KNOW_PERFECT(66, "订单用户偏好--是否安装艺龙未知", "-1"),

    HAS_COMPETE_PREFECT(67, "订单用户偏好--安装竞品", "1"),

    NO_COMPETE_PREFECT(68, "订单用户偏好--未安装竞品", "0"),

    COMPETE_UN_KNOW_PERFECT(69, "订单用户偏好--是否安装竞品未知", "-1"),

    HOTEL_FLIGHT_TT_PREFECT(70, "一年内有酒店订单，有机票订单用户偏好", "TT"),

    HOTEL_FLIGHT_TF_PREFECT(71, "一年内有酒店订单，没有机票订单用户偏好", "TF"),

    HOTEL_FLIGHT_FT_PREFECT(72, "一年内没有酒店订单，有机票订单用户偏好", "FT"),

    HOTEL_FLIGHT_FF_PREFECT(73, "一年内没有酒店订单，没有机票订单用户偏好", "FF"),

    HOTEL_TRAIN_TT_PREFECT(74, "一年内有酒店订单，有火车票订单用户偏好", "TT"),

    HOTEL_TRAIN_TF_PREFECT(75, "一年内有酒店订单，没有火车票订单用户偏好", "TT"),

    HOTEL_TRAIN_FT_PREFECT(76, "一年内没有酒店订单，有火车票订单用户偏好", "TT"),

    HOTEL_TRAIN_FF_PREFECT(77, "一年内没有酒店订单，没有火车票订单用户偏好", "TT"),

    HOTEL_TICKET_TT_PREFECT(78, "一年内有酒店订单，有门票订单用户偏好", "TT"),

    HOTEL_TICKET_TF_PREFECT(79, "一年内有酒店订单，没有门票订单用户偏好", "TT"),

    HOTEL_TICKET_FT_PREFECT(80, "一年内没有酒店订单，有门票订单用户偏好", "TT"),

    HOTEL_TICKET_FF_PREFECT(81, "一年内没有酒店订单，没有门票订单用户偏好", "TT"),

    HOTEL_VACATION_TT_PREFECT(82, "一年内有酒店订单，有度假订单用户偏好", "TT"),

    HOTEL_VACATION_TF_PREFECT(83, "一年内有酒店订单，没有度假订单用户偏好", "TT"),

    HOTEL_VACATION_FT_PREFECT(84, "一年内没有酒店订单，有度假订单用户偏好", "TT"),

    HOTEL_VACATION_FF_PREFECT(85, "一年内没有酒店订单，没有度假订单用户偏好", "TT"),

    SAME_CITY_ORDER_RATE(86, "酒店同城场景订单占比", "same_city"),

    NOT_SAME_CITY_ORDER_RATE(87, "酒店非同城场景订单占比", "not_same_city"),

    WEEKEND_ORDER_RATE(88, "酒店周末订单占比", ""),

    HOLIDAY_ORDER_RATE(89, "节假日订单占比", ""),

    HEAD_IMAGE_SCORE(90, "头图分", ""),

    IMAGE_NUM(91, "酒店图片数量", ""),

    CTR_3_MONTH(92, "3个月点击率", ""),

    CTR_3_MONTH_ZHIXIN(93, "3个月点击率--置信", ""),

    CTR_7_DAY(94, "7天点击率", ""),

    CTR_7_DAY_ZHIXIN(95, "7天点击率--置信", ""),

    NO_WEEKEND_ORDER_RATE(96, "非周末订单占比", ""),

    NO_HOLIDAY_ORDER_RATE(97, "非节假日订单占比", ""),

    WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE(98, "酒店过去365天内订单的间夜是周末及节假日占比", ""),

    NO_WEEKEND_HOLIDAY_ORDER_MIDNIGHT_RATE(99, "酒店过去365天内订单的间夜是非周末及节假日占比", ""),

    CHECK_IN_ORDER_NUM_1_DAY(100, "酒店过去365天内被下单入住1天的订单数", ""),

    CHECK_IN_ORDER_RATE_1_DAY(101, "酒店过去365天内被下单入住1天的订单占比", ""),

    CHECK_IN_ORDER_NUM_2_DAY(102, "酒店过去365天内被下单入住2天的订单数", ""),

    CHECK_IN_ORDER_RATE_2_DAY(103, "酒店过去365天内被下单入住2天的订单占比", ""),

    CHECK_IN_ORDER_NUM_3_DAY(104, "酒店过去365天内被下单入住3天的订单数", ""),

    CHECK_IN_ORDER_RATE_3_DAY(105, "酒店过去365天内被下单入住3天的订单占比", ""),

    CHECK_IN_ORDER_NUM_4_DAY(106, "酒店过去365天内被下单入住4天的订单数", ""),

    CHECK_IN_ORDER_RATE_4_DAY(107, "酒店过去365天内被下单入住4天的订单占比", ""),

    CHECK_IN_ORDER_NUM_5_DAY(108, "酒店过去365天内被下单入住5天的订单数", ""),

    CHECK_IN_ORDER_RATE_5_DAY(109, "酒店过去365天内被下单入住5天的订单占比", ""),

    CHECK_IN_ORDER_NUM_6_DAY(110, "酒店过去365天内被下单入住6天的订单数", ""),

    CHECK_IN_ORDER_RATE_6_DAY(111, "酒店过去365天内被下单入住6天的订单占比", ""),

    CHECK_IN_ORDER_NUM_7_DAY(112, "酒店过去365天内被下单入住7天的订单数", ""),

    CHECK_IN_ORDER_RATE_7_DAY(113, "酒店过去365天内被下单入住7天的订单占比", ""),

    CHECK_IN_ORDER_NUM_8_DAY(114, "酒店过去365天内被下单入住大于等于8天的订单数", ""),

    CHECK_IN_ORDER_RATE_8_DAY(115, "酒店过去365天内被下单入住大于等于8天的订单占比", ""),

    DISPLAY_SEARCH_PV_60_DAY(116, "酒店过去60天内曝光PV", ""),

    DISPLAY_SEARCH_PV_21_DAY(117, "酒店过去21天内曝光PV", ""),

    DISPLAY_SEARCH_PV_14_DAY(118, "酒店过去14天内曝光PV", ""),

    DISPLAY_SEARCH_PV_7_DAY(119, "酒店过去7天内曝光PV", ""),

    DISPLAY_PV_S2O_60_DAY(120, "酒店过去60天内曝光s2o", ""),

    DISPLAY_PV_S2O_21_DAY(121, "酒店过去21天内曝光s2o", ""),

    DISPLAY_PV_S2O_14_DAY(122, "酒店过去14天内曝光s2o", ""),

    DISPLAY_PV_S2O_7_DAY(123, "酒店过去7天内曝光s2o", ""),

    NEARBY_SCENE_S2O_7_DAY(124, "酒店nearby过去7天内s2o", ""),

    SAMECITY_SCENE_S2O_7_DAY(125, "酒店same_city过去7天内s2o", ""),

    NOTSAMECITY_SCENE_S2O_7_DAY(126, "酒店not_same_city过去7天内s2o", ""),

    POIKEY_SCENE_S2O_7_DAY(127, "酒店poi_key过去7天内s2o", ""),

    NEARBY_SCENE_S2O_14_DAY(128, "酒店nearby过去14天内s2o", ""),

    SAMECITY_SCENE_S2O_14_DAY(129, "酒店same_city过去14天内s2o", ""),

    NOTSAMECITY_SCENE_S2O_14_DAY(130, "酒店not_same_city过去14天内s2o", ""),

    POIKEY_SCENE_S2O_14_DAY(131, "酒店poi_key过去14天内s2o", ""),

    NEARBY_SCENE_S2O_21_DAY(132, "酒店nearby过去21天内s2o", ""),

    SAMECITY_SCENE_S2O_21_DAY(133, "酒店same_city过去21天内s2o", ""),

    NOTSAMECITY_SCENE_S2O_21_DAY(134, "酒店not_same_city过去21天内s2o", ""),

    POIKEY_SCENE_S2O_21_DAY(135, "酒店poi_key过去21天内s2o", ""),

    NEARBY_SCENE_S2O_60_DAY(136, "酒店nearby过去60天内s2o", ""),

    SAMECITY_SCENE_S2O_60_DAY(137, "酒店same_city过去60天内s2o", ""),

    NOTSAMECITY_SCENE_S2O_60_DAY(138, "酒店not_same_city过去60天内s2o", ""),

    POIKEY_SCENE_S2O_60_DAY(139, "酒店poi_key过去60天内s2o", ""),

    ADR_SHOW_PV_7_DAY(167,"酒店在安卓上7天展示量",""),

    IOS_SHOW_PV_7_DAY(168,"酒店在苹果上7天展示量",""),

    ADR_ORD_PV_7_DAY(169,"酒店在安卓上7天订单量",""),

    IOS_ORD_PV_7_DAY(170,"酒店在苹果上7天订单量",""),

    ADR_ORD_PV_21_DAY(171,"酒店在安卓上21天订单量",""),

    IOS_ORD_PV_21_DAY(172,"酒店在苹果上21天订单量",""),

    ADR_AVG_PRICE_180_DAY(173,"酒店在安卓上180天平均价格",""),

    IOS_AVG_PRICE_180_DAY(174,"酒店在苹果上180天平均价格",""),

    //  -- 分场景ctr
    NEARBY_SCENE_CTR_7_DAY(175,"nearby场景7天ctr",""),
    NEARBY_SCENE_CTR_21_DAY(176,"nearby场景21天ctr",""),
    NEARBY_SCENE_CTR_60_DAY(177,"nearby场景60天ctr",""),
    POI_SCENE_CTR_7_DAY(178,"poi场景7天ctr",""),
    POI_SCENE_CTR_21_DAY(179,"poi场景21天ctr",""),
    POI_SCENE_CTR_60_DAY(180,"poi场景60天ctr",""),
    SAMECITY_SCENE_CTR_7_DAY(181,"same city 场景7天ctr",""),
    SAMECITY_SCENE_CTR_21_DAY(182,"same city 场景21天ctr",""),
    SAMECITY_SCENE_CTR_60_DAY(183,"same city 场景60天ctr",""),
    NOTSAMECITY_SCENE_CTR_7_DAY(184,"not same场景7天ctr",""),
    NOTSAMECITY_SCENE_CTR_21_DAY(185,"not same场景21天ctr",""),
    NOTSAMECITY_SCENE_CTR_60_DAY(186,"not same场景60天ctr",""),

    // 同城市曝光、点击、订单占比
    HOTEL_CITY_SHOW_RATE(187,"酒店在同城市曝光占比",""),
    HOTEL_CITY_CLICK_RATE(188,"酒店在同城市点击占比",""),
    HOTEL_CITY_ORDER_RATE(189,"酒店在同城市订单占比",""),

    // 去位置偏置的ctr
    HOTEL_POS_COEC_CTR_7DAY(190,"酒店7天去位置偏置ctr",""),
    HOTEL_POS_COEC_CTR_14DAY(191,"酒店14天去位置偏置ctr",""),
    HOTEL_POS_COEC_CTR_21DAY(192,"酒店21天去位置偏置ctr",""),

    /**
     * geo 维度的相关特征
     */
    CITY_HOTELS_CNT(193,"城市酒店数量",""),
    GEO_HOTELS_CNT(194,"geo酒店数量",""),
    AROUND_HOTELS_CNT(195,"around酒店数量",""),
    GEO_PV_7_CNT(196,"geo7天pv量",""),
    GEO_PV_AVG_7_PRICE(197,"geo7天平均价格",""),
    GEO_ORDER_7_CNT(198,"geo7天订单量",""),
    GEO_CLICK_7_CNT(199,"geo7天点击量",""),
    AROUND_PV_7_CNT(200,"around7天pv量",""),
    AROUND_AVG_7_PRICE(201,"around7天平均价格",""),
    AROUND_ORDER_7_CNT(202,"around7天订单量",""),
    AROUND_CLICK_7_CNT(203,"around7天点击量",""),
    CITY_7_PVS(204,"city7天pv量",""),
    CITY_PV_7_PRICE(205,"city7天平均价格",""),
    CITY_7_ORDERS(206,"city7天订单量",""),
    CITY_7_CLICKS(207,"city7天点击量",""),
    /**
     * D36相关特征
     */
    UV_CTCVR_7D(208, "酒店7天的uv_ctcvr",""),
    UV_CTCVR_21D(209, "酒店21天的uv_ctcvr",""),
    UV_CTCVR_90D(210, "酒店90天的uv_ctcvr",""),
    UV_CTR_7D(211, "酒店7天的uv_ctr",""),
    UV_CTR_21D(212, "酒店21天的uv_ctr",""),
    UV_CTR_90D(213, "酒店90天的uv_ctr",""),

    SAME_CITY_UV_CTCVR_7D(214, "酒店同城7天的uv_ctcvr",""),
    SAME_CITY_UV_CTCVR_21D(215, "酒店同城21天的uv_ctcvr",""),
    SAME_CITY_UV_CTCVR_90D(216, "酒店同城90天的uv_ctcvr",""),
    SAME_CITY_UV_CTR_7D(217, "酒店同城7天的uv_ctr",""),
    SAME_CITY_UV_CTR_21D(218, "酒店同城21天的uv_ctr",""),
    SAME_CITY_UV_CTR_90D(219, "酒店同城90天的uv_ctr",""),

    NEARBY_UV_CTCVR_7D(220, "酒店身边7天的uv_ctcvr",""),
    NEARBY_UV_CTCVR_21D(221, "酒店身边21天的uv_ctcvr",""),
    NEARBY_UV_CTCVR_90D(222, "酒店身边90天的uv_ctcvr",""),
    NEARBY_UV_CTR_7D(223, "酒店身边7天的uv_ctr",""),
    NEARBY_UV_CTR_21D(224, "酒店身边21天的uv_ctr",""),
    NEARBY_UV_CTR_90D(225, "酒店身边90天的uv_ctr",""),

    POI_KEY_UV_CTCVR_7D(226, "酒店poi7天的uv_ctcvr",""),
    POI_KEY_UV_CTCVR_21D(227, "酒店poi21天的uv_ctcvr",""),
    POI_KEY_UV_CTCVR_90D(228, "酒店poi90天的uv_ctcvr",""),
    POI_KEY_UV_CTR_7D(229, "酒店poi7天的uv_ctr",""),
    POI_KEY_UV_CTR_21D(230, "酒店poi21天的uv_ctr",""),
    POI_KEY_UV_CTR_90D(231, "酒店poi90天的uv_ctr",""),

    NOT_SAME_CITY_UV_CTCVR_7D(232, "酒店异地7天的uv_ctcvr",""),
    NOT_SAME_CITY_UV_CTCVR_21D(233, "酒店异地21天的uv_ctcvr",""),
    NOT_SAME_CITY_UV_CTCVR_90D(234, "酒店异地90天的uv_ctcvr",""),
    NOT_SAME_CITY_UV_CTR_7D(235, "酒店异地7天的uv_ctr",""),
    NOT_SAME_CITY_UV_CTR_21D(236, "酒店异地21天的uv_ctr",""),
    NOT_SAME_CITY_UV_CTR_90D(237, "酒店异地90天的uv_ctr",""),

    UV_ORDER_IN_CITY_RATE_7D(238, "酒店7天内的下单用户数占该城市总下单用户总数的比",""),
    UV_ORDER_IN_CITY_RATE_21D(239, "酒店21天内的下单用户数占该城市总下单用户总数的比",""),
    UV_ORDER_IN_CITY_RATE_90D(240, "酒店90天内的下单用户数占该城市总下单用户总数的比",""),

    SUBWAY_ORDER_RATE_21D(241, "酒店靠近地铁，则21天内近地铁的订单占城市所有订单占比，否则为0",""),
    AIRPORT_ORDER_RATE_21D(242, "酒店靠近机场，则21天内近机场的订单占城市所有订单占比，否则为0",""),
    RAILWAY_ORDER_RATE_21D(243, "酒店靠近火车站，则21天内近火车站的订单占城市所有订单占比，否则为0",""),
    HOSPITAL_ORDER_RATE_21D(244, "酒店靠近医院，则21天内近医院的订单占城市所有订单占比，否则为0",""),
    UNIVERSITY_ORDER_RATE_21D(245, "酒店靠近大学，则21天内近大学的订单占城市所有订单占比，否则为0",""),
    SPOTPLACES_ORDER_RATE_21D(246, "酒店靠近景区，则21天内近景区的订单占城市所有订单占比，否则为0",""),

    HOTEL_LOCATION_IS_SCENIC_AREA(247, "酒店是否在景区内",""),

    /**
     * D42 版本特征
     */
    AREA_OFTEN_USER_ORDER_RATE_7D(248, "该行政区或县级市的常驻用户7天订单/该地级市的常驻用户7天订单量",""),
    AREA_NOT_OFTEN_USER_ORDER_RATE_7D(249, "该行政区或县级市的非常驻用户7天订单/该地级市的非常驻用户7天订单量",""),
    CITY_OFTEN_USER_ORDER_NUM_7D(250, "该地级市常驻用户7天订单量",""),
    CITY_NOT_OFTEN_USER_ORDER_NUM_7D(251, "该地级市非常驻用户7天订单量",""),
    CITY_OFTEN_USER_ORDER_RATE_7D(252, "该地级市常驻用户7天订单占所有订单的比例",""),
    CITY_NOT_OFTEN_USER_ORDER_RATE_7D(253, "该地级市非常驻用户7天订单占所有订单的比例",""),

    CITY_ORDER_PRICE_STD_7D(254, "该地级市7天下单间夜价格标准差（只统计请求时对应的）",""),
    CITY_ORDER_PRICE_AVG_7D(255, "该地级市7天下单间夜价格平均值（只统计请求时对应的）",""),
    CITY_ORDER_PRICE_MIN_7D(256, "该地级市7天下单间夜价格最小值（只统计请求时对应的）",""),
    CITY_ORDER_PRICE_MEDIA_7D(257, "该地级市7天下单间夜价格中位值（只统计请求时对应的）",""),
    CITY_ORDER_PRICE_MAX_7D(258, "该地级市7天下单间夜价格最大值（只统计请求时对应的）",""),

    AREA_ORDER_PRICE_STD_7D(259, "该县级市7天下单间夜价格标准差（只统计请求时对应的）",""),
    AREA_ORDER_PRICE_AVG_7D(260, "该县级市7天下单间夜价格平均值（只统计请求时对应的）",""),
    AREA_ORDER_PRICE_MIN_7D(261, "该县级市7天下单间夜价格最小值（只统计请求时对应的）",""),
    AREA_ORDER_PRICE_MEDIA_7D(262, "该县级市7天下单间夜价格中位值（只统计请求时对应的）",""),
    AREA_ORDER_PRICE_MAX_7D(263, "该县级市7天下单间夜价格最大值（只统计请求时对应的）",""),

    AREA_HOTEL_NUM(264, "该行政区或县级市的酒店数",""),
    CITY_HOTEL_NUM(265, "该地级市的酒店数",""),
    AREA_GRADE_1_HOTEL_NUM(266, "该行政区或县级市档次1的酒店数",""),
    AREA_GRADE_2_HOTEL_NUM(267, "该行政区或县级市档次2的酒店数",""),
    AREA_GRADE_3_HOTEL_NUM(268, "该行政区或县级市档次3的酒店数",""),
    AREA_GRADE_4_HOTEL_NUM(269, "该行政区或县级市档次4的酒店数",""),
    AREA_GRADE_5_HOTEL_NUM(270, "该行政区或县级市档次5的酒店数",""),
    CITY_GRADE_1_HOTEL_NUM(271, "该地级市档次1的酒店数",""),
    CITY_GRADE_2_HOTEL_NUM(272, "该地级市档次2的酒店数",""),
    CITY_GRADE_3_HOTEL_NUM(273, "该地级市档次3的酒店数",""),
    CITY_GRADE_4_HOTEL_NUM(274, "该地级市档次4的酒店数",""),
    CITY_GRADE_5_HOTEL_NUM(275, "该地级市档次5的酒店数",""),

    HOTEL_OFTEN_USER_CTR_7D(276, "酒店在常驻用户下7天ctr",""),
    HOTEL_NOT_OFTEN_USER_CTR_7D(277, "酒店在非常驻用户下7天ctr",""),
    HOTEL_OFTEN_USER_CTCVR_7D(278, "酒店在常驻用户下7天ctcvr",""),
    HOTEL_NOT_OFTEN_USER_CTCVR_7D(279, "酒店在非常驻用户下7天ctcvr",""),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    private static final Map<Integer, FeatureType> MAP;

    static {
        MAP = Arrays.stream(FeatureType.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    FeatureType(int index, String desc, String typeCode) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
    }

    public static FeatureType matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static FeatureType matchIndex(int key) {
        return MAP.get(key);
    }
}
