package com.qunar.search.common.enums;

/**
 * UserStatus
 *
 * 将用户身份封装枚举类rank给twell的
 * 传给twell参数logicBit+userBit
 *
 * <AUTHOR>
 * @date 16-11-24.
 */
public final class UserStatus {

    /**
     * kylin的userIdentityBit信息+kylin额外参数信息
     */
    public enum LogicBit{

        IS_FIVE_PERCENT(14, "五折大促"),
        FIVE_PERCENT_IS_CREDIT(15, "五折大促授信"),
        FIVE_PERCENT_IS_NOT_CREDIT(16, "五折大促非授信"),
        FIVE_PERCENT_OLD_USER(20, "如果是老用户传bit20为1标识"),
        PLANE_TRAIN_USER(23, "机火用户"),
        INCLUDE_EXCESS(32, "超返超减"),
        FIVE_PERCENT_NEW_USER(28, "如果是新用户传bit28为1标识"),
        ;

        private int type;
        private String desc;
        LogicBit(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }
        public int getType() {
            return type;
        }
    }
    /**
     * kylin的userBit信息+kylin额外参数信息
     */
    public enum UserBit{

        IS_LM(0, "夜销用户"),
        VOICE_LIST(1, "语音报价"),
        INCLUDE_EXCESS(2, "超返超减"),
        NEED_CONPON(3, "星券低价"),
        NEW_USER(4, "新用户标识"),
        TOUCH_USER(5, "超返超减"),
        IS_FIVE_PERCENT(6, "五折大促"),
        FIVE_PERCENT_IS_CREDIT(7, "五折大促授信"),
        A2B_SHIELD_ALL(8, "A2B报价屏蔽全部"),
        IS_LT(9, "超返超减"),
        IS_RED_BAG_FIRST_ORDER(10, "签到红包首单"),
        IS_VIP_USER(11, "新用户标识"),
        ;

        private int type;
        private String desc;
        UserBit(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }
        public int getType() {
            return type;
        }


    }

    public static void main (String[] args) {
        System.out.println(UserStatus.LogicBit.FIVE_PERCENT_OLD_USER.getType());
        System.out.println(UserStatus.UserBit.A2B_SHIELD_ALL.getType());
    }



}
