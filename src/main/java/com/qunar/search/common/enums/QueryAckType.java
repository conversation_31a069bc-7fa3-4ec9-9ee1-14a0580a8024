package com.qunar.search.common.enums;

/**
 * Qhotel接口推荐和查询接口wiki [http://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=218933349]
 * @Author: yunpeng.wu
 * @Date: 2018/11/7 11:12 AM
 */
public enum QueryAckType {

    QUERY_RESULT(1,"搜索结果"),
    TUIJIAN_RESULT(2,"推荐结果"),
	@Deprecated //国内无用
    QUERY_TUIJIAN_RESULT(3,"搜索和推荐结果"),
    STICKY_RECOMMEND_RESULT(4, "触底推荐结果")
    ;

    private int id;
    private String desc;

    QueryAckType(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
