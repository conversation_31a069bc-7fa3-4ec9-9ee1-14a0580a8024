package com.qunar.search.common.enums;

/**
 * 酒店报价状态, hprice相关.
 */
public enum HotelPriceStatus {

    STATUS_FULL(-1),//满房
    STATUS_AVAIL(1),//可订
    STATUS_PARTAVAIL(-2),//部分日期可订
    STATUS_NONE(0),//无报价
    STATUS_LOWPRICE(-3), ;//用于国际酒店搜索无报价，但有lowprice报价


    private final int status;

    public static final int STATUS_AVAIL_RANK = 50000;
    public static final int STATUS_LOWPRICE_RANK = 30000;
    public static final int STATUS_PARTAVAIL_RANK = 20000;

    HotelPriceStatus(int value) {
        this.status = value;
    }

    public int getStatus() {
        return status;
    }

    public static HotelPriceStatus parse(Integer value) {
        if (value == null) {
            return null;
        }
        try {
            for (HotelPriceStatus e : HotelPriceStatus.values()) {
                if (e.getStatus() == value) return e;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
