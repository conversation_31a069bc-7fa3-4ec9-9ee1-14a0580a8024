package com.qunar.search.common.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 过滤类型,地区过滤, 房型过滤等.
 */
public enum FilterType {
    poi,
    brand,
    tradingArea,
    hotelarea,
    dangci,
    sheshi,
    theme,
    fangxing,
    subway,
    group,
    hotelName,
    alliance,
    hotelPartname,
    hotStayPlace,
    ugcComment,//ugc评论总数
    ugcRecommend,//砖家推荐
    bizZone,
    city,
    hotelTag,
    subjectTag,
    commentsTag,
    hotelCoreWord,
    fuzzyWord,
    extra,
    hotActivity,
    subjectRoom,
    scenicZone,
    ctripScenicZone
    ;

    private static final Map<String, FilterType> MAP = Maps.newHashMap();

    static {
        for (FilterType value : FilterType.values()) {
            MAP.put(value.name(), value);
        }
    }

    public static FilterType codeOf(String code){
        return MAP.get(code);
    }
}