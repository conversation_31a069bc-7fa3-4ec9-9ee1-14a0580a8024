package com.qunar.search.common.enums.cUserDatas;

import com.qunar.search.common.enums.FeatureValueTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum UserPoiOrderDistancePriceData {

    AVG_CLICK_DISTANCE(1, "poi下的平均点击距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    RELATED_CLICK_DISTANCE_99(2, "poi关联酒店的点击距离99分位", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    CLICK_DISTANCE_99(3, "poi搜索下的点击距离99分位", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    AVG_ORDER_DISTANCE(4, "poi平均订单距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    RELATED_ORDER_DISTANCE_99(5, "poi联酒店订单99分位距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    ORDER_DISTANCE_99(6, "poi搜索下的订单距离99分位", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    AVG_CLICK_PRICE(7, "poi搜索下平均点击价格", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    CLICK_PRICE_9(8, "poi搜索下点击价格的9分位", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    AVG_ORDER_PRICE(9, "poi搜索下平均订单价格", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    ORDER_PRICE_9(10, "poi搜索下订单价格9分位", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TOP1_HOTEL_SEQ(11, "poi搜索下成单top1酒店", "", FeatureValueTypeEnum.STRING_TYPE, 0),
    TOP1_ORDER_hotel_DISTANCE(12, "poi搜索下成单top1酒店的距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TOP2_HOTEL_SEQ(13, "poi搜索下成单top2酒店", "", FeatureValueTypeEnum.STRING_TYPE, 0),
    TOP2_ORDER_hotel_DISTANCE(14, "poi搜索下成单top2酒店的距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TOP3_HOTEL_SEQ(15, "poi搜索下成单top3酒店", "", FeatureValueTypeEnum.STRING_TYPE, 0),
    TOP3_ORDER_hotel_DISTANCE(16, "poi搜索下成单top3酒店的距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TOP4_HOTEL_SEQ(17, "poi搜索下成单top4酒店", "", FeatureValueTypeEnum.STRING_TYPE, 0),
    TOP4_ORDER_hotel_DISTANCE(18, "poi搜索下成单top4酒店的距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    TOP5_HOTEL_SEQ(19, "poi搜索下成单top5酒店", "", FeatureValueTypeEnum.STRING_TYPE, 0),
    TOP5_ORDER_hotel_DISTANCE(20, "poi搜索下成单top5酒店的距离", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, UserPoiOrderDistancePriceData> MAP;

    static {
        MAP = Arrays.stream(UserPoiOrderDistancePriceData.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    UserPoiOrderDistancePriceData(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static UserPoiOrderDistancePriceData matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static UserPoiOrderDistancePriceData matchIndex(int key) {
        return MAP.get(key);
    }
}
