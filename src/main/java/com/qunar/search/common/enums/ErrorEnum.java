package com.qunar.search.common.enums;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * http请求错误信息枚举
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-3-17.
 */
public enum ErrorEnum {
    ERROR_NONE(0, "NO ERROR", 0),
    ERROR_PARA(1, "PARAM ERROR", 100),
    ERROR_TIMEOUT(2, "TIME OUT", 2),
    ERROR_PARSE(3, "PARSE ERROR", 90),
    ERROR_HTTP(4, "HTTP ERROR", 4),
    ERROR_EXCEPTION(5, "EXCEPTION", 80),
    ERROR_DATA(6, "DATA NULL", 3);

    private final int code;
    private final String msg;
    private final int sort;

    ErrorEnum(int code, String msg, int sort) {
        this.code = code;
        this.msg = msg;
        this.sort = sort;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public int getSort() {
        return sort;
    }

    public static ErrorEnum getErrorBySort(List<ErrorEnum> enums) {
        if (CollectionUtils.isEmpty(enums)) {
            return null;
        }
        ErrorEnum errorEnumResult = null;
        for (ErrorEnum errorEnum : enums) {
            if (null == errorEnumResult || errorEnumResult.getSort() < errorEnum.getSort()) {
                errorEnumResult = errorEnum;
            }
        }
        return errorEnumResult;
    }
}
