package com.qunar.search.common.enums;

import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

/**
 * 新老客身份类别区分
 */
public enum IdentityOfNewOrOldEnum {

    /**
     * QM新用户
     */
    QM(0, ""),

    /**
     * QC新用户
     */
    QC(1, "ctripOneYearOrder"),

    /**
     * 老客
     */
    OLD(2, "oldUser");

    @Getter
    private final Integer flag;

    @Getter
    private final String code;

    IdentityOfNewOrOldEnum(Integer flag, String code) {
        this.flag = flag;
        this.code = code;
    }

    public static IdentityOfNewOrOldEnum getEnumByIdentityCodes(Set<String> identityCodes) {
        if (CollectionUtils.isEmpty(identityCodes) ) {
            return IdentityOfNewOrOldEnum.OLD;
        } else if (identityCodes.contains(IdentityOfNewOrOldEnum.OLD.getCode())) {
            return IdentityOfNewOrOldEnum.OLD;
        }else if (identityCodes.contains("newUser") && identityCodes.contains(IdentityOfNewOrOldEnum.QC.getCode())) {
            return IdentityOfNewOrOldEnum.QC;
        }else if (identityCodes.contains("newUser") && !identityCodes.contains(IdentityOfNewOrOldEnum.QC.getCode())) {
            return IdentityOfNewOrOldEnum.QM;
        }else {
            return IdentityOfNewOrOldEnum.OLD;
        }
    }
}
