package com.qunar.search.common.enums;

/**
 * HotelAdvertiseEnum
 *
 * <AUTHOR>
 * @date 16-12-28.
 */
public final class HotelAdvertiseEnum {

    /**
     * 直通车酒店替换信息枚举
     */
    public enum ChangeInfo {

        /* 正常酒店 */
        NORMAL(0, "NORMAL"),
        /* 替换的广告酒店 */
        HOTEL_ADVERTISE(1, "HOTEL_ADVERTISE"),
        /* 被替换的相似酒店 */
        HOTEL_SIMILAR(2, "HOTEL_SIMILAR");

        private int type;
        private String name;

        ChangeInfo(int type, String name) {
            this.type = type;
            this.name = name;
        }

        public int getType() {
            return type;
        }
    }

    /**
     * 直通车内部排序策略枚举
     */
    public enum SortStrategy {

        /* 广告酒店首屏，相似酒店替换到2/3屏策略 */
        TWO_THREE_SCREEN_ORDER(1, "TWO_THREE_SCREEN_ORDER"),
        /* 广告酒店首屏，相似酒店首屏位置顺延 */
        EXTEND_ORDER(2, "EXTEND_ORDER");

        private int type;
        private String name;

        SortStrategy(int type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    public static void main(String[] args) {
        System.out.println(ChangeInfo.NORMAL.getType());
        System.out.println(SortStrategy.EXTEND_ORDER.getName());
    }
}
