package com.qunar.search.common.enums;

import com.qunar.search.common.cache.CityInfoCache;
import com.qunar.search.common.util.GlobalProperties;
import com.qunar.search.common.util.PropertiesUtil;

/**
 * 国内国际集群分流
 *
 * <AUTHOR>
 */
public enum Env {
	/**
	 * 全部
	 */
	A,
	/**
	 * 国际
	 */
	B,
	/**
	 * 国内国际
	 */
	C,
	/**
	 * 国内
	 */
	D;

	public boolean match(String cityUrl) {
		switch (this) {
			//全量
			case A:
				return true;
			//国际城市（包含港澳台）
			case B:
				return !CityInfoCache.isMainLand(cityUrl);
			//全量
			case C:
				return true;
			case D:
				return CityInfoCache.isMainLand(cityUrl);
			default:
				break;
		}

		throw new RuntimeException("获取环境失败");
	}

	public static boolean match(Env env) {
		return env == A || env == C;
	}

	public static Env current() {
		return localEnv;
	}

	private static final Env localEnv;

	static {
		if (GlobalProperties.has("environmentGroup")) {
			localEnv = Env.valueOf(GlobalProperties.get("environmentGroup", "D"));
		} else {
			localEnv = Env.A;
		}
	}
}
