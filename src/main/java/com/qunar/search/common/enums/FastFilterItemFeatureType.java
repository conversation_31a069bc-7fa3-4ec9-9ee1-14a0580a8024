package com.qunar.search.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2023-09-14
 * @DESCRIPTION 快筛排序的特征数据枚举类
 **/
@Getter
public enum FastFilterItemFeatureType {

    ALL_CLICK_UV_RATE_1_DAY(1, "该筛选项1天内在(综筛/位置筛选/快筛)中被点击uv占比", "0"),
    ALL_CLICK_PV_RATE_1_DAY(2, "该筛选项1天内在(综筛/位置筛选/快筛)中被点击pv占比", "1"),
    ALL_CLICK_UV_RANK_1_DAY(3, "该筛选项1天内在(综筛/位置筛选/快筛)中被点击uv的排序", "2"),
    ALL_CLICK_PV_RANK_1_DAY(4, "该筛选项1天内在(综筛/位置筛选/快筛)中被点击pv的排序", "3"),
    ALL_CLICK_UV_RATE_7_DAY(5, "该筛选项7天内在(综筛/位置筛选/快筛)中被点击uv占比", "4"),
    ALL_CLICK_PV_RATE_7_DAY(6, "该筛选项7天内在(综筛/位置筛选/快筛)中被点击pv占比", "5"),
    ALL_CLICK_UV_RANK_7_DAY(7, "该筛选项7天内在(综筛/位置筛选/快筛)中被点击uv的排序", "6"),
    ALL_CLICK_PV_RANK_7_DAY(8, "该筛选项7天内在(综筛/位置筛选/快筛)中被点击pv的排序", "7"),
    ALL_CLICK_UV_RATE_30_DAY(9, "该筛选项30天内在(综筛/位置筛选/快筛)中被点击uv占比", "8"),
    ALL_CLICK_PV_RATE_30_DAY(10, "该筛选项30天内在(综筛/位置筛选/快筛)中被点击pv占比", "9"),
    ALL_CLICK_UV_RANK_30_DAY(11, "该筛选项30天内在(综筛/位置筛选/快筛)中被点击uv的排序", "10"),
    ALL_CLICK_PV_RANK_30_DAY(12, "该筛选项30天内在(综筛/位置筛选/快筛)中被点击pv的排序", "11"),
    COMPREHENSIVE_LOC_CLICK_UV_RATE_1_DAY(13, "该筛选项1天内在(综筛/位置筛选)中被点击uv占比", "12"),
    COMPREHENSIVE_LOC_CLICK_PV_RATE_1_DAY(14, "该筛选项1天内在(综筛/位置筛选)中被点击pv占比", "13"),
    COMPREHENSIVE_LOC_CLICK_UV_RANK_1_DAY(15, "该筛选项1天内在(综筛/位置筛选)中被点击uv的排序", "14"),
    COMPREHENSIVE_LOC_CLICK_PV_RANK_1_DAY(16, "该筛选项1天内在(综筛/位置筛选)中被点击pv的排序", "15"),
    COMPREHENSIVE_LOC_CLICK_UV_RATE_7_DAY(17, "该筛选项7天内在(综筛/位置筛选)中被点击uv占比", "16"),
    COMPREHENSIVE_LOC_CLICK_PV_RATE_7_DAY(18, "该筛选项7天内在(综筛/位置筛选)中被点击pv占比", "17"),
    COMPREHENSIVE_LOC_CLICK_UV_RANK_7_DAY(19, "该筛选项7天内在(综筛/位置筛选)中被点击uv的排序", "18"),
    COMPREHENSIVE_LOC_CLICK_PV_RANK_7_DAY(20, "该筛选项7天内在(综筛/位置筛选)中被点击pv的排序", "19"),
    COMPREHENSIVE_LOC_CLICK_UV_RATE_30_DAY(21, "该筛选项30天内在(综筛/位置筛选)中被点击uv占比", "20"),
    COMPREHENSIVE_LOC_CLICK_PV_RATE_30_DAY(22, "该筛选项30天内在(综筛/位置筛选)中被点击pv占比", "21"),
    COMPREHENSIVE_LOC_CLICK_UV_RANK_30_DAY(23, "该筛选项30天内在(综筛/位置筛选)中被点击uv的排序", "22"),
    COMPREHENSIVE_LOC_CLICK_PV_RANK_30_DAY(24, "该筛选项30天内在(综筛/位置筛选)中被点击pv的排序", "23"),
    FAST_CLICK_UV_RATE_1_DAY(25, "该筛选项1天内在快筛中被点击uv占比", "24"),
    FAST_CLICK_PV_RATE_1_DAY(26, "该筛选项1天内在快筛中被点击pv占比", "25"),
    FAST_CLICK_UV_RANK_1_DAY(27, "该筛选项1天内在快筛中被点击uv的排序", "26"),
    FAST_CLICK_PV_RANK_1_DAY(28, "该筛选项1天内在快筛中被点击pv的排序", "27"),
    FAST_CLICK_UV_RATE_7_DAY(29, "该筛选项7天内在快筛中被点击uv占比", "28"),
    FAST_CLICK_PV_RATE_7_DAY(30, "该筛选项7天内在快筛中被点击pv占比", "29"),
    FAST_CLICK_UV_RANK_7_DAY(31, "该筛选项7天内在快筛中被点击uv的排序", "30"),
    FAST_CLICK_PV_RANK_7_DAY(32, "该筛选项7天内在快筛中被点击pv的排序", "31"),
    FAST_CLICK_UV_RATE_30_DAY(33, "该筛选项30天内在快筛中被点击uv占比", "32"),
    FAST_CLICK_PV_RATE_30_DAY(34, "该筛选项30天内在快筛中被点击pv占比", "33"),
    FAST_CLICK_UV_RANK_30_DAY(35, "该筛选项30天内在快筛中被点击uv的排序", "34"),
    FAST_CLICK_PV_RANK_30_DAY(36, "该筛选项30天内在快筛中被点击pv的排序", "35"),
    COMPREHENSIVE_FILTER_OUV(37, "综筛的下单uv", "36"),
    LOCATION_FILTER_OUV(38, "位置筛选的下单uv", "37"),

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    private static final Map<Integer, FastFilterItemFeatureType> MAP;

    static {
        MAP = Arrays.stream(FastFilterItemFeatureType.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    FastFilterItemFeatureType(int index, String desc, String typeCode) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
    }

    public static FastFilterItemFeatureType matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static FastFilterItemFeatureType matchIndex(int key) {
        return MAP.get(key);
    }
}
