/*
* Copyright (c) 2017 Qunar.com. All Rights Reserved.
*/
package com.qunar.search.common.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR> Date: 18/5/7 Time: 下午4:38
 * @version $Id$
 */
public enum QPoiTagType {
    poi("poi", 1),
    tradingArea("tradingArea", 2);

    private final int code;
    private final String name;

    QPoiTagType(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private static Map<Integer, QPoiTagType> ELEMENTS = Maps.newHashMap();

    static {
        for (QPoiTagType qPoiTagType : QPoiTagType.values()) {
            ELEMENTS.put(qPoiTagType.getCode(), qPoiTagType);
        }
    }

    public static QPoiTagType codeOf(int code) {
        return ELEMENTS.get(code);
    }

}
