package com.qunar.search.common.enums;

public enum MobileRoomStatus {

    RECOMMAND("无线推荐", 1, 3),
    BOOKABLE("无线可订", 2, 3),
    TEL_BOOKABLE("无线电话", 3, 3),
    FULL_ROOM("无线满房", 4, 2),
    NO_PRICE("无线暂无报价", 5, 1),
    STATIC_HOTEL("无线静态酒店", 6, 1),
    UNKNOWN("未知", 9, 1);

    private final int value;
    private final String name;
    /**
     * 房态分组得分
     */
    private final int score;

    MobileRoomStatus(String name, int value, int score) {
        this.name = name;
        this.value = value;
        this.score = score;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public int getScore() {
        return score;
    }

    public static MobileRoomStatus parseByName(String name) {
        if (name == null) return UNKNOWN;
        for (MobileRoomStatus mrs : values()) {
            if (mrs.name().equals(name)) {
                return mrs;
            }
        }
        return UNKNOWN;
    }

    public static MobileRoomStatus parseByValue(int value, boolean isOpen) {
        if (!isOpen) return UNKNOWN;
        for (MobileRoomStatus mrs : values()) {
            if (mrs.getValue() == value) {
                return mrs;
            }
        }
        return UNKNOWN;
    }

    public static boolean isBookable(MobileRoomStatus mobileRoomStatus) {

        int value = mobileRoomStatus.getValue();
        if (BOOKABLE.getValue() == value || RECOMMAND.getValue() == value) {
            return true;
        }
        return false;
    }

    public static MobileRoomStatus parseByValue(int value) {
        return parseByValue(value, true);
    }
}
