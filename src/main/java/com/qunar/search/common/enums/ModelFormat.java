package com.qunar.search.common.enums;

/**模型的分类, 目前只有两种
 * Created by fangxue.zhang on 2016/11/15.
 */
public enum ModelFormat {

    GBDT("gbdt", "决策树"),
    LIBLINEAR("liblinear", "liblinear库定义的格式");

    private String code;
    private String desc;

    ModelFormat(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public ModelFormat getByCode(String code){
        for(ModelFormat modelFormat : values()){
            if(modelFormat.getCode().equals(code)){
                return modelFormat;
            }
        }
        return null;
    }
}
