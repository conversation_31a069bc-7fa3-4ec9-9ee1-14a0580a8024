package com.qunar.search.common.enums;

import org.apache.commons.lang.StringUtils;

/**
 * GeneralPlatformType
 *
 * <AUTHOR>
 * @date 17-1-22.
 */
public enum GeneralPlatformType {

    ERROR("error", "错误"),
    ALL("all", "所有平台"),
    APP("app", "app端"),
    PC("pc", "pc端");

    private final String name;
    private final String desc;

    GeneralPlatformType(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public static boolean isValidGeneralPlatformType4Pc(GeneralPlatformType platformType){
        boolean result = false;
        if (PC == platformType){
            result = true;
        }
        return result;
    }

    public static boolean isValidGeneralPlatformType4App(GeneralPlatformType platformType){
        boolean result = false;
        if (APP == platformType){
            result = true;
        }
        return result;
    }


    public static GeneralPlatformType valueOfByName(String name){
        GeneralPlatformType platformType = ERROR;
        if (StringUtils.isEmpty(name)){
            return platformType;
        }
        for (GeneralPlatformType gt : GeneralPlatformType.values()){
            if (gt.getName().equals(name)){
                platformType = gt;
                break;
            }
        }
        return platformType;
    }


}
