package com.qunar.search.common.enums;

/**
 * 拒单页酒店推荐标签
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-3-20.
 */
public enum RefuseRecTagType {

    //默认
    DEFAULT(0, "默认值"),

    //价格更低
    PRICE_LOWER(1, "价格更低"),

    //评分更高
    COMMENT_SCORE_HIGHER(2, "评分更高");

    private final int code;
    private final String name;

    RefuseRecTagType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
