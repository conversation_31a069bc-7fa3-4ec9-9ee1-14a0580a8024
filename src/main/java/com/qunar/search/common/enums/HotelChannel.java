package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 各频道入口, 目前最全的, 各个业务建议一致使用这个类来区分模块.
 * <AUTHOR>
 *
 */
public enum HotelChannel {
	hotelChannel("酒店频道", 5, "hotel"),
	myHotel("我的酒店", 6, "myHotel"),

    error("错误频道", -1, "error");

    private final String channelName;
    private final int channelNum;
    private final String code;

    HotelChannel(String channelName, int channelNum, String code) {
        this.channelName = channelName;
        this.channelNum = channelNum;
        this.code = code;
    }

	public String getChannelName() {
		return channelName;
	}

	public int getChannelNum() {
		return channelNum;
	}

    public static HotelChannel getHotelChannel (String idsString) {

		if (StringUtils.isBlank(idsString)) {//酒店列表
			return HotelChannel.hotelChannel;
        } else {
            return HotelChannel.myHotel;
        }
    }

    public String getCode() {
        return code;
    }
}