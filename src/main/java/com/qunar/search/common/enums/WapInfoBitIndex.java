package com.qunar.search.common.enums;

/**
 * @author: luming.lv
 * @date: 2015-11-11
 *
 * wapInfo字段bit位对应关系, 从第0位开始
 */
public enum WapInfoBitIndex {

    /**
     * 夜宵标记
     */
    WRAPPER_LM(0),

    /**
     * 团购标记
     */
    WRAPPER_TUAN(1),
    /**
     * 五星红包酒店
     */
    WRAPPER_FIVE_STAR(5),
    /**
     * 周边特惠
     */
    WRAPPER_SURROUND_TEHUI(7),
    /**
     * 团购五折大促
     */
    WRAPPER_TUAN_HALF_PRICE(10),

    /**
     * 代理商包房可订
     */
    WRAPPER_BAOFANG(15);

    private int bitIndex;

    WapInfoBitIndex(int bitIndex) {
        this.bitIndex = bitIndex;
    }

    public int getBitIndex() {
        return bitIndex;
    }
}
