package com.qunar.search.common.enums;


import com.google.common.collect.Maps;
import com.qunar.search.common.model.dnn.DnnConstants;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qunar.search.common.constants.CommonConstants.BLANK_JOINER;
import static com.qunar.search.common.constants.CommonConstants.BLANK_SPLITTER;
import static com.qunar.search.common.model.dnn.DnnConstants.COLON;

/**
 * 特征索引枚举
 * todo 其他没有枚举出来的特征统统按照double 类型处理
 */
@Getter
public enum FastFilterFeatureIndexEnum {

    /**
     * 未知特征 - 模型不用
     */
    FEATURE_UNKNOWN("unknown", 0, FeatureValueTypeEnum.DOUBLE_TYPE),

    INDEX_1("是否新用户", 1, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_2("用户等级", 2, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_3("是否是学生", 3, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_4("性别", 4, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_5("年龄", 5, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_6("用户过去一年的订单平均价格", 6, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_7("用户过去一年的订单最大价格", 7, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_8("用户过去一年的订单最小价格", 8, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_9("用户过去一年的订单数", 9, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_10("用户酒店星级偏好", 10, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_11("商务订单占比", 11, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_12("节假日订单占比", 12, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_13("周末订单占比", 13, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_14("用户点击该筛选的时间排序", 14, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_15("用户点击该筛选次数的排序", 15, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_16("用户7天内点击该筛选的pv数", 16, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_17("用户1年内点击该筛选的pv数", 17, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_18("用户7天内浏览该筛选的pv数", 18, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_19("用户1年内浏览该筛选的pv数", 19, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_20("用户7天内对该筛选的ctr", 20, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_21("用户1年内对该筛选的ctr", 21, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_22("该筛选项1天内在(综筛/位置筛选/快筛)中被点击uv占比", 22, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_23("该筛选项1天内在(综筛/位置筛选/快筛)中被点击pv占比", 23, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_24("该筛选项1天内在(综筛/位置筛选/快筛)中被点击uv的排序", 24, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_25("该筛选项1天内在(综筛/位置筛选/快筛)中被点击pv的排序", 25, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_26("该筛选项7天内在(综筛/位置筛选/快筛)中被点击uv占比", 26, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_27("该筛选项7天内在(综筛/位置筛选/快筛)中被点击pv占比", 27, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_28("该筛选项7天内在(综筛/位置筛选/快筛)中被点击uv的排序", 28, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_29("该筛选项7天内在(综筛/位置筛选/快筛)中被点击pv的排序", 29, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_30("该筛选项30天内在(综筛/位置筛选/快筛)中被点击uv占比", 30, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_31("该筛选项30天内在(综筛/位置筛选/快筛)中被点击pv占比", 31, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_32("该筛选项30天内在(综筛/位置筛选/快筛)中被点击uv的排序", 32, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_33("该筛选项30天内在(综筛/位置筛选/快筛)中被点击pv的排序", 33, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_34("该筛选项1天内在(综筛/位置筛选)中被点击uv占比", 34, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_35("该筛选项1天内在(综筛/位置筛选)中被点击pv占比", 35, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_36("该筛选项1天内在(综筛/位置筛选)中被点击uv的排序", 36, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_37("该筛选项1天内在(综筛/位置筛选)中被点击pv的排序", 37, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_38("该筛选项7天内在(综筛/位置筛选)中被点击uv占比", 38, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_39("该筛选项7天内在(综筛/位置筛选)中被点击pv占比", 39, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_40("该筛选项7天内在(综筛/位置筛选)中被点击uv的排序", 40, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_41("该筛选项7天内在(综筛/位置筛选)中被点击pv的排序", 41, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_42("该筛选项30天内在(综筛/位置筛选)中被点击uv占比", 42, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_43("该筛选项30天内在(综筛/位置筛选)中被点击pv占比", 43, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_44("该筛选项30天内在(综筛/位置筛选)中被点击uv的排序", 44, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_45("该筛选项30天内在(综筛/位置筛选)中被点击pv的排序", 45, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_46("该筛选项1天内在快筛中被点击uv占比", 46, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_47("该筛选项1天内在快筛中被点击pv占比", 47, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_48("该筛选项1天内在快筛中被点击uv的排序", 48, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_49("该筛选项1天内在快筛中被点击pv的排序", 49, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_50("该筛选项7天内在快筛中被点击uv占比", 50, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_51("该筛选项7天内在快筛中被点击pv占比", 51, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_52("该筛选项7天内在快筛中被点击uv的排序", 52, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_53("该筛选项7天内在快筛中被点击pv的排序", 53, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_54("该筛选项30天内在快筛中被点击uv占比", 54, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_55("该筛选项30天内在快筛中被点击pv占比", 55, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_56("该筛选项30天内在快筛中被点击uv的排序", 56, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_57("该筛选项30天内在快筛中被点击pv的排序", 57, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_58("综筛的下单uv", 58, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_59("位置筛选的下单uv", 59, FeatureValueTypeEnum.DOUBLE_TYPE),

    // V2特征
    INDEX_60("星期几", 60, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_61("是否周末", 61, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_62("是否法定节假日", 62, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_63("时间(小时)", 63, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_64("入店日期距离查询日期的天数", 64, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_65("离店日期距离查询日期的天数", 65, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_66("是否同城市", 66, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_67("用户24h在综筛/位置筛选里点击该筛选次数", 67, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_68("用户24h在综筛/位置是否是最近一次点击该筛选(0、1)", 68, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_69("用户24h在综筛/位置筛选里点击筛选项的排名（1-10）", 69, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_70("用户24h在快筛里点击筛选项次数", 70, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_71("用户24h在快筛是否是最近一次点击该筛选", 71, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_72("用户24h在快筛里点击筛选项的时间排名（1-10）", 72, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_73("用户24h在综筛/位置/快筛里点击该筛选总次数", 73, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_74("用户24h在综筛/位置/快筛是否是最近一次点击该筛选", 74, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_75("用户24h在综筛/位置/快筛里点击筛选项排名（1-10）", 75, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_76("用户24h点击酒店的酒店标签（该筛选）次数", 76, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_77("用户24h收藏酒店的酒店标签（该筛选）次数", 77, FeatureValueTypeEnum.DOUBLE_TYPE),
    // V3
    INDEX_78("query词下的（该筛选）被点击次数", 78, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_79("query词下的（该筛选）被点击次数占比", 79, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_80("query词下（该筛选）点击排名", 80, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_81("query词下（该筛选）点击排名是否top1", 81, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_82("query词下（该筛选）点击排名是否top2", 82, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_83("query词下（该筛选）点击排名是否top3", 83, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_84("query词下（该筛选）点击排名是否top4", 84, FeatureValueTypeEnum.DOUBLE_TYPE),
    INDEX_85("query词下（该筛选）点击排名是否top5", 85, FeatureValueTypeEnum.DOUBLE_TYPE),

    ;


    /**
     * 特征描述
     */
    private final String desc;

    /**
     * 特征索引
     */
    private final Integer index;

    /**
     * 特征的取值类型
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    FastFilterFeatureIndexEnum(String desc, int index, FeatureValueTypeEnum featureValueTypeEnum) {
        this.index = index;
        this.desc = desc;
        this.featureValueTypeEnum = featureValueTypeEnum;
    }

    @Getter
    private static final Map<Integer, FastFilterFeatureIndexEnum> MAPIndex;

    static {
        MAPIndex = Arrays.stream(FastFilterFeatureIndexEnum.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    FastFilterFeatureIndexEnum(String desc, int index) {
        this.desc = desc;
        this.index = index;
    }

    /**
     * 根据key的类型将特征map 转换成libSvm 字符串
     */
    public static String toLibSvm(Map<Integer, Object> featureMap) {
        if (MapUtils.isEmpty(featureMap)) {
            return StringUtils.EMPTY;
        }

        List<String> stringList = featureMap.entrySet().stream()
                .sorted((Map.Entry<Integer, Object> a, Map.Entry<Integer, Object> b) -> (a.getKey() - (b.getKey())))
                .map(s -> {
                        FastFilterFeatureIndexEnum indexEnum = MAPIndex.getOrDefault(s.getKey(), FEATURE_UNKNOWN);
                        if (indexEnum.getFeatureValueTypeEnum() == FeatureValueTypeEnum.DOUBLE_TYPE
                                && Math.abs((Double) s.getValue() - 0.0) < DnnConstants.DOUBLE_ZEROS) {
                            return null;
                        }
                        String value = indexEnum.getFeatureValueTypeEnum().toString(s.getValue());
                        return s.getKey() + ":" + value;
        }).collect(Collectors.toList());
        return BLANK_JOINER.join(stringList);
    }

    /**
     * 根据key的类型将 libSvm 字符串 转换成 特征map
     */
    public static Map<Integer, Object> toObjectMap(String libSvmStr) {
        if (StringUtils.isEmpty(libSvmStr)) {
            return Collections.emptyMap();
        }

        List<String> stringList = BLANK_SPLITTER.splitToList(libSvmStr);
        if (CollectionUtils.isEmpty(stringList)) {
            return Collections.emptyMap();
        }

        // 判断第一个或者最后一个是否label
        if (!stringList.get(0).contains(COLON)) {
            stringList.remove(0);
        } else if (!stringList.get(stringList.size() - 1).contains(COLON)){
            stringList.remove(stringList.size() - 1);
        }


        Map<Integer, Object> map = Maps.newHashMap();
        for (String keyValue : stringList) {
            String[] keyAndValue = StringUtils.split(keyValue, COLON);
            if (keyAndValue.length != 2) {
                throw new RuntimeException("libsvm 必须为kv格式二元组");
            }
            int index = Integer.parseInt(keyAndValue[0]);

            FastFilterFeatureIndexEnum indexEnum = MAPIndex.getOrDefault(index, FEATURE_UNKNOWN);
            Object object = indexEnum.getFeatureValueTypeEnum().toObject(keyAndValue[1]);
            map.put(index, object);
        }
        return map;
    }
}
