/*
* Copyright (c) 2017 Qunar.com. All Rights Reserved.
*/
package com.qunar.search.common.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR> Date: 18/4/23 Time: 下午7:18
 * @version $Id$
 */
public enum PositionType {

    airport("机场", 1,"airport"),
    railwayStation("火车站", 2, "railwayStation"),
    busStop("汽车站", 3, "busStop"),
    hotLocation("热门位置", 4, "hotLocation"),
    university("大学", 5, "university"),
    hospital("医院", 6, "hospital"),
    region("行政区", 7, "region"),
    visitPlaces("观光景点", 8, "visitPlaces"),
    subway("地铁线路", 9, "subway"),
    outingPlaces("郊游景点", 10, "outingPlaces"),
    shopping("购物", 11, "shopping"),
    pier("码头", 12, "pier");

    private final int code;
    private final String name;
    private final String category;

    PositionType(String name, int code ,String category) {
        this.name = name;
        this.code = code;
        this.category = category;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getCategory() {
        return category;
    }

    private static Map<Integer, PositionType> ELEMENTS = Maps.newHashMap();

    static {
        for (PositionType positionType : PositionType.values()) {
            ELEMENTS.put(positionType.getCode(), positionType);
        }
    }

    public static PositionType codeOf(int code) {
        return ELEMENTS.get(code);
    }

}
