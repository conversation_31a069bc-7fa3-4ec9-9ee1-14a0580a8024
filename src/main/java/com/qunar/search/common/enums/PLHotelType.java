package com.qunar.search.common.enums;

/**
 * 地级市酒店分类枚举类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-9-1.
 */
public enum PLHotelType {
    DISTRICT_HOTEL("市辖区酒店", 1),
    NOT_DISTRICT_HOTEL("非市辖区酒店", 2);

    private final int type;
    private final String name;

    PLHotelType(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
