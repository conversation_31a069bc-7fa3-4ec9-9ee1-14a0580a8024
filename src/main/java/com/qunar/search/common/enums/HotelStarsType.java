package com.qunar.search.common.enums;

/**
 * 酒店的星级.
 */
public enum HotelStarsType {
	ZERO(0,"无星"),
	ONE(1,"一星"),
	TWO(2,"二星"),
	THREE(3,"三星"),
	FOUR(4,"四星"),
	FIVE(5,"五星");
	
	private final int star;
	private final String starName;
	HotelStarsType(int star, String starName) {
		this.star = star;
		this.starName = starName;
	}
	public int getStar() {
		return star;
	}
	public String getStarName() {
		return starName;
	}
	
	public static HotelStarsType parse(int star){
		try{
			for(HotelStarsType hstype : HotelStarsType.values()){
				if(hstype.getStar() == star){
					return hstype;
				}
			}
		}catch(Exception e){
			return ZERO;
		}
		
		return ZERO;
	}
}
