package com.qunar.search.common.enums;

/**
 * 关键词命中类型
 *
 * <AUTHOR>
 * @datetime on 1/22/16 2:52 PM
 */
public enum HotelNameMatchType {

    FULL_HOTEL_NAME("FullHotelName"), // 精确命中
    HOTEL_NAME("HotelName"),          // 判定精确匹配(有相关性信息)
    PART_HOTEL_NAME("PartHotelName"), ; // 模糊匹配
    private String value;

    HotelNameMatchType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
