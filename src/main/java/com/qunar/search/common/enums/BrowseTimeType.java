package com.qunar.search.common.enums;

/**
 * 对浏览时间的一个枚举.目前pc专用.
 */
public enum BrowseTimeType {
    THISMONTH("一个月内", 1),
    LASTMONTH("一个月前", 2),
    INSIDETHISMONTH("内部一个月内", 4),
    INSIDELASTMONTH("内部一个月前", 8), ;

    private final int value;
    private final String name;

    BrowseTimeType(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
