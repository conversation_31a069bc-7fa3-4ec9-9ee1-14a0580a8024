package com.qunar.search.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum UserQCOrderClickFeature {

    C_6M_ORD_HOTELSEQ_LIST(1, "半年内在C订单酒店列表seq", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 50),
    C_6M_CLK_HOTELSEQ_LIST(2, "半年内在C的点击酒店列表seq", "", FeatureValueTypeEnum.LIST_STRING_TYPE, 50),
    C_1Y_AVG_RN_PRICE(3, "过去一年在C的平均订单间夜价格", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0),
    C_1M_AVG_HOTEL_PRICE(4, "过去一个月在C的平均点击价格", "", FeatureValueTypeEnum.DOUBLE_TYPE, 0)

    ;

    /**
     * 编号，对应离线 libsvm 的key 值
     */
    int index;

    /**
     * 特征描述
     */
    String desc;

    /**
     * 同一类特征中编码
     */
    String typeCode;

    /**
     * 特征的取值类型 解析时有用
     */
    private FeatureValueTypeEnum featureValueTypeEnum;

    /**
     * 特征是list时的长度限制
     */
    private int featureSize;

    private static final Map<Integer, UserQCOrderClickFeature> MAP;

    static {
        MAP = Arrays.stream(UserQCOrderClickFeature.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    UserQCOrderClickFeature(int index, String desc, String typeCode, FeatureValueTypeEnum featureValueTypeEnum, int featureSize) {
        this.index = index;
        this.desc = desc;
        this.typeCode = typeCode;
        this.featureValueTypeEnum = featureValueTypeEnum;
        this.featureSize = featureSize;
    }

    public static UserQCOrderClickFeature matchIndex(String key) {
        return matchIndex(Integer.parseInt(key));
    }

    public static UserQCOrderClickFeature matchIndex(int key) {
        return MAP.get(key);
    }
}
