package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * activity type
 */
public enum CarnivalType {
    CARNIVAL("客栈嘉年华", "carnival", false,0),
    CHEAPEST("震撼低价", "cheapest", true, 1<<0),
    ALLINONE("一价全包", "allInOne", true,1<<1),
    GIFT("超值买赠","gift", true, 1<<2),
    LZ("连住特惠","lz", true, 1<<3);

    private final String name;
    private final String code;
    private final boolean isChild;//判断是否为子活动，子活动展示在外网中，父活动选中时，默认选中了所有的子活动
    private final int mask;

    CarnivalType(String name, String code, boolean isChild, int mask) {
        this.name = name;
        this.code = code;
        this.isChild = isChild;
        this.mask = mask;
    }

    /**
     * 通过code 返回当前对应的是哪种优惠类型
     * @param code
     * @return
     */
    public static CarnivalType parseCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }

        for(CarnivalType type : CarnivalType.values()){
            if(type.getCode().equals(code)){
                return type;
            }
        }

        return null;
    }


    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public boolean isChild(){
        return isChild;
    }

    public int getMask() {
        return mask;
    }
}
