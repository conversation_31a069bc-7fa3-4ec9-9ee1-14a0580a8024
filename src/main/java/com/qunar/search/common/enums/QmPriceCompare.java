package com.qunar.search.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum QmPriceCompare {

    /**
     * q 比价 胜出
     */
    PRICE_BEAT("Qbeat",2),

    /**
     * q 比价 相等
     */
    PRICE_EQUAL( "Qmeet",1),

    /**
     * q 比价 失败
     */
    PRICE_LOSER( "Qlose",0),

    /**
     * q 比价 未知
     */
    PRICE_UNKNOWN("unknown",-1);

    /**
     * qm 比价消息类型
     */
    private String type;

    private int index;

    private static final Map<String, QmPriceCompare> MAP;
    private static final Map<Integer, QmPriceCompare> MAPIndex;

    static {
        MAP = Arrays.stream(QmPriceCompare.values()).collect(Collectors.toMap(f -> f.type, f -> f, (a, b) -> a));
    }

    static {
        MAPIndex = Arrays.stream(QmPriceCompare.values()).collect(Collectors.toMap(f -> f.index, f -> f, (a, b) -> a));
    }

    QmPriceCompare(String type,int index) {
        this.type = type;
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public int getIndex() {
        return index;
    }

    public static QmPriceCompare match(String key) {
        if (StringUtils.isEmpty(key)) {
            return PRICE_UNKNOWN;
        }

        return MAP.getOrDefault(key, PRICE_UNKNOWN);
    }

    public static QmPriceCompare matchByIndex(int key) {

        return MAPIndex.getOrDefault(key, PRICE_UNKNOWN);
    }

}
