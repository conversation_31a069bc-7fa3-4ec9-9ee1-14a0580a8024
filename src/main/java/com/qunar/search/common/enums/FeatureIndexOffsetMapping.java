package com.qunar.search.common.enums;

/**
 * 排序特征与偏移量的对应关系
 *
 * @author: luming.lv
 * @date: 2016-04-25
 */
public enum FeatureIndexOffsetMapping {

    /* 过去半年预订酒店次数 */
    HOTEL_ORDER_CNT_LAST_6_MONTH(100),

    /* 用户历史订单档次命中当前酒店 */
    DANGCI_SIMILARITY_OF_HISTORY_ORDER(140),

    /* 用户历史订单星级命中当前酒店 */
    STARS_SIMILARITY_OF_HISTORY_ORDER(150),

    /* 用户历史订单品牌命中当前酒店 */
    BRAND_SIMILARITY_OF_HISTORY_ORDER(160),

    /* 用户历史订单酒店类型命中当前酒店 */
    HOTEL_TYPE_SIMILARITY_OF_HISTORY_ORDER(170),

    /* 用户历史订单商圈命中当前酒店 */
    TRADING_SIMILARITY_OF_HISTORY_ORDER(180),

    /* 过去半年用户在该城市最后一次订单为当前酒店 */
    LAST_ORDER_IN_CURRENT_CITY(200),
    /* 用户星级档次相似性 */
    USER_DANGCI_SIMILITY(230),

    /* 酒店近三周的订单量分段 */
    ORDER_CNT_OF_HOTEL_IN_LAST_3_WEEKS(300),

    /* 酒店评分, 分13段 */
    COMMENT_SCORE_OF_HOTEL(400),

    /* 酒店房型特征 */
    ROOM_STATUS_OF_HOTEL(413),

    /* 酒店质量得分 */
    QUALITY_SCORE_OF_HOTEL(450),

    /* 搜索该关键词产生的历史订单数 */
    ORDER_CNT_FROM_QUERY_WORD(500),

    /* poi到酒店的距离 */
    DIST_BETWEEN_HOTEL_AND_POI(600),

    /* 用户到酒店的距离 */
    DIST_BETWEEN_HOTEL_AND_USER(620),

    /* 酒店折扣率 */
    DISCOUNT_RATE_OF_HOTEL(800),

    /* 酒店转化率 */
    SEARCH_TO_ORDER_RATE(1800),

    /* 过去三周浏览和点击酒店的次数 */
    SHOW_CLICK_CNT_IN_LAST_3_WEEKS(1000),

    /* 过去3周用户浏览该酒店的次数, mart model使用 */
    SHOW_CNT_IN_LAST_3_WEEKS_FOR_MART(1010),

    /* 过去3周用户点击该酒店的次数, mart model使用 */
    CLICK_CNT_IN_LAST_3_WEEKS_FOR_MART(1020),

    /* 过去三周到达填单页 */
    REACH_ORDER_PAGE_IN_LAST_3_WEEKS(1200),

    /* 过去三周在该酒店下过单 */
    ORDERED_IN_LAST_3_WEEKS(1300),

    /* 点击档次, 次数排名 */
    CLICK_DANGCI_CNT_SORT(1600),

    /* 点击档次, 次数排名 */
    CLICK_DANGCI_CNT_SORTNEW(1650),

    /* 点击价格与酒店当前价格距离分段 */
    DIST_BETWEEN_CLICK_PRICE_AND_HOTEL_PRICE(1622),

    /* 点击均价, mart model使用 */
//    CLICK_PRICE_AVG_FOR_MART(1622),

    /* 酒店当前价格 */
    HOTEL_CURRENT_PRICE(1628),

    /* 订单均价相似度 mart model使用 */
    SIMILARIY_OF_PRICE_AVG_BETWEEN_USER_AND_HOTEL_FOR_MART(1988),

    /* 酒店历史订单均价与用户历史订单均价距离分段 */
    HISTORY_PRICE_SIMILARITY(2001),

    /* 4天之前收藏过该酒店 */
    HISTORY_COLLECTION(2019),

    /* 4天之内收藏过该酒店 */
    CURRENT_COLLECTION(2020),

    /* 筛选档次, 时间排名 */
    FILTER_DANGCI_TIME_SORT(1760),

    /* 筛选档次, 时间排名 */
    FILTER_BRAND_TIME_SORT(1770),

    /* 筛选档次, 时间排名 */
    FILTER_TRADING_TIME_SORT(1780),

    /* 筛选档次, 时间排名 */
    FILTER_PRICE_TIME_SORT(1790),

    /* 筛选档次, 时间排名 */
    FILTER_QUERY_TIME_SORT(1800);

    private int offset;

    FeatureIndexOffsetMapping(int offset) {
        this.offset = offset;
    }

    public int getOffset() {
        return offset;
    }

}
