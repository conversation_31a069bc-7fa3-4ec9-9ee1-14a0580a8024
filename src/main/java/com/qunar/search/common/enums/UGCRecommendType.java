package com.qunar.search.common.enums;

/**
 * Created by andy on 2016/12/28.
 */
public enum UGCRecommendType {

    UGC_RECOMMEND_MORE_THAN_5("5条以上", "UGC_RECOMMEND_MORE_THAN_5", 5),

    UGC_RECOMMEND_MORE_THAN_3("3条以上","UGC_RECOMMEND_MORE_THAN_3", 3),

    UGC_RECOMMEND_MORE_THAN_2("2条以上","UGC_RECOMMEND_MORE_THAN_2", 2),

    ;


    UGCRecommendType(String name, String key, int count) {
        this.name = name;
        this.key = key;
        this.count = count;
    }

    private String name;   //推荐类型中文
    private String key;    //推荐类型,英文
    private int count ;    //个数

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }

    public int getCount() {
        return count;
    }

}
