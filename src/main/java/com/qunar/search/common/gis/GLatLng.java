package com.qunar.search.common.gis;

import org.apache.commons.lang.StringUtils;
import java.io.Serializable;

/**
 * 描述google坐标,
 */
public class GLatLng implements Serializable {
	private static final long serialVersionUID = 4535692326902363524L;
	private static final double EARTH_RADIUS = 6366.70;

	private double lat;
	private double lng;

	public GLatLng() {
	}

	public GLatLng(double lat, double lng) {
		this.lat = lat;
		this.lng = lng;
	}

	/**
	 * 经纬度坐标之间的距离计算
	 *
	 * https://www.movable-type.co.uk/scripts/latlong.html
	 *
	 * @param ll
	 * @return
	 */
	public double distance(GLatLng ll) {
		double latFrom = Math.toRadians(lat);
		double latTo = Math.toRadians(ll.lat);
		double lngFrom = Math.toRadians(lng);
		double lngTo = Math.toRadians(ll.lng);

		double x = (lngTo - lngFrom) * Math.cos((latTo + latFrom) / 2);
		double y = latTo - latFrom;

		return Math.sqrt(x * x + y * y) * EARTH_RADIUS;
	}

	public double getLat() {
		return lat;
	}

	public double getLng() {
		return lng;
	}

	public String toString() {
		return lat + "," + lng;
	}

	public static GLatLng getLatLng(String s) {
		try {
			String[] m = StringUtils.split(s,",");
			return new GLatLng(Float.parseFloat(m[0]), Float.parseFloat(m[1]));
		} catch (Exception e) {
			return null;
		}
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result;
		long temp;
		temp = Double.doubleToLongBits(lat);
		result = (int) (temp ^ (temp >>> 32));
		temp = Double.doubleToLongBits(lng);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		GLatLng other = (GLatLng) obj;
		if (Double.doubleToLongBits(lat) != Double.doubleToLongBits(other.lat))
			return false;
		if (Double.doubleToLongBits(lng) != Double.doubleToLongBits(other.lng))
			return false;
		return true;
	}
}